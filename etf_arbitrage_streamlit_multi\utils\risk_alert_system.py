#!/usr/bin/env python3
"""
风险管理和报警系统
提供多层次风险控制和智能报警功能
"""

import logging
import threading
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Optional, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path

# 邮件相关导入（可选）
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
    logging.warning("邮件功能不可用，将跳过邮件通知")
import sqlite3
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class RiskRule:
    """风险规则"""
    id: str
    name: str
    condition: str  # 条件表达式
    threshold: float
    message: str
    level: str  # 'INFO', 'WARNING', 'CRITICAL'
    enabled: bool = True
    last_triggered: Optional[datetime] = None
    trigger_count: int = 0

@dataclass
class AlertMessage:
    """报警消息"""
    id: str
    timestamp: datetime
    rule_id: str
    level: str
    title: str
    message: str
    data: Dict
    acknowledged: bool = False

class RiskMetrics:
    """风险指标计算器"""
    
    def __init__(self):
        self.metrics_history = []
        
    def calculate_metrics(self, positions: List[Dict], trades: List[Dict], 
                         current_capital: float, initial_capital: float) -> Dict:
        """计算风险指标"""
        try:
            metrics = {
                'timestamp': datetime.now(),
                'current_capital': current_capital,
                'initial_capital': initial_capital,
                'total_pnl': current_capital - initial_capital,
                'return_rate': (current_capital - initial_capital) / initial_capital,
                'position_count': len(positions),
                'total_market_value': sum(pos.get('quantity', 0) * pos.get('current_price', 0) for pos in positions),
                'available_cash': current_capital - sum(pos.get('cost', 0) for pos in positions),
                'position_ratio': 0.0,
                'max_drawdown': 0.0,
                'volatility': 0.0,
                'sharpe_ratio': 0.0,
                'var_95': 0.0,  # 95% VaR
                'max_single_loss': 0.0,
                'concentration_risk': 0.0
            }
            
            # 计算仓位比例
            if current_capital > 0:
                metrics['position_ratio'] = metrics['total_market_value'] / current_capital
            
            # 计算最大回撤
            if len(self.metrics_history) > 0:
                capital_series = [m['current_capital'] for m in self.metrics_history] + [current_capital]
                peak = np.maximum.accumulate(capital_series)
                drawdown = (np.array(capital_series) - peak) / peak
                metrics['max_drawdown'] = np.min(drawdown)
            
            # 计算波动率
            if len(self.metrics_history) >= 10:
                returns = []
                for i in range(1, len(self.metrics_history)):
                    prev_capital = self.metrics_history[i-1]['current_capital']
                    curr_capital = self.metrics_history[i]['current_capital']
                    if prev_capital > 0:
                        returns.append((curr_capital - prev_capital) / prev_capital)
                
                if returns:
                    metrics['volatility'] = np.std(returns) * np.sqrt(252)  # 年化波动率
                    
                    # 计算夏普比率
                    if metrics['volatility'] > 0:
                        avg_return = np.mean(returns) * 252  # 年化收益率
                        metrics['sharpe_ratio'] = avg_return / metrics['volatility']
                    
                    # 计算VaR
                    if len(returns) >= 20:
                        metrics['var_95'] = np.percentile(returns, 5) * current_capital
            
            # 计算最大单笔亏损
            sell_trades = [t for t in trades if t.get('action') == 'SELL' and t.get('pnl', 0) < 0]
            if sell_trades:
                metrics['max_single_loss'] = min(t.get('pnl', 0) for t in sell_trades)
            
            # 计算集中度风险
            if positions:
                position_values = [pos.get('quantity', 0) * pos.get('current_price', 0) for pos in positions]
                if sum(position_values) > 0:
                    weights = np.array(position_values) / sum(position_values)
                    metrics['concentration_risk'] = np.sum(weights ** 2)  # HHI指数
            
            # 保存历史记录
            self.metrics_history.append(metrics.copy())
            
            # 只保留最近1000条记录
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算风险指标失败: {e}")
            return {}

class NotificationChannel:
    """通知渠道基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.enabled = True
        
    def send(self, message: AlertMessage) -> bool:
        """发送通知"""
        raise NotImplementedError

class ConsoleNotifier(NotificationChannel):
    """控制台通知"""
    
    def __init__(self):
        super().__init__("Console")
        
    def send(self, message: AlertMessage) -> bool:
        try:
            level_colors = {
                'INFO': '\033[92m',      # 绿色
                'WARNING': '\033[93m',   # 黄色
                'CRITICAL': '\033[91m'   # 红色
            }
            reset_color = '\033[0m'
            
            color = level_colors.get(message.level, '')
            print(f"{color}[{message.level}] {message.timestamp.strftime('%H:%M:%S')} - {message.title}{reset_color}")
            print(f"  {message.message}")
            
            return True
            
        except Exception as e:
            logger.error(f"控制台通知失败: {e}")
            return False

class EmailNotifier(NotificationChannel):
    """邮件通知"""
    
    def __init__(self, smtp_server: str, smtp_port: int, username: str, 
                 password: str, recipients: List[str]):
        super().__init__("Email")
        if not EMAIL_AVAILABLE:
            logger.warning("邮件功能不可用，EmailNotifier将无法发送邮件")
            self.enabled = False
            return
            
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.username = username
        self.password = password
        self.recipients = recipients
        
    def send(self, message: AlertMessage) -> bool:
        if not EMAIL_AVAILABLE:
            logger.warning("邮件功能不可用，跳过邮件发送")
            return False
            
        try:
            msg = MimeMultipart()
            msg['From'] = self.username
            msg['To'] = ', '.join(self.recipients)
            msg['Subject'] = f"[{message.level}] {message.title}"
            
            body = f"""
            时间: {message.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
            级别: {message.level}
            标题: {message.title}
            
            消息内容:
            {message.message}
            
            相关数据:
            {json.dumps(message.data, indent=2, ensure_ascii=False)}
            
            ---
            实时交易系统自动发送
            """
            
            msg.attach(MimeText(body, 'plain', 'utf-8'))
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls()
                server.login(self.username, self.password)
                server.send_message(msg)
                
            return True
            
        except Exception as e:
            logger.error(f"邮件通知失败: {e}")
            return False

class FileNotifier(NotificationChannel):
    """文件通知"""
    
    def __init__(self, log_file: str):
        super().__init__("File")
        self.log_file = Path(log_file)
        self.log_file.parent.mkdir(parents=True, exist_ok=True)
        
    def send(self, message: AlertMessage) -> bool:
        try:
            log_entry = {
                'timestamp': message.timestamp.isoformat(),
                'level': message.level,
                'title': message.title,
                'message': message.message,
                'data': message.data
            }
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(log_entry, ensure_ascii=False) + '\n')
                
            return True
            
        except Exception as e:
            logger.error(f"文件通知失败: {e}")
            return False

class RiskAlertSystem:
    """风险报警系统"""
    
    def __init__(self):
        self.risk_rules: Dict[str, RiskRule] = {}
        self.notification_channels: List[NotificationChannel] = []
        self.alert_history: List[AlertMessage] = []
        self.risk_metrics = RiskMetrics()
        
        # 监控线程
        self.monitor_thread = None
        self.is_monitoring = False
        self.stop_event = threading.Event()
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        
        # 初始化默认规则
        self._init_default_rules()
        
        # 初始化默认通知渠道
        self._init_default_channels()
        
    def _init_default_rules(self):
        """初始化默认风险规则"""
        default_rules = [
            RiskRule(
                id="daily_loss_limit",
                name="日亏损限制",
                condition="total_pnl < -20000",
                threshold=-20000,
                message="日亏损超过限制",
                level="CRITICAL"
            ),
            RiskRule(
                id="max_drawdown_limit",
                name="最大回撤限制",
                condition="max_drawdown < -0.1",
                threshold=-0.1,
                message="最大回撤超过限制",
                level="CRITICAL"
            ),
            RiskRule(
                id="position_ratio_warning",
                name="仓位比例警告",
                condition="position_ratio > 0.9",
                threshold=0.9,
                message="仓位比例过高",
                level="WARNING"
            ),
            RiskRule(
                id="single_loss_warning",
                name="单笔亏损警告",
                condition="max_single_loss < -5000",
                threshold=-5000,
                message="单笔亏损过大",
                level="WARNING"
            ),
            RiskRule(
                id="concentration_risk",
                name="集中度风险",
                condition="concentration_risk > 0.8",
                threshold=0.8,
                message="持仓过于集中",
                level="WARNING"
            ),
            RiskRule(
                id="volatility_warning",
                name="波动率警告",
                condition="volatility > 0.3",
                threshold=0.3,
                message="波动率过高",
                level="WARNING"
            )
        ]
        
        for rule in default_rules:
            self.risk_rules[rule.id] = rule
            
    def _init_default_channels(self):
        """初始化默认通知渠道"""
        # 控制台通知
        self.notification_channels.append(ConsoleNotifier())
        
        # 文件通知
        self.notification_channels.append(FileNotifier("logs/risk_alerts.log"))
        
    def add_notification_channel(self, channel: NotificationChannel):
        """添加通知渠道"""
        self.notification_channels.append(channel)
        
    def add_alert_callback(self, callback: Callable):
        """添加报警回调函数"""
        self.alert_callbacks.append(callback)
        
    def add_risk_rule(self, rule: RiskRule):
        """添加风险规则"""
        self.risk_rules[rule.id] = rule
        
    def remove_risk_rule(self, rule_id: str):
        """移除风险规则"""
        if rule_id in self.risk_rules:
            del self.risk_rules[rule_id]
            
    def enable_rule(self, rule_id: str, enabled: bool = True):
        """启用/禁用规则"""
        if rule_id in self.risk_rules:
            self.risk_rules[rule_id].enabled = enabled
            
    def start_monitoring(self, check_interval: int = 5):
        """启动风险监控"""
        if self.is_monitoring:
            logger.warning("风险监控已在运行中")
            return False
            
        self.is_monitoring = True
        self.stop_event.clear()
        
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            args=(check_interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        logger.info("风险监控已启动")
        return True
        
    def stop_monitoring(self):
        """停止风险监控"""
        if not self.is_monitoring:
            return False
            
        self.stop_event.set()
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
            
        logger.info("风险监控已停止")
        return True
        
    def _monitoring_loop(self, check_interval: int):
        """监控循环"""
        while not self.stop_event.is_set():
            try:
                # 这里应该获取实际的交易数据
                # 目前使用模拟数据进行演示
                self._check_risks_with_mock_data()
                
                time.sleep(check_interval)
                
            except Exception as e:
                logger.error(f"风险监控循环出错: {e}")
                time.sleep(10)
                
    def _check_risks_with_mock_data(self):
        """使用模拟数据检查风险（演示用）"""
        # 模拟交易数据
        mock_positions = []
        mock_trades = []
        mock_current_capital = 980000  # 模拟亏损2万
        mock_initial_capital = 1000000
        
        self.check_risks(mock_positions, mock_trades, mock_current_capital, mock_initial_capital)
        
    def check_risks(self, positions: List[Dict], trades: List[Dict], 
                   current_capital: float, initial_capital: float):
        """检查风险"""
        try:
            # 计算风险指标
            metrics = self.risk_metrics.calculate_metrics(
                positions, trades, current_capital, initial_capital
            )
            
            if not metrics:
                return
                
            # 检查每个风险规则
            for rule in self.risk_rules.values():
                if not rule.enabled:
                    continue
                    
                try:
                    # 评估条件
                    if self._evaluate_condition(rule.condition, metrics):
                        self._trigger_alert(rule, metrics)
                        
                except Exception as e:
                    logger.error(f"评估风险规则 {rule.id} 失败: {e}")
                    
        except Exception as e:
            logger.error(f"检查风险失败: {e}")
            
    def _evaluate_condition(self, condition: str, metrics: Dict) -> bool:
        """评估条件表达式"""
        try:
            # 创建安全的评估环境
            safe_dict = {
                '__builtins__': {},
                **metrics
            }
            
            # 评估条件
            return eval(condition, safe_dict)
            
        except Exception as e:
            logger.error(f"评估条件失败: {condition}, {e}")
            return False
            
    def _trigger_alert(self, rule: RiskRule, metrics: Dict):
        """触发报警"""
        try:
            # 检查是否需要限制报警频率
            now = datetime.now()
            if rule.last_triggered:
                time_diff = (now - rule.last_triggered).total_seconds()
                if time_diff < 300:  # 5分钟内不重复报警
                    return
                    
            # 创建报警消息
            alert_id = f"ALERT_{int(time.time())}_{rule.id}"
            alert = AlertMessage(
                id=alert_id,
                timestamp=now,
                rule_id=rule.id,
                level=rule.level,
                title=rule.name,
                message=rule.message,
                data=metrics.copy()
            )
            
            # 更新规则状态
            rule.last_triggered = now
            rule.trigger_count += 1
            
            # 保存报警历史
            self.alert_history.append(alert)
            
            # 只保留最近1000条报警记录
            if len(self.alert_history) > 1000:
                self.alert_history = self.alert_history[-1000:]
                
            # 发送通知
            self._send_notifications(alert)
            
            # 调用回调函数
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    logger.error(f"报警回调失败: {e}")
                    
            logger.warning(f"触发风险报警: {rule.name} - {rule.message}")
            
        except Exception as e:
            logger.error(f"触发报警失败: {e}")
            
    def _send_notifications(self, alert: AlertMessage):
        """发送通知"""
        for channel in self.notification_channels:
            if channel.enabled:
                try:
                    success = channel.send(alert)
                    if not success:
                        logger.warning(f"通知渠道 {channel.name} 发送失败")
                except Exception as e:
                    logger.error(f"通知渠道 {channel.name} 出错: {e}")
                    
    def get_alert_summary(self, hours: int = 24) -> Dict:
        """获取报警摘要"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_alerts = [
                alert for alert in self.alert_history 
                if alert.timestamp >= cutoff_time
            ]
            
            summary = {
                'total_alerts': len(recent_alerts),
                'critical_alerts': len([a for a in recent_alerts if a.level == 'CRITICAL']),
                'warning_alerts': len([a for a in recent_alerts if a.level == 'WARNING']),
                'info_alerts': len([a for a in recent_alerts if a.level == 'INFO']),
                'most_frequent_rules': {},
                'latest_alerts': recent_alerts[-10:] if recent_alerts else []
            }
            
            # 统计最频繁的规则
            rule_counts = {}
            for alert in recent_alerts:
                rule_counts[alert.rule_id] = rule_counts.get(alert.rule_id, 0) + 1
                
            summary['most_frequent_rules'] = dict(
                sorted(rule_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            )
            
            return summary
            
        except Exception as e:
            logger.error(f"获取报警摘要失败: {e}")
            return {}
            
    def acknowledge_alert(self, alert_id: str):
        """确认报警"""
        for alert in self.alert_history:
            if alert.id == alert_id:
                alert.acknowledged = True
                break
                
    def get_risk_dashboard_data(self) -> Dict:
        """获取风险仪表板数据"""
        try:
            if not self.risk_metrics.metrics_history:
                return {}
                
            latest_metrics = self.risk_metrics.metrics_history[-1]
            alert_summary = self.get_alert_summary()
            
            return {
                'current_metrics': latest_metrics,
                'alert_summary': alert_summary,
                'active_rules': len([r for r in self.risk_rules.values() if r.enabled]),
                'total_rules': len(self.risk_rules),
                'monitoring_status': self.is_monitoring,
                'metrics_history': self.risk_metrics.metrics_history[-100:]  # 最近100条记录
            }
            
        except Exception as e:
            logger.error(f"获取风险仪表板数据失败: {e}")
            return {}

# 全局实例
risk_alert_system = RiskAlertSystem()

# 便捷函数
def start_risk_monitoring(check_interval: int = 5) -> bool:
    """启动风险监控"""
    return risk_alert_system.start_monitoring(check_interval)

def stop_risk_monitoring() -> bool:
    """停止风险监控"""
    return risk_alert_system.stop_monitoring()

def check_trading_risks(positions: List[Dict], trades: List[Dict], 
                       current_capital: float, initial_capital: float):
    """检查交易风险"""
    risk_alert_system.check_risks(positions, trades, current_capital, initial_capital)

def get_risk_status() -> Dict:
    """获取风险状态"""
    return risk_alert_system.get_risk_dashboard_data()

def add_email_notification(smtp_server: str, smtp_port: int, username: str, 
                          password: str, recipients: List[str]):
    """添加邮件通知"""
    if EMAIL_AVAILABLE:
        email_notifier = EmailNotifier(smtp_server, smtp_port, username, password, recipients)
        risk_alert_system.add_notification_channel(email_notifier)
    else:
        logger.warning("邮件功能不可用，无法添加邮件通知")