"""
交易动作优化处理器
提供统一的交易动作处理、性能优化和批量操作功能
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TradeActionType(Enum):
    """交易动作类型枚举"""
    BUY = "BUY"
    SELL = "SELL"
    PARTIAL_SELL = "PARTIAL_SELL"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    FORCE_CLOSE = "FORCE_CLOSE"
    HOLD = "HOLD"

@dataclass
class TradeActionConfig:
    """交易动作配置"""
    action_type: TradeActionType
    display_name: str
    icon: str
    color: str
    bg_color: str
    marker_symbol: str
    marker_size: int
    affects_capital: bool
    capital_multiplier: float  # 1.0 for buy, -1.0 for sell

class OptimizedTradeActionProcessor:
    """优化的交易动作处理器"""
    
    # 预定义的动作配置
    ACTION_CONFIGS = {
        TradeActionType.BUY: TradeActionConfig(
            TradeActionType.BUY, "买入", "🟢", "#28a745", "#d4edda",
            "triangle-up", 10, True, -1.0
        ),
        TradeActionType.SELL: TradeActionConfig(
            TradeActionType.SELL, "卖出", "🔴", "#dc3545", "#f8d7da",
            "triangle-down", 10, True, 1.0
        ),
        TradeActionType.PARTIAL_SELL: TradeActionConfig(
            TradeActionType.PARTIAL_SELL, "部分卖出", "🟡", "#ffc107", "#fff3cd",
            "triangle-down", 8, True, 0.5
        ),
        TradeActionType.STOP_LOSS: TradeActionConfig(
            TradeActionType.STOP_LOSS, "止损", "🛑", "#dc3545", "#f8d7da",
            "x", 12, True, 1.0
        ),
        TradeActionType.TAKE_PROFIT: TradeActionConfig(
            TradeActionType.TAKE_PROFIT, "止盈", "💰", "#28a745", "#d4edda",
            "star", 12, True, 1.0
        ),
        TradeActionType.FORCE_CLOSE: TradeActionConfig(
            TradeActionType.FORCE_CLOSE, "强制平仓", "⚠️", "#fd7e14", "#ffeaa7",
            "diamond", 10, True, 1.0
        ),
        TradeActionType.HOLD: TradeActionConfig(
            TradeActionType.HOLD, "持有", "⏸️", "#6c757d", "#e2e3e5",
            "circle", 6, False, 0.0
        )
    }
    
    # 动作别名映射
    ACTION_ALIASES = {
        "买": TradeActionType.BUY,
        "买入": TradeActionType.BUY,
        "购买": TradeActionType.BUY,
        "卖": TradeActionType.SELL,
        "卖出": TradeActionType.SELL,
        "出售": TradeActionType.SELL,
        "持有": TradeActionType.HOLD,
        "等待": TradeActionType.HOLD,
        "观望": TradeActionType.HOLD,
        "止损": TradeActionType.STOP_LOSS,
        "止盈": TradeActionType.TAKE_PROFIT,
        "部分卖出": TradeActionType.PARTIAL_SELL,
        "强制平仓": TradeActionType.FORCE_CLOSE
    }
    
    @classmethod
    def normalize_action(cls, action: str) -> TradeActionType:
        """标准化交易动作"""
        if not action:
            return TradeActionType.HOLD
        
        action = action.upper().strip()
        
        # 直接匹配枚举值
        try:
            return TradeActionType(action)
        except ValueError:
            pass
        
        # 使用别名映射
        return cls.ACTION_ALIASES.get(action, TradeActionType.HOLD)
    
    @classmethod
    def get_action_config(cls, action: TradeActionType) -> TradeActionConfig:
        """获取动作配置"""
        return cls.ACTION_CONFIGS.get(action, cls.ACTION_CONFIGS[TradeActionType.HOLD])
    
    @classmethod
    def format_action_display(cls, action: TradeActionType, style: str = "full") -> str:
        """格式化动作显示"""
        config = cls.get_action_config(action)
        
        if style == "icon_only":
            return config.icon
        elif style == "text_only":
            return config.display_name
        elif style == "colored":
            return f"<span style='color: {config.color}'>{config.icon} {config.display_name}</span>"
        else:  # full
            return f"{config.icon} {config.display_name}"
    
    @classmethod
    def batch_process_trades(cls, trades: List[Dict[str, Any]]) -> pd.DataFrame:
        """批量处理交易记录，返回优化的DataFrame"""
        if not trades:
            return pd.DataFrame()
        
        # 转换为DataFrame进行向量化处理
        df = pd.DataFrame(trades)
        
        # 标准化动作
        df['action_normalized'] = df['action'].apply(
            lambda x: cls.normalize_action(str(x)) if pd.notna(x) else TradeActionType.HOLD
        )
        
        # 添加动作配置信息
        df['action_config'] = df['action_normalized'].apply(cls.get_action_config)
        df['action_display'] = df['action_normalized'].apply(
            lambda x: cls.format_action_display(x, "full")
        )
        df['action_color'] = df['action_config'].apply(lambda x: x.color)
        df['action_icon'] = df['action_config'].apply(lambda x: x.icon)
        
        # 计算资金影响
        df['capital_impact'] = df.apply(
            lambda row: row['amount'] * row['action_config'].capital_multiplier
            if row['action_config'].affects_capital else 0, axis=1
        )
        
        return df
    
    @classmethod
    def calculate_capital_curve_optimized(cls, trades_df: pd.DataFrame, 
                                        initial_capital: float) -> Tuple[List[datetime], List[float]]:
        """优化的资金曲线计算"""
        if trades_df.empty:
            return [], []
        
        # 确保时间戳格式正确
        if 'timestamp' in trades_df.columns:
            trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            trades_df = trades_df.sort_values('timestamp')
        
        # 向量化计算累计资金变化
        capital_changes = trades_df['capital_impact'].fillna(0)
        cumulative_changes = capital_changes.cumsum()
        capital_values = initial_capital + cumulative_changes
        
        times = trades_df['timestamp'].tolist()
        capitals = capital_values.tolist()
        
        return times, capitals
    
    @classmethod
    def get_trade_statistics_optimized(cls, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """优化的交易统计计算"""
        if trades_df.empty:
            return {}
        
        stats = {}
        
        # 按动作类型分组统计
        action_counts = trades_df['action_normalized'].value_counts()
        
        for action_type in TradeActionType:
            count = action_counts.get(action_type, 0)
            config = cls.get_action_config(action_type)
            
            stats[action_type.value] = {
                'count': count,
                'display_name': config.display_name,
                'icon': config.icon,
                'color': config.color
            }
        
        # 计算总体统计
        total_trades = len(trades_df)
        buy_trades = action_counts.get(TradeActionType.BUY, 0)
        sell_trades = sum(action_counts.get(action, 0) for action in [
            TradeActionType.SELL, TradeActionType.PARTIAL_SELL, 
            TradeActionType.STOP_LOSS, TradeActionType.TAKE_PROFIT
        ])
        
        stats['summary'] = {
            'total_trades': total_trades,
            'buy_trades': buy_trades,
            'sell_trades': sell_trades,
            'buy_ratio': buy_trades / total_trades if total_trades > 0 else 0,
            'sell_ratio': sell_trades / total_trades if total_trades > 0 else 0
        }
        
        return stats
    
    @classmethod
    def filter_trades_by_action(cls, trades_df: pd.DataFrame, 
                              action_types: List[TradeActionType]) -> pd.DataFrame:
        """按动作类型过滤交易记录"""
        if trades_df.empty:
            return trades_df
        
        return trades_df[trades_df['action_normalized'].isin(action_types)]
    
    @classmethod
    def get_chart_markers_config(cls) -> Dict[str, Dict[str, Any]]:
        """获取图表标记配置"""
        markers = {}
        
        for action_type, config in cls.ACTION_CONFIGS.items():
            markers[action_type.value.lower()] = {
                'name': config.display_name,
                'color': config.color,
                'symbol': config.marker_symbol,
                'size': config.marker_size,
                'hovertemplate': f'{config.display_name}<br>时间: %{{x}}<br>资金: ¥%{{y:,.2f}}<extra></extra>'
            }
        
        return markers
