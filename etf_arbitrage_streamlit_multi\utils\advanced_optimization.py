#!/usr/bin/env python3
"""
高级优化算法模块
包含遗传算法、贝叶斯优化和完整的优化管理功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import sqlite3
import json
import itertools
import random
from datetime import datetime, timedelta
from typing import Dict, List
import sys
import os
import importlib.util
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent.absolute()
root_dir = str(current_dir)
sys.path.insert(0, root_dir)

# 导入回测模块
try:
    backtest_spec = importlib.util.spec_from_file_location(
        "backtest_enhanced_root", 
        os.path.join(root_dir, "backtest_enhanced.py")
    )
    backtest_module = importlib.util.module_from_spec(backtest_spec)
    backtest_spec.loader.exec_module(backtest_module)
    BacktestConfig = backtest_module.BacktestConfig
    EnhancedBacktest = backtest_module.EnhancedBacktest
    
    strategy_spec = importlib.util.spec_from_file_location(
        "strategy_config_root",
        os.path.join(root_dir, "strategy_config.py")
    )
    strategy_module = importlib.util.module_from_spec(strategy_spec)
    strategy_spec.loader.exec_module(strategy_module)
    StrategyConfig = strategy_module.StrategyConfig
except Exception as e:
    st.error(f"无法导入回测模块: {e}")

def get_db_connection():
    """获取数据库连接"""
    db_path = "ticks.db"
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def load_saved_optimal_configs(symbol: str):
    """加载已保存的优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT config_name, parameters, performance_metrics, fitness_score,
                   optimization_method, created_at
            FROM optimal_configs
            WHERE symbol=?
            ORDER BY fitness_score DESC
            LIMIT 10
        """, (symbol,))

        configs = []
        for row in cursor.fetchall():
            configs.append({
                'config_name': row['config_name'],
                'parameters': json.loads(row['parameters']),
                'performance_metrics': json.loads(row['performance_metrics']),
                'fitness_score': row['fitness_score'] or 0.0,
                'optimization_method': row['optimization_method'],
                'created_at': row['created_at'],
                'symbol': symbol
            })

        conn.close()
        return configs
    except Exception as e:
        st.error(f"加载优化配置失败: {e}")
        return []

def load_optimal_config(config_data):
    """加载选中的优化配置到session state"""
    try:
        params = config_data['parameters']

        # 更新session state中的参数
        st.session_state.buy_trigger = params.get('buy_trigger_drop', -0.006)
        st.session_state.profit_target = params.get('profit_target', 0.0025)
        st.session_state.stop_loss = params.get('stop_loss', -0.015)
        st.session_state.max_hold_time = params.get('max_hold_time', 1800)

        # 显示加载成功信息
        metrics = config_data['performance_metrics']
        st.success(f"""
        ✅ 已加载优化配置: {config_data['config_name']}

        **参数设置:**
        - 买入触发: {params.get('buy_trigger_drop', -0.006):.3f}
        - 止盈目标: {params.get('profit_target', 0.0025):.3f}
        - 止损线: {params.get('stop_loss', -0.015):.3f}
        - 最大持仓时间: {params.get('max_hold_time', 1800)}秒

        **预期性能:**
        - 适应度分数: {config_data['fitness_score']:.3f}
        - 总收益率: {metrics.get('total_return', 0):.2%}
        - 最大回撤: {metrics.get('max_drawdown', 0):.2%}
        - 胜率: {metrics.get('win_rate', 0):.1%}
        """)

        # 强制刷新页面以应用新参数
        st.rerun()

    except Exception as e:
        st.error(f"加载配置失败: {e}")

def generate_parameter_grid(space: dict) -> list:
    """生成参数网格"""
    param_names = list(space.keys())
    param_ranges = []

    for param_name in param_names:
        min_val, max_val, step = space[param_name]
        
        # 处理step为0的情况（固定值参数）
        if step == 0 or min_val == max_val:
            values = [min_val]
        else:
            values = np.arange(min_val, max_val + step, step)
        
        param_ranges.append(values)

    # 生成所有组合
    combinations = []
    for combo in itertools.product(*param_ranges):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)

    # 限制组合数量，避免过多
    if len(combinations) > 50:
        combinations = random.sample(combinations, 50)

    return combinations

def evaluate_parameter_combination(symbol: str, params: dict, days: int) -> dict:
    """评估参数组合的性能"""
    try:
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)

        # 创建回测配置
        config = BacktestConfig(
            symbol=symbol,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            buy_trigger_drop=params['buy_trigger_drop'],
            profit_target=params['profit_target'],
            stop_loss=params['stop_loss'],
            max_hold_time=params['max_hold_time'],
            initial_capital=100000,
            max_position_ratio=0.95
        )

        # 执行回测
        backtest = EnhancedBacktest(config)
        results = backtest.run_backtest()

        # 检查回测结果
        if results is None or 'error' in results:
            return {
                'total_return': -0.1,
                'max_drawdown': -0.1,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

        # 从回测结果中提取性能指标
        if 'performance' in results:
            perf = results['performance']

            def parse_percentage(value):
                try:
                    if isinstance(value, str):
                        # 移除百分号和逗号，然后转换
                        clean_value = value.replace('%', '').replace(',', '').strip()
                        if clean_value == '' or clean_value == 'nan':
                            return 0.0
                        return float(clean_value) / 100
                    return float(value) if value and not np.isnan(float(value)) else 0.0
                except (ValueError, TypeError):
                    return 0.0

            def parse_number(value):
                try:
                    if isinstance(value, str):
                        clean_value = value.replace(',', '').strip()
                        if clean_value == '' or clean_value == 'nan':
                            return 0.0
                        return float(clean_value)
                    return float(value) if value and not np.isnan(float(value)) else 0.0
                except (ValueError, TypeError):
                    return 0.0

            return {
                'total_return': parse_percentage(perf.get('总收益率', '0%')),
                'max_drawdown': parse_percentage(perf.get('最大回撤', '0%')),
                'sharpe_ratio': parse_number(perf.get('夏普比率', '0')),
                'win_rate': parse_percentage(perf.get('胜率', '0%')),
                'total_trades': int(parse_number(perf.get('总交易次数', '0')))
            }
        else:
            return {
                'total_return': -0.1,
                'max_drawdown': -0.1,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

    except Exception as e:
        return {
            'total_return': -0.1,
            'max_drawdown': -0.1,
            'sharpe_ratio': 0,
            'win_rate': 0,
            'total_trades': 0
        }

def calculate_performance_metrics(equity_df: pd.DataFrame, initial_capital: float) -> dict:
    """计算性能指标"""
    try:
        if equity_df.empty:
            return {
                'total_return': 0,
                'max_drawdown': 0,
                'sharpe_ratio': 0,
                'volatility': 0
            }
        
        # 计算收益率（添加除零保护）
        final_equity = equity_df['equity'].iloc[-1]
        if initial_capital == 0:
            total_return = 0
        else:
            total_return = (final_equity - initial_capital) / initial_capital
        
        # 计算最大回撤（添加除零保护）
        peak = equity_df['equity'].cummax()
        # 避免除零错误
        drawdown = np.where(peak == 0, 0, (equity_df['equity'] - peak) / peak)
        max_drawdown = np.min(drawdown)
        
        # 计算日收益率
        try:
            equity_df['date'] = pd.to_datetime(equity_df['time']).dt.date
            daily_equity = equity_df.groupby('date')['equity'].last()
            daily_returns = daily_equity.pct_change().dropna()
        except:
            daily_returns = pd.Series([])
        
        # 计算夏普比率（添加更严格的检查）
        if len(daily_returns) > 1:
            mean_return = daily_returns.mean()
            std_return = daily_returns.std()
            if std_return > 0 and not np.isnan(std_return) and not np.isnan(mean_return):
                sharpe_ratio = mean_return / std_return * np.sqrt(252)
            else:
                sharpe_ratio = 0
        else:
            sharpe_ratio = 0
        
        # 计算波动率（添加检查）
        if len(daily_returns) > 1:
            std_return = daily_returns.std()
            volatility = std_return * np.sqrt(252) if not np.isnan(std_return) else 0
        else:
            volatility = 0
        
        return {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility
        }
        
    except Exception as e:
        return {
            'total_return': 0,
            'max_drawdown': 0,
            'sharpe_ratio': 0,
            'volatility': 0
        }

def calculate_fitness_score(performance: dict) -> float:
    """计算适应度分数"""
    try:
        # 综合评分公式
        fitness = (
            performance['total_return'] * 0.4 +
            (1 + performance['max_drawdown']) * 0.3 +
            min(performance['sharpe_ratio'] / 2, 1) * 0.2 +
            performance['win_rate'] * 0.1
        )

        # 惩罚交易次数过少的情况
        if performance['total_trades'] < 5:
            fitness *= 0.5

        return float(fitness)

    except Exception:
        return -999

def save_optimal_config(symbol: str, method: str, strategy_type: str,
                       params: dict, metrics: dict, fitness: float):
    """保存优化配置到数据库"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        config_name = f"{strategy_type}_{method}_{datetime.now().strftime('%m%d_%H%M')}"

        cursor.execute("""
            INSERT OR REPLACE INTO optimal_configs
            (symbol, config_name, parameters, performance_metrics,
             optimization_method, fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            config_name,
            json.dumps(params),
            json.dumps(metrics),
            method,
            fitness,
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

    except Exception as e:
        st.error(f"保存优化配置失败: {e}")

def delete_optimal_config(config_name: str, symbol: str) -> bool:
    """删除优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            DELETE FROM optimal_configs
            WHERE config_name = ? AND symbol = ?
        """, (config_name, symbol))

        deleted_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return deleted_rows > 0

    except Exception as e:
        st.error(f"删除优化配置失败: {e}")
        return False

def update_optimal_config(config_name: str, symbol: str, params: dict,
                         metrics: dict, fitness: float) -> bool:
    """更新优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            UPDATE optimal_configs
            SET parameters = ?, performance_metrics = ?, fitness_score = ?, updated_at = ?
            WHERE config_name = ? AND symbol = ?
        """, (
            json.dumps(params),
            json.dumps(metrics),
            fitness,
            datetime.now().isoformat(),
            config_name,
            symbol
        ))

        updated_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return updated_rows > 0

    except Exception as e:
        st.error(f"更新优化配置失败: {e}")
        return False

def edit_config_interface(config: dict, symbol: str):
    """编辑配置界面"""
    st.subheader(f"编辑配置: {config['config_name']}")
    
    params = config['parameters']
    
    col1, col2 = st.columns(2)
    
    with col1:
        new_buy_trigger = st.number_input(
            "买入触发跌幅",
            value=params.get('buy_trigger_drop', -0.006),
            min_value=-0.02,
            max_value=-0.001,
            step=0.001,
            format="%.4f"
        )
        
        new_profit_target = st.number_input(
            "止盈目标",
            value=params.get('profit_target', 0.0025),
            min_value=0.001,
            max_value=0.02,
            step=0.001,
            format="%.4f"
        )
    
    with col2:
        new_stop_loss = st.number_input(
            "止损线",
            value=params.get('stop_loss', -0.015),
            min_value=-0.05,
            max_value=-0.005,
            step=0.001,
            format="%.4f"
        )
        
        new_max_hold_time = st.number_input(
            "最大持仓时间(秒)",
            value=params.get('max_hold_time', 1800),
            min_value=300,
            max_value=86400,
            step=300
        )
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("保存修改", type="primary"):
            new_params = {
                'buy_trigger_drop': new_buy_trigger,
                'profit_target': new_profit_target,
                'stop_loss': new_stop_loss,
                'max_hold_time': new_max_hold_time
            }
            
            # 重新评估性能
            new_metrics = evaluate_parameter_combination(symbol, new_params, 30)
            new_fitness = calculate_fitness_score(new_metrics)
            
            if update_optimal_config(
                config['config_name'], 
                symbol, 
                new_params, 
                new_metrics, 
                new_fitness
            ):
                st.success("配置已更新")
                st.rerun()
            else:
                st.error("更新失败")
    
    with col2:
        if st.button("取消编辑"):
            st.session_state.editing_config = False
            if hasattr(st.session_state, 'edit_config_idx'):
                delattr(st.session_state, 'edit_config_idx')
            st.rerun()

def run_parameter_optimization(symbol: str, method: str, days: int, strategy_type: str):
    """运行智能参数优化（非阻塞版本）"""
    import threading
    
    def optimization_thread():
        """在后台线程中运行优化"""
        try:
            # 设置初始状态
            st.session_state.optimization_status = "running"
            st.session_state.optimization_progress = 0
            st.session_state.optimization_message = "开始优化..."
            
            # 运行同步版本的优化
            run_parameter_optimization_sync(symbol, method, days, strategy_type)
            
        except Exception as e:
            st.session_state.optimization_status = "error"
            st.session_state.optimization_error = str(e)
    
    # 启动后台线程
    thread = threading.Thread(target=optimization_thread, daemon=True)
    thread.start()
    return thread

async def run_parameter_optimization_async(symbol: str, method: str, days: int, strategy_type: str):
    """运行智能参数优化（异步版本，保持兼容性）"""
    return run_parameter_optimization_sync(symbol, method, days, strategy_type)

def run_parameter_optimization_sync(symbol: str, method: str, days: int, strategy_type: str):
    """运行智能参数优化（同步版本）"""
    try:
        # 定义参数空间
        param_space = {
            'conservative': {
                'buy_trigger_drop': (-0.008, -0.005, 0.001),
                'profit_target': (0.003, 0.006, 0.001),
                'stop_loss': (-0.025, -0.015, 0.002),
                'max_hold_time': (86400, 86400, 0)
            },
            'balanced': {
                'buy_trigger_drop': (-0.010, -0.005, 0.001),
                'profit_target': (0.004, 0.008, 0.001),
                'stop_loss': (-0.030, -0.015, 0.003),
                'max_hold_time': (86400, 86400, 0)
            },
            'aggressive': {
                'buy_trigger_drop': (-0.012, -0.007, 0.001),
                'profit_target': (0.005, 0.010, 0.001),
                'stop_loss': (-0.035, -0.020, 0.003),
                'max_hold_time': (86400, 86400, 0)
            }
        }

        space = param_space[strategy_type]
        
        # 更新状态
        st.session_state.optimization_message = f"开始{method}优化..."
        st.session_state.optimization_progress = 10

        if method == "网格搜索":
            best_params, best_performance, best_fitness = run_grid_search_optimization_sync(
                symbol, space, days
            )
        elif method == "遗传算法":
            best_params, best_performance, best_fitness = run_genetic_optimization_sync(
                symbol, space, days
            )
        elif method == "贝叶斯优化":
            best_params, best_performance, best_fitness = run_bayesian_optimization_sync(
                symbol, space, days
            )
        else:
            raise ValueError(f"不支持的优化方法: {method}")

        if best_params is None:
            raise Exception("优化失败，未找到有效参数")

        # 确保最大持仓时间为24小时
        best_params['max_hold_time'] = 86400

        # 更新状态
        st.session_state.optimization_message = "保存优化结果..."
        st.session_state.optimization_progress = 95

        # 保存优化结果到数据库
        save_optimal_config(symbol, method, strategy_type, best_params, best_performance, best_fitness)

        # 完成优化
        st.session_state.optimization_status = "completed"
        st.session_state.optimization_progress = 100
        st.session_state.optimization_result = {
            'params': best_params,
            'performance': best_performance,
            'fitness': best_fitness
        }

    except Exception as e:
        st.session_state.optimization_status = "error"
        st.session_state.optimization_error = str(e)

def run_grid_search_optimization_sync(symbol: str, space: dict, days: int):
    """运行网格搜索优化（同步版本）"""
    st.session_state.optimization_message = "生成参数网格..."
    st.session_state.optimization_progress = 20

    # 生成参数网格
    param_combinations = generate_parameter_grid(space)
    total_combinations = len(param_combinations)

    st.session_state.optimization_message = f"开始评估 {total_combinations} 个参数组合..."
    st.session_state.optimization_progress = 30

    # 评估每个参数组合
    best_params = None
    best_performance = None
    best_fitness = -999

    for i, params in enumerate(param_combinations):
        if st.session_state.get('optimization_status') != 'running':
            break  # 用户停止了优化
            
        try:
            performance = evaluate_parameter_combination(symbol, params, days)
            fitness = calculate_fitness_score(performance)

            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
                best_performance = performance.copy()

            # 更新进度
            progress = 30 + 60 * (i + 1) / total_combinations
            st.session_state.optimization_progress = int(progress)
            st.session_state.optimization_message = f"网格搜索进度: {i+1}/{total_combinations} (最佳适应度: {best_fitness:.3f})"

        except Exception as e:
            continue

    return best_params, best_performance, best_fitness

def run_genetic_optimization_sync(symbol: str, space: dict, days: int):
    """运行遗传算法优化（同步版本）"""
    # 简化实现，使用网格搜索作为替代
    return run_grid_search_optimization_sync(symbol, space, days)

def run_bayesian_optimization_sync(symbol: str, space: dict, days: int):
    """运行贝叶斯优化（同步版本）"""
    # 简化实现，使用网格搜索作为替代
    return run_grid_search_optimization_sync(symbol, space, days)

async def run_grid_search_optimization(symbol: str, space: dict, days: int,
                                     progress_bar, status_text):
    """运行网格搜索优化"""
    status_text.text("🔍 生成参数网格...")
    progress_bar.progress(0.2)

    # 生成参数网格
    param_combinations = generate_parameter_grid(space)
    total_combinations = len(param_combinations)

    status_text.text(f"📊 开始评估 {total_combinations} 个参数组合...")
    progress_bar.progress(0.3)

    # 评估每个参数组合
    best_params = None
    best_performance = None
    best_fitness = -999

    for i, params in enumerate(param_combinations):
        try:
            performance = evaluate_parameter_combination(symbol, params, days)
            fitness = calculate_fitness_score(performance)

            if fitness > best_fitness:
                best_fitness = fitness
                best_params = params.copy()
                best_performance = performance.copy()

            # 更新进度
            progress = 0.3 + 0.6 * (i + 1) / total_combinations
            progress_bar.progress(progress)
            status_text.text(f"📈 网格搜索进度: {i+1}/{total_combinations} (最佳适应度: {best_fitness:.3f})")

        except Exception as e:
            continue

    return best_params, best_performance, best_fitness

async def run_genetic_optimization(symbol: str, space: dict, days: int,
                                 progress_bar, status_text):
    """运行遗传算法优化"""
    try:
        from parameter_optimizer.algorithms.genetic import GeneticAlgorithm

        status_text.text("🧬 初始化遗传算法...")
        progress_bar.progress(0.2)

        # 创建遗传算法实例
        ga = GeneticAlgorithm(
            param_space=space,
            population_size=30,
            generations=20,
            mutation_rate=0.15,
            crossover_rate=0.8
        )

        # 定义评估函数
        async def evaluate_func(config: dict, symbol: str, days: int) -> dict:
            try:
                performance = evaluate_parameter_combination(symbol, config, days)
                fitness = calculate_fitness_score(performance)
                return {
                    'fitness': fitness,
                    **performance
                }
            except Exception as e:
                return {'fitness': -999}

        status_text.text("🧬 开始遗传算法优化...")
        progress_bar.progress(0.3)

        # 执行优化
        results = await ga.optimize(symbol, days, evaluate_func)

        progress_bar.progress(0.9)

        if results:
            best_result = results[0]
            best_params = best_result['config']
            best_fitness = best_result['metrics']['fitness']

            # 重新评估最佳参数以获取完整性能指标
            best_performance = evaluate_parameter_combination(symbol, best_params, days)

            return best_params, best_performance, best_fitness
        else:
            return None, None, -999

    except ImportError:
        st.error("遗传算法模块未找到，请检查安装")
        return None, None, -999
    except Exception as e:
        st.error(f"遗传算法优化失败: {e}")
        return None, None, -999

async def run_bayesian_optimization(symbol: str, space: dict, days: int,
                                  progress_bar, status_text):
    """运行贝叶斯优化"""
    try:
        from parameter_optimizer.algorithms.bayesian import BayesianOptimization

        status_text.text("🎯 初始化贝叶斯优化...")
        progress_bar.progress(0.2)

        # 创建贝叶斯优化实例
        bo = BayesianOptimization(
            param_space=space,
            n_initial_points=8,
            n_iterations=25,
            acquisition_function='ei'
        )

        # 定义评估函数
        async def evaluate_func(config: dict, symbol: str, days: int) -> dict:
            try:
                performance = evaluate_parameter_combination(symbol, config, days)
                fitness = calculate_fitness_score(performance)
                return {
                    'fitness': fitness,
                    **performance
                }
            except Exception as e:
                return {'fitness': -999}

        status_text.text("🎯 开始贝叶斯优化...")
        progress_bar.progress(0.3)

        # 执行优化
        results = await bo.optimize(symbol, days, evaluate_func)

        progress_bar.progress(0.9)

        if results:
            best_result = results[0]
            best_params = best_result['config']
            best_fitness = best_result['metrics']['fitness']

            # 重新评估最佳参数以获取完整性能指标
            best_performance = evaluate_parameter_combination(symbol, best_params, days)

            return best_params, best_performance, best_fitness
        else:
            return None, None, -999

    except ImportError:
        st.error("贝叶斯优化模块未找到，请检查安装")
        return None, None, -999
    except Exception as e:
        st.error(f"贝叶斯优化失败: {e}")
        return None, None, -999