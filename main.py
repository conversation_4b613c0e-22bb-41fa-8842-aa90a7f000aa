#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一入口：交互式菜单
- 初始化数据库
- 采集逐笔（API轮询 或 Playwright 监听）
- 启动回测面板（增强版Streamlit界面）
- 启动实时交易模拟面板（增强版Streamlit界面）

运行：python main.py
"""
"""
主要功能

●初始化数据库：调用 init_db.py
●采集逐笔
    ○ API 轮询：fetch_ticks_eastmoney.api_poll_loop（推荐上手）
    ○ Playwright 监听：fetch_ticks_eastmoney.run（需安装 playwright && playwright install）
●回测面板：启动增强版Streamlit回测界面，支持
    ○ 可视化参数设置：买入触发、止盈止损、分批交易等
    ○ 实时回测结果展示：净值曲线、统计指标、交易记录
    ○ 报表导出：CSV格式的交易记录和统计数据
●实时交易模拟面板：启动增强版Streamlit实时交易界面，支持
    ○ 实时策略监控：仓位状态、盈亏情况、风险指标
    ○ 参数动态调整：实时修改交易参数
    ○ 交易记录追踪：实时交易日志和统计

使用方法

●运行：python main.py

●菜单说明：
    ○ 1 初始化数据库
    ○ 2 采集逐笔（API 轮询）
    ○ 3 采集逐笔（Playwright 监听）
    ○ 4 启动回测面板（增强版Streamlit界面，可视化参数设置和结果展示）
    ○ 5 启动实时交易模拟面板（增强版Streamlit界面，实时监控和交易）
    ○ 0 退出

●如果选择 Playwright 监听而未安装依赖，按提示安装：
    ○ pip install playwright
    ○ playwright install

●备注
    ○ 回测面板和实时交易模拟面板提供完整的可视化界面，支持参数调整和结果展示。
    ○ 面板默认标的 159740，可在界面中修改。
    ○ 需要安装 streamlit: pip install streamlit
"""
import asyncio
import sys
import threading
import time
from typing import List, Optional
import subprocess

def _ask(prompt: str, default: Optional[str] = None) -> str:
    p = f"{prompt} [{default}]: " if default is not None else f"{prompt}: "
    try:
        s = input(p).strip()
        return s if s else (default if default is not None else "")
    except (EOFError, KeyboardInterrupt):
        print("\n用户中断，返回菜单。")
        return default if default is not None else ""

def _ask_int(prompt: str, default: int) -> int:
    s = _ask(prompt, str(default))
    try:
        return int(s)
    except Exception:
        print(f"输入无效，使用默认值 {default}")
        return int(default)

def _ask_bool(prompt: str, default: bool) -> bool:
    s = _ask(prompt, "y" if default else "n").lower()
    if s in ("y","yes","1","t","true","是","好"):
        return True
    if s in ("n","no","0","f","false","否","不"):
        return False
    return default

def action_init_db() -> None:
    try:
        from init_db import init_db, DB  # type: ignore
    except Exception as e:
        print(f"导入初始化模块失败: {e}")
        return
    try:
        init_db()
        print(f"已初始化数据库: {DB}")
    except Exception as e:
        print(f"初始化数据库失败: {e}")

def action_fetch_api() -> None:
    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,159934,510300)", "159740,159934,510300")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    poll_seconds = _ask_int("API 轮询间隔秒", 5)
    count = _ask_int("每次拉取最近条数(count)", 200)
    continuous = _ask_bool("在交易时间内持续监听? (y/n)", True)
    print("开始 API 轮询，Ctrl+C 可停止...")
    try:
        fmod = __import__("fetch_ticks_eastmoney")
        if not hasattr(fmod, "api_poll_loop"):
            print("fetch_ticks_eastmoney 中未找到 api_poll_loop")
            return
        threads: List[threading.Thread] = []
        for sym in symbols:
            t = threading.Thread(
                target=fmod.api_poll_loop,
                args=(sym,),
                kwargs={"poll_seconds": poll_seconds, "count": count, "continuous": continuous},
                daemon=True,
            )
            t.start()
            threads.append(t)
        print(f"已启动 {len(threads)} 个API轮询: {', '.join(symbols)}")
        try:
            while any(t.is_alive() for t in threads):
                time.sleep(0.5)
        except KeyboardInterrupt:
            print("\n已停止 API 轮询。")
    except Exception as e:
        print(f"启动 API 轮询失败: {e}\n提示：该脚本顶层依赖 Playwright，未安装可能导致导入失败。请执行: pip install playwright && playwright install")

def action_fetch_playwright() -> None:
    symbol_input = _ask("标的代码，可逗号分隔(如: 159740,159934,510300)", "159740,159934,510300")
    symbols = [s.strip() for s in symbol_input.split(",") if s.strip()]
    dump_dir = _ask("响应样本保存目录(可为空)", "")
    dump_dir = dump_dir if dump_dir else None
    listen_seconds = _ask_int("监听秒数", 30)
    continuous = _ask_bool("在交易时间内持续监听? (y/n)", True)
    print("启动 Playwright 监听，Ctrl+C 可停止...")
    try:
        fmod = __import__("fetch_ticks_eastmoney")
        if not hasattr(fmod, "run"):
            print("fetch_ticks_eastmoney 中未找到 run (async)")
            return
        async def _run_many():
            tasks = [asyncio.create_task(fmod.run(sym, dump_dir, listen_seconds, continuous=continuous)) for sym in symbols]
            await asyncio.gather(*tasks)
        asyncio.run(_run_many())
    except KeyboardInterrupt:
        print("\n已停止 Playwright 监听。")
    except Exception as e:
        print(f"启动 Playwright 失败: {e}\n请确保已安装: pip install playwright && playwright install")

def action_enhanced_backtest_panel() -> None:
    """
    启动增强版回测面板（Streamlit）
    """
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8501)

    python = sys.executable
    p = None
    try:
        print("启动增强版回测面板中... Ctrl+C 可停止")
        # 使用streamlit运行增强版回测面板
        cmd = [python, "-m", "streamlit", "run", "app_enhanced_backtest_dashboard.py",
               "--server.address", host, "--server.port", str(port)]
        p = subprocess.Popen(cmd)
        print(f"已启动：增强版回测面板 → http://{host}:{port}")
        print("在面板中可以选择标的代码和设置参数")
        p.wait()
    except KeyboardInterrupt:
        print("\n停止增强版回测面板...")
    except Exception as e:
        print(f"增强版回测面板启动失败: {e}")
        print("请确保已安装: pip install streamlit")
    finally:
        try:
            if p and p.poll() is None:
                p.terminate()
        except Exception:
            pass

def action_enhanced_realtime_panel() -> None:
    """
    启动增强版实时交易模拟面板（Streamlit）
    """
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8502)

    python = sys.executable
    p = None
    try:
        print("启动增强版实时交易模拟面板中... Ctrl+C 可停止")
        # 使用streamlit运行增强版实时交易模拟面板
        cmd = [python, "-m", "streamlit", "run", "app_enhanced_realtime_dashboard.py",
               "--server.address", host, "--server.port", str(port)]
        p = subprocess.Popen(cmd)
        print(f"已启动：增强版实时交易模拟面板 → http://{host}:{port}")
        print("在面板中可以选择标的代码和配置交易参数")
        p.wait()
    except KeyboardInterrupt:
        print("\n停止增强版实时交易模拟面板...")
    except Exception as e:
        print(f"增强版实时交易模拟面板启动失败: {e}")
        print("请确保已安装: pip install streamlit")
    finally:
        try:
            if p and p.poll() is None:
                p.terminate()
        except Exception:
            pass


def action_dashboard() -> None:
    try:
        from app_dashboard import run_dashboard  # type: ignore
    except Exception as e:
        print(f"导入监测面板失败: {e}")
        return
    symbol = _ask("监测标的(单一，如: 159740)", "159740")
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8050)
    print("启动监测面板中... Ctrl+C 可停止")
    try:
        run_dashboard(symbol=symbol, host=host, port=port)
    except KeyboardInterrupt:
        print("\n已停止监测面板。")
    except Exception as e:
        print("监测面板启动失败: {0}\n请先安装: pip install dash feffery-antd-components pandas plotly requests".format(e))

def action_backtest_panel() -> None:
    """
    启动回测监测面板（Dash+Feffery）
    """
    symbol = _ask("回测标的(单一，如: 159740)", "159740")
    host = _ask("服务Host", "127.0.0.1")
    port = _ask_int("服务端口", 8051)

    python = sys.executable
    p = None
    try:
        print("启动回测监测面板中... Ctrl+C 可停止")
        p = subprocess.Popen([python, "app_backtest_dashboard.py", "--symbol", symbol, "--host", host, "--port", str(port)])
        print(f"已启动：回测面板 → http://{host}:{port}")
        p.wait()
    except KeyboardInterrupt:
        print("\n停止回测面板...")
    except Exception as e:
        print(f"回测面板启动失败: {e}")
    finally:
        try:
            if p and p.poll() is None:
                p.terminate()
        except Exception:
            pass

def action_oneclick() -> None:
    """
    一键运行：采集(API连续)+策略引擎+监测面板。Ctrl+C 统一退出。
    """
    symbol = _ask("一键运行标的(单一，如: 159740)", "159740")
    host = "127.0.0.1"
    port = _ask_int("面板端口", 8050)

    python = sys.executable
    procs: List = []
    try:
        print("启动采集(API连续)...")
        p1 = subprocess.Popen([python, "fetch_ticks_eastmoney.py", "--symbol", symbol, "--continuous"])
        procs.append(p1)

        print("启动策略引擎...")
        p2 = subprocess.Popen([python, "strategy_engine.py", "--symbol", symbol, "--poll-sec", "1"])
        procs.append(p2)

        print("启动监测面板...")
        p3 = subprocess.Popen([python, "app_dashboard.py", "--symbol", symbol, "--host", host, "--port", str(port)])
        procs.append(p3)

        print(f"已启动：采集+策略+面板 → http://{host}:{port}  (Ctrl+C 停止)")
        while True:
            time.sleep(1.0)
    except KeyboardInterrupt:
        print("\n收到中断，正在停止子进程...")
    except Exception as e:
        print(f"一键运行启动失败: {e}")
    finally:
        for p in procs:
            try:
                p.terminate()
            except Exception:
                pass
        time.sleep(0.5)
        for p in procs:
            try:
                if p.poll() is None:
                    p.kill()
            except Exception:
                pass
        print("已停止所有子进程。")

def main() -> None:
    while True:
        print("\n==== ETF Arbitrage 统一入口 ====")
        print("1) 初始化数据库")
        print("2) 采集逐笔 - API轮询")
        print("3) 采集逐笔 - Playwright监听")
        print("4) 启动回测面板")
        print("5) 启动实时交易模拟面板")
        # print("6) 实时监测面板(Dash+Feffery)")
        # print("7) 一键运行：采集+策略+面板")
        # print("8) 回测监测面板(Dash+Feffery-回测)")
        print("0) 退出")
        choice = _ask("请选择功能编号", "4")
        if choice == "1":
            action_init_db()
        elif choice == "2":
            action_fetch_api()
        elif choice == "3":
            action_fetch_playwright()
        elif choice == "4":
            action_enhanced_backtest_panel()
        elif choice == "5":
            action_enhanced_realtime_panel()
        # elif choice == "6":
        #     action_dashboard()
        # elif choice == "7":
        #     action_oneclick()
        # elif choice == "8":
        #     action_backtest_panel()
        elif choice == "0":
            print("已退出。")
            break
        else:
            print("无效的选择，请重试。")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n已退出。")
        sys.exit(0)