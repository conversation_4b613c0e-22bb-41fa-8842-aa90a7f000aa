#!/usr/bin/env python3
"""
简化的重构测试
"""

import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """测试导入"""
    print("🔍 测试核心模块导入...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.database_manager import DatabaseManager
        print("✅ DatabaseManager 导入成功")
        
        from etf_arbitrage_streamlit_multi.core.config_manager import ConfigManager
        print("✅ ConfigManager 导入成功")
        
        from etf_arbitrage_streamlit_multi.core.logger_manager import LoggerManager
        print("✅ LoggerManager 导入成功")
        
        from etf_arbitrage_streamlit_multi.core.exception_handler import ExceptionHandler
        print("✅ ExceptionHandler 导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_enhanced_trader():
    """测试增强版交易器"""
    print("🔍 测试增强版交易器导入...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import (
            RealTimeDataFeed, CORE_INFRASTRUCTURE_AVAILABLE
        )
        
        print(f"✅ 增强版交易器导入成功")
        print(f"✅ 核心基础设施可用: {CORE_INFRASTRUCTURE_AVAILABLE}")
        
        # 测试数据源创建
        data_feed = RealTimeDataFeed()
        print(f"✅ 数据源创建成功: {data_feed.db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强版交易器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始简化重构测试")
    print("=" * 40)
    
    tests = [test_imports, test_enhanced_trader]
    passed = 0
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"📊 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 重构测试通过！")
    else:
        print("⚠️ 部分测试失败")

if __name__ == "__main__":
    main()
