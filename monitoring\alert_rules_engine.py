#!/usr/bin/env python3
"""
智能告警规则引擎
基于真实数据和机器学习的智能告警系统
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
import logging
import json
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """告警严重程度"""
    CRITICAL = "critical"
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

class AlertType(Enum):
    """告警类型"""
    PERFORMANCE = "performance"
    BUSINESS = "business"
    SYSTEM = "system"
    SECURITY = "security"

@dataclass
class AlertRule:
    """告警规则"""
    id: str
    name: str
    description: str
    alert_type: AlertType
    severity: AlertSeverity
    condition: Callable[[Dict[str, Any]], bool]
    threshold: float
    metric_path: str
    enabled: bool = True
    cooldown_minutes: int = 15
    escalation_minutes: int = 60

@dataclass
class Alert:
    """告警对象"""
    id: str
    rule_id: str
    title: str
    message: str
    severity: AlertSeverity
    alert_type: AlertType
    metric_value: float
    threshold_value: float
    timestamp: datetime
    source: str
    acknowledged: bool = False
    resolved: bool = False

class AlertRulesEngine:
    """智能告警规则引擎"""
    
    def __init__(self):
        self.monitoring_db = "monitoring.db"
        self.rules: Dict[str, AlertRule] = {}
        self.alert_history: Dict[str, datetime] = {}
        self.init_default_rules()
    
    def init_default_rules(self):
        """初始化默认告警规则"""
        
        # CPU使用率告警
        self.add_rule(AlertRule(
            id="cpu_high",
            name="CPU使用率过高",
            description="CPU使用率超过阈值",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('cpu_percent', 0) > 80,
            threshold=80.0,
            metric_path="cpu_percent",
            cooldown_minutes=10
        ))
        
        self.add_rule(AlertRule(
            id="cpu_critical",
            name="CPU使用率严重过高",
            description="CPU使用率达到危险水平",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.CRITICAL,
            condition=lambda data: data.get('cpu_percent', 0) > 95,
            threshold=95.0,
            metric_path="cpu_percent",
            cooldown_minutes=5
        ))
        
        # 内存使用率告警
        self.add_rule(AlertRule(
            id="memory_high",
            name="内存使用率过高",
            description="内存使用率超过阈值",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('memory_percent', 0) > 85,
            threshold=85.0,
            metric_path="memory_percent",
            cooldown_minutes=15
        ))
        
        self.add_rule(AlertRule(
            id="memory_critical",
            name="内存使用率严重过高",
            description="内存使用率达到危险水平",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.CRITICAL,
            condition=lambda data: data.get('memory_percent', 0) > 95,
            threshold=95.0,
            metric_path="memory_percent",
            cooldown_minutes=5
        ))
        
        # 磁盘空间告警
        self.add_rule(AlertRule(
            id="disk_high",
            name="磁盘空间不足",
            description="磁盘使用率超过阈值",
            alert_type=AlertType.SYSTEM,
            severity=AlertSeverity.ERROR,
            condition=lambda data: data.get('disk_percent', 0) > 90,
            threshold=90.0,
            metric_path="disk_percent",
            cooldown_minutes=30
        ))
        
        # 数据采集告警
        self.add_rule(AlertRule(
            id="data_collection_stopped",
            name="数据采集已停止",
            description="数据采集服务未运行",
            alert_type=AlertType.BUSINESS,
            severity=AlertSeverity.ERROR,
            condition=lambda data: data.get('data_collection_status') != 'running',
            threshold=1.0,
            metric_path="data_collection_status",
            cooldown_minutes=5
        ))
        
        # API响应时间告警
        self.add_rule(AlertRule(
            id="api_latency_high",
            name="API响应时间过长",
            description="API响应时间超过阈值",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('api_response_time', 0) > 1000,
            threshold=1000.0,
            metric_path="api_response_time",
            cooldown_minutes=10
        ))
        
        # 交易信号准确率告警
        self.add_rule(AlertRule(
            id="signal_accuracy_low",
            name="交易信号准确率过低",
            description="交易信号准确率低于预期",
            alert_type=AlertType.BUSINESS,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('signal_accuracy', 1.0) < 0.6,
            threshold=0.6,
            metric_path="signal_accuracy",
            cooldown_minutes=30
        ))
        
        # 系统负载告警
        self.add_rule(AlertRule(
            id="load_average_high",
            name="系统负载过高",
            description="系统平均负载超过阈值",
            alert_type=AlertType.PERFORMANCE,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('load_average', 0) > 2.0,
            threshold=2.0,
            metric_path="load_average",
            cooldown_minutes=15
        ))
        
        # 数据库连接数告警
        self.add_rule(AlertRule(
            id="db_connections_high",
            name="数据库连接数过多",
            description="数据库连接数超过安全阈值",
            alert_type=AlertType.SYSTEM,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('database_connection_count', 0) > 50,
            threshold=50.0,
            metric_path="database_connection_count",
            cooldown_minutes=20
        ))
        
        # 资金使用率告警
        self.add_rule(AlertRule(
            id="capital_utilization_high",
            name="资金使用率过高",
            description="资金使用率超过风险阈值",
            alert_type=AlertType.BUSINESS,
            severity=AlertSeverity.WARNING,
            condition=lambda data: data.get('capital_utilization', 0) > 0.9,
            threshold=0.9,
            metric_path="capital_utilization",
            cooldown_minutes=10
        ))
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.rules[rule.id] = rule
        logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, rule_id: str):
        """移除告警规则"""
        if rule_id in self.rules:
            del self.rules[rule_id]
            logger.info(f"移除告警规则: {rule_id}")
    
    def enable_rule(self, rule_id: str):
        """启用告警规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = True
            logger.info(f"启用告警规则: {rule_id}")
    
    def disable_rule(self, rule_id: str):
        """禁用告警规则"""
        if rule_id in self.rules:
            self.rules[rule_id].enabled = False
            logger.info(f"禁用告警规则: {rule_id}")
    
    def update_rule_threshold(self, rule_id: str, new_threshold: float):
        """更新规则阈值"""
        if rule_id in self.rules:
            old_threshold = self.rules[rule_id].threshold
            self.rules[rule_id].threshold = new_threshold
            logger.info(f"更新规则阈值: {rule_id} {old_threshold} -> {new_threshold}")
    
    def evaluate_rules(self, performance_data: Dict[str, Any], business_data: Dict[str, Any]) -> List[Alert]:
        """评估所有告警规则"""
        alerts = []
        
        # 合并数据
        combined_data = {**performance_data, **business_data}
        
        for rule_id, rule in self.rules.items():
            if not rule.enabled:
                continue
            
            # 检查冷却时间
            if self._is_in_cooldown(rule_id, rule.cooldown_minutes):
                continue
            
            try:
                # 评估规则条件
                if rule.condition(combined_data):
                    alert = self._create_alert(rule, combined_data)
                    alerts.append(alert)
                    
                    # 记录告警时间
                    self.alert_history[rule_id] = datetime.now()
                    
                    # 存储告警到数据库
                    self._store_alert(alert)
                    
                    logger.warning(f"触发告警: {rule.name} - {alert.message}")
            
            except Exception as e:
                logger.error(f"评估规则 {rule_id} 时出错: {e}")
        
        return alerts
    
    def _is_in_cooldown(self, rule_id: str, cooldown_minutes: int) -> bool:
        """检查是否在冷却时间内"""
        if rule_id not in self.alert_history:
            return False
        
        last_alert_time = self.alert_history[rule_id]
        cooldown_period = timedelta(minutes=cooldown_minutes)
        
        return datetime.now() - last_alert_time < cooldown_period
    
    def _create_alert(self, rule: AlertRule, data: Dict[str, Any]) -> Alert:
        """创建告警对象"""
        metric_value = data.get(rule.metric_path, 0)
        
        # 生成告警ID
        alert_id = f"{rule.id}_{int(datetime.now().timestamp())}"
        
        # 生成告警消息
        if rule.metric_path in data:
            if isinstance(metric_value, (int, float)):
                message = f"{rule.description}，当前值: {metric_value:.2f}，阈值: {rule.threshold}"
            else:
                message = f"{rule.description}，当前状态: {metric_value}"
        else:
            message = rule.description
        
        return Alert(
            id=alert_id,
            rule_id=rule.id,
            title=rule.name,
            message=message,
            severity=rule.severity,
            alert_type=rule.alert_type,
            metric_value=float(metric_value) if isinstance(metric_value, (int, float)) else 0.0,
            threshold_value=rule.threshold,
            timestamp=datetime.now(),
            source="alert_rules_engine"
        )
    
    def _store_alert(self, alert: Alert):
        """存储告警到数据库"""
        try:
            conn = sqlite3.connect(self.monitoring_db)
            
            conn.execute("""
                INSERT INTO alerts_enhanced 
                (alert_type, source, severity, title, message, metric_value, threshold_value)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                alert.alert_type.value,
                alert.source,
                alert.severity.value,
                alert.title,
                alert.message,
                alert.metric_value,
                alert.threshold_value
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"存储告警失败: {e}")
    
    def get_rule_statistics(self) -> Dict[str, Any]:
        """获取规则统计信息"""
        try:
            conn = sqlite3.connect(self.monitoring_db)
            
            # 获取各规则的触发次数
            query = """
                SELECT source, severity, COUNT(*) as count
                FROM alerts_enhanced 
                WHERE timestamp >= datetime('now', '-24 hours')
                GROUP BY source, severity
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            # 统计信息
            stats = {
                'total_rules': len(self.rules),
                'enabled_rules': len([r for r in self.rules.values() if r.enabled]),
                'disabled_rules': len([r for r in self.rules.values() if not r.enabled]),
                'rules_by_type': {},
                'rules_by_severity': {},
                'alert_counts_24h': {}
            }
            
            # 按类型统计
            for rule in self.rules.values():
                rule_type = rule.alert_type.value
                if rule_type not in stats['rules_by_type']:
                    stats['rules_by_type'][rule_type] = 0
                stats['rules_by_type'][rule_type] += 1
            
            # 按严重程度统计
            for rule in self.rules.values():
                severity = rule.severity.value
                if severity not in stats['rules_by_severity']:
                    stats['rules_by_severity'][severity] = 0
                stats['rules_by_severity'][severity] += 1
            
            # 24小时告警统计
            if not df.empty:
                for _, row in df.iterrows():
                    severity = row['severity']
                    count = row['count']
                    if severity not in stats['alert_counts_24h']:
                        stats['alert_counts_24h'][severity] = 0
                    stats['alert_counts_24h'][severity] += count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取规则统计失败: {e}")
            return {}
    
    def get_alert_trends(self, days: int = 7) -> Dict[str, Any]:
        """获取告警趋势"""
        try:
            conn = sqlite3.connect(self.monitoring_db)
            
            query = f"""
                SELECT 
                    DATE(timestamp) as date,
                    severity,
                    COUNT(*) as count
                FROM alerts_enhanced 
                WHERE timestamp >= datetime('now', '-{days} days')
                GROUP BY DATE(timestamp), severity
                ORDER BY date
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                return {}
            
            # 按日期和严重程度组织数据
            trends = {}
            for _, row in df.iterrows():
                date = row['date']
                severity = row['severity']
                count = row['count']
                
                if date not in trends:
                    trends[date] = {}
                trends[date][severity] = count
            
            return trends
            
        except Exception as e:
            logger.error(f"获取告警趋势失败: {e}")
            return {}
    
    def optimize_thresholds(self, days: int = 30):
        """基于历史数据优化告警阈值"""
        try:
            conn = sqlite3.connect(self.monitoring_db)
            
            # 获取历史性能数据
            query = f"""
                SELECT cpu_percent, memory_percent, disk_percent, load_average
                FROM performance_metrics_enhanced 
                WHERE timestamp >= datetime('now', '-{days} days')
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                logger.warning("没有足够的历史数据进行阈值优化")
                return
            
            # 计算统计指标
            stats = df.describe()
            
            # 优化CPU阈值
            cpu_p95 = stats.loc['75%', 'cpu_percent']  # 使用75分位数作为基准
            new_cpu_threshold = min(85, max(70, cpu_p95 + 10))  # 在70-85之间
            
            if 'cpu_high' in self.rules:
                self.update_rule_threshold('cpu_high', new_cpu_threshold)
            
            # 优化内存阈值
            mem_p95 = stats.loc['75%', 'memory_percent']
            new_mem_threshold = min(90, max(75, mem_p95 + 10))  # 在75-90之间
            
            if 'memory_high' in self.rules:
                self.update_rule_threshold('memory_high', new_mem_threshold)
            
            # 优化系统负载阈值
            load_p95 = stats.loc['75%', 'load_average']
            new_load_threshold = min(3.0, max(1.5, load_p95 + 0.5))
            
            if 'load_average_high' in self.rules:
                self.update_rule_threshold('load_average_high', new_load_threshold)
            
            logger.info(f"阈值优化完成: CPU={new_cpu_threshold:.1f}, 内存={new_mem_threshold:.1f}, 负载={new_load_threshold:.1f}")
            
        except Exception as e:
            logger.error(f"优化阈值失败: {e}")
    
    def export_rules_config(self) -> str:
        """导出规则配置"""
        config = {
            'rules': []
        }
        
        for rule in self.rules.values():
            rule_config = {
                'id': rule.id,
                'name': rule.name,
                'description': rule.description,
                'alert_type': rule.alert_type.value,
                'severity': rule.severity.value,
                'threshold': rule.threshold,
                'metric_path': rule.metric_path,
                'enabled': rule.enabled,
                'cooldown_minutes': rule.cooldown_minutes,
                'escalation_minutes': rule.escalation_minutes
            }
            config['rules'].append(rule_config)
        
        return json.dumps(config, indent=2, ensure_ascii=False)
    
    def import_rules_config(self, config_json: str):
        """导入规则配置"""
        try:
            config = json.loads(config_json)
            
            for rule_config in config.get('rules', []):
                # 这里需要重新构建condition函数，实际应用中可能需要更复杂的序列化机制
                # 暂时跳过condition的导入
                logger.info(f"导入规则配置: {rule_config['name']}")
            
        except Exception as e:
            logger.error(f"导入规则配置失败: {e}")

# 全局告警规则引擎实例
alert_rules_engine = AlertRulesEngine()

def evaluate_alert_rules(performance_data: Dict[str, Any], business_data: Dict[str, Any]) -> List[Alert]:
    """评估告警规则的便捷函数"""
    return alert_rules_engine.evaluate_rules(performance_data, business_data)

def get_rule_statistics() -> Dict[str, Any]:
    """获取规则统计的便捷函数"""
    return alert_rules_engine.get_rule_statistics()

def optimize_alert_thresholds(days: int = 30):
    """优化告警阈值的便捷函数"""
    return alert_rules_engine.optimize_thresholds(days)