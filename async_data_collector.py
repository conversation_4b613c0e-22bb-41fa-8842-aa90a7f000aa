#!/usr/bin/env python3
"""
异步数据采集器
实现高性能的异步数据采集和处理
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
import json
import time
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import queue
import threading

logger = logging.getLogger(__name__)

@dataclass
class DataPoint:
    """数据点"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    signal: float = 0.0
    metadata: Dict[str, Any] = None

class AsyncDataCollector:
    """异步数据采集器"""
    
    def __init__(self, 
                 max_concurrent_requests: int = 10,
                 request_timeout: int = 30,
                 retry_attempts: int = 3,
                 rate_limit_per_second: int = 100):
        """
        初始化异步数据采集器
        
        Args:
            max_concurrent_requests: 最大并发请求数
            request_timeout: 请求超时时间
            retry_attempts: 重试次数
            rate_limit_per_second: 每秒请求限制
        """
        self.max_concurrent_requests = max_concurrent_requests
        self.request_timeout = request_timeout
        self.retry_attempts = retry_attempts
        self.rate_limit_per_second = rate_limit_per_second
        
        # 并发控制
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        self.rate_limiter = asyncio.Semaphore(rate_limit_per_second)
        
        # 数据缓存
        self.data_cache: Dict[str, List[DataPoint]] = {}
        self.cache_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        logger.info(f"异步数据采集器初始化: 并发={max_concurrent_requests}, 限速={rate_limit_per_second}/s")
    
    async def collect_single_symbol(self, symbol: str, 
                                   data_source: str = 'akshare') -> Optional[DataPoint]:
        """
        采集单个标的数据
        
        Args:
            symbol: 标的代码
            data_source: 数据源
            
        Returns:
            数据点或None
        """
        async with self.semaphore:  # 并发控制
            async with self.rate_limiter:  # 限速控制
                try:
                    self.stats['total_requests'] += 1
                    
                    # 检查缓存
                    cached_data = await self._get_from_cache(symbol)
                    if cached_data:
                        self.stats['cache_hits'] += 1
                        return cached_data
                    
                    self.stats['cache_misses'] += 1
                    
                    # 根据数据源采集数据
                    if data_source == 'akshare':
                        data_point = await self._collect_from_akshare(symbol)
                    elif data_source == 'tushare':
                        data_point = await self._collect_from_tushare(symbol)
                    else:
                        data_point = await self._collect_mock_data(symbol)
                    
                    if data_point:
                        # 缓存数据
                        await self._cache_data(symbol, data_point)
                        self.stats['successful_requests'] += 1
                        return data_point
                    else:
                        self.stats['failed_requests'] += 1
                        return None
                        
                except Exception as e:
                    logger.error(f"采集 {symbol} 数据失败: {e}")
                    self.stats['failed_requests'] += 1
                    return None
    
    async def collect_multiple_symbols(self, symbols: List[str],
                                     data_source: str = 'akshare') -> Dict[str, Optional[DataPoint]]:
        """
        并发采集多个标的数据
        
        Args:
            symbols: 标的代码列表
            data_source: 数据源
            
        Returns:
            标的代码到数据点的映射
        """
        logger.info(f"开始并发采集 {len(symbols)} 个标的数据...")
        
        # 创建并发任务
        tasks = [
            self.collect_single_symbol(symbol, data_source)
            for symbol in symbols
        ]
        
        # 并发执行
        start_time = time.time()
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()
        
        # 整理结果
        data_dict = {}
        successful_count = 0
        
        for symbol, result in zip(symbols, results):
            if isinstance(result, Exception):
                logger.error(f"采集 {symbol} 异常: {result}")
                data_dict[symbol] = None
            else:
                data_dict[symbol] = result
                if result:
                    successful_count += 1
        
        logger.info(f"并发采集完成: {successful_count}/{len(symbols)} 成功, "
                   f"耗时 {end_time - start_time:.2f}秒")
        
        return data_dict
    
    async def _collect_from_akshare(self, symbol: str) -> Optional[DataPoint]:
        """从akshare采集数据"""
        try:
            # 在线程池中执行同步的akshare调用
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor(max_workers=5) as executor:
                future = loop.run_in_executor(executor, self._sync_akshare_call, symbol)
                result = await asyncio.wait_for(future, timeout=self.request_timeout)
            
            if result:
                return DataPoint(
                    symbol=symbol,
                    timestamp=datetime.now(),
                    price=result.get('price', 0.0),
                    volume=result.get('volume', 0),
                    signal=result.get('signal', 0.0),
                    metadata={'source': 'akshare', 'raw_data': result}
                )
            
            return None
            
        except asyncio.TimeoutError:
            logger.warning(f"akshare采集 {symbol} 超时")
            return None
        except Exception as e:
            logger.error(f"akshare采集 {symbol} 失败: {e}")
            return None
    
    def _sync_akshare_call(self, symbol: str) -> Optional[Dict]:
        """同步的akshare调用"""
        try:
            import akshare as ak
            
            # 获取实时数据
            df = ak.stock_zh_a_spot_em()
            if df is not None and not df.empty:
                # 查找对应的股票
                stock_data = df[df['代码'] == symbol]
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    return {
                        'price': float(row['最新价']),
                        'volume': int(row['成交量']),
                        'signal': 0.0  # 需要后续计算
                    }
            
            return None
            
        except Exception as e:
            logger.error(f"同步akshare调用失败: {e}")
            return None
    
    async def _collect_from_tushare(self, symbol: str) -> Optional[DataPoint]:
        """从tushare采集数据"""
        try:
            # 模拟tushare API调用
            await asyncio.sleep(0.1)  # 模拟网络延迟
            
            # 这里应该是真实的tushare API调用
            # 暂时返回模拟数据
            return await self._collect_mock_data(symbol)
            
        except Exception as e:
            logger.error(f"tushare采集 {symbol} 失败: {e}")
            return None
    
    async def _collect_mock_data(self, symbol: str) -> Optional[DataPoint]:
        """采集模拟数据"""
        try:
            await asyncio.sleep(0.05)  # 模拟网络延迟
            
            import random
            
            # 生成模拟数据
            base_price = 1.0
            price = base_price + random.uniform(-0.1, 0.1)
            volume = random.randint(1000, 10000)
            signal = random.uniform(-0.02, 0.02)
            
            return DataPoint(
                symbol=symbol,
                timestamp=datetime.now(),
                price=price,
                volume=volume,
                signal=signal,
                metadata={'source': 'mock'}
            )
            
        except Exception as e:
            logger.error(f"模拟数据生成失败: {e}")
            return None
    
    async def _get_from_cache(self, symbol: str) -> Optional[DataPoint]:
        """从缓存获取数据"""
        async with self.cache_lock:
            if symbol in self.data_cache:
                cache_list = self.data_cache[symbol]
                if cache_list:
                    latest_data = cache_list[-1]
                    # 检查数据是否过期（5秒内有效）
                    if (datetime.now() - latest_data.timestamp).total_seconds() < 5:
                        return latest_data
            return None
    
    async def _cache_data(self, symbol: str, data_point: DataPoint):
        """缓存数据"""
        async with self.cache_lock:
            if symbol not in self.data_cache:
                self.data_cache[symbol] = []
            
            self.data_cache[symbol].append(data_point)
            
            # 保持缓存大小（最多保留100个数据点）
            if len(self.data_cache[symbol]) > 100:
                self.data_cache[symbol] = self.data_cache[symbol][-100:]
    
    async def start_continuous_collection(self, symbols: List[str],
                                        interval_seconds: int = 5,
                                        callback: Callable = None):
        """
        启动连续数据采集
        
        Args:
            symbols: 标的代码列表
            interval_seconds: 采集间隔（秒）
            callback: 数据回调函数
        """
        logger.info(f"启动连续数据采集: {len(symbols)} 个标的, 间隔 {interval_seconds}秒")
        
        while True:
            try:
                # 采集数据
                data_dict = await self.collect_multiple_symbols(symbols)
                
                # 调用回调函数
                if callback:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(data_dict)
                        else:
                            callback(data_dict)
                    except Exception as e:
                        logger.error(f"数据回调函数执行失败: {e}")
                
                # 等待下一次采集
                await asyncio.sleep(interval_seconds)
                
            except Exception as e:
                logger.error(f"连续数据采集异常: {e}")
                await asyncio.sleep(interval_seconds)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self.stats['total_requests']
        if total_requests > 0:
            success_rate = self.stats['successful_requests'] / total_requests
            cache_hit_rate = self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])
        else:
            success_rate = 0.0
            cache_hit_rate = 0.0
        
        return {
            **self.stats,
            'success_rate': success_rate,
            'cache_hit_rate': cache_hit_rate,
            'cached_symbols': len(self.data_cache)
        }
    
    async def cleanup(self):
        """清理资源"""
        async with self.cache_lock:
            self.data_cache.clear()
        
        logger.info("异步数据采集器资源已清理")


# 测试函数
async def test_async_data_collector():
    """测试异步数据采集器"""
    logger.info("开始测试异步数据采集器...")
    
    try:
        # 创建采集器
        collector = AsyncDataCollector(
            max_concurrent_requests=5,
            rate_limit_per_second=10
        )
        
        # 测试单个标的采集
        logger.info("测试单个标的采集...")
        data_point = await collector.collect_single_symbol("159740")
        if data_point:
            logger.info(f"✅ 单个采集成功: {data_point.symbol} - {data_point.price}")
        
        # 测试多个标的并发采集
        logger.info("测试多个标的并发采集...")
        symbols = ["159740", "159741", "159742", "159743", "159744"]
        data_dict = await collector.collect_multiple_symbols(symbols)
        
        successful_count = sum(1 for data in data_dict.values() if data)
        logger.info(f"✅ 并发采集完成: {successful_count}/{len(symbols)} 成功")
        
        # 显示统计信息
        stats = collector.get_stats()
        logger.info(f"📊 采集统计: 成功率 {stats['success_rate']:.2%}, "
                   f"缓存命中率 {stats['cache_hit_rate']:.2%}")
        
        # 清理资源
        await collector.cleanup()
        
        logger.info("✅ 异步数据采集器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 异步数据采集器测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_async_data_collector())
