#!/usr/bin/env python3
"""
性能监控系统
实时监控系统性能指标和资源使用情况
"""

import asyncio
import logging
import time
import psutil
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque
import json
import weakref

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_sent_mb: float
    network_recv_mb: float
    active_threads: int
    open_files: int
    
    # 应用级指标
    request_count: int = 0
    response_time_ms: float = 0.0
    error_count: int = 0
    cache_hit_rate: float = 0.0
    queue_size: int = 0

@dataclass
class AlertThreshold:
    """告警阈值"""
    metric_name: str
    warning_threshold: float
    critical_threshold: float
    duration_seconds: int = 60  # 持续时间
    enabled: bool = True

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, 
                 collection_interval: int = 5,
                 history_size: int = 1000,
                 enable_alerts: bool = True):
        """
        初始化性能监控器
        
        Args:
            collection_interval: 采集间隔(秒)
            history_size: 历史数据保留数量
            enable_alerts: 是否启用告警
        """
        self.collection_interval = collection_interval
        self.history_size = history_size
        self.enable_alerts = enable_alerts
        
        # 性能数据历史
        self.metrics_history: deque = deque(maxlen=history_size)
        
        # 告警阈值配置
        self.alert_thresholds = self._init_default_thresholds()
        
        # 告警状态
        self.alert_states: Dict[str, Dict] = {}
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task = None
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        self.metrics_callbacks: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'total_collections': 0,
            'alert_count': 0,
            'last_collection_time': None
        }
        
        logger.info(f"性能监控器初始化: 间隔{collection_interval}s, 历史{history_size}条")
    
    def _init_default_thresholds(self) -> Dict[str, AlertThreshold]:
        """初始化默认告警阈值"""
        return {
            'cpu_percent': AlertThreshold('CPU使用率', 70.0, 90.0, 60),
            'memory_percent': AlertThreshold('内存使用率', 80.0, 95.0, 60),
            'disk_io_read_mb': AlertThreshold('磁盘读取', 100.0, 500.0, 30),
            'disk_io_write_mb': AlertThreshold('磁盘写入', 100.0, 500.0, 30),
            'response_time_ms': AlertThreshold('响应时间', 1000.0, 5000.0, 30),
            'error_count': AlertThreshold('错误数量', 10, 50, 60),
            'queue_size': AlertThreshold('队列大小', 1000, 5000, 30)
        }
    
    async def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            logger.warning("监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("性能监控已启动")
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("性能监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        logger.info("开始性能监控循环")
        
        while self.is_monitoring:
            try:
                # 收集性能指标
                metrics = await self._collect_metrics()
                
                # 存储历史数据
                self.metrics_history.append(metrics)
                
                # 检查告警
                if self.enable_alerts:
                    await self._check_alerts(metrics)
                
                # 调用回调函数
                await self._notify_metrics_callbacks(metrics)
                
                # 更新统计
                self.stats['total_collections'] += 1
                self.stats['last_collection_time'] = datetime.now()
                
                # 等待下次采集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.collection_interval)
        
        logger.info("性能监控循环结束")
    
    async def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        try:
            # 系统资源指标
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 磁盘I/O
            disk_io = psutil.disk_io_counters()
            disk_read_mb = disk_io.read_bytes / 1024 / 1024 if disk_io else 0
            disk_write_mb = disk_io.write_bytes / 1024 / 1024 if disk_io else 0
            
            # 网络I/O
            network_io = psutil.net_io_counters()
            network_sent_mb = network_io.bytes_sent / 1024 / 1024 if network_io else 0
            network_recv_mb = network_io.bytes_recv / 1024 / 1024 if network_io else 0
            
            # 进程信息
            process = psutil.Process()
            active_threads = process.num_threads()
            open_files = len(process.open_files())
            
            metrics = PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_used_mb=memory.used / 1024 / 1024,
                disk_io_read_mb=disk_read_mb,
                disk_io_write_mb=disk_write_mb,
                network_sent_mb=network_sent_mb,
                network_recv_mb=network_recv_mb,
                active_threads=active_threads,
                open_files=open_files
            )
            
            return metrics
            
        except Exception as e:
            logger.error(f"收集性能指标失败: {e}")
            # 返回默认指标
            return PerformanceMetrics(
                timestamp=datetime.now(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                disk_io_read_mb=0.0,
                disk_io_write_mb=0.0,
                network_sent_mb=0.0,
                network_recv_mb=0.0,
                active_threads=0,
                open_files=0
            )
    
    async def _check_alerts(self, metrics: PerformanceMetrics):
        """检查告警条件"""
        current_time = datetime.now()
        
        for metric_name, threshold in self.alert_thresholds.items():
            if not threshold.enabled:
                continue
            
            # 获取指标值
            metric_value = getattr(metrics, metric_name, 0)
            
            # 检查阈值
            alert_level = None
            if metric_value >= threshold.critical_threshold:
                alert_level = 'critical'
            elif metric_value >= threshold.warning_threshold:
                alert_level = 'warning'
            
            # 处理告警状态
            if alert_level:
                await self._handle_alert(metric_name, alert_level, metric_value, 
                                        threshold, current_time)
            else:
                # 清除告警状态
                if metric_name in self.alert_states:
                    await self._clear_alert(metric_name)
    
    async def _handle_alert(self, metric_name: str, level: str, value: float,
                          threshold: AlertThreshold, current_time: datetime):
        """处理告警"""
        if metric_name not in self.alert_states:
            # 新告警
            self.alert_states[metric_name] = {
                'level': level,
                'start_time': current_time,
                'value': value,
                'notified': False
            }
        else:
            # 更新现有告警
            alert_state = self.alert_states[metric_name]
            alert_state['level'] = level
            alert_state['value'] = value
            
            # 检查是否需要发送告警
            if not alert_state['notified']:
                duration = (current_time - alert_state['start_time']).total_seconds()
                if duration >= threshold.duration_seconds:
                    await self._send_alert(metric_name, level, value, duration)
                    alert_state['notified'] = True
                    self.stats['alert_count'] += 1
    
    async def _clear_alert(self, metric_name: str):
        """清除告警"""
        if metric_name in self.alert_states:
            alert_state = self.alert_states[metric_name]
            if alert_state['notified']:
                await self._send_alert_recovery(metric_name)
            del self.alert_states[metric_name]
    
    async def _send_alert(self, metric_name: str, level: str, value: float, duration: float):
        """发送告警"""
        alert_data = {
            'type': 'performance_alert',
            'metric_name': metric_name,
            'level': level,
            'value': value,
            'duration': duration,
            'timestamp': datetime.now().isoformat(),
            'message': f"{metric_name} {level}告警: 当前值 {value:.2f}, 持续 {duration:.0f}秒"
        }
        
        logger.warning(f"性能告警: {alert_data['message']}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert_data)
                else:
                    callback(alert_data)
            except Exception as e:
                logger.error(f"告警回调执行失败: {e}")
    
    async def _send_alert_recovery(self, metric_name: str):
        """发送告警恢复"""
        recovery_data = {
            'type': 'performance_recovery',
            'metric_name': metric_name,
            'timestamp': datetime.now().isoformat(),
            'message': f"{metric_name} 告警已恢复"
        }
        
        logger.info(f"告警恢复: {recovery_data['message']}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(recovery_data)
                else:
                    callback(recovery_data)
            except Exception as e:
                logger.error(f"恢复回调执行失败: {e}")
    
    async def _notify_metrics_callbacks(self, metrics: PerformanceMetrics):
        """通知指标回调"""
        for callback in self.metrics_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(metrics)
                else:
                    callback(metrics)
            except Exception as e:
                logger.error(f"指标回调执行失败: {e}")
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def add_metrics_callback(self, callback: Callable):
        """添加指标回调"""
        self.metrics_callbacks.append(callback)
    
    def update_threshold(self, metric_name: str, warning: float, critical: float):
        """更新告警阈值"""
        if metric_name in self.alert_thresholds:
            threshold = self.alert_thresholds[metric_name]
            threshold.warning_threshold = warning
            threshold.critical_threshold = critical
            logger.info(f"更新阈值 {metric_name}: 警告={warning}, 严重={critical}")
    
    def get_current_metrics(self) -> Optional[PerformanceMetrics]:
        """获取当前指标"""
        return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self, minutes: int = 60) -> List[PerformanceMetrics]:
        """获取历史指标"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [m for m in self.metrics_history if m.timestamp >= cutoff_time]
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        active_alerts = len(self.alert_states)
        critical_alerts = sum(1 for state in self.alert_states.values() 
                            if state['level'] == 'critical')
        warning_alerts = sum(1 for state in self.alert_states.values() 
                           if state['level'] == 'warning')
        
        return {
            'active_alerts': active_alerts,
            'critical_alerts': critical_alerts,
            'warning_alerts': warning_alerts,
            'total_alerts': self.stats['alert_count'],
            'alert_details': self.alert_states
        }
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.get_metrics_history(10)  # 最近10分钟
        
        if not recent_metrics:
            return {}
        
        # 计算平均值
        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_response_time = sum(m.response_time_ms for m in recent_metrics) / len(recent_metrics)
        
        return {
            'monitoring_status': 'active' if self.is_monitoring else 'stopped',
            'collection_interval': self.collection_interval,
            'total_collections': self.stats['total_collections'],
            'last_collection': self.stats['last_collection_time'].isoformat() if self.stats['last_collection_time'] else None,
            'recent_averages': {
                'cpu_percent': round(avg_cpu, 2),
                'memory_percent': round(avg_memory, 2),
                'response_time_ms': round(avg_response_time, 2)
            },
            'alert_summary': self.get_alert_summary()
        }


# 测试函数
async def test_performance_monitor():
    """测试性能监控器"""
    logger.info("开始测试性能监控器...")
    
    try:
        # 创建监控器
        monitor = PerformanceMonitor(collection_interval=2, history_size=10)
        
        # 添加回调函数
        def alert_handler(alert_data):
            logger.info(f"收到告警: {alert_data['message']}")
        
        def metrics_handler(metrics):
            logger.debug(f"性能指标: CPU={metrics.cpu_percent:.1f}%, "
                        f"内存={metrics.memory_percent:.1f}%")
        
        monitor.add_alert_callback(alert_handler)
        monitor.add_metrics_callback(metrics_handler)
        
        # 启动监控
        await monitor.start_monitoring()
        
        # 运行一段时间
        await asyncio.sleep(10)
        
        # 获取性能摘要
        summary = monitor.get_performance_summary()
        logger.info(f"性能摘要: {summary}")
        
        # 停止监控
        await monitor.stop_monitoring()
        
        logger.info("✅ 性能监控器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 性能监控器测试失败: {e}")
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_performance_monitor())
