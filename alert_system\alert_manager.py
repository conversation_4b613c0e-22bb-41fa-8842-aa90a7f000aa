#!/usr/bin/env python3
"""
预警管理器
统一管理预警规则和通知发送
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

from .rule_engine import RuleEngine
from .notifiers.notifier_factory import NotifierFactory, NotifierManager

logger = logging.getLogger(__name__)

class AlertManager:
    """预警管理器"""
    
    def __init__(self, notifiers: List[Any] = None):
        """
        初始化预警管理器
        
        Args:
            notifiers: 通知器列表
        """
        self.rule_engine = RuleEngine()
        self.notifier_manager = NotifierManager(notifiers or [])
        
        # 预警历史
        self.alert_history: List[Dict] = []
        
        # 预警统计
        self.alert_stats = {
            'total_alerts': 0,
            'successful_notifications': 0,
            'failed_notifications': 0
        }
        
        logger.info("预警管理器初始化完成")
    
    def add_notifier(self, notifier: Any):
        """添加通知器"""
        self.notifier_manager.add_notifier(notifier)
        logger.info(f"添加通知器: {notifier.__class__.__name__}")
    
    def add_rule(self, rule_name: str, condition: str, message_template: str = None):
        """添加预警规则"""
        self.rule_engine.add_rule(rule_name, condition, message_template)
        logger.info(f"添加预警规则: {rule_name}")
    
    def remove_rule(self, rule_name: str):
        """移除预警规则"""
        self.rule_engine.remove_rule(rule_name)
        logger.info(f"移除预警规则: {rule_name}")
    
    async def check_alerts(self, data: Dict[str, Any]) -> List[Dict]:
        """
        检查预警条件
        
        Args:
            data: 市场数据
            
        Returns:
            触发的预警列表
        """
        triggered_alerts = []
        
        try:
            # 检查所有规则
            for rule_name, rule in self.rule_engine.rules.items():
                try:
                    if self.rule_engine.evaluate_rule(rule_name, data):
                        alert = {
                            'rule_name': rule_name,
                            'condition': rule['condition'],
                            'message': rule.get('message_template', f"预警: {rule_name}"),
                            'data': data,
                            'timestamp': datetime.now().isoformat(),
                            'triggered': True
                        }
                        triggered_alerts.append(alert)
                        
                        # 发送通知
                        await self._send_alert_notification(alert)
                        
                except Exception as e:
                    logger.error(f"评估规则 {rule_name} 失败: {e}")
            
            # 更新统计
            self.alert_stats['total_alerts'] += len(triggered_alerts)
            
            # 记录历史
            for alert in triggered_alerts:
                self.alert_history.append(alert)
            
        except Exception as e:
            logger.error(f"检查预警失败: {e}")
        
        return triggered_alerts
    
    async def _send_alert_notification(self, alert: Dict):
        """发送预警通知"""
        try:
            # 提取数据
            symbol = alert['data'].get('symbol', 'UNKNOWN')
            price = alert['data'].get('price', 0.0)
            signal = alert['data'].get('signal', 0.0)
            
            # 发送预警
            results = await self.notifier_manager.send_alert(
                symbol=symbol,
                alert_type=alert['rule_name'],
                condition=alert['condition'],
                price=price,
                signal=signal,
                details=alert['message'],
                recommendation="请根据策略执行相应操作"
            )
            
            # 统计结果
            success_count = sum(1 for success in results.values() if success)
            fail_count = len(results) - success_count
            
            self.alert_stats['successful_notifications'] += success_count
            self.alert_stats['failed_notifications'] += fail_count
            
            logger.info(f"预警通知发送: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            logger.error(f"发送预警通知失败: {e}")
            self.alert_stats['failed_notifications'] += 1
    
    async def send_summary_report(self, report_data: Dict):
        """发送总结报告"""
        try:
            results = await self.notifier_manager.send_summary(
                total_return=report_data.get('total_return', 0.0),
                trade_count=report_data.get('trade_count', 0),
                win_rate=report_data.get('win_rate', 0.0),
                max_drawdown=report_data.get('max_drawdown', 0.0),
                alert_count=self.alert_stats['total_alerts'],
                successful_trades=report_data.get('successful_trades', 0)
            )
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"总结报告发送: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            logger.error(f"发送总结报告失败: {e}")
    
    async def send_error_notification(self, error_type: str, error_message: str, 
                                    module: str, suggestion: str = ""):
        """发送错误通知"""
        try:
            results = await self.notifier_manager.send_error(
                error_type=error_type,
                error_message=error_message,
                module=module,
                suggestion=suggestion
            )
            
            success_count = sum(1 for success in results.values() if success)
            logger.info(f"错误通知发送: {success_count}/{len(results)} 成功")
            
        except Exception as e:
            logger.error(f"发送错误通知失败: {e}")
    
    def get_alert_history(self, limit: int = 100) -> List[Dict]:
        """获取预警历史"""
        return self.alert_history[-limit:]
    
    def get_alert_stats(self) -> Dict:
        """获取预警统计"""
        return self.alert_stats.copy()
    
    def get_active_rules(self) -> List[str]:
        """获取活跃规则列表"""
        return list(self.rule_engine.rules.keys())
    
    def clear_history(self):
        """清空预警历史"""
        self.alert_history.clear()
        logger.info("预警历史已清空")
    
    def reset_stats(self):
        """重置统计数据"""
        self.alert_stats = {
            'total_alerts': 0,
            'successful_notifications': 0,
            'failed_notifications': 0
        }
        logger.info("预警统计已重置")


# 测试函数
async def test_alert_manager():
    """测试预警管理器"""
    logger.info("开始测试预警管理器...")
    
    try:
        # 创建通知器
        notifiers = NotifierFactory.create_from_env()
        
        # 创建预警管理器
        manager = AlertManager(notifiers)
        
        # 添加测试规则
        manager.add_rule(
            "price_drop",
            "signal <= -0.006",
            "价格下跌信号触发"
        )
        
        manager.add_rule(
            "high_volatility",
            "abs(signal) >= 0.01",
            "高波动率预警"
        )
        
        # 测试数据
        test_data = {
            'symbol': 'TEST',
            'price': 1.0,
            'signal': -0.008,
            'timestamp': datetime.now().isoformat()
        }
        
        # 检查预警
        alerts = await manager.check_alerts(test_data)
        
        logger.info(f"✅ 预警管理器测试成功")
        logger.info(f"   触发预警: {len(alerts)} 个")
        logger.info(f"   活跃规则: {manager.get_active_rules()}")
        logger.info(f"   预警统计: {manager.get_alert_stats()}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 预警管理器测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_alert_manager())
