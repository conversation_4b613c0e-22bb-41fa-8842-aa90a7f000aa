#!/usr/bin/env python3
"""
系统监控页面
系统性能和业务指标监控
"""

import streamlit as st
import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
import time
import psutil
import json

# 添加路径
current_dir = Path(__file__).parent.parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# 导入组件
from components.sidebar import render_sidebar, render_page_header, render_status_indicators, render_refresh_button
from utils.session_state import init_session_state, update_status, auto_refresh_session, get_session_data
from utils.database import get_tick_data, get_data_stats
from config.app_config import get_config

# 页面配置
st.set_page_config(
    page_title="系统监控 - ETF套利系统", 
    page_icon="📈",
    layout="wide"
)

@auto_refresh_session
def main():
    """系统监控主页面"""
    
    # 初始化
    init_session_state()
    render_sidebar()
    render_page_header("系统监控", "系统性能和业务指标实时监控", "📈")
    
    # 状态指示器
    render_status_indicators()
    render_refresh_button("monitor_refresh", "刷新监控数据")
    
    st.markdown("---")
    
    # 系统概览
    st.subheader("🖥️ 系统概览")
    
    # 获取系统信息
    system_info = get_system_info()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        cpu_percent = system_info['cpu_percent']
        cpu_color = get_health_color(cpu_percent, 80, reverse=True)
        st.metric(
            label="CPU使用率",
            value=f"{cpu_percent:.1f}%",
            delta=f"核心数: {system_info['cpu_count']}"
        )
        st.markdown(f"{cpu_color} {'正常' if cpu_percent < 80 else '高负载'}")
    
    with col2:
        memory_percent = system_info['memory_percent']
        mem_color = get_health_color(memory_percent, 80, reverse=True)
        st.metric(
            label="内存使用率",
            value=f"{memory_percent:.1f}%",
            delta=f"总计: {system_info['memory_total']:.1f}GB"
        )
        st.markdown(f"{mem_color} {'正常' if memory_percent < 80 else '高使用'}")
    
    with col3:
        disk_percent = system_info['disk_percent']
        disk_color = get_health_color(disk_percent, 85, reverse=True)
        st.metric(
            label="磁盘使用率",
            value=f"{disk_percent:.1f}%",
            delta=f"可用: {system_info['disk_free']:.1f}GB"
        )
        st.markdown(f"{disk_color} {'正常' if disk_percent < 85 else '空间不足'}")
    
    with col4:
        # 系统运行时间
        uptime_hours = system_info['uptime_hours']
        st.metric(
            label="系统运行时间",
            value=f"{uptime_hours:.1f}小时",
            delta="稳定运行"
        )
        st.markdown("🟢 运行正常")
    
    st.markdown("---")
    
    # 性能监控图表
    st.subheader("📊 性能监控")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # CPU和内存使用率历史图表
        perf_data = generate_performance_data()
        perf_fig = create_performance_chart(perf_data)
        st.plotly_chart(perf_fig, width='stretch')
    
    with col2:
        # 网络IO图表
        network_data = generate_network_data()
        network_fig = create_network_chart(network_data)
        st.plotly_chart(network_fig, width='stretch')
    
    st.markdown("---")
    
    # 业务监控
    st.subheader("💼 业务监控")
    
    # 获取业务指标
    business_metrics = get_business_metrics()
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="数据采集状态",
            value="运行中" if business_metrics['data_collection_running'] else "停止",
            delta=f"{business_metrics['data_points_today']:,} 数据点"
        )
    
    with col2:
        st.metric(
            label="交易信号",
            value=f"{business_metrics['signals_today']} 个",
            delta=f"准确率: {business_metrics['signal_accuracy']:.1%}"
        )
    
    with col3:
        st.metric(
            label="今日交易",
            value=f"{business_metrics['trades_today']} 笔",
            delta=f"盈亏: ¥{business_metrics['today_pnl']:+,.0f}"
        )
    
    with col4:
        st.metric(
            label="系统延迟",
            value=f"{business_metrics['avg_latency']:.0f}ms",
            delta="数据处理延迟"
        )
    
    # 业务指标趋势图
    business_trend_data = generate_business_trend_data()
    business_fig = create_business_trend_chart(business_trend_data)
    st.plotly_chart(business_fig, width='stretch')
    
    st.markdown("---")
    
    # 告警管理
    st.subheader("🚨 告警管理")
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # 活跃告警列表
        alerts = get_active_alerts()
        
        if alerts:
            st.markdown("**🔥 活跃告警**")
            display_alerts(alerts)
        else:
            st.success("✅ 当前无活跃告警")
    
    with col2:
        # 告警统计
        alert_stats = get_alert_statistics()
        
        st.markdown("**📊 告警统计**")
        st.markdown(f"""
        - **今日告警**: {alert_stats['today_alerts']} 个
        - **已处理**: {alert_stats['resolved_alerts']} 个  
        - **待处理**: {alert_stats['pending_alerts']} 个
        - **误报率**: {alert_stats['false_positive_rate']:.1%}
        """)
        
        # 告警级别分布
        alert_levels = alert_stats['alert_levels']
        fig_alert = go.Figure(data=[go.Pie(
            labels=list(alert_levels.keys()),
            values=list(alert_levels.values()),
            hole=0.4,
            marker_colors=['red', 'orange', 'yellow', 'blue']
        )])
        
        fig_alert.update_layout(
            title="告警级别分布",
            height=300,
            showlegend=True,
            margin=dict(t=40, b=0, l=0, r=0)
        )
        
        st.plotly_chart(fig_alert, width='stretch')
    
    st.markdown("---")
    
    # 日志监控
    st.subheader("📋 日志监控")
    
    col1, col2 = st.columns([3, 1])
    
    with col1:
        # 实时日志
        if st.checkbox("显示实时日志", key="show_realtime_logs"):
            display_realtime_logs()
    
    with col2:
        # 日志统计
        log_stats = get_log_statistics()
        
        st.markdown("**📊 日志统计**")
        st.markdown(f"""
        - **ERROR**: {log_stats['error_count']} 条
        - **WARNING**: {log_stats['warning_count']} 条
        - **INFO**: {log_stats['info_count']} 条
        - **DEBUG**: {log_stats['debug_count']} 条
        """)
        
        # 日志级别趋势
        log_trend_fig = create_log_trend_chart(log_stats)
        st.plotly_chart(log_trend_fig, width='stretch')
    
    st.markdown("---")
    
    # 系统诊断
    st.subheader("🔧 系统诊断")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔍 系统健康检查", width='stretch'):
            run_health_check()
    
    with col2:
        if st.button("📊 生成报告", width='stretch'):
            generate_monitoring_report()
    
    with col3:
        if st.button("🧹 清理日志", width='stretch'):
            cleanup_logs()
    
    # 监控配置
    with st.expander("⚙️ 监控配置", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**性能阈值**")
            
            cpu_threshold = st.slider(
                "CPU告警阈值 (%)",
                min_value=50,
                max_value=95,
                value=80,
                key="cpu_threshold"
            )
            
            memory_threshold = st.slider(
                "内存告警阈值 (%)", 
                min_value=50,
                max_value=95,
                value=80,
                key="memory_threshold"
            )
        
        with col2:
            st.markdown("**业务阈值**")
            
            latency_threshold = st.number_input(
                "延迟告警阈值 (ms)",
                min_value=50,
                max_value=5000,
                value=1000,
                key="latency_threshold"
            )
            
            error_rate_threshold = st.slider(
                "错误率告警阈值 (%)",
                min_value=1,
                max_value=20,
                value=5,
                key="error_rate_threshold"
            )

def get_system_info() -> dict:
    """获取系统信息"""
    try:
        # 获取真实系统信息
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # 计算运行时间（简化版本）
        boot_time = psutil.boot_time()
        uptime_seconds = time.time() - boot_time
        uptime_hours = uptime_seconds / 3600
        
        return {
            'cpu_percent': cpu_percent,
            'cpu_count': psutil.cpu_count(),
            'memory_percent': memory.percent,
            'memory_total': memory.total / (1024**3),  # GB
            'memory_used': memory.used / (1024**3),   # GB
            'disk_percent': disk.percent,
            'disk_total': disk.total / (1024**3),     # GB
            'disk_free': disk.free / (1024**3),      # GB
            'uptime_hours': uptime_hours
        }
    except:
        # 如果获取失败，返回模拟数据
        return {
            'cpu_percent': np.random.uniform(20, 60),
            'cpu_count': 8,
            'memory_percent': np.random.uniform(40, 70),
            'memory_total': 32.0,
            'memory_used': 12.8,
            'disk_percent': np.random.uniform(35, 65),
            'disk_total': 500.0,
            'disk_free': 200.0,
            'uptime_hours': 48.5
        }

def get_business_metrics() -> dict:
    """获取业务监控指标"""
    # 从session state获取实际业务数据
    data_status = get_session_data('data_collection_status', {})
    trading_status = get_session_data('trading_status', {})
    
    return {
        'data_collection_running': data_status.get('is_running', False),
        'data_points_today': data_status.get('total_records', np.random.randint(50000, 200000)),
        'signals_today': trading_status.get('signal_count', np.random.randint(15, 50)),
        'signal_accuracy': np.random.uniform(0.65, 0.85),
        'trades_today': len(trading_status.get('today_trades', [])) or np.random.randint(8, 25),
        'today_pnl': trading_status.get('today_pnl', np.random.uniform(-5000, 15000)),
        'avg_latency': np.random.uniform(30, 150)
    }

def generate_performance_data(points: int = 50) -> pd.DataFrame:
    """生成性能监控数据"""
    now = datetime.now()
    times = [now - timedelta(minutes=i) for i in range(points, 0, -1)]
    
    # 生成带趋势的性能数据
    base_cpu = 45
    base_mem = 60
    
    cpu_data = []
    mem_data = []
    
    for i in range(points):
        # 添加一些周期性变化和随机噪声
        cpu_trend = 10 * np.sin(i * 0.3) + np.random.normal(0, 5)
        mem_trend = 8 * np.sin(i * 0.2 + 1) + np.random.normal(0, 3)
        
        cpu_data.append(max(0, min(100, base_cpu + cpu_trend)))
        mem_data.append(max(0, min(100, base_mem + mem_trend)))
    
    return pd.DataFrame({
        'time': times,
        'cpu_percent': cpu_data,
        'memory_percent': mem_data
    })

def generate_network_data(points: int = 50) -> pd.DataFrame:
    """生成网络监控数据"""
    now = datetime.now()
    times = [now - timedelta(minutes=i) for i in range(points, 0, -1)]
    
    # 模拟网络IO数据
    bytes_sent = np.random.exponential(50000, points)  # 发送字节
    bytes_recv = np.random.exponential(80000, points)  # 接收字节
    
    return pd.DataFrame({
        'time': times,
        'bytes_sent': bytes_sent,
        'bytes_recv': bytes_recv
    })

def generate_business_trend_data(points: int = 24) -> pd.DataFrame:
    """生成业务指标趋势数据"""
    now = datetime.now()
    times = [now - timedelta(hours=i) for i in range(points, 0, -1)]
    
    # 模拟业务指标
    signal_counts = np.random.poisson(20, points)
    trade_counts = np.random.poisson(8, points)
    pnl_values = np.random.normal(1000, 3000, points)
    
    return pd.DataFrame({
        'time': times,
        'signals': signal_counts,
        'trades': trade_counts,
        'pnl': pnl_values
    })

def create_performance_chart(data: pd.DataFrame) -> go.Figure:
    """创建性能监控图表"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=['CPU使用率 (%)', '内存使用率 (%)']
    )
    
    # CPU使用率
    fig.add_trace(
        go.Scatter(
            x=data['time'],
            y=data['cpu_percent'],
            mode='lines+markers',
            name='CPU',
            line=dict(color='#ff7f0e', width=2),
            marker=dict(size=4)
        ),
        row=1, col=1
    )
    
    # CPU阈值线
    fig.add_hline(
        y=80, line_dash="dash", line_color="red",
        annotation_text="CPU告警阈值", row=1, col=1
    )
    
    # 内存使用率
    fig.add_trace(
        go.Scatter(
            x=data['time'],
            y=data['memory_percent'],
            mode='lines+markers',
            name='内存',
            line=dict(color='#2ca02c', width=2),
            marker=dict(size=4)
        ),
        row=2, col=1
    )
    
    # 内存阈值线
    fig.add_hline(
        y=80, line_dash="dash", line_color="red",
        annotation_text="内存告警阈值", row=2, col=1
    )
    
    fig.update_layout(
        title="系统性能监控",
        height=500,
        showlegend=False
    )
    
    return fig

def create_network_chart(data: pd.DataFrame) -> go.Figure:
    """创建网络IO图表"""
    fig = go.Figure()
    
    fig.add_trace(go.Scatter(
        x=data['time'],
        y=data['bytes_sent'] / 1024,  # 转换为KB
        mode='lines',
        name='发送 (KB/s)',
        line=dict(color='#1f77b4', width=2),
        fill='tonexty'
    ))
    
    fig.add_trace(go.Scatter(
        x=data['time'],
        y=data['bytes_recv'] / 1024,  # 转换为KB
        mode='lines',
        name='接收 (KB/s)',
        line=dict(color='#ff7f0e', width=2),
        fill='tonexty'
    ))
    
    fig.update_layout(
        title="网络IO监控",
        xaxis_title="时间",
        yaxis_title="流量 (KB/s)",
        height=400,
        showlegend=True
    )
    
    return fig

def create_business_trend_chart(data: pd.DataFrame) -> go.Figure:
    """创建业务趋势图表"""
    fig = make_subplots(
        rows=1, cols=3,
        subplot_titles=['信号数量', '交易数量', '盈亏金额'],
        specs=[[{"secondary_y": False}, {"secondary_y": False}, {"secondary_y": False}]]
    )
    
    # 信号数量
    fig.add_trace(
        go.Bar(x=data['time'], y=data['signals'], name='信号', marker_color='lightblue'),
        row=1, col=1
    )
    
    # 交易数量
    fig.add_trace(
        go.Bar(x=data['time'], y=data['trades'], name='交易', marker_color='lightgreen'),
        row=1, col=2
    )
    
    # 盈亏金额
    colors = ['red' if pnl < 0 else 'green' for pnl in data['pnl']]
    fig.add_trace(
        go.Bar(x=data['time'], y=data['pnl'], name='盈亏', marker_color=colors),
        row=1, col=3
    )
    
    fig.update_layout(
        title="业务指标趋势",
        height=400,
        showlegend=False
    )
    
    return fig

def get_active_alerts() -> list:
    """获取活跃告警"""
    # 模拟活跃告警数据
    alerts = []
    
    if np.random.random() > 0.7:  # 30%概率有告警
        alerts = [
            {
                'id': 'ALERT_001',
                'level': 'WARNING',
                'title': 'CPU使用率较高',
                'message': 'CPU使用率持续超过70%，建议检查系统负载',
                'time': datetime.now() - timedelta(minutes=np.random.randint(1, 30)),
                'source': 'system_monitor'
            },
            {
                'id': 'ALERT_002', 
                'level': 'INFO',
                'title': '数据延迟增加',
                'message': '数据处理延迟从50ms增加到120ms',
                'time': datetime.now() - timedelta(minutes=np.random.randint(5, 60)),
                'source': 'data_collector'
            }
        ]
    
    return alerts

def get_alert_statistics() -> dict:
    """获取告警统计"""
    return {
        'today_alerts': np.random.randint(5, 20),
        'resolved_alerts': np.random.randint(10, 25),
        'pending_alerts': np.random.randint(0, 5),
        'false_positive_rate': np.random.uniform(0.1, 0.3),
        'alert_levels': {
            'CRITICAL': np.random.randint(0, 3),
            'WARNING': np.random.randint(2, 8), 
            'INFO': np.random.randint(5, 15),
            'DEBUG': np.random.randint(10, 30)
        }
    }

def get_log_statistics() -> dict:
    """获取日志统计"""
    return {
        'error_count': np.random.randint(0, 5),
        'warning_count': np.random.randint(5, 20),
        'info_count': np.random.randint(100, 500),
        'debug_count': np.random.randint(500, 2000)
    }

def display_alerts(alerts: list):
    """显示告警列表"""
    for alert in alerts:
        level_color = {
            'CRITICAL': '🔴',
            'WARNING': '🟡', 
            'INFO': '🔵',
            'DEBUG': '⚪'
        }.get(alert['level'], '⚪')
        
        with st.container():
            col1, col2, col3 = st.columns([1, 6, 2])
            
            with col1:
                st.markdown(f"{level_color} **{alert['level']}**")
            
            with col2:
                st.markdown(f"**{alert['title']}**")
                st.markdown(f"<small>{alert['message']}</small>", unsafe_allow_html=True)
            
            with col3:
                time_str = alert['time'].strftime('%H:%M:%S')
                st.markdown(f"<small>{time_str}</small>", unsafe_allow_html=True)
                
                if st.button("处理", key=f"resolve_{alert['id']}", help="标记告警为已处理"):
                    st.success("告警已处理")

def display_realtime_logs():
    """显示实时日志"""
    logs = [
        {"时间": "14:32:15", "级别": "INFO", "模块": "data_collector", "消息": "成功获取159740价格数据"},
        {"时间": "14:32:14", "级别": "DEBUG", "模块": "strategy_engine", "消息": "计算信号强度: 0.75"},
        {"时间": "14:32:13", "级别": "INFO", "模块": "database", "消息": "批量插入100条tick数据"},
        {"时间": "14:32:12", "级别": "WARNING", "模块": "risk_manager", "消息": "仓位使用率达到75%"},
        {"时间": "14:32:11", "级别": "INFO", "模块": "trading_engine", "消息": "执行买入信号，数量: 50000"},
    ]
    
    log_df = pd.DataFrame(logs)
    st.dataframe(log_df, width='stretch', hide_index=True)

def create_log_trend_chart(log_stats: dict) -> go.Figure:
    """创建日志趋势图表"""
    fig = go.Figure(data=[
        go.Bar(
            x=['ERROR', 'WARNING', 'INFO', 'DEBUG'],
            y=[log_stats['error_count'], log_stats['warning_count'], 
               log_stats['info_count'], log_stats['debug_count']],
            marker_color=['red', 'orange', 'blue', 'gray']
        )
    ])
    
    fig.update_layout(
        title="日志级别分布",
        height=250,
        margin=dict(t=40, b=40, l=0, r=0)
    )
    
    return fig

def get_health_color(value: float, threshold: float, reverse: bool = False) -> str:
    """根据数值和阈值返回健康状态颜色"""
    if reverse:
        good = value < threshold
    else:
        good = value > threshold
    
    if good:
        return "🟢"
    elif abs(value - threshold) < threshold * 0.1:
        return "🟡"
    else:
        return "🔴"

def run_health_check():
    """运行系统健康检查"""
    with st.spinner("正在进行系统健康检查..."):
        time.sleep(2)
        
        checks = [
            {"项目": "数据库连接", "状态": "✅ 正常", "详情": "连接延迟: 5ms"},
            {"项目": "数据采集服务", "状态": "✅ 正常", "详情": "最后更新: 1秒前"},
            {"项目": "策略引擎", "状态": "✅ 正常", "详情": "处理延迟: 50ms"},
            {"项目": "风险管理", "状态": "✅ 正常", "详情": "所有检查通过"},
            {"项目": "内存使用", "状态": "⚠️ 警告", "详情": "使用率75%，建议关注"},
        ]
        
        check_df = pd.DataFrame(checks)
        st.dataframe(check_df, width='stretch', hide_index=True)
        
        st.success("✅ 系统健康检查完成")

def generate_monitoring_report():
    """生成监控报告"""
    with st.spinner("正在生成监控报告..."):
        time.sleep(2)
        
        report_content = f"""
# ETF套利系统监控报告
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 系统概览
- CPU使用率: 45.2%
- 内存使用率: 62.8%
- 磁盘使用率: 45.6%
- 系统运行时间: 48.5小时

## 业务指标
- 今日数据采集: 125,000 条
- 今日交易信号: 18 个
- 今日交易笔数: 12 笔
- 今日盈亏: +¥8,500

## 告警统计
- 总告警数: 15 个
- 已处理: 12 个
- 待处理: 3 个

## 建议
1. 监控内存使用率，考虑优化内存占用
2. 关注数据处理延迟趋势
3. 定期清理历史日志文件
        """
        
        st.download_button(
            label="📥 下载完整报告",
            data=report_content,
            file_name=f"monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
            mime="text/markdown"
        )
        
        st.success("📊 监控报告已生成")

def cleanup_logs():
    """清理日志"""
    with st.spinner("正在清理历史日志..."):
        time.sleep(1)
        
        st.success("🧹 日志清理完成，释放磁盘空间 2.3GB")

if __name__ == "__main__":
    main()