#!/usr/bin/env python3
"""
智能优化功能模块
从 app_enhanced_backtest_dashboard.py 提取的参数优化功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import sqlite3
import json
import itertools
import random
from datetime import datetime, timedelta
from typing import Dict, List
import sys
import os
import importlib.util
from pathlib import Path
import threading
import time

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent.absolute()
root_dir = str(current_dir)
sys.path.insert(0, root_dir)

# 导入回测模块
try:
    backtest_spec = importlib.util.spec_from_file_location(
        "backtest_enhanced_root", 
        os.path.join(root_dir, "backtest_enhanced.py")
    )
    backtest_module = importlib.util.module_from_spec(backtest_spec)
    backtest_spec.loader.exec_module(backtest_module)
    BacktestConfig = backtest_module.BacktestConfig
    EnhancedBacktest = backtest_module.EnhancedBacktest
except Exception as e:
    st.error(f"无法导入回测模块: {e}")

def get_db_connection():
    """获取数据库连接"""
    db_path = "ticks.db"
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def load_saved_optimal_configs(symbol: str):
    """加载已保存的优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT config_name, parameters, performance_metrics, fitness_score,
                   optimization_method, created_at
            FROM optimal_configs
            WHERE symbol=?
            ORDER BY fitness_score DESC
            LIMIT 10
        """, (symbol,))

        configs = []
        for row in cursor.fetchall():
            configs.append({
                'config_name': row['config_name'],
                'parameters': json.loads(row['parameters']),
                'performance_metrics': json.loads(row['performance_metrics']),
                'fitness_score': row['fitness_score'] or 0.0,
                'optimization_method': row['optimization_method'],
                'created_at': row['created_at'],
                'symbol': symbol
            })

        conn.close()
        return configs
    except Exception as e:
        st.error(f"加载优化配置失败: {e}")
        return []

def load_optimal_config(config_data):
    """加载选中的优化配置到session state"""
    try:
        params = config_data['parameters']

        # 更新session state中的参数
        st.session_state.buy_trigger = params.get('buy_trigger_drop', -0.006)
        st.session_state.profit_target = params.get('profit_target', 0.0025)
        st.session_state.stop_loss = params.get('stop_loss', -0.015)
        st.session_state.max_hold_time = params.get('max_hold_time', 1800)

        # 显示加载成功信息
        metrics = config_data['performance_metrics']
        st.success(f"""
        ✅ 已加载优化配置: {config_data['config_name']}

        **参数设置:**
        - 买入触发: {params.get('buy_trigger_drop', -0.006):.3f}
        - 止盈目标: {params.get('profit_target', 0.0025):.3f}
        - 止损线: {params.get('stop_loss', -0.015):.3f}
        - 最大持仓时间: {params.get('max_hold_time', 1800)}秒

        **预期性能:**
        - 适应度分数: {config_data['fitness_score']:.3f}
        - 总收益率: {metrics.get('total_return', 0):.2%}
        - 最大回撤: {metrics.get('max_drawdown', 0):.2%}
        - 胜率: {metrics.get('win_rate', 0):.1%}
        """)

        # 强制刷新页面以应用新参数
        st.rerun()

    except Exception as e:
        st.error(f"加载配置失败: {e}")

def generate_parameter_grid(space: dict) -> list:
    """生成参数网格"""
    param_names = list(space.keys())
    param_ranges = []

    for param_name in param_names:
        min_val, max_val, step = space[param_name]
        values = np.arange(min_val, max_val + step, step)
        param_ranges.append(values)

    # 生成所有组合
    combinations = []
    for combo in itertools.product(*param_ranges):
        param_dict = dict(zip(param_names, combo))
        combinations.append(param_dict)

    # 限制组合数量，避免过多
    if len(combinations) > 50:
        combinations = random.sample(combinations, 50)

    return combinations

def evaluate_parameter_combination(symbol: str, params: dict, days: int) -> dict:
    """评估参数组合的性能"""
    try:
        # 计算日期范围
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)

        # 创建回测配置
        config = BacktestConfig(
            symbol=symbol,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
            buy_trigger_drop=params['buy_trigger_drop'],
            profit_target=params['profit_target'],
            stop_loss=params['stop_loss'],
            max_hold_time=params['max_hold_time'],
            initial_capital=100000,
            max_position_ratio=0.95
        )

        # 执行回测
        backtest = EnhancedBacktest(config)
        results = backtest.run_backtest()

        # 检查回测结果
        if results is None or 'error' in results:
            return {
                'total_return': -0.1,
                'max_drawdown': -0.1,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

        # 从回测结果中提取性能指标
        if 'performance' in results:
            perf = results['performance']

            def parse_percentage(value):
                if isinstance(value, str):
                    return float(value.replace('%', '').replace(',', '')) / 100
                return float(value) if value else 0

            def parse_number(value):
                if isinstance(value, str):
                    return float(value.replace(',', ''))
                return float(value) if value else 0

            return {
                'total_return': parse_percentage(perf.get('总收益率', '0%')),
                'max_drawdown': parse_percentage(perf.get('最大回撤', '0%')),
                'sharpe_ratio': parse_number(perf.get('夏普比率', '0')),
                'win_rate': parse_percentage(perf.get('胜率', '0%')),
                'total_trades': int(parse_number(perf.get('总交易次数', '0')))
            }
        else:
            return {
                'total_return': -0.1,
                'max_drawdown': -0.1,
                'sharpe_ratio': 0,
                'win_rate': 0,
                'total_trades': 0
            }

    except Exception as e:
        return {
            'total_return': -0.1,
            'max_drawdown': -0.1,
            'sharpe_ratio': 0,
            'win_rate': 0,
            'total_trades': 0
        }

def calculate_fitness_score(performance: dict) -> float:
    """计算适应度分数"""
    try:
        # 综合评分公式
        fitness = (
            performance['total_return'] * 0.4 +
            (1 + performance['max_drawdown']) * 0.3 +
            min(performance['sharpe_ratio'] / 2, 1) * 0.2 +
            performance['win_rate'] * 0.1
        )

        # 惩罚交易次数过少的情况
        if performance['total_trades'] < 5:
            fitness *= 0.5

        return float(fitness)

    except Exception:
        return -999

def save_optimal_config(symbol: str, method: str, strategy_type: str,
                       params: dict, metrics: dict, fitness: float):
    """保存优化配置到数据库"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        config_name = f"{strategy_type}_{method}_{datetime.now().strftime('%m%d_%H%M')}"

        cursor.execute("""
            INSERT OR REPLACE INTO optimal_configs
            (symbol, config_name, parameters, performance_metrics,
             optimization_method, fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            config_name,
            json.dumps(params),
            json.dumps(metrics),
            method,
            fitness,
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        conn.close()

    except Exception as e:
        st.error(f"保存优化配置失败: {e}")

def run_optimization_in_background(symbol: str, method: str, days: int, strategy_type: str):
    """在后台线程中运行优化"""
    try:
        # 更新状态
        st.session_state.optimization_status = "running"
        st.session_state.optimization_progress = 0
        st.session_state.optimization_message = "开始优化..."
        
        # 定义参数空间
        param_space = {
            'conservative': {
                'buy_trigger_drop': (-0.008, -0.005, 0.001),
                'profit_target': (0.003, 0.006, 0.001),
                'stop_loss': (-0.025, -0.015, 0.002),
                'max_hold_time': (86400, 86400, 0)
            },
            'balanced': {
                'buy_trigger_drop': (-0.010, -0.005, 0.001),
                'profit_target': (0.004, 0.008, 0.001),
                'stop_loss': (-0.030, -0.015, 0.003),
                'max_hold_time': (86400, 86400, 0)
            },
            'aggressive': {
                'buy_trigger_drop': (-0.012, -0.007, 0.001),
                'profit_target': (0.005, 0.010, 0.001),
                'stop_loss': (-0.035, -0.020, 0.003),
                'max_hold_time': (86400, 86400, 0)
            }
        }

        space = param_space[strategy_type]
        
        # 生成参数网格
        st.session_state.optimization_message = "生成参数网格..."
        st.session_state.optimization_progress = 20
        param_combinations = generate_parameter_grid(space)
        total_combinations = len(param_combinations)

        # 评估每个参数组合
        best_params = None
        best_performance = None
        best_fitness = -999

        for i, params in enumerate(param_combinations):
            if st.session_state.get('optimization_status') != 'running':
                break  # 用户停止了优化
                
            try:
                performance = evaluate_parameter_combination(symbol, params, days)
                fitness = calculate_fitness_score(performance)

                if fitness > best_fitness:
                    best_fitness = fitness
                    best_params = params.copy()
                    best_performance = performance.copy()

                # 更新进度
                progress = 20 + 70 * (i + 1) / total_combinations
                st.session_state.optimization_progress = int(progress)
                st.session_state.optimization_message = f"评估进度: {i+1}/{total_combinations} (最佳适应度: {best_fitness:.3f})"

            except Exception as e:
                continue

        if best_params is None:
            st.session_state.optimization_status = "error"
            st.session_state.optimization_error = "优化失败，未找到有效参数"
            return

        # 确保最大持仓时间为24小时
        best_params['max_hold_time'] = 86400

        # 保存优化结果
        st.session_state.optimization_message = "保存优化结果..."
        st.session_state.optimization_progress = 95
        save_optimal_config(symbol, method, strategy_type, best_params, best_performance, best_fitness)

        # 完成优化
        st.session_state.optimization_status = "completed"
        st.session_state.optimization_progress = 100
        st.session_state.optimization_result = {
            'params': best_params,
            'performance': best_performance,
            'fitness': best_fitness
        }

    except Exception as e:
        st.session_state.optimization_status = "error"
        st.session_state.optimization_error = str(e)

def start_optimization_thread(symbol: str, method: str, days: int, strategy_type: str):
    """启动优化线程"""
    thread = threading.Thread(
        target=run_optimization_in_background,
        args=(symbol, method, days, strategy_type),
        daemon=True
    )
    thread.start()
    return thread

def run_parameter_optimization(symbol: str, method: str, days: int, strategy_type: str):
    """运行参数优化（兼容旧接口）"""
    return start_optimization_thread(symbol, method, days, strategy_type)

def delete_optimal_config(config_name: str, symbol: str) -> bool:
    """删除优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            DELETE FROM optimal_configs
            WHERE config_name = ? AND symbol = ?
        """, (config_name, symbol))

        deleted_rows = cursor.rowcount
        conn.commit()
        conn.close()

        return deleted_rows > 0

    except Exception as e:
        st.error(f"删除优化配置失败: {e}")
        return False