#!/usr/bin/env python3
"""
增强版策略回测系统（修复版）
支持增强版策略的所有功能：风险控制、分批交易、动态参数等
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from typing import List, Optional, Tuple
from datetime import datetime
import logging

# 导入增强策略的组件
from strategy_engine_enhanced import Position, RiskManager, MarketRegime
# 导入统一配置
from strategy_config import StrategyConfig
# 导入A股交易规则管理器
from etf_arbitrage_streamlit_multi.utils.trading_rules_manager import get_trading_rules_manager

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

logger = logging.getLogger(__name__)


class BacktestConfig:
    """回测配置 - 使用统一参数配置"""
    
    def __init__(self, **kwargs):
        """初始化配置，使用统一配置的默认值"""
        # 基本参数
        self.symbol = kwargs.get('symbol', "159740")
        self.start_date = kwargs.get('start_date', "2025-08-25")
        self.end_date = kwargs.get('end_date', "2025-08-27")
        
        # 从统一配置获取默认值
        defaults = StrategyConfig.get_default_values()
        
        # 设置所有参数，优先使用传入的kwargs，否则使用默认值
        for key, default_value in defaults.items():
            setattr(self, key, kwargs.get(key, default_value))
    
    # 风险控制参数
    daily_loss_limit: float = -0.05    # 日损失限制
    max_drawdown_limit: float = -0.10  # 最大回撤限制
    exit_at_close: bool = False        # 是否在收盘前平仓


class EnhancedBacktest:
    """增强版回测引擎"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.position = Position()
        self.risk_manager = RiskManager(config.initial_capital)

        # 🆕 A股交易规则管理器
        self.trading_rules = get_trading_rules_manager()

        # 回测结果记录
        self.trades = []
        self.equity_curve = []
        self.signals = []

        # 性能统计
        self.total_trades = 0
        self.winning_trades = 0
        self.total_commission = 0.0
        self.total_stamp_tax = 0.0  # 🔍 添加印花税跟踪
        self.total_transfer_fee = 0.0  # 🔍 添加过户费跟踪

        # 🆕 A股规则统计
        self.trading_restrictions = {
            'time_restrictions': 0,      # 交易时间限制次数
            'price_limit_restrictions': 0,  # 涨跌停限制次数
            't1_restrictions': 0,        # T+1限制次数
            'quantity_normalizations': 0, # 数量标准化次数
            'total_violations': 0        # 总违规次数
        }
        
    def load_data(self) -> pd.DataFrame:
        """加载回测数据"""
        try:
            conn = sqlite3.connect("ticks.db")
            
            # 修复日期查询问题：添加时间范围以匹配数据库中的完整时间戳
            start_datetime = f"{self.config.start_date} 00:00:00"
            end_datetime = f"{self.config.end_date} 23:59:59"

            query = """
            SELECT tick_time as time, price, volume
            FROM ticks
            WHERE symbol=? AND tick_time BETWEEN ? AND ?
            ORDER BY tick_time ASC
            """

            df = pd.read_sql_query(
                query, conn,
                params=[self.config.symbol, start_datetime, end_datetime],
                parse_dates=['time']
            )
            conn.close()
            
            if df.empty:
                raise ValueError(f"没有找到 {self.config.symbol} 在 {self.config.start_date} 到 {self.config.end_date} 的数据")
            
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce').fillna(0)
            df = df.dropna(subset=['price'])
            
            logger.info(f"加载数据: {len(df)} 条记录，时间范围: {df['time'].min()} 到 {df['time'].max()}")
            return df
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return pd.DataFrame()
    
    def calculate_signal(self, df: pd.DataFrame, idx: int) -> float:
        """计算交易信号（最高价回撤，与实时监控一致）"""
        try:
            window = self.config.signal_window
            if idx < window:
                return 0.0
            
            # 使用配置的窗口大小计算最高价回撤（与实时监控保持一致）
            window_data = df.iloc[max(0, idx-window):idx+1]
            
            if len(window_data) < 2:
                return 0.0
            
            high_price = float(window_data['price'].max())
            current_price = float(window_data['price'].iloc[-1])
            
            if high_price == 0:
                return 0.0
            
            # 计算从最高点的回撤（负值表示下跌）
            signal = (current_price - high_price) / high_price
            return signal
            
        except Exception as e:
            logger.error(f"计算信号失败: {e}")
            return 0.0

    def validate_trading_conditions(self, current_time: datetime, current_price: float,
                                   prev_close: float, trade_type: str, quantity: int) -> Tuple[bool, str, int]:
        """
        验证A股交易条件

        Args:
            current_time: 当前时间
            current_price: 当前价格
            prev_close: 前收盘价
            trade_type: 交易类型 ('buy' 或 'sell')
            quantity: 交易数量

        Returns:
            (是否允许交易, 原因说明, 标准化后的数量)
        """
        try:
            # 使用交易规则管理器进行合规性检查
            compliance = self.trading_rules.validate_trade_compliance(
                symbol=self.config.symbol,
                trade_type=trade_type,
                quantity=quantity,
                current_price=current_price,
                prev_close=prev_close,
                current_time=current_time
            )

            # 更新统计信息
            if not compliance['is_compliant']:
                self.trading_restrictions['total_violations'] += 1

                # 分类统计违规原因
                for violation in compliance['violations']:
                    if '交易时间限制' in violation:
                        self.trading_restrictions['time_restrictions'] += 1
                    elif '涨停' in violation or '跌停' in violation:
                        self.trading_restrictions['price_limit_restrictions'] += 1
                    elif 'T+1限制' in violation:
                        self.trading_restrictions['t1_restrictions'] += 1

            # 统计数量标准化
            if compliance['normalized_quantity'] != quantity:
                self.trading_restrictions['quantity_normalizations'] += 1

            # 返回结果
            if compliance['is_compliant']:
                return True, "交易条件满足", compliance['normalized_quantity']
            else:
                reasons = "; ".join(compliance['violations'])
                return False, f"交易被拒绝: {reasons}", compliance['normalized_quantity']

        except Exception as e:
            logger.error(f"验证交易条件时出错: {e}")
            self.trading_restrictions['total_violations'] += 1
            return False, f"验证失败: {str(e)}", quantity

    def normalize_quantity(self, quantity: int) -> int:
        """标准化交易数量为100股整数倍"""
        return self.trading_rules.normalize_quantity(quantity)

    def check_price_limits(self, current_price: float, prev_close: float) -> Tuple[bool, str]:
        """检查价格涨跌停限制"""
        try:
            price_check = self.trading_rules.check_price_limits(
                symbol=self.config.symbol,
                current_price=current_price,
                prev_close=prev_close
            )

            if not price_check['is_valid']:
                self.trading_restrictions['price_limit_restrictions'] += 1
                return False, price_check['message']

            return True, price_check['message']

        except Exception as e:
            logger.error(f"检查价格限制时出错: {e}")
            return False, f"价格检查失败: {str(e)}"

    def should_buy(self, signal: float, current_price: float) -> bool:
        """判断是否应该买入"""
        # 信号检查
        if signal <= self.config.buy_trigger_drop:
            # 仓位检查
            if self.position.total_quantity >= self.config.max_position:
                return False
            
            # 价格检查
            if current_price <= 0:
                return False
            
            # 风险控制检查
            if not self.risk_manager.check_risk_limits():
                return False
            
            return True
        
        return False
    
    def should_sell(self, current_price: float, current_time: datetime) -> Tuple[bool, float, str]:
        """判断卖出条件"""
        if self.position.total_quantity <= 0:
            return False, 0.0, ""
        
        profit_rate = self.position.get_profit_rate(current_price)
        
        # 计算持仓时间（传入当前回测时间）
        hold_time = self.position.get_hold_time(current_time)
        
        # 止损检查 - 使用动态参数，但添加最小持仓时间保护
        if profit_rate <= self.config.stop_loss and hold_time >= self.config.min_hold_time:
            return True, 1.0, f"止损: {profit_rate:.2%} (设置: {self.config.stop_loss:.2%})"
        
        # 时间止损 - 支持可选的时间控制
        # 检查是否启用时间控制
        enable_time_control = getattr(self.config, 'enable_time_control', True)
        
        if enable_time_control:
            # 1. 秒级限制 (max_hold_time) - 0表示禁用
            max_hold_time = getattr(self.config, 'max_hold_time', 0)
            if max_hold_time > 0 and hold_time >= max_hold_time:
                return True, 1.0, f"时间止损(秒): {hold_time}s >= {max_hold_time}s"
            
            # 2. 天级限制 (max_holding_days) - 0表示禁用
            max_holding_days = getattr(self.config, 'max_holding_days', 0)
            if max_holding_days > 0:
                max_hold_seconds = max_holding_days * 24 * 3600  # 转换为秒
                if hold_time >= max_hold_seconds:
                    hold_days = hold_time / (24 * 3600)
                    return True, 1.0, f"时间止损(天): {hold_days:.1f}天 >= {max_holding_days}天"
        
        # 分批止盈检查 - 使用动态参数
        partial_sell_result = self.position.check_partial_sell(current_price, self.config)
        if partial_sell_result[0]:
            # 找到满足条件的分批止盈
            _, level_idx, sell_ratio, reason = partial_sell_result
            return True, sell_ratio, reason
        
        return False, 0.0, ""
    
    def check_fund_sufficiency(self, buy_qty: int, current_price: float) -> tuple[bool, float, str]:
        """检查资金是否充足"""
        try:
            # 计算当前可用资金
            realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
            total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
            available_cash = self.config.initial_capital - self.position.total_cost - total_fees + realized_pnl
            
            # 计算本次买入所需资金
            actual_price = current_price * (1 + self.config.slippage)
            required_fund = buy_qty * actual_price
            commission = required_fund * self.config.commission_rate
            total_required = required_fund + commission
            
            # 检查资金充足性
            if available_cash < total_required:
                return False, available_cash, f"资金不足：需要{total_required:,.2f}，可用{available_cash:,.2f}"
            
            return True, available_cash, "资金充足"
            
        except Exception as e:
            logger.error(f"资金充足性检查出错: {e}")
            return False, 0.0, f"检查出错: {e}"
    
    def calculate_affordable_quantity(self, target_qty: int, current_price: float) -> int:
        """根据可用资金计算实际可买入数量"""
        try:
            if target_qty <= 0:
                return 0
                
            realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
            total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
            available_cash = self.config.initial_capital - self.position.total_cost - total_fees + realized_pnl
            
            # 使用配置的资金缓冲比例
            fund_buffer_ratio = getattr(self.config, 'fund_buffer_ratio', 0.05)
            usable_cash = available_cash * (1 - fund_buffer_ratio)
            
            if usable_cash <= 0:
                return 0
            
            actual_price = current_price * (1 + self.config.slippage)
            
            # 考虑手续费的最大可买数量
            # 设 x 为买入数量，则：x * actual_price * (1 + commission_rate) ≤ usable_cash
            if actual_price <= 0:
                return 0
                
            max_affordable = int(usable_cash / (actual_price * (1 + self.config.commission_rate)))
            
            return min(target_qty, max_affordable)
            
        except Exception as e:
            logger.error(f"计算可负担数量出错: {e}")
            return 0
    
    def get_fund_utilization_metrics(self, current_price: float) -> dict[str, float]:
        """获取资金利用率指标"""
        try:
            realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
            total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
            available_cash = self.config.initial_capital - self.position.total_cost - total_fees + realized_pnl
            
            market_value = self.position.total_quantity * current_price if self.position.total_quantity > 0 else 0.0
            total_equity = available_cash + market_value
            
            return {
                'initial_capital': float(self.config.initial_capital),
                'available_cash': float(available_cash),
                'market_value': float(market_value),
                'total_equity': float(total_equity),
                'cash_ratio': float(available_cash / total_equity) if total_equity > 0 else 0.0,
                'position_ratio': float(market_value / total_equity) if total_equity > 0 else 0.0,
                'fund_utilization': float((self.config.initial_capital - available_cash) / self.config.initial_capital) if self.config.initial_capital > 0 else 0.0,
                'leverage_ratio': float(market_value / self.config.initial_capital) if self.config.initial_capital > 0 else 0.0
            }
            
        except Exception as e:
            logger.error(f"计算资金利用率指标出错: {e}")
            return {
                'initial_capital': 0.0,
                'available_cash': 0.0,
                'market_value': 0.0,
                'total_equity': 0.0,
                'cash_ratio': 0.0,
                'position_ratio': 0.0,
                'fund_utilization': 0.0,
                'leverage_ratio': 0.0
            }

    def execute_buy(self, current_price: float, current_time: datetime, prev_close: float = None) -> int:
        """执行买入（带A股规则验证和资金检查）"""
        # 🆕 A股规则验证 - 预先验证基本条件
        initial_quantity = self.config.position_size  # 使用配置的基础仓位大小进行验证
        is_valid, reason, normalized_qty = self.validate_trading_conditions(
            current_time=current_time,
            current_price=current_price,
            prev_close=prev_close,
            trade_type='buy',
            quantity=initial_quantity
        )

        if not is_valid:
            logger.warning(f"买入被A股规则拒绝: {reason}")
            return 0

        remaining = self.config.max_position - self.position.total_quantity
        total_bought = 0
        total_commission_for_this_buy = 0.0  # 🔍 修复：累积本次买入的总佣金
        total_transfer_fee_for_this_buy = 0.0  # 🔍 修复：累积本次买入的总过户费
        
        # 使用动态分层参数
        layers = [self.config.layer1_ratio, self.config.layer2_ratio, self.config.layer3_ratio]
        
        for i, pct in enumerate(layers):
            # 计算目标买入数量
            target_qty = int(self.config.position_size * pct)
            if target_qty <= 0 or remaining <= 0:
                continue

            # 🆕 A股规则：标准化数量为100股整数倍
            target_qty = self.normalize_quantity(target_qty)
            if target_qty <= 0:
                continue

            # 根据资金情况调整实际买入数量
            affordable_qty = self.calculate_affordable_quantity(target_qty, current_price)
            alloc = min(affordable_qty, remaining)

            # 🆕 再次标准化最终买入数量
            alloc = self.normalize_quantity(alloc)
            
            if alloc <= 0:
                # 使用debug级别，避免重复警告
                logger.debug(f"资金不足，跳过第{i+1}层买入（目标{target_qty}股）")
                continue
            
            # 执行买入
            actual_price = current_price * (1 + self.config.slippage)
            
            self.position.add_position(alloc, actual_price, current_time)
            total_bought += alloc
            remaining -= alloc
            
            # 🔍 修复：计算手续费和过户费，只累积到本次买入，不立即累加到总计
            commission = max(alloc * actual_price * self.config.commission_rate, 5.0)  # 最低5元佣金
            transfer_fee = alloc * actual_price * 0.00001  # 过户费：十万分之一
            total_commission_for_this_buy += commission
            total_transfer_fee_for_this_buy += transfer_fee
            
            logger.debug(f"第{i+1}层买入：目标{target_qty}股，实际{alloc}股，价格{actual_price:.4f}")
        
        if total_bought > 0:
            # 🔍 修复：统一累积费用到总计
            self.total_commission += total_commission_for_this_buy
            self.total_transfer_fee += total_transfer_fee_for_this_buy
            self.trades.append({
                'time': current_time,
                'type': 'BUY',
                'quantity': total_bought,
                'price': current_price,
                'actual_price': actual_price,
                'commission': total_commission_for_this_buy,  # 🔍 修复：记录实际佣金
                'transfer_fee': total_transfer_fee_for_this_buy,  # 🔍 修复：记录实际过户费
                'reason': '分层买入'
            })
            
            # 记录资金利用情况
            fund_metrics = self.get_fund_utilization_metrics(current_price)
            logger.info(f"买入完成: {total_bought}@{actual_price:.4f}, 总仓位: {self.position.total_quantity}, "
                       f"资金利用率: {fund_metrics['fund_utilization']:.2%}, "
                       f"杠杆率: {fund_metrics['leverage_ratio']:.2f}")
        
        return total_bought
    
    def execute_sell(self, current_price: float, current_time: datetime,
                    sell_ratio: float, reason: str, batch_size: Optional[int] = None,
                    prev_close: float = None) -> List[Tuple[int, float]]:
        """
        执行卖出（支持分批卖出，带A股规则验证）

        Args:
            current_price: 当前价格
            current_time: 当前时间
            sell_ratio: 卖出比例
            reason: 卖出原因
            batch_size: 单批次卖出数量，如果为None则使用配置的batch_size
            prev_close: 前收盘价

        Returns:
            List[Tuple[int, float]]: 每笔交易的数量和价格
        """
        sell_qty = int(self.position.total_quantity * sell_ratio)
        if sell_qty <= 0:
            return []

        # 🆕 A股规则验证
        is_valid, reason_msg, normalized_qty = self.validate_trading_conditions(
            current_time=current_time,
            current_price=current_price,
            prev_close=prev_close,
            trade_type='sell',
            quantity=sell_qty
        )

        if not is_valid:
            logger.warning(f"卖出被A股规则拒绝: {reason_msg}")
            return []

        # 使用标准化后的数量
        sell_qty = min(normalized_qty, self.position.total_quantity)
        
        # 修复：合理设置batch_size，避免过度分批
        batch_size = batch_size or max(sell_qty, 1000)  # 至少1000股一批，或者一次性卖完
        
        # 如果卖出数量较小，直接一次性卖出
        if sell_qty <= 2000:
            batch_size = sell_qty
        
        num_batches = max(1, (sell_qty + batch_size - 1) // batch_size)
        executed_trades = []
        total_proceeds = 0
        total_cost_reduced = 0
        total_commission_for_this_sell = 0  # 🔍 修复：重命名避免变量冲突
        
        for i in range(num_batches):
            this_batch_size = min(batch_size, sell_qty - sum(qty for qty, _ in executed_trades))
            if this_batch_size <= 0:
                break

            # 🆕 A股规则：标准化批次数量为100股整数倍
            this_batch_size = self.normalize_quantity(this_batch_size)
            if this_batch_size <= 0:
                continue
                
            actual_price = current_price * (1 - self.config.slippage * (1 + i * 0.01))
            cost_reduced = self.position.reduce_position(this_batch_size)
            commission = max(this_batch_size * actual_price * self.config.commission_rate, 5.0)  # 最低5元佣金
            stamp_tax = this_batch_size * actual_price * 0.001  # 印花税0.1%（仅卖出）
            transfer_fee = this_batch_size * actual_price * 0.00001  # 过户费：十万分之一
            
            total_proceeds += this_batch_size * actual_price
            total_cost_reduced += cost_reduced
            total_commission_for_this_sell += commission  # 🔍 修复：累积到本次卖出的佣金
            self.total_commission += commission  # 🔍 修复：同时累积到总佣金
            self.total_stamp_tax += stamp_tax  # 🔍 累积印花税
            self.total_transfer_fee += transfer_fee  # 🔍 累积过户费
            
            executed_trades.append((this_batch_size, actual_price))
        
        # 修复：只记录一次卖出交易，而不是每批都记录
        if executed_trades:
            total_pnl = total_proceeds - total_cost_reduced
            # 🔍 修复：移除重复的佣金累积

            # 更新胜率统计
            self.total_trades += 1
            if total_pnl > 0:
                self.winning_trades += 1

            # 计算本次卖出的印花税和过户费总额
            total_stamp_tax_for_this_sell = sum(qty * price * 0.001 for qty, price in executed_trades)
            total_transfer_fee_for_this_sell = sum(qty * price * 0.00001 for qty, price in executed_trades)
            
            # 只记录一次交易记录
            trade_record = {
                'time': current_time,
                'type': 'SELL',
                'quantity': sum(qty for qty, _ in executed_trades),
                'price': current_price,
                'actual_price': sum(qty * price for qty, price in executed_trades) / sum(qty for qty, _ in executed_trades),
                'commission': total_commission_for_this_sell,  # 🔍 修复：使用正确的变量名
                'stamp_tax': total_stamp_tax_for_this_sell,   # 🔍 记录实际印花税
                'transfer_fee': total_transfer_fee_for_this_sell,  # 🔍 记录过户费
                'pnl': total_pnl,
                'reason': f"{reason} ({num_batches}批次)" if num_batches > 1 else reason
            }
            self.trades.append(trade_record)
        
        return executed_trades
    
    def run_backtest(self) -> Dict:
        """运行回测"""
        logger.info("开始增强版策略回测...")
        
        # 加载数据
        df = self.load_data()
        if df.empty:
            return {'error': '无法加载数据'}
        
        # 回测主循环
        prev_close = None  # 用于A股规则验证的前收盘价

        for idx in range(len(df)):
            row = df.iloc[idx]
            current_time = row['time']
            current_price = float(row['price'])

            # 🆕 计算前收盘价（简化处理：使用前一个交易日的最后价格）
            if idx > 0:
                # 检查是否是新的交易日
                prev_time = df.iloc[idx-1]['time']
                if current_time.date() != prev_time.date():
                    # 新交易日，使用前一日最后价格作为前收盘价
                    prev_close = float(df.iloc[idx-1]['price'])
                elif prev_close is None:
                    # 同一交易日但没有前收盘价，使用第一个价格
                    prev_close = current_price
            else:
                # 第一个数据点，使用当前价格作为前收盘价
                prev_close = current_price

            # 计算信号
            signal = self.calculate_signal(df, idx)
            
            # 记录信号
            self.signals.append({
                'time': current_time,
                'price': current_price,
                'signal': signal,
                'position': self.position.total_quantity
            })
            
            # 更新净值 - 修复计算逻辑
            if self.position.total_quantity > 0:
                # 有持仓时：净值 = 现金 + 持仓市值
                market_value = self.position.total_quantity * current_price
                # 现金 = 初始资金 - 已投入成本 - 总费用 + 已实现盈亏
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
                cash = self.config.initial_capital - self.position.total_cost - total_fees + realized_pnl
                current_equity = cash + market_value
            else:
                # 无持仓时：净值 = 初始资金 + 已实现盈亏 - 总费用
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
                current_equity = self.config.initial_capital + realized_pnl - total_fees

            self.risk_manager.update_equity(current_equity)
            
            # 记录净值曲线
            if self.position.total_quantity > 0:
                market_value = self.position.total_quantity * current_price
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
                cash = self.config.initial_capital - self.position.total_cost - total_fees + realized_pnl
            else:
                market_value = 0
                realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
                total_fees = self.total_commission + self.total_stamp_tax + self.total_transfer_fee
                cash = self.config.initial_capital + realized_pnl - total_fees

            self.equity_curve.append({
                'time': current_time,
                'equity': current_equity,
                'position': self.position.total_quantity,
                'price': current_price,
                'cash': cash,
                'market_value': market_value,
                'realized_pnl': realized_pnl,
                'total_commission': self.total_commission,
                'drawdown': (current_equity - self.risk_manager.peak_equity) / self.risk_manager.peak_equity if self.risk_manager.peak_equity > 0 else 0
            })
            
            # 新增：检查是否应该在时段结束前平仓
            should_close, close_reason = self.should_close_before_session_end(current_time)
            if should_close and self.position.total_quantity > 0:
                logger.info(f"{close_reason}: {current_time}")
                self.execute_sell(current_price, current_time, 1.0, close_reason, prev_close=prev_close)
                continue

            # 检查是否需要收盘前平仓（兼容模式）
            if (hasattr(self.config, 'exit_at_close') and self.config.exit_at_close and
                self.position.total_quantity > 0):
                # 检查是否接近收盘时间（14:55以后）
                if (current_time.hour == 14 and current_time.minute >= 55) or current_time.hour == 15:
                    logger.info(f"收盘前平仓（兼容模式）: {current_time}")
                    self.execute_sell(current_price, current_time, 1.0, "收盘前平仓（兼容模式）", prev_close=prev_close)
                    continue

            # 卖出判断（优先级高于买入）
            should_sell, sell_ratio, sell_reason = self.should_sell(current_price, current_time)
            if should_sell:
                self.execute_sell(current_price, current_time, sell_ratio, sell_reason, prev_close=prev_close)
                # 卖出后跳过本tick的买入判断，避免同时买卖
                continue

            # 买入判断（只有在没有卖出时才执行）
            # 新增：检查是否在允许的交易时段内
            if self.is_trading_time_allowed(current_time) and self.should_buy(signal, current_price):
                # 买入前重置止盈标记，确保新买入的仓位可以享受完整的止盈策略
                if self.position.total_quantity > 0:
                    # 如果已有仓位，重置部分止盈标记
                    self.position.reset_partial_sold_flags()
                self.execute_buy(current_price, current_time, prev_close=prev_close)
        
        # 计算最终结果
        return self.calculate_results()
    
    def calculate_results(self) -> Dict:
        """计算回测结果"""
        if not self.equity_curve:
            return {'error': '没有回测数据'}
        
        equity_df = pd.DataFrame(self.equity_curve)
        
        # 基本统计
        initial_equity = self.config.initial_capital
        final_equity = equity_df['equity'].iloc[-1]
        total_return = (final_equity - initial_equity) / initial_equity
        
        # 最大回撤
        peak = equity_df['equity'].cummax()
        drawdown = (equity_df['equity'] - peak) / peak
        max_drawdown = drawdown.min()
        
        # 胜率 - 基于实际交易数据计算
        sell_trades = [t for t in self.trades if t['type'] == 'SELL' and 'pnl' in t]
        if sell_trades:
            profitable_trades = len([t for t in sell_trades if t['pnl'] > 0])
            win_rate = profitable_trades / len(sell_trades)
        else:
            win_rate = 0
        
        # 夏普比率（简化计算）
        returns = equity_df['equity'].pct_change().dropna()
        if len(returns) > 1 and returns.std() > 0:
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252 * 24 * 60)  # 年化
        else:
            sharpe_ratio = 0
        
        # 交易统计
        buy_trades = [t for t in self.trades if t['type'] == 'BUY']
        sell_trades = [t for t in self.trades if t['type'] == 'SELL']
        
        # 计算资金管理指标
        final_row = equity_df.iloc[-1]
        fund_metrics = self.get_fund_utilization_metrics(final_row['price'])
        
        # 🆕 A股规则合规性统计
        total_restrictions = sum(self.trading_restrictions.values())
        compliance_rate = 1.0 if total_restrictions == 0 else max(0, 1 - self.trading_restrictions['total_violations'] / max(1, len(self.trades)))

        results = {
            'config': self.config,
            'performance': {
                '初始资金': f"{initial_equity:,.2f}",
                '期末净值': f"{final_equity:,.2f}",
                '总收益率': f"{total_return:.2%}",
                '最大回撤': f"{max_drawdown:.2%}",
                '夏普比率': f"{sharpe_ratio:.4f}",
                '总交易次数': len(self.trades),
                '买入次数': len(buy_trades),
                '卖出次数': len(sell_trades),
                '胜率': f"{win_rate:.2%}",
                '总佣金': f"{self.total_commission:.2f}元",
                '总印花税': f"{self.total_stamp_tax:.2f}元",
                '总过户费': f"{self.total_transfer_fee:.2f}元",
                '总费用': f"{(self.total_commission + self.total_stamp_tax + self.total_transfer_fee):.2f}元"
            },
            'fund_metrics': fund_metrics,
            # 🆕 A股规则合规性统计
            'a_stock_compliance': {
                '合规率': f"{compliance_rate:.2%}",
                '交易时间限制次数': self.trading_restrictions['time_restrictions'],
                '涨跌停限制次数': self.trading_restrictions['price_limit_restrictions'],
                'T+1限制次数': self.trading_restrictions['t1_restrictions'],
                '数量标准化次数': self.trading_restrictions['quantity_normalizations'],
                '总违规次数': self.trading_restrictions['total_violations'],
                '规则版本': self.trading_rules.rules_version
            },
            'raw_data': {
                'equity_curve': equity_df,
                'trades': pd.DataFrame(self.trades) if self.trades else pd.DataFrame(),
                'signals': pd.DataFrame(self.signals) if self.signals else pd.DataFrame()
            }
        }
        
        return results

    def plot_results(self, results: Dict = None, save_path: str = None) -> None:
        """绘制回测结果图表"""
        if results is None:
            logger.error("没有回测结果数据")
            return
            
        if 'raw_data' not in results:
            logger.error("回测结果中缺少原始数据")
            return
            
        try:
            # 设置matplotlib后端，避免GUI问题
            import matplotlib
            matplotlib.use('Agg')  # 使用非交互式后端
            
            equity_df = results['raw_data']['equity_curve']
            trades_df = results['raw_data']['trades']
            signals_df = results['raw_data']['signals']
            
            if equity_df.empty:
                logger.error("净值曲线数据为空")
                return
            
            # 创建图表
            fig, axes = plt.subplots(3, 1, figsize=(15, 12))
            fig.suptitle(f'增强版策略回测结果 - {self.config.symbol}', fontsize=16, fontweight='bold')
            
            # 1. 净值曲线和价格走势
            ax1 = axes[0]
            ax1_twin = ax1.twinx()
            
            # 净值曲线
            ax1.plot(equity_df['time'], equity_df['equity'], 'b-', linewidth=2, label='净值曲线')
            ax1.axhline(y=self.config.initial_capital, color='gray', linestyle='--', alpha=0.7, label='初始资金')
            ax1.set_ylabel('净值 (元)', color='b')
            ax1.tick_params(axis='y', labelcolor='b')
            ax1.legend(loc='upper left')
            ax1.grid(True, alpha=0.3)
            
            # 价格走势
            ax1_twin.plot(equity_df['time'], equity_df['price'], 'r-', alpha=0.7, linewidth=1, label='价格')
            ax1_twin.set_ylabel('价格 (元)', color='r')
            ax1_twin.tick_params(axis='y', labelcolor='r')
            ax1_twin.legend(loc='upper right')
            
            # 标记交易点
            if not trades_df.empty:
                buy_trades = trades_df[trades_df['type'] == 'BUY']
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                
                if not buy_trades.empty:
                    ax1_twin.scatter(buy_trades['time'], buy_trades['price'], 
                                   color='green', marker='^', s=100, alpha=0.8, 
                                   label=f'买入 ({len(buy_trades)}次)', zorder=5)
                
                if not sell_trades.empty:
                    ax1_twin.scatter(sell_trades['time'], sell_trades['price'], 
                                   color='red', marker='v', s=100, alpha=0.8, 
                                   label=f'卖出 ({len(sell_trades)}次)', zorder=5)
                
                ax1_twin.legend(loc='center right')
            
            ax1.set_title('净值曲线与价格走势')
            
            # 2. 仓位变化
            ax2 = axes[1]
            ax2.plot(equity_df['time'], equity_df['position'], 'g-', linewidth=2, label='持仓数量')
            ax2.fill_between(equity_df['time'], equity_df['position'], alpha=0.3, color='green')
            ax2.set_ylabel('持仓数量')
            ax2.set_title('仓位变化')
            ax2.legend()
            ax2.grid(True, alpha=0.3)
            
            # 3. 交易信号
            ax3 = axes[2]
            if not signals_df.empty:
                ax3.plot(signals_df['time'], signals_df['signal'], 'purple', linewidth=1, alpha=0.7, label='交易信号')
                ax3.axhline(y=-self.config.buy_trigger_drop, color='red', linestyle='--', alpha=0.7, 
                           label=f'买入阈值 ({-self.config.buy_trigger_drop:.1%})')
                ax3.fill_between(signals_df['time'], signals_df['signal'], -self.config.buy_trigger_drop, 
                               where=(signals_df['signal'] <= -self.config.buy_trigger_drop), 
                               color='red', alpha=0.2, label='买入区域')
            
            ax3.set_ylabel('信号值')
            ax3.set_xlabel('时间')
            ax3.set_title('交易信号')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存或显示图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {save_path}")
            else:
                # 默认保存到当前目录
                default_path = f"backtest_result_{self.config.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                logger.info(f"图表已保存到: {default_path}")
            
            plt.close(fig)  # 关闭图表释放内存
                
        except Exception as e:
            logger.error(f"绘图失败: {e}")
            import traceback
            traceback.print_exc()

    def is_trading_time_allowed(self, current_time) -> bool:
        """检查当前时间是否允许交易"""
        # 获取交易时段设置，默认为全天
        trading_session = getattr(self.config, 'trading_session', '全天')

        # 获取当前时间
        current_hour = current_time.hour
        current_minute = current_time.minute
        current_time_minutes = current_hour * 60 + current_minute

        # 定义交易时段（分钟表示）
        morning_start = 9 * 60 + 30   # 9:30
        morning_end = 11 * 60 + 30    # 11:30
        afternoon_start = 13 * 60     # 13:00
        afternoon_end = 15 * 60       # 15:00

        # 检查是否在交易时段内
        is_morning_session = morning_start <= current_time_minutes <= morning_end
        is_afternoon_session = afternoon_start <= current_time_minutes <= afternoon_end

        # 根据设置的交易时段进行判断
        if trading_session == "全天":
            return is_morning_session or is_afternoon_session
        elif trading_session == "上午":
            return is_morning_session
        elif trading_session == "下午":
            return is_afternoon_session
        else:
            return False

    def should_close_before_session_end(self, current_time) -> tuple:
        """检查是否应该在时段结束前平仓"""
        # 获取平仓时间设置
        close_before_morning = getattr(self.config, 'close_before_morning', False)
        close_before_afternoon = getattr(self.config, 'close_before_afternoon', True)

        current_hour = current_time.hour
        current_minute = current_time.minute
        current_time_minutes = current_hour * 60 + current_minute

        # 上午收盘前平仓检查（11:25）
        if close_before_morning:
            morning_close_time = 11 * 60 + 25  # 11:25
            if current_time_minutes >= morning_close_time and current_time_minutes <= 11 * 60 + 30:
                return True, "上午收盘前平仓"

        # 下午收盘前平仓检查（14:55）
        if close_before_afternoon:
            afternoon_close_time = 14 * 60 + 55  # 14:55
            if current_time_minutes >= afternoon_close_time and current_time_minutes <= 15 * 60:
                return True, "下午收盘前平仓"

        return False, ""


def run_enhanced_backtest(config: BacktestConfig) -> dict:
    """运行增强版回测的便捷函数"""
    backtest = EnhancedBacktest(config)
    return backtest.run_backtest()


def main():
    """主函数"""
    import argparse

    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")

    parser = argparse.ArgumentParser(description="增强版策略回测")
    parser.add_argument("--symbol", default="159740", help="交易标的")
    parser.add_argument("--start-date", default="2025-08-25", help="开始日期")
    parser.add_argument("--end-date", default="2025-08-27", help="结束日期")
    parser.add_argument("--initial-capital", type=float, default=1000000, help="初始资金")

    args = parser.parse_args()

    # 创建回测配置
    config = BacktestConfig(
        symbol=args.symbol,
        start_date=args.start_date,
        end_date=args.end_date,
        initial_capital=args.initial_capital
    )

    # 运行回测
    results = run_enhanced_backtest(config)

    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return

    # 显示结果
    print("\n=== 增强版策略回测结果 ===")
    print(f"标的: {config.symbol}")
    print(f"时间: {config.start_date} 到 {config.end_date}")
    print("\n性能指标:")
    for key, value in results['performance'].items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()