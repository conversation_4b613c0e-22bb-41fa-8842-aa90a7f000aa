#!/usr/bin/env python3
"""
实时交易引擎模块
处理实时信号生成、持仓管理和模拟交易执行
"""

import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable
import pandas as pd
import json
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    avg_price: float
    open_time: datetime
    cost: float
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    
    def update_price(self, price: float):
        """更新当前价格和未实现盈亏"""
        self.current_price = price
        self.unrealized_pnl = (price - self.avg_price) * self.quantity

@dataclass
class TradeSignal:
    """交易信号"""
    timestamp: datetime
    symbol: str
    signal_type: str  # 'buy', 'sell', 'hold'
    price: float
    confidence: float
    reason: str
    quantity: int = 0

@dataclass
class TradeRecord:
    """交易记录"""
    timestamp: datetime
    symbol: str
    action: str  # 'buy', 'sell'
    quantity: int
    price: float
    amount: float
    commission: float
    pnl: float = 0.0
    position_id: str = ""

class RealTimeTrader:
    """实时交易引擎"""
    
    def __init__(self):
        self.is_running = False
        self.positions: Dict[str, Position] = {}
        self.signals: List[TradeSignal] = []
        self.trades: List[TradeRecord] = []
        self.trader_thread = None
        self.stop_event = threading.Event()
        self.status_callbacks = []
        
        # 交易参数
        self.strategy_params = {
            'buy_trigger_drop': -0.002,
            'profit_target': 0.0025,
            'stop_loss': -0.02,
            'max_hold_time': 3600,
            'position_size': 100000,
            'max_position': 1000000,
            'commission_rate': 0.0003
        }
        
        # 统计信息
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'start_time': None,
            'last_signal_time': None
        }
    
    def add_status_callback(self, callback: Callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, status: Dict):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")
    
    def update_strategy_params(self, params: Dict):
        """更新策略参数"""
        self.strategy_params.update(params)
        logger.info(f"策略参数已更新: {params}")
    
    def start_trading(self, symbol: str) -> bool:
        """启动实时交易"""
        if self.is_running:
            logger.warning("实时交易已在运行中")
            return False
        
        try:
            self.stop_event.clear()
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            
            self.trader_thread = threading.Thread(
                target=self._trading_loop,
                args=(symbol,),
                daemon=True
            )
            self.trader_thread.start()
            
            logger.info(f"开始实时交易: {symbol}")
            self._notify_status_change({
                'is_running': True,
                'symbol': symbol,
                'start_time': self.stats['start_time'].isoformat()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"启动实时交易失败: {e}")
            self.is_running = False
            return False
    
    def stop_trading(self) -> bool:
        """停止实时交易"""
        if not self.is_running:
            return True
        
        try:
            self.stop_event.set()
            
            if self.trader_thread and self.trader_thread.is_alive():
                self.trader_thread.join(timeout=10)
            
            self.is_running = False
            
            logger.info("实时交易已停止")
            self._notify_status_change({
                'is_running': False,
                'stop_time': datetime.now().isoformat(),
                'final_stats': self.stats.copy()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"停止实时交易失败: {e}")
            return False
    
    def _trading_loop(self, symbol: str):
        """交易主循环"""
        try:
            logger.info(f"启动交易循环: {symbol}")
            
            while not self.stop_event.is_set():
                try:
                    # 获取最新数据
                    from utils.data_collector import get_recent_data
                    recent_data = get_recent_data(symbol, limit=100)
                    
                    if recent_data['success'] and recent_data['count'] > 0:
                        # 生成交易信号
                        signal = self._generate_signal(recent_data['data'])
                        
                        if signal:
                            self.signals.append(signal)
                            self.stats['last_signal_time'] = signal.timestamp
                            
                            # 执行交易
                            if signal.signal_type in ['buy', 'sell']:
                                trade = self._execute_trade(signal)
                                if trade:
                                    self.trades.append(trade)
                                    self._update_statistics(trade)
                    
                    # 更新持仓价格
                    if recent_data['success'] and recent_data['count'] > 0:
                        latest_price = recent_data['data'][0]['price']
                        self._update_positions(symbol, latest_price)
                    
                    # 检查止损止盈
                    self._check_exit_conditions(symbol)
                    
                except Exception as e:
                    logger.error(f"交易循环错误: {e}")
                
                # 等待下次检查
                self.stop_event.wait(1)  # 1秒检查一次
                
        except Exception as e:
            logger.error(f"交易循环异常: {e}")
        finally:
            self.is_running = False
    
    def _generate_signal(self, data: List[Dict]) -> Optional[TradeSignal]:
        """生成交易信号"""
        try:
            if len(data) < 10:
                return None
            
            # 转换为DataFrame进行分析
            df = pd.DataFrame(data)
            df['time'] = pd.to_datetime(df['time'])
            df = df.sort_values('time')
            
            current_price = df['price'].iloc[-1]
            recent_prices = df['price'].tail(self.strategy_params.get('signal_window', 20))
            
            # 计算价格变化
            price_change = (current_price - recent_prices.mean()) / recent_prices.mean()
            
            # 买入信号：价格下跌超过阈值
            if price_change <= self.strategy_params['buy_trigger_drop']:
                return TradeSignal(
                    timestamp=datetime.now(),
                    symbol=df['time'].iloc[0] if len(df) > 0 else "unknown",
                    signal_type='buy',
                    price=current_price,
                    confidence=min(abs(price_change) / abs(self.strategy_params['buy_trigger_drop']), 1.0),
                    reason=f"价格下跌{price_change:.3%}，触发买入",
                    quantity=self.strategy_params['position_size']
                )
            
            # 检查是否有持仓需要止盈
            symbol = df['time'].iloc[0] if len(df) > 0 else "unknown"
            if symbol in self.positions:
                position = self.positions[symbol]
                position.update_price(current_price)
                
                profit_rate = position.unrealized_pnl / position.cost
                
                # 止盈信号
                if profit_rate >= self.strategy_params['profit_target']:
                    return TradeSignal(
                        timestamp=datetime.now(),
                        symbol=symbol,
                        signal_type='sell',
                        price=current_price,
                        confidence=1.0,
                        reason=f"达到止盈目标{profit_rate:.3%}",
                        quantity=position.quantity
                    )
                
                # 止损信号
                if profit_rate <= self.strategy_params['stop_loss']:
                    return TradeSignal(
                        timestamp=datetime.now(),
                        symbol=symbol,
                        signal_type='sell',
                        price=current_price,
                        confidence=1.0,
                        reason=f"触发止损{profit_rate:.3%}",
                        quantity=position.quantity
                    )
            
            return None
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return None
    
    def _execute_trade(self, signal: TradeSignal) -> Optional[TradeRecord]:
        """执行交易"""
        try:
            commission = signal.price * signal.quantity * self.strategy_params['commission_rate']
            amount = signal.price * signal.quantity
            
            if signal.signal_type == 'buy':
                # 检查是否超过最大持仓
                current_position = self.positions.get(signal.symbol)
                if current_position and current_position.quantity * signal.price > self.strategy_params['max_position']:
                    logger.warning(f"超过最大持仓限制，忽略买入信号")
                    return None
                
                # 执行买入
                if current_position:
                    # 加仓
                    total_cost = current_position.cost + amount + commission
                    total_quantity = current_position.quantity + signal.quantity
                    avg_price = total_cost / total_quantity
                    
                    current_position.quantity = total_quantity
                    current_position.avg_price = avg_price
                    current_position.cost = total_cost
                else:
                    # 新建持仓
                    self.positions[signal.symbol] = Position(
                        symbol=signal.symbol,
                        quantity=signal.quantity,
                        avg_price=signal.price,
                        open_time=signal.timestamp,
                        cost=amount + commission
                    )
                
                trade = TradeRecord(
                    timestamp=signal.timestamp,
                    symbol=signal.symbol,
                    action='buy',
                    quantity=signal.quantity,
                    price=signal.price,
                    amount=amount,
                    commission=commission
                )
                
                logger.info(f"执行买入: {signal.symbol} {signal.quantity}股 @{signal.price:.3f}")
                
            elif signal.signal_type == 'sell':
                # 执行卖出
                current_position = self.positions.get(signal.symbol)
                if not current_position or current_position.quantity <= 0:
                    logger.warning(f"无持仓，无法卖出")
                    return None
                
                sell_quantity = min(signal.quantity, current_position.quantity)
                pnl = (signal.price - current_position.avg_price) * sell_quantity - commission
                
                current_position.quantity -= sell_quantity
                current_position.realized_pnl += pnl
                
                # 如果完全卖出，删除持仓
                if current_position.quantity <= 0:
                    del self.positions[signal.symbol]
                
                trade = TradeRecord(
                    timestamp=signal.timestamp,
                    symbol=signal.symbol,
                    action='sell',
                    quantity=sell_quantity,
                    price=signal.price,
                    amount=amount,
                    commission=commission,
                    pnl=pnl
                )
                
                logger.info(f"执行卖出: {signal.symbol} {sell_quantity}股 @{signal.price:.3f} 盈亏:{pnl:.2f}")
            
            return trade
            
        except Exception as e:
            logger.error(f"执行交易失败: {e}")
            return None
    
    def _update_positions(self, symbol: str, price: float):
        """更新持仓价格"""
        if symbol in self.positions:
            self.positions[symbol].update_price(price)
    
    def _check_exit_conditions(self, symbol: str):
        """检查退出条件"""
        try:
            if symbol not in self.positions:
                return
            
            position = self.positions[symbol]
            
            # 检查最大持仓时间
            hold_time = (datetime.now() - position.open_time).total_seconds()
            if hold_time > self.strategy_params['max_hold_time']:
                # 生成强制卖出信号
                signal = TradeSignal(
                    timestamp=datetime.now(),
                    symbol=symbol,
                    signal_type='sell',
                    price=position.current_price,
                    confidence=1.0,
                    reason=f"达到最大持仓时间{hold_time:.0f}秒",
                    quantity=position.quantity
                )
                
                trade = self._execute_trade(signal)
                if trade:
                    self.trades.append(trade)
                    self._update_statistics(trade)
                    
        except Exception as e:
            logger.error(f"检查退出条件失败: {e}")
    
    def _update_statistics(self, trade: TradeRecord):
        """更新统计信息"""
        try:
            self.stats['total_trades'] += 1
            
            if trade.action == 'sell' and trade.pnl > 0:
                self.stats['winning_trades'] += 1
            
            if trade.action == 'sell':
                self.stats['total_pnl'] += trade.pnl
                
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def get_status(self) -> Dict:
        """获取交易状态"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        return {
            'is_running': self.is_running,
            'positions': {symbol: asdict(pos) for symbol, pos in self.positions.items()},
            'total_positions': len(self.positions),
            'total_trades': self.stats['total_trades'],
            'winning_trades': self.stats['winning_trades'],
            'total_pnl': self.stats['total_pnl'],
            'unrealized_pnl': total_unrealized_pnl,
            'recent_signals': [asdict(s) for s in self.signals[-10:]],
            'recent_trades': [asdict(t) for t in self.trades[-10:]],
            'win_rate': self.stats['winning_trades'] / max(self.stats['total_trades'], 1),
            'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None,
            'last_signal_time': self.stats['last_signal_time'].isoformat() if self.stats['last_signal_time'] else None
        }

# 全局交易引擎实例
real_time_trader = RealTimeTrader()

# 便捷函数
def start_real_time_trading(symbol: str, params: Dict = None) -> bool:
    """启动实时交易的便捷函数"""
    if params:
        real_time_trader.update_strategy_params(params)
    return real_time_trader.start_trading(symbol)

def stop_real_time_trading() -> bool:
    """停止实时交易的便捷函数"""
    return real_time_trader.stop_trading()

def get_trading_status() -> Dict:
    """获取交易状态的便捷函数"""
    return real_time_trader.get_status()

def update_trading_params(params: Dict):
    """更新交易参数的便捷函数"""
    return real_time_trader.update_strategy_params(params)