#!/usr/bin/env python3
"""
增量计算模块
实现高效的增量计算和缓存机制
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
import time
import hashlib
import pickle
from dataclasses import dataclass, field
from collections import deque
import weakref

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    timestamp: datetime
    access_count: int = 0
    size_bytes: int = 0
    dependencies: List[str] = field(default_factory=list)

class IncrementalCache:
    """增量缓存"""
    
    def __init__(self, max_size_mb: int = 100, ttl_seconds: int = 3600):
        """
        初始化增量缓存
        
        Args:
            max_size_mb: 最大缓存大小(MB)
            ttl_seconds: 缓存生存时间(秒)
        """
        self.max_size_bytes = max_size_mb * 1024 * 1024
        self.ttl_seconds = ttl_seconds
        
        self.cache: Dict[str, CacheEntry] = {}
        self.access_order = deque()  # LRU队列
        self.current_size_bytes = 0
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'size_bytes': 0,
            'entry_count': 0
        }
        
        logger.info(f"增量缓存初始化: 最大 {max_size_mb}MB, TTL {ttl_seconds}秒")
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if key not in self.cache:
            self.stats['misses'] += 1
            return None
        
        entry = self.cache[key]
        
        # 检查TTL
        if self._is_expired(entry):
            self._remove_entry(key)
            self.stats['misses'] += 1
            return None
        
        # 更新访问信息
        entry.access_count += 1
        self._update_access_order(key)
        
        self.stats['hits'] += 1
        return entry.value
    
    def put(self, key: str, value: Any, dependencies: List[str] = None):
        """存储缓存值"""
        # 计算值的大小
        try:
            size_bytes = len(pickle.dumps(value))
        except:
            size_bytes = 1024  # 估算大小
        
        # 检查是否需要清理空间
        while (self.current_size_bytes + size_bytes > self.max_size_bytes and 
               len(self.cache) > 0):
            self._evict_lru()
        
        # 如果键已存在，先删除旧值
        if key in self.cache:
            self._remove_entry(key)
        
        # 创建新条目
        entry = CacheEntry(
            key=key,
            value=value,
            timestamp=datetime.now(),
            size_bytes=size_bytes,
            dependencies=dependencies or []
        )
        
        self.cache[key] = entry
        self.current_size_bytes += size_bytes
        self._update_access_order(key)
        
        self._update_stats()
    
    def invalidate(self, key: str):
        """使缓存失效"""
        if key in self.cache:
            self._remove_entry(key)
            
            # 递归使依赖此键的缓存失效
            dependent_keys = [k for k, entry in self.cache.items() 
                            if key in entry.dependencies]
            for dep_key in dependent_keys:
                self.invalidate(dep_key)
    
    def invalidate_pattern(self, pattern: str):
        """按模式使缓存失效"""
        keys_to_remove = [key for key in self.cache.keys() if pattern in key]
        for key in keys_to_remove:
            self.invalidate(key)
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
        self.access_order.clear()
        self.current_size_bytes = 0
        self._update_stats()
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查条目是否过期"""
        return (datetime.now() - entry.timestamp).total_seconds() > self.ttl_seconds
    
    def _remove_entry(self, key: str):
        """删除缓存条目"""
        if key in self.cache:
            entry = self.cache[key]
            self.current_size_bytes -= entry.size_bytes
            del self.cache[key]
            
            # 从访问队列中移除
            try:
                self.access_order.remove(key)
            except ValueError:
                pass
    
    def _evict_lru(self):
        """驱逐最近最少使用的条目"""
        if self.access_order:
            lru_key = self.access_order.popleft()
            self._remove_entry(lru_key)
            self.stats['evictions'] += 1
    
    def _update_access_order(self, key: str):
        """更新访问顺序"""
        try:
            self.access_order.remove(key)
        except ValueError:
            pass
        self.access_order.append(key)
    
    def _update_stats(self):
        """更新统计信息"""
        self.stats['size_bytes'] = self.current_size_bytes
        self.stats['entry_count'] = len(self.cache)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self.stats['hits'] + self.stats['misses']
        hit_rate = self.stats['hits'] / total_requests if total_requests > 0 else 0.0
        
        return {
            **self.stats,
            'hit_rate': hit_rate,
            'size_mb': self.current_size_bytes / 1024 / 1024
        }

class IncrementalComputer:
    """增量计算器"""
    
    def __init__(self, cache_size_mb: int = 100):
        """
        初始化增量计算器
        
        Args:
            cache_size_mb: 缓存大小(MB)
        """
        self.cache = IncrementalCache(cache_size_mb)
        self.computation_graph: Dict[str, Dict] = {}
        
        logger.info("增量计算器初始化完成")
    
    def register_computation(self, 
                           name: str,
                           func: Callable,
                           dependencies: List[str] = None,
                           cache_ttl: int = 3600):
        """
        注册计算函数
        
        Args:
            name: 计算名称
            func: 计算函数
            dependencies: 依赖的计算
            cache_ttl: 缓存生存时间
        """
        self.computation_graph[name] = {
            'func': func,
            'dependencies': dependencies or [],
            'cache_ttl': cache_ttl
        }
        
        logger.debug(f"注册计算: {name}")
    
    def compute(self, name: str, *args, **kwargs) -> Any:
        """
        执行计算（带缓存）
        
        Args:
            name: 计算名称
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            计算结果
        """
        if name not in self.computation_graph:
            raise ValueError(f"未注册的计算: {name}")
        
        # 生成缓存键
        cache_key = self._generate_cache_key(name, args, kwargs)
        
        # 尝试从缓存获取
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"缓存命中: {name}")
            return cached_result
        
        # 执行计算
        computation = self.computation_graph[name]
        func = computation['func']
        dependencies = computation['dependencies']
        
        logger.debug(f"执行计算: {name}")
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            
            # 缓存结果
            self.cache.put(cache_key, result, dependencies)
            
            execution_time = time.time() - start_time
            logger.debug(f"计算完成: {name} ({execution_time:.4f}秒)")
            
            return result
            
        except Exception as e:
            logger.error(f"计算失败: {name} - {e}")
            raise
    
    def invalidate_computation(self, name: str):
        """使计算缓存失效"""
        pattern = f"{name}_"
        self.cache.invalidate_pattern(pattern)
    
    def _generate_cache_key(self, name: str, args: tuple, kwargs: dict) -> str:
        """生成缓存键"""
        # 创建参数的哈希
        param_str = f"{name}_{args}_{sorted(kwargs.items())}"
        return hashlib.md5(param_str.encode()).hexdigest()

class IncrementalIndicators:
    """增量技术指标计算"""
    
    def __init__(self, cache_size_mb: int = 50):
        """初始化增量指标计算器"""
        self.computer = IncrementalComputer(cache_size_mb)
        self._register_indicators()
        
        # 数据缓存
        self.data_cache: Dict[str, pd.DataFrame] = {}
        
        logger.info("增量指标计算器初始化完成")
    
    def _register_indicators(self):
        """注册技术指标计算函数"""
        # 移动平均
        self.computer.register_computation(
            'moving_average',
            self._compute_moving_average,
            cache_ttl=1800
        )
        
        # 标准差
        self.computer.register_computation(
            'rolling_std',
            self._compute_rolling_std,
            dependencies=['moving_average'],
            cache_ttl=1800
        )
        
        # RSI
        self.computer.register_computation(
            'rsi',
            self._compute_rsi,
            cache_ttl=1800
        )
        
        # MACD
        self.computer.register_computation(
            'macd',
            self._compute_macd,
            dependencies=['moving_average'],
            cache_ttl=1800
        )
        
        # 布林带
        self.computer.register_computation(
            'bollinger_bands',
            self._compute_bollinger_bands,
            dependencies=['moving_average', 'rolling_std'],
            cache_ttl=1800
        )
    
    def update_data(self, symbol: str, new_data: pd.DataFrame):
        """更新数据并触发增量计算"""
        if symbol not in self.data_cache:
            self.data_cache[symbol] = new_data.copy()
        else:
            # 增量更新
            old_data = self.data_cache[symbol]
            
            # 检查是否有新数据
            if not new_data.empty:
                # 合并数据
                combined_data = pd.concat([old_data, new_data]).drop_duplicates()
                self.data_cache[symbol] = combined_data.sort_index()
                
                # 使相关缓存失效
                self.computer.invalidate_computation(f"{symbol}_")
        
        logger.debug(f"数据更新: {symbol}, 总行数: {len(self.data_cache[symbol])}")
    
    def get_indicator(self, symbol: str, indicator: str, **params) -> pd.Series:
        """
        获取技术指标
        
        Args:
            symbol: 标的代码
            indicator: 指标名称
            **params: 指标参数
            
        Returns:
            指标序列
        """
        if symbol not in self.data_cache:
            raise ValueError(f"没有 {symbol} 的数据")
        
        data = self.data_cache[symbol]
        return self.computer.compute(indicator, data, symbol, **params)
    
    def _compute_moving_average(self, data: pd.DataFrame, symbol: str, 
                               window: int = 20, column: str = 'close') -> pd.Series:
        """计算移动平均"""
        return data[column].rolling(window=window).mean()
    
    def _compute_rolling_std(self, data: pd.DataFrame, symbol: str,
                            window: int = 20, column: str = 'close') -> pd.Series:
        """计算滚动标准差"""
        return data[column].rolling(window=window).std()
    
    def _compute_rsi(self, data: pd.DataFrame, symbol: str,
                    window: int = 14, column: str = 'close') -> pd.Series:
        """计算RSI"""
        delta = data[column].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _compute_macd(self, data: pd.DataFrame, symbol: str,
                     fast: int = 12, slow: int = 26, signal: int = 9,
                     column: str = 'close') -> Dict[str, pd.Series]:
        """计算MACD"""
        ema_fast = data[column].ewm(span=fast).mean()
        ema_slow = data[column].ewm(span=slow).mean()
        
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal).mean()
        histogram = macd_line - signal_line
        
        return {
            'macd': macd_line,
            'signal': signal_line,
            'histogram': histogram
        }
    
    def _compute_bollinger_bands(self, data: pd.DataFrame, symbol: str,
                                window: int = 20, num_std: float = 2.0,
                                column: str = 'close') -> Dict[str, pd.Series]:
        """计算布林带"""
        # 使用已缓存的移动平均和标准差
        ma = self.get_indicator(symbol, 'moving_average', window=window, column=column)
        std = self.get_indicator(symbol, 'rolling_std', window=window, column=column)
        
        upper_band = ma + (std * num_std)
        lower_band = ma - (std * num_std)
        
        return {
            'upper': upper_band,
            'middle': ma,
            'lower': lower_band
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.computer.cache.get_stats()


# 测试函数
def test_incremental_computing():
    """测试增量计算"""
    logger.info("开始测试增量计算...")
    
    try:
        # 创建增量指标计算器
        indicators = IncrementalIndicators()
        
        # 生成测试数据
        np.random.seed(42)
        n_points = 1000
        dates = pd.date_range('2024-01-01', periods=n_points, freq='1min')
        prices = 100 + np.cumsum(np.random.randn(n_points) * 0.01)
        
        test_data = pd.DataFrame({
            'datetime': dates,
            'close': prices,
            'volume': np.random.randint(1000, 10000, n_points)
        }).set_index('datetime')
        
        symbol = "TEST"
        
        # 初始数据更新
        logger.info("初始数据更新...")
        indicators.update_data(symbol, test_data[:500])
        
        # 计算指标（第一次，会缓存）
        logger.info("计算技术指标...")
        start_time = time.time()
        
        ma = indicators.get_indicator(symbol, 'moving_average', window=20)
        rsi = indicators.get_indicator(symbol, 'rsi', window=14)
        
        first_time = time.time() - start_time
        logger.info(f"首次计算耗时: {first_time:.4f}秒")
        
        # 再次计算相同指标（应该从缓存获取）
        start_time = time.time()
        
        ma2 = indicators.get_indicator(symbol, 'moving_average', window=20)
        rsi2 = indicators.get_indicator(symbol, 'rsi', window=14)
        
        cached_time = time.time() - start_time
        logger.info(f"缓存计算耗时: {cached_time:.4f}秒")
        
        # 验证结果一致性
        if ma.equals(ma2) and rsi.equals(rsi2):
            logger.info("✅ 缓存结果一致性验证通过")
        else:
            logger.warning("⚠️ 缓存结果不一致")
        
        # 增量数据更新
        logger.info("增量数据更新...")
        indicators.update_data(symbol, test_data[500:])
        
        # 重新计算指标
        ma3 = indicators.get_indicator(symbol, 'moving_average', window=20)
        logger.info(f"✅ 增量更新后指标长度: {len(ma3)}")
        
        # 显示缓存统计
        cache_stats = indicators.get_cache_stats()
        logger.info(f"📊 缓存统计: 命中率 {cache_stats['hit_rate']:.2%}, "
                   f"大小 {cache_stats['size_mb']:.2f}MB")
        
        # 计算加速比
        if cached_time > 0:
            speedup = first_time / cached_time
            logger.info(f"🚀 缓存加速比: {speedup:.1f}x")
        
        logger.info("✅ 增量计算测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 增量计算测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    test_incremental_computing()
