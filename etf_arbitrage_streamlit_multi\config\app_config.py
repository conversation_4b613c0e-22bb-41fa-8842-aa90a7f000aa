"""
应用配置文件
"""

from typing import Dict, Any
from pathlib import Path

# 应用基础配置
APP_CONFIG = {
    'title': 'ETF套利交易系统',
    'description': '专业的ETF套利交易解决方案',
    'version': '2.0.0',
    'author': 'ETF Trading Team',
    
    # 界面配置
    'theme': {
        'primary_color': '#1f77b4',
        'background_color': '#ffffff',
        'text_color': '#262730'
    },
    
    # 数据库配置
    'database': {
        'ticks_db': 'ticks.db',
        'monitoring_db': 'monitoring.db',
        'backup_interval': 3600  # 1小时备份一次
    },
    
    # ETF标的配置
    'symbols': {
        '159740': {
            'name': '纳指ETF',
            'market': 'sz',
            'description': '跟踪纳斯达克100指数'
        },
        '159915': {
            'name': '创业板ETF', 
            'market': 'sz',
            'description': '跟踪创业板指数'
        },
        '159919': {
            'name': '沪深300ETF',
            'market': 'sz', 
            'description': '跟踪沪深300指数'
        },
        '512880': {
            'name': '券商ETF',
            'market': 'sh',
            'description': '跟踪中证全指证券公司指数'
        }
    },
    
    # 默认策略参数
    'default_strategy_params': {
        'buy_trigger_drop': -0.0060,
        'profit_target': 0.0055,
        'stop_loss': -0.02,
        'max_hold_time': 3600,
        'position_size': 100000,
        'layer_ratios': [0.4, 0.35, 0.25]
    },
    
    # 数据采集配置
    'data_collection': {
        'fetch_interval': 5,  # 秒
        'batch_size': 10,
        'max_retries': 3,
        'timeout': 30
    },
    
    # 监控配置
    'monitoring': {
        'performance_check_interval': 60,  # 秒
        'alert_cooldown': 300,  # 5分钟
        'max_cpu_usage': 80,  # %
        'max_memory_usage': 80  # %
    },
    
    # 页面配置
    'pages': {
        '数据采集': {
            'icon': '📊',
            'description': '实时数据采集和存储管理'
        },
        '回测分析': {
            'icon': '🔬', 
            'description': '历史数据回测和参数优化'
        },
        '实时交易': {
            'icon': '🚀',
            'description': '实时信号监控和模拟交易'
        },
        '系统监控': {
            'icon': '📈',
            'description': '系统性能和业务指标监控'
        },
        '配置管理': {
            'icon': '⚙️',
            'description': '策略参数和系统设置管理'
        }
    }
}

# 获取项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

def get_db_path(db_name: str) -> str:
    """获取数据库文件路径"""
    return str(PROJECT_ROOT / db_name)

def get_config(key: str, default: Any = None) -> Any:
    """获取配置项"""
    keys = key.split('.')
    config = APP_CONFIG
    
    try:
        for k in keys:
            config = config[k]
        return config
    except KeyError:
        return default

def update_config(key: str, value: Any) -> None:
    """更新配置项"""
    keys = key.split('.')
    config = APP_CONFIG
    
    # 导航到父级配置
    for k in keys[:-1]:
        if k not in config:
            config[k] = {}
        config = config[k]
    
    # 设置值
    config[keys[-1]] = value