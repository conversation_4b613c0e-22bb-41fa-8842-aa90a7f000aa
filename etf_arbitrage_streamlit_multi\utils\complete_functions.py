#!/usr/bin/env python3
"""
完整功能模块 - 包含原版所有核心函数
从 app_enhanced_backtest_dashboard.py 提取的完整功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import sqlite3
import json
import itertools
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sys
import os
import importlib.util
from pathlib import Path
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent.absolute()
root_dir = str(current_dir)
sys.path.insert(0, root_dir)

# 导入回测模块
try:
    backtest_spec = importlib.util.spec_from_file_location(
        "backtest_enhanced_root", 
        os.path.join(root_dir, "backtest_enhanced.py")
    )
    backtest_module = importlib.util.module_from_spec(backtest_spec)
    backtest_spec.loader.exec_module(backtest_module)
    BacktestConfig = backtest_module.BacktestConfig
    EnhancedBacktest = backtest_module.EnhancedBacktest
    
    strategy_spec = importlib.util.spec_from_file_location(
        "strategy_config_root",
        os.path.join(root_dir, "strategy_config.py")
    )
    strategy_module = importlib.util.module_from_spec(strategy_spec)
    strategy_spec.loader.exec_module(strategy_module)
    StrategyConfig = strategy_module.StrategyConfig
except Exception as e:
    st.error(f"无法导入回测模块: {e}")

@st.cache_data
def load_available_symbols():
    """加载可用的交易标的"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("SELECT DISTINCT symbol FROM ticks ORDER BY symbol")
        symbols = [row[0] for row in cursor.fetchall()]
        conn.close()
        return symbols
    except:
        return ["159740"]

@st.cache_data
def get_data_date_range(symbol: str):
    """获取数据的日期范围"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute(
            "SELECT MIN(DATE(tick_time)) as min_date, MAX(DATE(tick_time)) as max_date FROM ticks WHERE symbol=?",
            (symbol,)
        )
        result = cursor.fetchone()
        conn.close()
        if result and result[0] and result[1]:
            return result[0], result[1]
        return None, None
    except:
        return None, None

def run_enhanced_backtest(config: BacktestConfig) -> Dict:
    """运行增强版回测"""
    backtest = EnhancedBacktest(config)
    return backtest.run_backtest()

def create_price_and_signals_chart(df_signals: pd.DataFrame, df_trades: pd.DataFrame, config) -> go.Figure:
    """创建价格和信号图表"""
    try:
        # 检查数据有效性
        if df_signals is None or df_signals.empty:
            st.warning("信号数据为空，无法生成图表")
            return go.Figure()
        
        fig = make_subplots(
            rows=3, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.08,
            subplot_titles=('价格走势与交易信号', '持仓变化', '交易信号强度'),
            row_heights=[0.5, 0.25, 0.25]
        )
    except Exception as e:
        st.error(f"图表初始化失败: {e}")
        return go.Figure()
    
    # 价格走势 - 安全处理
    try:
        if 'time' in df_signals.columns and 'price' in df_signals.columns:
            price_data = df_signals['price'].dropna()
            time_data = df_signals['time'][price_data.index]
            
            fig.add_trace(
                go.Scatter(
                    x=time_data,
                    y=price_data,
                    mode='lines',
                    name='价格',
                    line=dict(color='blue', width=1),
                    hovertemplate='时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
                ),
                row=1, col=1
            )
    except Exception as e:
        st.warning(f"价格数据处理失败: {e}")
    
    # 交易点位 - 安全处理
    try:
        if df_trades is not None and not df_trades.empty:
            # 尝试不同的列名组合
            type_col = None
            for col in ['type', 'action', 'side']:
                if col in df_trades.columns:
                    type_col = col
                    break
            
            if type_col:
                buy_trades = df_trades[df_trades[type_col].isin(['BUY', 'buy', 'Buy'])]
                sell_trades = df_trades[df_trades[type_col].isin(['SELL', 'sell', 'Sell'])]
                
                if not buy_trades.empty and 'time' in buy_trades.columns and 'price' in buy_trades.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=buy_trades['time'],
                            y=buy_trades['price'],
                            mode='markers',
                            name='买入',
                            marker=dict(
                                symbol='triangle-up',
                                size=10,
                                color='green',
                                line=dict(width=1, color='darkgreen')
                            ),
                            hovertemplate='买入<br>时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
                        ),
                        row=1, col=1
                    )
                
                if not sell_trades.empty and 'time' in sell_trades.columns and 'price' in sell_trades.columns:
                    fig.add_trace(
                        go.Scatter(
                            x=sell_trades['time'],
                            y=sell_trades['price'],
                            mode='markers',
                            name='卖出',
                            marker=dict(
                                symbol='triangle-down',
                                size=10,
                                color='red',
                                line=dict(width=1, color='darkred')
                            ),
                            hovertemplate='卖出<br>时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
                        ),
                        row=1, col=1
                    )
    except Exception as e:
        st.warning(f"交易数据处理失败: {e}")
    
    # 持仓变化 - 安全处理
    try:
        if 'position' in df_signals.columns:
            position_data = df_signals['position'].dropna()
            time_data = df_signals['time'][position_data.index]
            
            fig.add_trace(
                go.Scatter(
                    x=time_data,
                    y=position_data,
                    mode='lines',
                    name='持仓',
                    line=dict(color='orange', width=2),
                    hovertemplate='时间: %{x}<br>持仓: %{y}<extra></extra>'
                ),
                row=2, col=1
            )
    except Exception as e:
        st.warning(f"持仓数据处理失败: {e}")
    
    # 交易信号强度
    buy_threshold = config.buy_trigger_drop
    profit_threshold = config.profit_target
    
    signal_colors = [
        'green' if s <= buy_threshold 
        else 'red' if s >= profit_threshold 
        else 'gray' 
        for s in df_signals['signal']
    ]
    
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['signal'],
            mode='markers',
            name='信号',
            marker=dict(
                color=signal_colors,
                size=4,
                opacity=0.6
            ),
            hovertemplate='时间: %{x}<br>信号: %{y:.6f}<extra></extra>'
        ),
        row=3, col=1
    )
    
    # 添加参数化的信号阈值线
    fig.add_hline(y=buy_threshold, line_dash="dash", line_color="red", 
                  annotation_text=f"买入阈值: {buy_threshold:.4f}", row=3, col=1)
    fig.add_hline(y=profit_threshold, line_dash="dash", line_color="green", 
                  annotation_text=f"止盈阈值: {profit_threshold:.4f}", row=3, col=1)
    
    # 更新布局
    fig.update_layout(
        height=800,
        showlegend=True,
        hovermode='x unified',
        xaxis3=dict(
            title="时间",
            rangeslider=dict(visible=True, thickness=0.05),
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),
                dict(bounds=[16, 9], pattern="hour"),
            ]
        )
    )
    
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="持仓数量", row=2, col=1)
    fig.update_yaxes(title_text="信号强度", row=3, col=1)
    
    return fig

def create_equity_curve_chart(df_equity: pd.DataFrame, config: BacktestConfig) -> go.Figure:
    """创建净值曲线图表"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('净值曲线', '回撤曲线'),
        row_heights=[0.7, 0.3]
    )
    
    # 净值曲线
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=df_equity['equity'],
            mode='lines',
            name='净值',
            line=dict(color='blue', width=2),
            hovertemplate='时间: %{x}<br>净值: %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 初始资金线
    fig.add_hline(
        y=config.initial_capital,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"初始资金: {config.initial_capital:,.0f}",
        row=1, col=1
    )
    
    # 回撤曲线
    peak = df_equity['equity'].cummax()
    drawdown = (df_equity['equity'] - peak) / peak
    
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=drawdown,
            mode='lines',
            name='回撤',
            line=dict(color='red', width=1),
            fill='tonexty',
            fillcolor='rgba(255,0,0,0.3)',
            hovertemplate='时间: %{x}<br>回撤: %{y:.2%}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 更新布局
    fig.update_layout(
        height=600,
        showlegend=True,
        hovermode='x unified',
        xaxis2=dict(
            title="时间",
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),
                dict(bounds=[16, 9], pattern="hour"),
            ]
        )
    )
    
    fig.update_yaxes(title_text="净值", row=1, col=1)
    fig.update_yaxes(title_text="回撤", tickformat='.2%', row=2, col=1)
    
    return fig

def create_trade_analysis_chart(df_trades: pd.DataFrame) -> go.Figure:
    """创建交易分析图表"""
    if df_trades.empty:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无交易数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=20)
        )
        return fig
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('交易时间分布', '买卖比例', '交易原因分析', '盈亏分布'),
        specs=[[{"type": "histogram"}, {"type": "pie"}],
               [{"type": "bar"}, {"type": "histogram"}]]
    )
    
    # 交易时间分布
    df_trades['hour'] = pd.to_datetime(df_trades['time']).dt.hour
    fig.add_trace(
        go.Histogram(
            x=df_trades['hour'],
            nbinsx=24,
            name='交易时间',
            marker_color='lightblue'
        ),
        row=1, col=1
    )
    
    # 买卖比例
    trade_counts = df_trades['type'].value_counts()
    fig.add_trace(
        go.Pie(
            labels=trade_counts.index,
            values=trade_counts.values,
            name='买卖比例',
            marker_colors=['green', 'red']
        ),
        row=1, col=2
    )
    
    # 交易原因分析
    if 'reason' in df_trades.columns:
        reason_counts = df_trades['reason'].value_counts()
        fig.add_trace(
            go.Bar(
                x=reason_counts.index,
                y=reason_counts.values,
                name='交易原因',
                marker_color='orange'
            ),
            row=2, col=1
        )
    
    # 盈亏分布
    sell_trades = df_trades[df_trades['type'] == 'SELL']
    if not sell_trades.empty and 'pnl' in sell_trades.columns:
        fig.add_trace(
            go.Histogram(
                x=sell_trades['pnl'],
                name='盈亏分布',
                marker_color='purple'
            ),
            row=2, col=2
        )
    
    fig.update_layout(
        title="交易行为分析",
        height=600,
        showlegend=False
    )
    
    return fig

def analyze_tick_volatility(symbol: str, start_date: str, end_date: str, window: int = 20) -> Dict:
    """分析tick数据波动统计（基于策略窗口）"""
    try:
        conn = sqlite3.connect("ticks.db")
        
        query = """
        SELECT tick_time as time, price, volume 
        FROM ticks 
        WHERE symbol=? AND tick_time BETWEEN ? AND ?
        ORDER BY tick_time ASC
        """
        
        df = pd.read_sql_query(
            query, conn, 
            params=[symbol, start_date, end_date],
            parse_dates=['time']
        )
        conn.close()
        
        if df.empty:
            return {'error': '无数据'}
        
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df = df.dropna(subset=['price'])
        
        if len(df) < window:
            return {'error': f'数据不足，需要至少{window}个tick'}
        
        # 基于20个tick窗口的分析（与策略一致）
        window_returns = []
        window_drawdowns = []
        
        for i in range(window, len(df)):
            # 计算20个tick窗口的收益率（与策略信号计算一致）
            window_data = df.iloc[i-window:i+1]
            p0 = float(window_data['price'].iloc[0])
            p1 = float(window_data['price'].iloc[-1])
            
            if p0 > 0:
                window_return = (p1 - p0) / p0
                window_returns.append(window_return)
            
            # 计算20个tick窗口内的最大回撤
            window_high = float(window_data['price'].max())
            window_current = float(window_data['price'].iloc[-1])
            
            if window_high > 0:
                window_drawdown = (window_current - window_high) / window_high
                window_drawdowns.append(window_drawdown)
        
        window_returns = np.array(window_returns)
        window_drawdowns = np.array(window_drawdowns)
        
        # 统计分析
        analysis = {
            '数据概况': {
                'tick总数': len(df),
                f'{window}tick窗口数': len(window_returns),
                '价格范围': f"{df['price'].min():.4f} - {df['price'].max():.4f}",
                '平均价格': f"{df['price'].mean():.4f}"
            },
            f'{window}tick窗口波动分析': {
                f'平均{window}tick收益率': f"{window_returns.mean():.6f} ({window_returns.mean()*100:.4f}%)",
                f'{window}tick收益率标准差': f"{window_returns.std():.6f} ({window_returns.std()*100:.4f}%)",
                f'最大{window}tick涨幅': f"{window_returns.max():.4f} ({window_returns.max()*100:.2f}%)",
                f'最小{window}tick跌幅': f"{window_returns.min():.4f} ({window_returns.min()*100:.2f}%)"
            },
            f'{window}tick窗口回撤分析': {
                f'最大{window}tick回撤': f"{window_drawdowns.min():.4f} ({window_drawdowns.min()*100:.2f}%)",
                f'平均{window}tick回撤': f"{window_drawdowns.mean():.4f} ({window_drawdowns.mean()*100:.2f}%)",
                f'{window}tick回撤标准差': f"{window_drawdowns.std():.4f} ({window_drawdowns.std()*100:.2f}%)"
            },
            '策略参数建议': {
                '建议买入触发跌幅': f"{np.percentile(window_drawdowns, 10):.4f} ({np.percentile(window_drawdowns, 10)*100:.2f}%)",
                '建议止盈目标': f"{np.percentile(window_returns, 90):.4f} ({np.percentile(window_returns, 90)*100:.2f}%)",
                '建议止损线': f"{np.percentile(window_drawdowns, 5):.4f} ({np.percentile(window_drawdowns, 5)*100:.2f}%)"
            },
            'raw_data': {
                'original_df': df,
                'window_returns': window_returns,
                'window_drawdowns': window_drawdowns
            }
        }
        
        return analysis
        
    except Exception as e:
        return {'error': f'分析失败: {str(e)}'}