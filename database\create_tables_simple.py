#!/usr/bin/env python3
"""
简化的数据库表创建脚本
直接使用Python执行SQL语句
"""

import sqlite3
import logging
from pathlib import Path
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_tables():
    """创建新功能所需的数据库表"""
    
    db_path = Path(__file__).parent.parent / "ticks.db"
    logger.info(f"连接数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建参数优化结果表
        logger.info("创建 optimal_configs 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS optimal_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                config_name TEXT NOT NULL,
                parameters TEXT NOT NULL,
                performance_metrics TEXT NOT NULL,
                backtest_period TEXT,
                optimization_method TEXT,
                market_characteristics TEXT,
                fitness_score REAL,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL,
                UNIQUE(symbol, config_name)
            )
        """)
        logger.info("✅ optimal_configs 表创建成功")
        
        # 2. 创建预警规则表
        logger.info("创建 alert_rules 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS alert_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                rule_name TEXT NOT NULL,
                condition_expr TEXT NOT NULL,
                channels TEXT NOT NULL,
                template TEXT NOT NULL,
                cooldown_seconds INTEGER DEFAULT 300,
                enabled BOOLEAN DEFAULT 1,
                priority INTEGER DEFAULT 1,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        logger.info("✅ alert_rules 表创建成功")
        
        # 3. 创建预警历史表
        logger.info("创建 alert_history 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS alert_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                rule_name TEXT NOT NULL,
                rule_id INTEGER,
                message TEXT NOT NULL,
                alert_time TEXT NOT NULL,
                channels TEXT NOT NULL,
                success BOOLEAN DEFAULT 1,
                response_data TEXT,
                trigger_data TEXT
            )
        """)
        logger.info("✅ alert_history 表创建成功")
        
        # 4. 创建市场特征分析表
        logger.info("创建 market_analysis 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS market_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                analysis_date TEXT NOT NULL,
                analysis_period_days INTEGER NOT NULL,
                volatility_metrics TEXT NOT NULL,
                trend_metrics TEXT NOT NULL,
                liquidity_metrics TEXT NOT NULL,
                trading_patterns TEXT NOT NULL,
                recommendations TEXT,
                created_at TEXT NOT NULL,
                UNIQUE(symbol, analysis_date)
            )
        """)
        logger.info("✅ market_analysis 表创建成功")
        
        # 5. 创建优化任务表
        logger.info("创建 optimization_tasks 表...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS optimization_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                task_name TEXT NOT NULL,
                optimization_method TEXT NOT NULL,
                strategy_type TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                progress REAL DEFAULT 0.0,
                start_time TEXT,
                end_time TEXT,
                error_message TEXT,
                result_config_ids TEXT,
                created_at TEXT NOT NULL,
                updated_at TEXT NOT NULL
            )
        """)
        logger.info("✅ optimization_tasks 表创建成功")
        
        # 提交表创建
        conn.commit()
        logger.info("✅ 所有表创建完成")
        
        # 创建索引
        create_indexes(cursor)
        
        # 插入默认数据
        insert_default_data(cursor)
        
        # 提交所有更改
        conn.commit()
        
        # 验证表
        verify_tables(cursor)
        
        conn.close()
        logger.info("✅ 数据库设置完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库设置失败: {e}")
        return False

def create_indexes(cursor):
    """创建索引"""
    logger.info("创建索引...")
    
    indexes = [
        # optimal_configs 表索引
        "CREATE INDEX IF NOT EXISTS idx_optimal_configs_symbol ON optimal_configs(symbol)",
        "CREATE INDEX IF NOT EXISTS idx_optimal_configs_method ON optimal_configs(optimization_method)",
        "CREATE INDEX IF NOT EXISTS idx_optimal_configs_fitness ON optimal_configs(fitness_score DESC)",
        "CREATE INDEX IF NOT EXISTS idx_optimal_configs_created ON optimal_configs(created_at DESC)",
        
        # alert_rules 表索引
        "CREATE INDEX IF NOT EXISTS idx_alert_rules_symbol ON alert_rules(symbol, enabled)",
        "CREATE INDEX IF NOT EXISTS idx_alert_rules_priority ON alert_rules(priority DESC)",
        
        # alert_history 表索引
        "CREATE INDEX IF NOT EXISTS idx_alert_history_symbol ON alert_history(symbol)",
        "CREATE INDEX IF NOT EXISTS idx_alert_history_time ON alert_history(alert_time DESC)",
        "CREATE INDEX IF NOT EXISTS idx_alert_history_rule ON alert_history(rule_id)",
        "CREATE INDEX IF NOT EXISTS idx_alert_history_success ON alert_history(success)",
        
        # market_analysis 表索引
        "CREATE INDEX IF NOT EXISTS idx_market_analysis_symbol ON market_analysis(symbol)",
        "CREATE INDEX IF NOT EXISTS idx_market_analysis_date ON market_analysis(analysis_date DESC)",
        
        # optimization_tasks 表索引
        "CREATE INDEX IF NOT EXISTS idx_optimization_tasks_symbol ON optimization_tasks(symbol)",
        "CREATE INDEX IF NOT EXISTS idx_optimization_tasks_status ON optimization_tasks(status)",
        "CREATE INDEX IF NOT EXISTS idx_optimization_tasks_created ON optimization_tasks(created_at DESC)",
        
        # 现有表的性能优化索引
        "CREATE INDEX IF NOT EXISTS idx_ticks_symbol_time_desc ON ticks(symbol, tick_time DESC)",
        "CREATE INDEX IF NOT EXISTS idx_ticks_time_price ON ticks(tick_time, price)",
        "CREATE INDEX IF NOT EXISTS idx_signals_symbol_ts_desc ON strategy_signals(symbol, ts DESC)",
        "CREATE INDEX IF NOT EXISTS idx_signals_signal_type ON strategy_signals(signal)"
    ]
    
    for index_sql in indexes:
        try:
            cursor.execute(index_sql)
            logger.info(f"✅ 索引创建成功")
        except sqlite3.Error as e:
            if "already exists" in str(e).lower():
                logger.info(f"⚠️  索引已存在，跳过")
            else:
                logger.error(f"❌ 索引创建失败: {e}")

def insert_default_data(cursor):
    """插入默认数据"""
    logger.info("插入默认预警规则...")
    
    default_rules = [
        {
            'symbol': '159740',
            'rule_name': '买入信号预警',
            'condition_expr': 'signal <= -0.006',
            'channels': '["email"]',
            'template': '🔥 {symbol} 触发买入信号！\\n当前价格: {price}\\n信号强度: {signal:.4f}\\n时间: {time}',
            'cooldown_seconds': 300,
            'priority': 2
        },
        {
            'symbol': '159740',
            'rule_name': '强烈买入信号',
            'condition_expr': 'signal <= -0.008',
            'channels': '["email", "wechat"]',
            'template': '🚨 {symbol} 强烈买入信号！\\n当前价格: {price}\\n信号强度: {signal:.4f}\\n建议立即关注！\\n时间: {time}',
            'cooldown_seconds': 180,
            'priority': 1
        },
        {
            'symbol': '159740',
            'rule_name': '止盈信号预警',
            'condition_expr': 'position_profit_rate >= 0.005',
            'channels': '["email"]',
            'template': '💰 {symbol} 达到止盈条件！\\n当前收益率: {position_profit_rate:.2%}\\n建议考虑止盈\\n时间: {time}',
            'cooldown_seconds': 600,
            'priority': 3
        },
        {
            'symbol': '159740',
            'rule_name': '风险预警',
            'condition_expr': 'position_profit_rate <= -0.015',
            'channels': '["email", "wechat"]',
            'template': '⚠️ {symbol} 风险预警！\\n当前亏损: {position_profit_rate:.2%}\\n请注意风险控制\\n时间: {time}',
            'cooldown_seconds': 300,
            'priority': 1
        }
    ]
    
    current_time = datetime.now().isoformat()
    
    for rule in default_rules:
        try:
            cursor.execute("""
                INSERT OR IGNORE INTO alert_rules 
                (symbol, rule_name, condition_expr, channels, template, 
                 cooldown_seconds, priority, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                rule['symbol'],
                rule['rule_name'],
                rule['condition_expr'],
                rule['channels'],
                rule['template'],
                rule['cooldown_seconds'],
                rule['priority'],
                current_time,
                current_time
            ))
            logger.info(f"✅ 预警规则 '{rule['rule_name']}' 插入成功")
        except sqlite3.Error as e:
            logger.error(f"❌ 预警规则插入失败: {e}")

def verify_tables(cursor):
    """验证表是否创建成功"""
    logger.info("验证数据库表...")
    
    expected_tables = [
        'optimal_configs',
        'alert_rules', 
        'alert_history',
        'market_analysis',
        'optimization_tasks'
    ]
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = [row[0] for row in cursor.fetchall()]
    
    all_success = True
    for table in expected_tables:
        if table in existing_tables:
            # 检查表结构
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            logger.info(f"✅ 表 {table} 创建成功，当前记录数: {count}")
        else:
            logger.error(f"❌ 表 {table} 创建失败")
            all_success = False
    
    return all_success

def test_operations():
    """测试数据库操作"""
    logger.info("测试数据库操作...")
    
    db_path = Path(__file__).parent.parent / "ticks.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试插入优化配置
        test_config = {
            'symbol': 'TEST',
            'config_name': 'test_config',
            'parameters': '{"buy_trigger_drop": -0.006, "profit_target": 0.005}',
            'performance_metrics': '{"total_return": 0.05, "max_drawdown": -0.02}',
            'optimization_method': 'test',
            'fitness_score': 0.85,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        cursor.execute("""
            INSERT OR REPLACE INTO optimal_configs 
            (symbol, config_name, parameters, performance_metrics, optimization_method, 
             fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_config['symbol'],
            test_config['config_name'],
            test_config['parameters'],
            test_config['performance_metrics'],
            test_config['optimization_method'],
            test_config['fitness_score'],
            test_config['created_at'],
            test_config['updated_at']
        ))
        
        # 测试查询
        cursor.execute("SELECT * FROM optimal_configs WHERE symbol=?", ('TEST',))
        result = cursor.fetchone()
        
        if result:
            logger.info("✅ 数据库操作测试成功")
            # 清理测试数据
            cursor.execute("DELETE FROM optimal_configs WHERE symbol=?", ('TEST',))
        else:
            logger.error("❌ 数据库操作测试失败")
        
        conn.commit()
        conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库操作测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始创建ETF套利系统数据库表...")
    print("=" * 50)
    
    success = create_tables()
    
    if success:
        print("\n🎉 数据库表创建成功！")
        
        # 运行测试
        if test_operations():
            print("✅ 数据库操作测试通过")
        
        print("\n📋 已完成:")
        print("✅ 新功能数据表已创建")
        print("✅ 性能优化索引已添加")
        print("✅ 默认预警规则已插入")
        print("✅ 数据库操作测试通过")
        
        print("\n📋 下一步:")
        print("1. 实现数据库连接池")
        print("2. 开发参数优化器")
        print("3. 开发预警系统")
        
    else:
        print("\n❌ 数据库表创建失败，请检查错误信息")
    
    print("=" * 50)
