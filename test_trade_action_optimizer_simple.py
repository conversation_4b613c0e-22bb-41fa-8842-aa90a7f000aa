"""
简化的交易动作优化器测试
直接测试OptimizedTradeActionProcessor的功能
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from etf_arbitrage_streamlit_multi.utils.trade_action_optimizer import (
        OptimizedTradeActionProcessor, TradeActionType
    )
    print("✅ 成功导入交易动作优化器")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_action_normalization():
    """测试动作标准化"""
    print("\n🧪 测试动作标准化...")
    
    test_cases = [
        ("BUY", TradeActionType.BUY),
        ("买入", TradeActionType.BUY),
        ("SELL", TradeActionType.SELL),
        ("卖出", TradeActionType.SELL),
        ("止损", TradeActionType.STOP_LOSS),
        ("", TradeActionType.HOLD)
    ]
    
    success = 0
    for input_val, expected in test_cases:
        result = OptimizedTradeActionProcessor.normalize_action(input_val)
        if result == expected:
            print(f"  ✅ '{input_val}' -> {result.value}")
            success += 1
        else:
            print(f"  ❌ '{input_val}' -> {result.value} (期望: {expected.value})")
    
    return success == len(test_cases)

def test_display_formatting():
    """测试显示格式化"""
    print("\n🎨 测试显示格式化...")
    
    action = TradeActionType.BUY
    styles = ["full", "icon_only", "text_only", "colored"]
    
    for style in styles:
        display = OptimizedTradeActionProcessor.format_action_display(action, style)
        print(f"  {style}: {display}")
    
    return True

def test_batch_processing():
    """测试批量处理"""
    print("\n⚡ 测试批量处理...")
    
    # 创建测试数据
    trades = []
    for i in range(100):
        trades.append({
            'timestamp': datetime.now() - timedelta(minutes=i),
            'action': np.random.choice(['BUY', 'SELL', 'HOLD']),
            'amount': 1000 + i * 10,
            'commission': 3.0
        })
    
    # 批量处理
    start_time = time.time()
    df = OptimizedTradeActionProcessor.batch_process_trades(trades)
    process_time = time.time() - start_time
    
    print(f"  处理 {len(trades)} 条记录耗时: {process_time:.4f}s")
    print(f"  结果DataFrame形状: {df.shape}")
    print(f"  包含列: {list(df.columns)}")
    
    # 验证结果
    if len(df) == len(trades):
        print("  ✅ 记录数量正确")
        return True
    else:
        print("  ❌ 记录数量不匹配")
        return False

def test_capital_curve():
    """测试资金曲线计算"""
    print("\n📈 测试资金曲线计算...")
    
    trades = [
        {
            'timestamp': datetime.now() - timedelta(hours=2),
            'action': 'BUY',
            'amount': 10000,
            'commission': 30
        },
        {
            'timestamp': datetime.now() - timedelta(hours=1),
            'action': 'SELL',
            'amount': 10500,
            'commission': 31.5
        }
    ]
    
    initial_capital = 100000
    df = OptimizedTradeActionProcessor.batch_process_trades(trades)
    times, capitals = OptimizedTradeActionProcessor.calculate_capital_curve_optimized(
        df, initial_capital
    )
    
    print(f"  初始资金: ¥{initial_capital:,.2f}")
    for i, capital in enumerate(capitals):
        print(f"  交易 {i+1} 后: ¥{capital:,.2f}")
    
    return len(times) == len(trades) and len(capitals) == len(trades)

def test_statistics():
    """测试统计功能"""
    print("\n📊 测试统计功能...")
    
    trades = []
    actions = ['BUY', 'SELL', 'HOLD'] * 10
    for i, action in enumerate(actions):
        trades.append({
            'timestamp': datetime.now() - timedelta(minutes=i),
            'action': action,
            'amount': 1000
        })
    
    df = OptimizedTradeActionProcessor.batch_process_trades(trades)
    stats = OptimizedTradeActionProcessor.get_trade_statistics_optimized(df)
    
    print("  统计结果:")
    for action_type in TradeActionType:
        action_stats = stats.get(action_type.value, {})
        count = action_stats.get('count', 0)
        if count > 0:
            print(f"    {action_stats.get('icon', '❓')} {action_stats.get('display_name', action_type.value)}: {count}")
    
    summary = stats.get('summary', {})
    print(f"  总交易: {summary.get('total_trades', 0)}")
    
    return 'summary' in stats

def test_chart_markers():
    """测试图表标记配置"""
    print("\n🎯 测试图表标记配置...")
    
    markers = OptimizedTradeActionProcessor.get_chart_markers_config()
    
    print(f"  配置了 {len(markers)} 种标记类型:")
    for action_key, config in markers.items():
        print(f"    {action_key}: {config['name']} ({config['color']})")
    
    return len(markers) > 0

def main():
    """主测试函数"""
    print("🚀 交易动作优化器简化测试")
    print("=" * 50)
    
    tests = [
        ("动作标准化", test_action_normalization),
        ("显示格式化", test_display_formatting),
        ("批量处理", test_batch_processing),
        ("资金曲线", test_capital_curve),
        ("统计功能", test_statistics),
        ("图表标记", test_chart_markers)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ 通过" if result else "❌ 失败"
            print(f"\n{status} {test_name}")
        except Exception as e:
            print(f"\n❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
    
    print(f"\n🎯 结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！交易动作优化器工作正常")
    else:
        print("⚠️ 部分测试失败，需要检查")
    
    return passed == total

if __name__ == "__main__":
    main()
