#!/usr/bin/env python3
"""
消息队列系统
实现高性能的异步消息传递和处理
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Union
from datetime import datetime
import json
import uuid
from dataclasses import dataclass, asdict
from enum import Enum
import weakref
from collections import defaultdict

logger = logging.getLogger(__name__)

class MessagePriority(Enum):
    """消息优先级"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Message:
    """消息类"""
    id: str
    topic: str
    payload: Any
    priority: MessagePriority = MessagePriority.NORMAL
    timestamp: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if not self.id:
            self.id = str(uuid.uuid4())

class MessageQueue:
    """消息队列"""
    
    def __init__(self, name: str, max_size: int = 10000):
        """
        初始化消息队列
        
        Args:
            name: 队列名称
            max_size: 最大队列大小
        """
        self.name = name
        self.max_size = max_size
        self.queue = asyncio.PriorityQueue(maxsize=max_size)
        self.dead_letter_queue = asyncio.Queue()
        
        # 统计信息
        self.stats = {
            'total_messages': 0,
            'processed_messages': 0,
            'failed_messages': 0,
            'dead_letter_messages': 0
        }
        
        logger.info(f"消息队列 {name} 初始化完成，最大大小: {max_size}")
    
    async def put(self, message: Message, timeout: float = None) -> bool:
        """
        放入消息
        
        Args:
            message: 消息对象
            timeout: 超时时间
            
        Returns:
            是否成功放入
        """
        try:
            # 使用优先级作为排序键（数值越大优先级越高，但PriorityQueue是最小堆）
            priority_key = -message.priority.value
            
            if timeout:
                await asyncio.wait_for(
                    self.queue.put((priority_key, message.timestamp, message)),
                    timeout=timeout
                )
            else:
                await self.queue.put((priority_key, message.timestamp, message))
            
            self.stats['total_messages'] += 1
            logger.debug(f"消息已放入队列 {self.name}: {message.id}")
            return True
            
        except asyncio.TimeoutError:
            logger.warning(f"消息放入队列 {self.name} 超时: {message.id}")
            return False
        except Exception as e:
            logger.error(f"消息放入队列 {self.name} 失败: {e}")
            return False
    
    async def get(self, timeout: float = None) -> Optional[Message]:
        """
        获取消息
        
        Args:
            timeout: 超时时间
            
        Returns:
            消息对象或None
        """
        try:
            if timeout:
                priority_key, timestamp, message = await asyncio.wait_for(
                    self.queue.get(), timeout=timeout
                )
            else:
                priority_key, timestamp, message = await self.queue.get()
            
            logger.debug(f"从队列 {self.name} 获取消息: {message.id}")
            return message
            
        except asyncio.TimeoutError:
            return None
        except Exception as e:
            logger.error(f"从队列 {self.name} 获取消息失败: {e}")
            return None
    
    async def put_dead_letter(self, message: Message):
        """放入死信队列"""
        await self.dead_letter_queue.put(message)
        self.stats['dead_letter_messages'] += 1
        logger.warning(f"消息进入死信队列: {message.id}")
    
    def size(self) -> int:
        """获取队列大小"""
        return self.queue.qsize()
    
    def is_empty(self) -> bool:
        """检查队列是否为空"""
        return self.queue.empty()
    
    def is_full(self) -> bool:
        """检查队列是否已满"""
        return self.queue.full()

class MessageBroker:
    """消息代理"""
    
    def __init__(self):
        """初始化消息代理"""
        self.queues: Dict[str, MessageQueue] = {}
        self.subscribers: Dict[str, List[Callable]] = defaultdict(list)
        self.running = False
        self.worker_tasks: List[asyncio.Task] = []
        
        logger.info("消息代理初始化完成")
    
    def create_queue(self, name: str, max_size: int = 10000) -> MessageQueue:
        """
        创建消息队列
        
        Args:
            name: 队列名称
            max_size: 最大大小
            
        Returns:
            消息队列对象
        """
        if name in self.queues:
            logger.warning(f"队列 {name} 已存在")
            return self.queues[name]
        
        queue = MessageQueue(name, max_size)
        self.queues[name] = queue
        
        logger.info(f"创建消息队列: {name}")
        return queue
    
    def get_queue(self, name: str) -> Optional[MessageQueue]:
        """获取消息队列"""
        return self.queues.get(name)
    
    async def publish(self, topic: str, payload: Any, 
                     priority: MessagePriority = MessagePriority.NORMAL) -> bool:
        """
        发布消息
        
        Args:
            topic: 主题
            payload: 消息内容
            priority: 优先级
            
        Returns:
            是否发布成功
        """
        message = Message(
            id=str(uuid.uuid4()),
            topic=topic,
            payload=payload,
            priority=priority
        )
        
        # 找到对应的队列
        queue = self.get_queue(topic)
        if not queue:
            # 自动创建队列
            queue = self.create_queue(topic)
        
        success = await queue.put(message)
        if success:
            logger.debug(f"消息发布成功: {topic} - {message.id}")
        
        return success
    
    def subscribe(self, topic: str, callback: Callable):
        """
        订阅主题
        
        Args:
            topic: 主题
            callback: 回调函数
        """
        self.subscribers[topic].append(callback)
        logger.info(f"订阅主题: {topic}")
        
        # 确保队列存在
        if topic not in self.queues:
            self.create_queue(topic)
    
    def unsubscribe(self, topic: str, callback: Callable):
        """取消订阅"""
        if topic in self.subscribers:
            try:
                self.subscribers[topic].remove(callback)
                logger.info(f"取消订阅主题: {topic}")
            except ValueError:
                logger.warning(f"回调函数不在订阅列表中: {topic}")
    
    async def start(self, worker_count: int = 5):
        """
        启动消息代理
        
        Args:
            worker_count: 工作线程数
        """
        if self.running:
            logger.warning("消息代理已在运行")
            return
        
        self.running = True
        logger.info(f"启动消息代理，工作线程数: {worker_count}")
        
        # 为每个队列启动工作线程
        for topic, queue in self.queues.items():
            for i in range(worker_count):
                task = asyncio.create_task(
                    self._worker(topic, queue),
                    name=f"worker-{topic}-{i}"
                )
                self.worker_tasks.append(task)
    
    async def stop(self):
        """停止消息代理"""
        if not self.running:
            return
        
        self.running = False
        logger.info("停止消息代理...")
        
        # 取消所有工作任务
        for task in self.worker_tasks:
            task.cancel()
        
        # 等待任务完成
        if self.worker_tasks:
            await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        
        self.worker_tasks.clear()
        logger.info("消息代理已停止")
    
    async def _worker(self, topic: str, queue: MessageQueue):
        """
        工作线程
        
        Args:
            topic: 主题
            queue: 消息队列
        """
        logger.info(f"工作线程启动: {topic}")
        
        while self.running:
            try:
                # 获取消息
                message = await queue.get(timeout=1.0)
                if not message:
                    continue
                
                # 处理消息
                await self._process_message(topic, message, queue)
                
            except asyncio.CancelledError:
                logger.info(f"工作线程被取消: {topic}")
                break
            except Exception as e:
                logger.error(f"工作线程异常: {topic} - {e}")
                await asyncio.sleep(1)
        
        logger.info(f"工作线程结束: {topic}")
    
    async def _process_message(self, topic: str, message: Message, queue: MessageQueue):
        """
        处理消息
        
        Args:
            topic: 主题
            message: 消息
            queue: 队列
        """
        try:
            # 获取订阅者
            callbacks = self.subscribers.get(topic, [])
            if not callbacks:
                logger.warning(f"主题 {topic} 没有订阅者")
                return
            
            # 调用所有回调函数
            for callback in callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(message)
                    else:
                        callback(message)
                except Exception as e:
                    logger.error(f"回调函数执行失败: {topic} - {e}")
                    
                    # 重试机制
                    message.retry_count += 1
                    if message.retry_count <= message.max_retries:
                        logger.info(f"消息重试: {message.id} ({message.retry_count}/{message.max_retries})")
                        await queue.put(message)
                    else:
                        logger.error(f"消息重试次数超限，进入死信队列: {message.id}")
                        await queue.put_dead_letter(message)
                    return
            
            # 处理成功
            queue.stats['processed_messages'] += 1
            logger.debug(f"消息处理成功: {message.id}")
            
        except Exception as e:
            logger.error(f"消息处理异常: {message.id} - {e}")
            queue.stats['failed_messages'] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = {
            'running': self.running,
            'worker_count': len(self.worker_tasks),
            'queue_count': len(self.queues),
            'subscriber_count': sum(len(subs) for subs in self.subscribers.values()),
            'queues': {}
        }
        
        for name, queue in self.queues.items():
            stats['queues'][name] = {
                'size': queue.size(),
                'is_full': queue.is_full(),
                **queue.stats
            }
        
        return stats


# 全局消息代理实例
message_broker = MessageBroker()

# 便捷函数
async def publish(topic: str, payload: Any, priority: MessagePriority = MessagePriority.NORMAL) -> bool:
    """发布消息的便捷函数"""
    return await message_broker.publish(topic, payload, priority)

def subscribe(topic: str, callback: Callable):
    """订阅主题的便捷函数"""
    message_broker.subscribe(topic, callback)

def unsubscribe(topic: str, callback: Callable):
    """取消订阅的便捷函数"""
    message_broker.unsubscribe(topic, callback)


# 测试函数
async def test_message_queue_system():
    """测试消息队列系统"""
    logger.info("开始测试消息队列系统...")
    
    try:
        # 创建消息代理
        broker = MessageBroker()
        
        # 测试数据
        received_messages = []
        
        # 定义回调函数
        async def test_callback(message: Message):
            received_messages.append(message)
            logger.info(f"收到消息: {message.topic} - {message.payload}")
        
        # 订阅主题
        broker.subscribe("test_topic", test_callback)
        
        # 启动代理
        await broker.start(worker_count=2)
        
        # 发布测试消息
        logger.info("发布测试消息...")
        for i in range(5):
            await broker.publish(
                "test_topic", 
                f"测试消息 {i}",
                MessagePriority.NORMAL if i % 2 == 0 else MessagePriority.HIGH
            )
        
        # 等待消息处理
        await asyncio.sleep(2)
        
        # 检查结果
        logger.info(f"✅ 收到 {len(received_messages)} 条消息")
        
        # 显示统计信息
        stats = broker.get_stats()
        logger.info(f"📊 代理统计: {stats}")
        
        # 停止代理
        await broker.stop()
        
        logger.info("✅ 消息队列系统测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 消息队列系统测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_message_queue_system())
