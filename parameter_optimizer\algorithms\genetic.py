#!/usr/bin/env python3
"""
遗传算法参数优化器
实现完整的遗传算法逻辑，包括选择、交叉、变异等操作
"""

import asyncio
import random
import numpy as np
import logging
from typing import Dict, List, Tuple, Callable, Optional
from dataclasses import dataclass
import copy

logger = logging.getLogger(__name__)

@dataclass
class Individual:
    """个体类"""
    genes: Dict[str, float]  # 基因（参数）
    fitness: float = -999.0  # 适应度
    age: int = 0  # 年龄
    
    def __post_init__(self):
        """初始化后处理"""
        if self.fitness == -999.0:
            self.fitness = random.uniform(0, 1)  # 随机初始适应度

class GeneticAlgorithm:
    """遗传算法优化器"""
    
    def __init__(self, 
                 param_space: Dict[str, Tuple[float, float, float]],
                 population_size: int = 50,
                 generations: int = 30,
                 mutation_rate: float = 0.1,
                 crossover_rate: float = 0.8,
                 elite_ratio: float = 0.1,
                 tournament_size: int = 3):
        """
        初始化遗传算法
        
        Args:
            param_space: 参数空间 {param_name: (min_val, max_val, step)}
            population_size: 种群大小
            generations: 进化代数
            mutation_rate: 变异率
            crossover_rate: 交叉率
            elite_ratio: 精英保留比例
            tournament_size: 锦标赛选择大小
        """
        self.param_space = param_space
        self.population_size = population_size
        self.generations = generations
        self.mutation_rate = mutation_rate
        self.crossover_rate = crossover_rate
        self.elite_ratio = elite_ratio
        self.tournament_size = tournament_size
        
        # 运行时数据
        self.population: List[Individual] = []
        self.best_individual: Optional[Individual] = None
        self.fitness_history: List[float] = []
        self.diversity_history: List[float] = []
        
        logger.info(f"遗传算法初始化: 种群={population_size}, 代数={generations}")
    
    def generate_random_individual(self) -> Individual:
        """生成随机个体"""
        genes = {}
        for param_name, (min_val, max_val, step) in self.param_space.items():
            # 在参数范围内生成随机值
            if step > 0:
                # 离散参数
                num_steps = int((max_val - min_val) / step) + 1
                random_step = random.randint(0, num_steps - 1)
                value = min_val + random_step * step
            else:
                # 连续参数
                value = random.uniform(min_val, max_val)
            
            genes[param_name] = value
        
        return Individual(genes=genes)
    
    def initialize_population(self) -> List[Individual]:
        """初始化种群"""
        logger.info("初始化种群...")
        
        population = []
        for i in range(self.population_size):
            individual = self.generate_random_individual()
            population.append(individual)
        
        logger.info(f"种群初始化完成: {len(population)} 个个体")
        return population
    
    def tournament_selection(self, population: List[Individual]) -> Individual:
        """锦标赛选择"""
        tournament = random.sample(population, min(self.tournament_size, len(population)))
        return max(tournament, key=lambda x: x.fitness)
    
    def roulette_wheel_selection(self, population: List[Individual]) -> Individual:
        """轮盘赌选择"""
        # 确保所有适应度为正数
        min_fitness = min(ind.fitness for ind in population)
        if min_fitness < 0:
            adjusted_fitness = [ind.fitness - min_fitness + 1 for ind in population]
        else:
            adjusted_fitness = [ind.fitness for ind in population]
        
        total_fitness = sum(adjusted_fitness)
        if total_fitness == 0:
            return random.choice(population)
        
        # 轮盘赌选择
        pick = random.uniform(0, total_fitness)
        current = 0
        for i, fitness in enumerate(adjusted_fitness):
            current += fitness
            if current >= pick:
                return population[i]
        
        return population[-1]  # 备选
    
    def uniform_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """均匀交叉"""
        child1_genes = {}
        child2_genes = {}
        
        for param_name in parent1.genes.keys():
            if random.random() < 0.5:
                child1_genes[param_name] = parent1.genes[param_name]
                child2_genes[param_name] = parent2.genes[param_name]
            else:
                child1_genes[param_name] = parent2.genes[param_name]
                child2_genes[param_name] = parent1.genes[param_name]
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    def arithmetic_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:
        """算术交叉"""
        alpha = random.uniform(0, 1)
        
        child1_genes = {}
        child2_genes = {}
        
        for param_name in parent1.genes.keys():
            p1_val = parent1.genes[param_name]
            p2_val = parent2.genes[param_name]
            
            child1_genes[param_name] = alpha * p1_val + (1 - alpha) * p2_val
            child2_genes[param_name] = (1 - alpha) * p1_val + alpha * p2_val
            
            # 确保在参数范围内
            min_val, max_val, _ = self.param_space[param_name]
            child1_genes[param_name] = np.clip(child1_genes[param_name], min_val, max_val)
            child2_genes[param_name] = np.clip(child2_genes[param_name], min_val, max_val)
        
        child1 = Individual(genes=child1_genes)
        child2 = Individual(genes=child2_genes)
        
        return child1, child2
    
    def gaussian_mutation(self, individual: Individual) -> Individual:
        """高斯变异"""
        mutated_genes = copy.deepcopy(individual.genes)
        
        for param_name, value in mutated_genes.items():
            if random.random() < self.mutation_rate:
                min_val, max_val, step = self.param_space[param_name]
                
                # 高斯变异
                sigma = (max_val - min_val) * 0.1  # 标准差为范围的10%
                mutation = random.gauss(0, sigma)
                new_value = value + mutation
                
                # 边界处理
                new_value = np.clip(new_value, min_val, max_val)
                
                # 如果是离散参数，四舍五入到最近的有效值
                if step > 0:
                    steps_from_min = round((new_value - min_val) / step)
                    new_value = min_val + steps_from_min * step
                    new_value = np.clip(new_value, min_val, max_val)
                
                mutated_genes[param_name] = new_value
        
        return Individual(genes=mutated_genes)
    
    def calculate_population_diversity(self, population: List[Individual]) -> float:
        """计算种群多样性"""
        if len(population) < 2:
            return 0.0
        
        total_distance = 0.0
        count = 0
        
        for i in range(len(population)):
            for j in range(i + 1, len(population)):
                distance = 0.0
                for param_name in population[i].genes.keys():
                    min_val, max_val, _ = self.param_space[param_name]
                    param_range = max_val - min_val
                    if param_range > 0:
                        normalized_diff = abs(population[i].genes[param_name] - 
                                            population[j].genes[param_name]) / param_range
                        distance += normalized_diff ** 2
                
                total_distance += np.sqrt(distance)
                count += 1
        
        return total_distance / count if count > 0 else 0.0
    
    async def optimize(self, symbol: str, days: int, evaluate_func: Callable) -> List[Dict]:
        """
        执行遗传算法优化
        
        Args:
            symbol: 交易标的
            days: 回测天数
            evaluate_func: 评估函数
            
        Returns:
            优化结果列表
        """
        logger.info(f"开始遗传算法优化: {symbol}, 回测天数: {days}")
        
        # 初始化种群
        self.population = self.initialize_population()
        
        # 评估初始种群
        await self._evaluate_population(self.population, symbol, days, evaluate_func)
        
        # 记录初始最优个体
        self.best_individual = max(self.population, key=lambda x: x.fitness)
        self.fitness_history.append(self.best_individual.fitness)
        self.diversity_history.append(self.calculate_population_diversity(self.population))
        
        logger.info(f"初始最优适应度: {self.best_individual.fitness:.4f}")
        
        # 进化循环
        for generation in range(self.generations):
            logger.info(f"第 {generation + 1}/{self.generations} 代进化...")
            
            # 创建新一代
            new_population = await self._create_next_generation()
            
            # 评估新一代
            await self._evaluate_population(new_population, symbol, days, evaluate_func)
            
            # 更新种群
            self.population = new_population
            
            # 更新最优个体
            current_best = max(self.population, key=lambda x: x.fitness)
            if current_best.fitness > self.best_individual.fitness:
                self.best_individual = current_best
                logger.info(f"发现更优个体: 适应度 {current_best.fitness:.4f}")
            
            # 记录历史
            self.fitness_history.append(self.best_individual.fitness)
            diversity = self.calculate_population_diversity(self.population)
            self.diversity_history.append(diversity)
            
            # 显示进度
            avg_fitness = np.mean([ind.fitness for ind in self.population])
            logger.info(f"第{generation + 1}代: 最优={self.best_individual.fitness:.4f}, "
                       f"平均={avg_fitness:.4f}, 多样性={diversity:.4f}")
        
        # 返回最优结果
        results = self._format_results()
        logger.info(f"遗传算法优化完成，最优适应度: {self.best_individual.fitness:.4f}")
        
        return results
    
    async def _create_next_generation(self) -> List[Individual]:
        """创建下一代种群"""
        new_population = []
        
        # 精英保留
        elite_count = int(self.population_size * self.elite_ratio)
        elites = sorted(self.population, key=lambda x: x.fitness, reverse=True)[:elite_count]
        new_population.extend(copy.deepcopy(elites))
        
        # 生成剩余个体
        while len(new_population) < self.population_size:
            # 选择父母
            parent1 = self.tournament_selection(self.population)
            parent2 = self.tournament_selection(self.population)
            
            # 交叉
            if random.random() < self.crossover_rate:
                child1, child2 = self.arithmetic_crossover(parent1, parent2)
            else:
                child1, child2 = copy.deepcopy(parent1), copy.deepcopy(parent2)
            
            # 变异
            child1 = self.gaussian_mutation(child1)
            child2 = self.gaussian_mutation(child2)
            
            # 添加到新种群
            if len(new_population) < self.population_size:
                new_population.append(child1)
            if len(new_population) < self.population_size:
                new_population.append(child2)
        
        return new_population[:self.population_size]
    
    async def _evaluate_population(self, population: List[Individual], 
                                 symbol: str, days: int, evaluate_func: Callable):
        """评估种群"""
        # 并发评估（限制并发数）
        semaphore = asyncio.Semaphore(5)
        
        async def evaluate_individual(individual: Individual):
            async with semaphore:
                try:
                    metrics = await evaluate_func(individual.genes, symbol, days)
                    individual.fitness = metrics.get('fitness', -999.0)
                except Exception as e:
                    logger.error(f"评估个体失败: {e}")
                    individual.fitness = -999.0
        
        # 并发执行评估
        tasks = [evaluate_individual(ind) for ind in population]
        await asyncio.gather(*tasks)
    
    def _format_results(self) -> List[Dict]:
        """格式化结果"""
        # 按适应度排序
        sorted_population = sorted(self.population, key=lambda x: x.fitness, reverse=True)
        
        results = []
        for i, individual in enumerate(sorted_population[:10]):  # 返回前10个
            result = {
                'config': individual.genes,
                'metrics': {
                    'fitness': individual.fitness,
                    'rank': i + 1
                }
            }
            results.append(result)
        
        return results
    
    def get_optimization_summary(self) -> Dict:
        """获取优化摘要"""
        if not self.population:
            return {'status': 'not_started'}
        
        valid_individuals = [ind for ind in self.population if ind.fitness > -999]
        
        return {
            'status': 'completed',
            'generations': len(self.fitness_history),
            'population_size': len(self.population),
            'valid_count': len(valid_individuals),
            'success_rate': len(valid_individuals) / len(self.population),
            'best_fitness': self.best_individual.fitness if self.best_individual else -999,
            'fitness_improvement': (self.fitness_history[-1] - self.fitness_history[0]) if len(self.fitness_history) > 1 else 0,
            'final_diversity': self.diversity_history[-1] if self.diversity_history else 0,
            'convergence_rate': self._calculate_convergence_rate()
        }
    
    def _calculate_convergence_rate(self) -> float:
        """计算收敛率"""
        if len(self.fitness_history) < 5:
            return 0.0
        
        # 计算最后5代的适应度变化
        recent_history = self.fitness_history[-5:]
        if max(recent_history) - min(recent_history) < 0.001:
            return 1.0  # 已收敛
        
        # 计算收敛趋势
        improvements = 0
        for i in range(1, len(self.fitness_history)):
            if self.fitness_history[i] > self.fitness_history[i-1]:
                improvements += 1
        
        return improvements / (len(self.fitness_history) - 1) if len(self.fitness_history) > 1 else 0.0


# 测试函数
async def test_genetic_algorithm():
    """测试遗传算法"""
    logger.info("开始测试遗传算法...")
    
    # 定义测试参数空间
    param_space = {
        'buy_trigger_drop': (-0.010, -0.005, 0.001),
        'profit_target': (0.003, 0.008, 0.001),
        'stop_loss': (-0.025, -0.015, 0.002)
    }
    
    # 创建遗传算法实例
    ga = GeneticAlgorithm(
        param_space=param_space,
        population_size=20,
        generations=10,
        mutation_rate=0.15,
        crossover_rate=0.8
    )
    
    # 模拟评估函数
    async def mock_evaluate_func(config: Dict, symbol: str, days: int) -> Dict:
        """模拟评估函数"""
        await asyncio.sleep(0.01)  # 模拟计算时间
        
        # 复杂的适应度计算
        fitness = (
            abs(config['buy_trigger_drop']) * 20 +
            config['profit_target'] * 150 +
            abs(config['stop_loss']) * 8 +
            np.random.normal(0, 0.05)  # 添加噪声
        )
        
        return {
            'fitness': fitness,
            'total_return': fitness * 0.01,
            'max_drawdown': -fitness * 0.003,
            'sharpe_ratio': fitness * 0.15
        }
    
    try:
        # 执行优化
        results = await ga.optimize("TEST", 30, mock_evaluate_func)
        
        logger.info(f"✅ 遗传算法完成，获得 {len(results)} 个结果")
        
        if results:
            best_result = results[0]
            logger.info(f"最优适应度: {best_result['metrics']['fitness']:.4f}")
            logger.info(f"最优配置: {best_result['config']}")
        
        # 获取优化摘要
        summary = ga.get_optimization_summary()
        logger.info(f"优化摘要: 成功率 {summary['success_rate']:.2%}, "
                   f"适应度提升 {summary['fitness_improvement']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 遗传算法测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_genetic_algorithm())
