#!/usr/bin/env python3
"""
数据采集页面
实时获取ETF价格数据并存储到本地数据库
"""

import streamlit as st
import sys
import os
from pathlib import Path
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import threading
import subprocess
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 添加路径
current_dir = Path(__file__).parent.parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# 导入组件
from components.sidebar import render_sidebar, render_page_header, render_status_indicators
from utils.session_state import init_session_state, update_status, auto_refresh_session
from utils.database import get_tick_data, get_data_stats
from utils.data_collector import start_data_collection, stop_data_collection, get_collection_status, get_recent_data
from config.app_config import get_config

# 页面配置
st.set_page_config(
    page_title="数据采集 - ETF套利系统", 
    page_icon="📊",
    layout="wide"
)

def sync_collection_status_to_session():
    """同步数据采集真实状态到session state"""
    real_status = get_collection_status()
    
    # 更新session state中的数据采集状态
    session_status = {
        'is_running': real_status.get('is_running', False),
        'total_records': real_status.get('total_collected', 0),
        'symbols': real_status.get('symbols', []),
        'last_update': real_status.get('last_update'),
        'error_count': real_status.get('error_count', 0)
    }
    
    st.session_state.data_collection_status = session_status
    return real_status

@auto_refresh_session
def main():
    """数据采集主页面"""
    
    # 初始化
    init_session_state()
    
    # 添加调试日志
    logger.info("=== 数据采集页面开始渲染 ===")
    logger.info(f"当前session_state keys: {list(st.session_state.keys())}")
    
    # 同步真实状态到session state（保持状态一致性）
    sync_collection_status_to_session()
    
    render_sidebar()
    render_page_header("数据采集", "实时ETF价格数据获取和存储管理", "📊")
    
    # 实现自动刷新逻辑
    auto_refresh = st.session_state.get('user_settings', {}).get('auto_refresh', True)
    refresh_interval = st.session_state.get('user_settings', {}).get('refresh_interval', 10)
    
    if auto_refresh:
        st.experimental_rerun_interval = refresh_interval
        st.markdown(f"<small>自动刷新已启用，间隔: {refresh_interval}秒</small>", unsafe_allow_html=True)
    
    # 获取当前标的
    symbol = st.session_state.get('current_symbol', '159740')
    symbol_name = get_config('symbols', {}).get(symbol, {}).get('name', symbol)
    
    # 获取所有可用标的
    available_symbols = get_config('symbols', {})
    
    # 多股票采集设置
    st.subheader("📈 股票选择")
    
    # 添加采集模式选择
    col1, col2 = st.columns(2)
    with col1:
        collection_mode = st.radio(
            "采集模式",
            ["单股票采集", "多股票采集"],
            index=0,
            key="collection_mode",
            help="选择采集单个股票还是多个股票"
        )
    
    with col2:
        logger.info(f"采集模式: {collection_mode}")
        
        if collection_mode == "单股票采集":
            # 单股票选择
            selected_symbols = [st.selectbox(
                "选择股票",
                options=list(available_symbols.keys()),
                index=list(available_symbols.keys()).index(symbol) if symbol in available_symbols else 0,
                format_func=lambda x: f"{x} - {available_symbols[x].get('name', x)}",
                key="single_symbol_select"
            )]
            logger.info(f"单股票模式选择: {selected_symbols}")
        else:
            # 多股票选择 - 使用multiselect避免session_state冲突
            st.markdown("**选择要采集的股票:**")
            logger.info("进入多股票选择模式")
            
            # 准备选项和默认值
            symbol_options = list(available_symbols.keys())
            symbol_labels = [f"{code} - {available_symbols[code].get('name', code)}" for code in symbol_options]
            
            # 获取默认选中的股票
            default_selected = st.session_state.get('multi_selected_symbols', [symbol])
            
            # 确保默认选中的股票在可用选项中
            default_selected = [s for s in default_selected if s in symbol_options]
            if not default_selected:
                default_selected = [symbol]  # 至少选中当前股票
            
            logger.debug(f"默认选中的股票: {default_selected}")
            
            # 使用multiselect组件
            selected_symbols = st.multiselect(
                "选择股票（可多选）",
                options=symbol_options,
                default=default_selected,
                format_func=lambda x: f"{x} - {available_symbols[x].get('name', x)}",
                key="multi_stock_selector",
                help="可以选择多只股票同时进行数据采集"
            )
            
            logger.debug(f"Multiselect选中的股票: {selected_symbols}")
            
            # 保存选择到session_state
            st.session_state.multi_selected_symbols = selected_symbols
            
            # 快速选择按钮
            st.markdown("**快速选择:**")
            col_a, col_b, col_c = st.columns(3)
            
            with col_a:
                if st.button("全选", key="select_all", help="选择所有股票"):
                    logger.info("全选按钮被点击")
                    st.session_state.multi_selected_symbols = symbol_options
                    st.rerun()
            
            with col_b:
                if st.button("全不选", key="select_none", help="取消选择所有股票"):
                    logger.info("全不选按钮被点击")
                    st.session_state.multi_selected_symbols = []
                    st.rerun()
            
            with col_c:
                if st.button("反选", key="select_inverse", help="反转当前选择"):
                    logger.info("反选按钮被点击")
                    current_selected = set(st.session_state.get('multi_selected_symbols', []))
                    all_symbols = set(symbol_options)
                    new_selected = list(all_symbols - current_selected)
                    st.session_state.multi_selected_symbols = new_selected
                    logger.debug(f"反选结果: {current_selected} -> {new_selected}")
                    st.rerun()
    
    # 显示选中的股票信息
    logger.info(f"最终选中的股票: {selected_symbols}")
    
    if selected_symbols:
        # 构建股票显示字符串，避免f-string中的反斜杠
        symbol_display = []
        for s in selected_symbols:
            name = available_symbols[s].get("name", s)
            symbol_display.append(f"{s}({name})")
        
        display_text = ", ".join(symbol_display)
        st.info(f"已选择 {len(selected_symbols)} 只股票: {display_text}")
        
        # 保存选择到session state
        st.session_state.selected_symbols = selected_symbols
        logger.info(f"保存到session_state.selected_symbols: {selected_symbols}")
    else:
        st.warning("请至少选择一只股票进行采集")
        selected_symbols = [symbol]  # 默认选择当前股票
        logger.warning(f"无选择，使用默认股票: {selected_symbols}")
    
    st.markdown("---")
    
    # 数据采集控制面板
    st.subheader("🎛️ 数据采集控制")
    
    # 获取当前采集状态
    collection_status = get_collection_status()
    is_running = collection_status.get('is_running', False)
    running_symbols = collection_status.get('symbols', [])
    
    col1, col2, col3 = st.columns([2, 2, 3])
    
    with col1:
        if collection_mode == "单股票采集":
            st.markdown(f"**当前标的**: {selected_symbols[0]} - {available_symbols[selected_symbols[0]].get('name', selected_symbols[0])}")
        else:
            st.markdown(f"**选中标的**: {len(selected_symbols)} 只")
            if len(selected_symbols) <= 3:
                for sym in selected_symbols:
                    st.markdown(f"  - {sym} ({available_symbols[sym].get('name', sym)})")
            else:
                st.markdown(f"  - {', '.join(selected_symbols[:2])} 等{len(selected_symbols)}只")
        
        # 数据采集状态
        status_color = "🟢" if is_running else "🔴"
        status_text = "运行中" if is_running else "已停止"
        st.markdown(f"**采集状态**: {status_color} {status_text}")
        
        # 显示当前运行的股票（如果与选中的不同）
        if is_running and running_symbols:
            if set(running_symbols) != set(selected_symbols):
                st.warning(f"⚠️ 当前运行中的股票: {', '.join(running_symbols)}")
        
        if collection_status.get('last_update'):
            st.markdown(f"**最后更新**: {collection_status['last_update'][:19].replace('T', ' ')}")
    
    with col2:
        # 采集参数设置
        st.markdown("**采集参数**")
        
        fetch_interval = st.number_input(
            "采集间隔(秒)",
            min_value=1,
            max_value=60,
            value=get_config('data_collection.fetch_interval', 5),
            key="fetch_interval",
            help="所有股票都会使用相同的采集间隔"
        )
        
        batch_size = st.number_input(
            "批次大小",
            min_value=10,
            max_value=1000,
            value=get_config('data_collection.batch_size', 100),
            key="batch_size",
            help="每次采集的数据条数（每只股票）"
        )
        
        # 多股票模式下的额外设置
        if collection_mode == "多股票采集":
            max_concurrent = st.number_input(
                "最大并发数",
                min_value=1,
                max_value=10,
                value=len(selected_symbols),
                key="max_concurrent",
                help="同时采集的股票数量"
            )
    
    with col3:
        st.markdown("**操作控制**")
        
        # 检查是否可以启动采集
        can_start = not is_running and len(selected_symbols) > 0
        stop_current_first = is_running and set(running_symbols) != set(selected_symbols)
        
        col3_1, col3_2 = st.columns(2)
        
        with col3_1:
            start_button_text = "🚀 启动采集" if collection_mode == "单股票采集" else f"🚀 启动采集({len(selected_symbols)}只)"
            
            if stop_current_first:
                st.warning("⚠️ 请先停止当前采集")
            
            if st.button(start_button_text, width='stretch', disabled=not can_start):
                logger.info(f"=== 启动数据采集按钮被点击 ===")
                logger.info(f"采集模式: {collection_mode}")
                logger.info(f"选中股票: {selected_symbols}")
                logger.info(f"采集间隔: {fetch_interval}")
                logger.info(f"批次大小: {batch_size}")
                
                # 显示启动确认信息
                with st.spinner(f"启动{collection_mode}..."):
                    logger.info("开始调用start_data_collection函数")
                    success = start_data_collection(
                        symbols=selected_symbols, 
                        interval=fetch_interval, 
                        batch_size=batch_size,
                        use_real_data=True
                    )
                    logger.info(f"start_data_collection返回结果: {success}")
                    
                    if success:
                        st.success(f"数据采集已启动！正在采集 {len(selected_symbols)} 只股票")
                        # 更新session state中的状态
                        update_status('data_collection', {'is_running': True, 'symbols': selected_symbols})
                        logger.info("更新session state状态完成")
                        time.sleep(1)  # 短暂延迟确保状态更新
                        logger.info("准备执行st.rerun()")
                        st.rerun()
                    else:
                        st.error("启动数据采集失败")
                        logger.error("数据采集启动失败")
                
        with col3_2:
            if st.button("⏹️ 停止采集", width='stretch', disabled=not is_running):
                with st.spinner("停止数据采集..."):
                    success = stop_data_collection()
                    if success:
                        st.info("数据采集已停止")
                        # 更新session state中的状态
                        update_status('data_collection', {'is_running': False})
                        time.sleep(1)  # 短暂延迟确保状态更新
                        st.rerun()
                    else:
                        st.error("停止数据采集失败")
    
    st.markdown("---")
    
    # 数据统计概览
    st.subheader("📈 数据统计")
    
    # 获取真实的采集状态
    collection_status = get_collection_status()
    symbol_stats = collection_status.get('symbol_stats', {})
    running_symbols = collection_status.get('symbols', [])
    
    if collection_mode == "单股票采集" or len(selected_symbols) == 1:
        # 单股票模式的统计显示
        current_symbol = selected_symbols[0]
        stats = get_data_stats(current_symbol)
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_records = stats.get('total_records', 0)
            # 显示实际记录数，而不是采集器计数
            actual_delta = symbol_stats.get(current_symbol, {}).get('total_collected', 0) if is_running else None
            st.metric(
                label="总记录数",
                value=f"{total_records:,}",
                delta=f"运行中 +{actual_delta}" if actual_delta and actual_delta > 0 else None,
                help=f"{current_symbol} 数据库中的实际记录总数"
            )
        
        with col2:
            latest_price = stats.get('latest_price', 0)
            latest_time = stats.get('end_time', None)  # 最新数据时间
            
            # 格式化最新时间显示
            time_display = None
            if latest_time:
                try:
                    dt = datetime.fromisoformat(latest_time.replace('Z', '+00:00'))
                    time_display = f"更新于 {dt.strftime('%H:%M:%S')}"
                except:
                    time_display = "最新数据"
            
            st.metric(
                label="最新价格",
                value=f"¥{latest_price:.3f}" if latest_price else "无数据",
                delta=time_display if is_running and latest_time else ("实时更新" if is_running else None),
                help=f"最新价格更新时间: {latest_time}" if latest_time else "暂无价格数据"
            )
        
        with col3:
            data_quality = stats.get('data_quality', 'unknown')
            quality_map = {'good': '优良', 'limited': '有限', 'poor': '较差', 'unknown': '未知'}
            
            st.metric(
                label="数据质量",
                value=quality_map.get(data_quality, '未知'),
                delta=f"基于{total_records:,}条记录" if total_records > 0 else None,
                help="数据质量评估: 1000+条=优良, 100-1000条=有限, <100条=较差"
            )
        
        with col4:
            error_count = symbol_stats.get(current_symbol, {}).get('error_count', 0) if symbol_stats else collection_status.get('error_count', 0)
            st.metric(
                label="错误次数",
                value=error_count,
                delta="需检查" if error_count > 5 else ("正常" if error_count == 0 else None),
                delta_color="inverse" if error_count > 5 else "normal"
            )
    
    else:
        # 多股票模式的统计显示
        st.markdown("### 📊 多股票统计总览")
        
        # 汇总统计
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            # 计算所有选中股票的总记录数
            total_all_records = 0
            for symbol in selected_symbols:
                stats = get_data_stats(symbol)
                total_all_records += stats.get('total_records', 0)
            
            st.metric(
                label="总记录数",
                value=f"{total_all_records:,}",
                help=f"所有 {len(selected_symbols)} 只股票的记录总数"
            )
        
        with col2:
            # 运行中的股票数量
            running_count = len(running_symbols) if is_running else 0
            st.metric(
                label="运行中股票",
                value=f"{running_count}/{len(selected_symbols)}",
                delta="运行中" if running_count > 0 else None,
                help=f"当前正在采集的股票数量"
            )
        
        with col3:
            # 平均数据质量
            quality_scores = []
            for symbol in selected_symbols:
                stats = get_data_stats(symbol)
                score_map = {'good': 3, 'limited': 2, 'poor': 1, 'unknown': 0}
                quality_scores.append(score_map.get(stats.get('data_quality', 'unknown'), 0))
            
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            quality_text = "优良" if avg_quality >= 2.5 else ("中等" if avg_quality >= 1.5 else "较差")
            
            st.metric(
                label="平均质量",
                value=quality_text,
                help=f"基于 {len(selected_symbols)} 只股票的平均数据质量"
            )
        
        with col4:
            # 总错误次数
            total_errors = sum(stats.get('error_count', 0) for stats in symbol_stats.values()) if symbol_stats else 0
            st.metric(
                label="总错误次数",
                value=total_errors,
                delta="需检查" if total_errors > 10 else ("正常" if total_errors == 0 else None),
                delta_color="inverse" if total_errors > 10 else "normal"
            )
        
        # 详细的每个股票统计
        if len(selected_symbols) <= 6:  # 只有股票数量不太多时才显示详细统计
            st.markdown("### 📋 各股票详细统计")
            
            # 创建动态列数（最多3列）
            cols_per_row = min(3, len(selected_symbols))
            
            for i in range(0, len(selected_symbols), cols_per_row):
                cols = st.columns(cols_per_row)
                
                for j, symbol in enumerate(selected_symbols[i:i+cols_per_row]):
                    with cols[j]:
                        # 获取该股票的统计数据
                        stats = get_data_stats(symbol)
                        symbol_stat = symbol_stats.get(symbol, {})
                        symbol_name = available_symbols.get(symbol, {}).get('name', symbol)
                        
                        # 创建股票卡片
                        with st.container():
                            st.markdown(f"**{symbol} - {symbol_name}**")
                            
                            # 运行状态指示器
                            if symbol in running_symbols:
                                st.success("🟢 采集中")
                                last_update = symbol_stat.get('last_update', '')
                                if last_update:
                                    st.caption(f"最后更新: {last_update[:19].replace('T', ' ')}")
                            else:
                                st.info("🔵 未运行")
                            
                            # 统计信息
                            records = stats.get('total_records', 0)
                            latest_price = stats.get('latest_price', 0)
                            
                            st.metric(
                                "记录数", 
                                f"{records:,}",
                                help=f"{symbol} 的历史记录总数"
                            )
                            
                            if latest_price:
                                st.metric(
                                    "最新价格", 
                                    f"¥{latest_price:.3f}",
                                    help=f"{symbol} 的最新价格"
                                )
                            
                            # 采集统计（如果正在运行）
                            if symbol in running_symbols and symbol_stat:
                                collected = symbol_stat.get('total_collected', 0)
                                duplicates = symbol_stat.get('total_duplicates', 0)
                                if collected > 0 or duplicates > 0:
                                    st.caption(f"本次: +{collected}条, 重复{duplicates}条")
        else:
            st.info(f"选择了 {len(selected_symbols)} 只股票，过多无法显示详细统计。请使用单股票模式查看详细信息。")
    
    # 数据时间范围显示（仅在单股票模式下显示）
    if collection_mode == "单股票采集" or len(selected_symbols) == 1:
        current_symbol = selected_symbols[0]
        stats = get_data_stats(current_symbol)
        if stats.get('start_time') and stats.get('end_time'):
            col1, col2 = st.columns(2)
            with col1:
                st.info(f"📅 **数据起始时间**: {stats['start_time']}")
            with col2:
                st.info(f"📅 **数据结束时间**: {stats['end_time']}")
    
    st.markdown("---")
    
    # 实时数据图表
    st.subheader("📊 实时数据图表")
    
    # 图表显示逻辑
    if collection_mode == "单股票采集" or len(selected_symbols) == 1:
        # 单股票图表
        current_symbol = selected_symbols[0]
        symbol_name = available_symbols.get(current_symbol, {}).get('name', current_symbol)
        
        # 添加采集状态提示
        if is_running and current_symbol in running_symbols:
            symbol_stat = symbol_stats.get(current_symbol, {})
            last_update = symbol_stat.get('last_update', collection_status.get('last_update', '未知'))
            st.info(f"🔄 {symbol_name} 数据采集运行中，最后更新: {last_update[:19].replace('T', ' ') if last_update != '未知' else '未知'}")
        
        # 显示单个股票的图表
        _display_single_symbol_chart(current_symbol, symbol_name, is_running, collection_status, symbol_stats)
        
    else:
        # 多股票图表选择
        st.markdown("### 📈 选择要查看的股票图表")
        
        # 创建选项卡用于切换不同股票
        if len(selected_symbols) <= 5:
            # 使用选项卡（适合少数股票）
            tab_names = [f"{sym}({available_symbols.get(sym, {}).get('name', sym)})" for sym in selected_symbols]
            tabs = st.tabs(tab_names)
            
            for i, (tab, symbol) in enumerate(zip(tabs, selected_symbols)):
                with tab:
                    symbol_name = available_symbols.get(symbol, {}).get('name', symbol)
                    
                    # 显示该股票的运行状态
                    if symbol in running_symbols:
                        symbol_stat = symbol_stats.get(symbol, {})
                        last_update = symbol_stat.get('last_update', '未知')
                        st.info(f"🟢 {symbol_name} 正在采集中，最后更新: {last_update[:19].replace('T', ' ') if last_update != '未知' else '未知'}")
                    else:
                        st.info(f"🔴 {symbol_name} 未运行")
                    
                    _display_single_symbol_chart(symbol, symbol_name, symbol in running_symbols, collection_status, symbol_stats)
        
        else:
            # 使用下拉选择（适合多股票）
            chart_symbol = st.selectbox(
                "选择要显示图表的股票",
                options=selected_symbols,
                format_func=lambda x: f"{x} - {available_symbols.get(x, {}).get('name', x)}",
                key="chart_symbol_select"
            )
            
            if chart_symbol:
                symbol_name = available_symbols.get(chart_symbol, {}).get('name', chart_symbol)
                
                # 显示运行状态
                if chart_symbol in running_symbols:
                    symbol_stat = symbol_stats.get(chart_symbol, {})
                    last_update = symbol_stat.get('last_update', '未知')
                    st.success(f"🟢 {symbol_name} 正在采集中，最后更新: {last_update[:19].replace('T', ' ') if last_update != '未知' else '未知'}")
                else:
                    st.info(f"🔴 {symbol_name} 未运行")
                
                _display_single_symbol_chart(chart_symbol, symbol_name, chart_symbol in running_symbols, collection_status, symbol_stats)
        
        # 多股票汇总图表（可选）
        if len(selected_symbols) <= 3 and st.checkbox("显示多股票对比图表", key="show_multi_chart"):
            st.markdown("### 📊 多股票价格对比")
            _display_multi_symbol_chart(selected_symbols, available_symbols)


def _display_single_symbol_chart(symbol: str, symbol_name: str, is_running: bool, collection_status: dict, symbol_stats: dict):
    """显示单个股票的图表"""
    
    # 加载最近数据
    recent_data_result = get_recent_data(symbol, limit=200)
    
    if recent_data_result['success'] and recent_data_result['count'] > 0:
        # 转换为DataFrame
        data = recent_data_result['data']
        df = pd.DataFrame(data)
        df['time'] = pd.to_datetime(df['time'])
        df = df.sort_values('time')
        
        # 数据新鲜度检查
        latest_data_time = df['time'].max()
        current_time = datetime.now()
        data_age_minutes = (current_time - latest_data_time).total_seconds() / 60
        
        # 显示数据状态
        data_status_col1, data_status_col2, data_status_col3 = st.columns(3)
        with data_status_col1:
            if data_age_minutes < 5:
                st.success(f"✅ 数据新鲜 ({data_age_minutes:.1f}分钟前)")
            elif data_age_minutes < 30:
                st.warning(f"⚠️ 数据稍旧 ({data_age_minutes:.1f}分钟前)")
            else:
                st.error(f"❌ 数据过期 ({data_age_minutes:.1f}分钟前)")
        
        with data_status_col2:
            st.info(f"📊 共 {len(df)} 个数据点")
        
        with data_status_col3:
            st.info(f"📈 最新价格: ¥{df['price'].iloc[-1]:.3f}")
        
        # 创建价格走势图
        fig = go.Figure()
        
        # 根据数据新鲜度设置颜色
        line_color = 'blue' if data_age_minutes < 5 else ('orange' if data_age_minutes < 30 else 'red')
        
        fig.add_trace(go.Scatter(
            x=df['time'],
            y=df['price'],
            mode='lines+markers',
            name=f'{symbol_name}价格',
            line=dict(color=line_color, width=2),
            marker=dict(size=4),
            hovertemplate=f'{symbol_name}<br>时间: %{{x}}<br>价格: ¥%{{y:.3f}}<extra></extra>'
        ))
        
        fig.update_layout(
            title=f"{symbol} {symbol_name} 实时价格走势 {'(实时)' if is_running and data_age_minutes < 5 else '(历史)'}",
            xaxis_title="时间",
            yaxis_title="价格 (¥)",
            height=400,
            showlegend=True,
            xaxis=dict(
                tickformat='%H:%M:%S',
                showgrid=True
            ),
            yaxis=dict(
                showgrid=True,
                tickformat='.3f'
            )
        )
        st.plotly_chart(fig, width='stretch')
        
        # 交易量和统计信息
        col1, col2 = st.columns(2)
        
        with col1:
            # 交易量图表
            volume_fig = go.Figure()
            volume_fig.add_trace(go.Bar(
                x=df['time'],
                y=df['volume'],
                name='成交量',
                marker_color='green',
                hovertemplate=f'{symbol_name}<br>时间: %{{x}}<br>成交量: %{{y}}<extra></extra>'
            ))
            
            volume_fig.update_layout(
                title=f"{symbol_name} 成交量分布",
                xaxis_title="时间",
                yaxis_title="成交量",
                height=300,
                xaxis=dict(tickformat='%H:%M:%S')
            )
            st.plotly_chart(volume_fig, width='stretch')
            
        with col2:
            # 统计信息
            st.markdown(f"**📊 {symbol_name} 数据统计**")
            
            price_std = df['price'].std()
            price_range = df['price'].max() - df['price'].min()
            avg_volume = df['volume'].mean()
            
            st.markdown(f"""
            - **价格波动率**: {price_std:.4f}
            - **价格区间**: {price_range:.4f}
            - **平均成交量**: {avg_volume:,.0f}
            - **数据点数**: {len(df):,}
            - **最新价格**: ¥{df['price'].iloc[-1]:.3f}
            - **最新时间**: {df['time'].iloc[-1].strftime('%Y-%m-%d %H:%M:%S')}
            - **数据时间跨度**: {(df['time'].max() - df['time'].min()).total_seconds() / 60:.1f}分钟
            """)
            
            # 显示该股票的采集统计
            if symbol in symbol_stats:
                symbol_stat = symbol_stats[symbol]
                st.markdown("**🔄 本次采集统计:**")
                st.markdown(f"- 实际插入: {symbol_stat.get('total_collected', 0)} 条")
                st.markdown(f"- 总获取: {symbol_stat.get('total_fetched', 0)} 条")
                st.markdown(f"- 重复数据: {symbol_stat.get('total_duplicates', 0)} 条")
                
                if symbol_stat.get('total_fetched', 0) > 0:
                    dup_rate = (symbol_stat.get('total_duplicates', 0) / symbol_stat['total_fetched']) * 100
                    if dup_rate > 50:
                        st.warning(f"重复率: {dup_rate:.1f}% (较高)")
                    else:
                        st.success(f"重复率: {dup_rate:.1f}% (正常)")
            
            # 实时状态
            if is_running:
                if data_age_minutes < 5:
                    st.success("🟢 数据实时更新中")
                else:
                    st.warning("🟡 数据采集中，但更新较慢")
                st.markdown("**提示**: 数据会自动更新")
            else:
                st.warning("🔴 数据采集已停止")
                st.markdown("**提示**: 启动数据采集以获取实时数据")
    else:
        st.warning(f"📋 {symbol_name} 暂无最近数据，请启动数据采集")
        
        # 显示模拟数据图表作为示例
        st.info(f"💡 以下显示 {symbol_name} 模拟数据示例，启动数据采集后将显示真实数据")
        
        # 创建示例数据
        import numpy as np
        time_range = pd.date_range(start=datetime.now() - timedelta(hours=2), 
                                  end=datetime.now(), 
                                  freq='1min')
        
        # 不同股票使用不同的基础价格
        base_prices = {
            '159740': 2.5,   # 纳指ETF
            '159915': 2.8,   # 创业板ETF  
            '159919': 4.2,   # 沪深300ETF
            '510300': 4.5,   # 沪深300ETF
            '510500': 7.1,   # 中证500ETF
        }
        base_price = base_prices.get(symbol, 2.5)
        
        prices = base_price + np.cumsum(np.random.normal(0, 0.001, len(time_range)))
        volumes = np.random.randint(100, 1000, len(time_range))
        
        demo_df = pd.DataFrame({
            'time': time_range,
            'price': prices,
            'volume': volumes
        })
        
        # 示例图表
        demo_fig = go.Figure()
        demo_fig.add_trace(go.Scatter(
            x=demo_df['time'],
            y=demo_df['price'],
            mode='lines',
            name=f'{symbol_name}价格(示例)',
            line=dict(color='gray', width=2, dash='dash')
        ))
        
        demo_fig.update_layout(
            title=f"{symbol} {symbol_name} 价格走势(示例数据)",
            xaxis_title="时间",
            yaxis_title="价格 (¥)",
            height=400
        )
        st.plotly_chart(demo_fig, width='stretch')
        
        st.info(f"☝️ 上图为 {symbol_name} 模拟数据，启动数据采集后将显示实时数据")


def _display_multi_symbol_chart(symbols: list, available_symbols: dict):
    """显示多股票对比图表"""
    
    # 获取所有股票的最近数据
    all_data = {}
    for symbol in symbols:
        result = get_recent_data(symbol, limit=100)
        if result['success'] and result['count'] > 0:
            df = pd.DataFrame(result['data'])
            df['time'] = pd.to_datetime(df['time'])
            df = df.sort_values('time')
            all_data[symbol] = df
    
    if not all_data:
        st.warning("暂无数据可对比，请先启动数据采集")
        return
    
    # 创建对比图表
    fig = go.Figure()
    
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    for i, (symbol, df) in enumerate(all_data.items()):
        symbol_name = available_symbols.get(symbol, {}).get('name', symbol)
        
        # 价格标准化（以第一个数据点为基准）
        if len(df) > 0:
            normalized_prices = df['price'] / df['price'].iloc[0] * 100
            
            fig.add_trace(go.Scatter(
                x=df['time'],
                y=normalized_prices,
                mode='lines',
                name=f'{symbol}({symbol_name})',
                line=dict(color=colors[i % len(colors)], width=2),
                hovertemplate=f'{symbol_name}<br>时间: %{{x}}<br>标准化价格: %{{y:.2f}}%<extra></extra>'
            ))
    
    fig.update_layout(
        title="多股票标准化价格对比（以首个数据点为100%基准）",
        xaxis_title="时间",
        yaxis_title="标准化价格 (%)",
        height=400,
        showlegend=True,
        xaxis=dict(tickformat='%H:%M:%S'),
        yaxis=dict(tickformat='.1f')
    )
    
    st.plotly_chart(fig, width='stretch')
    st.info("💡 此图表显示各股票的相对价格变化，便于比较不同股票的走势")


# 页面底部信息和运行监控部分移除了原始的重复代码，使用上面的多股票功能代替


if __name__ == "__main__":
    main()