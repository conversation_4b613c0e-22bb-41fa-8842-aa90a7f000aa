"""
参数优化器模块
提供智能参数优化功能，包括遗传算法、网格搜索等多种优化方法
"""

from .optimizer import ParameterOptimizer
from .market_analyzer import MarketAnalyzer
from .algorithms.genetic import GeneticAlgorithm
from .algorithms.grid_search import GridSearch
from .algorithms.bayesian import BayesianOptimization

__version__ = "1.0.0"
__author__ = "ETF Arbitrage System"

__all__ = [
    'ParameterOptimizer',
    'MarketAnalyzer',
    'GeneticAlgorithm',
    'GridSearch',
    'BayesianOptimization'
]
