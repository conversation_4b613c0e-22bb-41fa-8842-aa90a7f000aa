#!/usr/bin/env python3
"""
核心基础设施测试 - 修复版
测试数据库兼容性和核心服务
"""

import sys
import os
import tempfile
import sqlite3
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_database_compatibility():
    """测试数据库兼容性"""
    print("🔍 测试数据库兼容性...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.database_manager import DatabaseManager
        
        # 创建临时数据库文件
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            temp_db_path = tmp_file.name
        
        # 创建旧格式的数据库表（模拟现有数据库）
        conn = sqlite3.connect(temp_db_path)
        cursor = conn.cursor()
        
        # 创建旧格式的ticks表（没有created_at列）
        cursor.execute('''
            CREATE TABLE ticks (
                symbol TEXT,
                tick_time TEXT,
                price REAL,
                volume INTEGER,
                side TEXT,
                raw TEXT,
                PRIMARY KEY (symbol, tick_time)
            )
        ''')
        
        # 创建旧格式的last_ticks表（没有额外列）
        cursor.execute('''
            CREATE TABLE last_ticks (
                symbol TEXT PRIMARY KEY,
                last_time TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # 测试DatabaseManager是否能处理旧格式
        db_manager = DatabaseManager()
        db_manager._db_configs['ticks'] = temp_db_path
        
        # 初始化数据库（应该添加缺失的列）
        db_manager._init_ticks_db()
        
        # 测试保存数据
        success = db_manager.save_tick_data(
            symbol='159740',
            tick_time='2024-01-01 09:30:00',
            price=1.234,
            volume=1000,
            side='BUY'
        )
        
        if success:
            print("✅ 数据库兼容性测试通过")
        else:
            print("❌ 数据库兼容性测试失败")
            
        # 清理临时文件
        os.unlink(temp_db_path)
        
        return success
        
    except Exception as e:
        print(f"❌ 数据库兼容性测试失败: {e}")
        return False

def test_config_manager():
    """测试配置管理器"""
    print("🔍 测试配置管理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试默认配置
        trading_config = config_manager.get_trading_config()
        if trading_config.default_position_size > 0:
            print("✅ 配置管理器测试通过")
            return True
        else:
            print("❌ 配置管理器测试失败：配置值异常")
            return False
            
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        return False

def test_logger_manager():
    """测试日志管理器"""
    print("🔍 测试日志管理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.logger_manager import LoggerManager
        
        logger_manager = LoggerManager()
        
        # 创建测试日志器
        test_logger = logger_manager.create_module_logger('test')
        test_logger.info("测试日志消息")
        
        print("✅ 日志管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日志管理器测试失败: {e}")
        return False

def test_exception_handler():
    """测试异常处理器"""
    print("🔍 测试异常处理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.exception_handler import ExceptionHandler, ErrorCategory, ErrorSeverity
        
        exception_handler = ExceptionHandler()
        
        # 测试异常记录
        try:
            raise ValueError("测试异常")
        except Exception as e:
            exception_handler.handle_exception(e, ErrorCategory.VALIDATION_ERROR, ErrorSeverity.LOW)
        
        print("✅ 异常处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 异常处理器测试失败: {e}")
        return False

def test_service_registry():
    """测试服务注册表"""
    print("🔍 测试服务注册表...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.service_registry import ServiceRegistry
        
        service_registry = ServiceRegistry()
        
        # 测试服务注册表基本功能
        services = service_registry.list_services()
        
        print("✅ 服务注册表测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 服务注册表测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始核心基础设施测试（修复版）")
    print("=" * 50)
    
    tests = [
        test_database_compatibility,
        test_config_manager,
        test_logger_manager,
        test_exception_handler,
        test_service_registry
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
    
    print("=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有核心基础设施测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
