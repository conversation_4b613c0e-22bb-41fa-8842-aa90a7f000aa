#!/usr/bin/env python3
"""
测试增强版交易器V2
验证重构后的功能是否正常
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_enhanced_trader_v2_import():
    """测试增强版交易器V2导入"""
    print("🔍 测试增强版交易器V2导入...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import (
            EnhancedRealTimeTrader, RealTimeDataFeed, EnhancedRiskManager,
            CORE_INFRASTRUCTURE_AVAILABLE, STRATEGY_MANAGER_AVAILABLE,
            DATA_CONNECTOR_AVAILABLE, TRADING_RULES_AVAILABLE
        )
        
        print("✅ 增强版交易器V2导入成功")
        print(f"✅ 核心基础设施可用: {CORE_INFRASTRUCTURE_AVAILABLE}")
        print(f"✅ 策略管理器可用: {STRATEGY_MANAGER_AVAILABLE}")
        print(f"✅ 数据连接器可用: {DATA_CONNECTOR_AVAILABLE}")
        print(f"✅ 交易规则管理器可用: {TRADING_RULES_AVAILABLE}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_trader_initialization():
    """测试增强版交易器初始化"""
    print("🔍 测试增强版交易器初始化...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import EnhancedRealTimeTrader
        
        # 创建交易器实例
        trader = EnhancedRealTimeTrader(initial_capital=1000000)
        
        print(f"✅ 交易器初始化成功，初始资金: {trader.initial_capital:,.2f}")
        print(f"✅ 数据源类型: {type(trader.data_feed).__name__}")
        print(f"✅ 风险管理器类型: {type(trader.risk_manager).__name__}")
        print(f"✅ 策略参数数量: {len(trader.strategy_params)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_feed_functionality():
    """测试数据源功能"""
    print("🔍 测试数据源功能...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import RealTimeDataFeed
        
        # 创建数据源
        data_feed = RealTimeDataFeed()
        
        print(f"✅ 数据源创建成功，数据库路径: {data_feed.db_path}")
        
        # 测试订阅功能
        received_data = []
        
        def test_callback(data):
            received_data.append(data)
            print(f"📊 接收到数据: {data['symbol']} @ {data['price']:.4f}")
        
        data_feed.subscribe(test_callback)
        print(f"✅ 订阅功能测试通过，订阅者数量: {len(data_feed.subscribers)}")
        
        # 测试数据生成
        data_feed.current_symbol = "159740"
        tick_data = data_feed._get_latest_tick()
        
        if tick_data:
            print(f"✅ 数据生成测试通过: {tick_data}")
        else:
            print("⚠️ 数据生成测试：未生成数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据源功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_manager():
    """测试风险管理器"""
    print("🔍 测试风险管理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import (
            EnhancedRiskManager, TradeSignal, TradeType
        )
        
        # 创建风险管理器
        risk_manager = EnhancedRiskManager(1000000)
        
        print(f"✅ 风险管理器创建成功")
        print(f"✅ 最大持仓数: {risk_manager.max_positions}")
        print(f"✅ 佣金费率: {risk_manager.commission_rate}")
        
        # 测试风险检查
        test_signal = TradeSignal(
            symbol="159740",
            signal_type=TradeType.BUY,
            price=1.0,
            quantity=10000,
            confidence=0.8,
            reason="测试信号",
            timestamp=datetime.now()
        )
        
        risk_ok, risk_reason = risk_manager.check_risk(test_signal, {}, {'price': 1.0})
        print(f"✅ 风险检查结果: {risk_ok}, 原因: {risk_reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ 风险管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_generation():
    """测试信号生成"""
    print("🔍 测试信号生成...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import EnhancedRealTimeTrader
        
        trader = EnhancedRealTimeTrader()
        
        # 模拟市场数据
        market_data = {
            'symbol': '159740',
            'price': 1.234,
            'volume': 10000,
            'timestamp': datetime.now()
        }
        
        # 生成信号
        signal = trader._generate_signal(market_data)
        
        if signal:
            print(f"✅ 信号生成成功: {signal.signal_type.value} {signal.quantity}@{signal.price:.4f}")
            print(f"✅ 信号原因: {signal.reason}")
            print(f"✅ 信号置信度: {signal.confidence}")
        else:
            print("⚠️ 未生成信号（可能是正常情况）")
        
        return True
        
    except Exception as e:
        print(f"❌ 信号生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_reporting():
    """测试状态报告"""
    print("🔍 测试状态报告...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import EnhancedRealTimeTrader
        
        trader = EnhancedRealTimeTrader()
        
        # 获取基础状态
        base_status = trader.get_status()
        print(f"✅ 基础状态获取成功，包含 {len(base_status)} 个字段")
        
        # 获取增强状态
        enhanced_status = trader.get_enhanced_status()
        print(f"✅ 增强状态获取成功，包含 {len(enhanced_status)} 个字段")
        
        # 显示关键状态信息
        print(f"✅ 运行状态: {enhanced_status['is_running']}")
        print(f"✅ 持仓数量: {enhanced_status['stats']['total_trades']}")
        print(f"✅ 核心基础设施: {enhanced_status['core_infrastructure_available']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 状态报告测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_convenience_functions():
    """测试便捷函数"""
    print("🔍 测试便捷函数...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import (
            start_enhanced_trading, stop_enhanced_trading, 
            get_enhanced_trading_status, close_all_enhanced_positions
        )
        
        print("✅ 便捷函数导入成功")
        
        # 测试状态获取
        status = get_enhanced_trading_status()
        print(f"✅ 状态获取成功: 运行状态 = {status['is_running']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强版交易器V2测试")
    print("=" * 60)
    
    tests = [
        test_enhanced_trader_v2_import,
        test_enhanced_trader_initialization,
        test_data_feed_functionality,
        test_risk_manager,
        test_signal_generation,
        test_status_reporting,
        test_convenience_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有增强版交易器V2测试通过！")
        print("✅ 重构成功，新版本功能正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
