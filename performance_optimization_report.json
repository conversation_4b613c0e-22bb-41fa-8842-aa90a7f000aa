{"timestamp": "2025-09-24T14:18:37.307603", "test_results": {"database": {"query_performance": {"total_queries": 2, "cache_hit_rate": 50.0, "avg_execution_time": 0.0004981756210327148, "max_execution_time": 0.0009963512420654297, "min_execution_time": 0.0}, "connection_pools": {"ticks": "ConnectionPoolStats(total_connections=10, active_connections=0, idle_connections=10, total_requests=2, successful_requests=2, failed_requests=0, average_wait_time=0.0, cache_hit_rate=100.0)", "monitoring": "ConnectionPoolStats(total_connections=10, active_connections=0, idle_connections=10, total_requests=0, successful_requests=0, failed_requests=0, average_wait_time=0, cache_hit_rate=0.0)", "backtest": "ConnectionPoolStats(total_connections=10, active_connections=0, idle_connections=10, total_requests=0, successful_requests=0, failed_requests=0, average_wait_time=0, cache_hit_rate=0.0)"}, "cache_info": {"cache_size": 2, "redis_available": false}}, "computing": {"use_numba": false, "max_workers": 2, "performance_summary": {"technical_indicators": {"count": 2, "avg_execution_time": 0.02849721908569336, "min_execution_time": 0.015196561813354492, "max_execution_time": 0.04179787635803223, "avg_data_size": 25502.5, "total_data_processed": "51005", "throughput_ops_per_sec": 35.09114334956411}, "signal_generation": {"count": 1, "avg_execution_time": 0.0019943714141845703, "min_execution_time": 0.0019943714141845703, "max_execution_time": 0.0019943714141845703, "avg_data_size": 1000.0, "total_data_processed": "1000", "throughput_ops_per_sec": 501.41111775254035}}}, "memory": {"memory_metrics": {"timestamp": "2025-09-24 14:18:29.900612", "total_memory_mb": 12180.1328125, "available_memory_mb": 2147.10546875, "process_memory_mb": 83.609375, "memory_percent": 82.4, "gc_collections": {"0": 12, "1": 9, "2": 4}, "cache_size": 0, "cache_hit_rate": 0.0}, "cache_stats": {"size": 1, "max_size": 1000, "memory_usage_mb": 0.00022125244140625, "max_memory_mb": 500, "hit_rate": 100.0, "total_requests": 1, "hits": 1, "misses": 0, "evictions": 0, "memory_evictions": 0}, "gc_stats": {"generation_0": 438, "generation_1": 5, "generation_2": 2}, "optimization_recommendations": ["内存使用率较高，建议清理缓存"]}}, "recommendations": ["建议安装Numba以获得更好的计算性能", "内存使用率过高，建议增加内存或优化数据结构"]}