"""
数据库连接和操作工具
"""

import sqlite3
import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = "ticks.db"):
        self.db_path = Path(db_path)
        self.init_database()
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            with self.get_connection() as conn:
                # 创建tick数据表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS ticks (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        tick_time TEXT NOT NULL,
                        price REAL NOT NULL,
                        volume INTEGER NOT NULL,
                        side TEXT,
                        raw TEXT,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建索引
                conn.execute("""
                    CREATE INDEX IF NOT EXISTS idx_ticks_symbol_time 
                    ON ticks(symbol, tick_time)
                """)
                
                # 创建最后更新时间表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS last_ticks (
                        symbol TEXT PRIMARY KEY,
                        last_time TEXT NOT NULL,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建策略配置表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS strategy_configs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT UNIQUE NOT NULL,
                        symbol TEXT NOT NULL,
                        parameters TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建回测结果表
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS backtest_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        symbol TEXT NOT NULL,
                        config_name TEXT,
                        start_time TEXT NOT NULL,
                        end_time TEXT NOT NULL,
                        parameters TEXT NOT NULL,
                        results TEXT NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                # 创建系统状态表（用于线程间状态共享）
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS system_status (
                        module TEXT PRIMARY KEY,
                        status_data TEXT NOT NULL,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                """)
                
                conn.commit()
                logger.info("数据库初始化完成")
                
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def get_tick_data(self, symbol: str, start_time: str = None, end_time: str = None, limit: int = 1000) -> pd.DataFrame:
        """获取tick数据"""
        try:
            with self.get_connection() as conn:
                query = "SELECT * FROM ticks WHERE symbol = ?"
                params = [symbol]
                
                if start_time:
                    query += " AND tick_time >= ?"
                    params.append(start_time)
                
                if end_time:
                    query += " AND tick_time <= ?"
                    params.append(end_time)
                
                query += " ORDER BY tick_time DESC LIMIT ?"
                params.append(limit)
                
                df = pd.read_sql_query(query, conn, params=params)
                
                if not df.empty:
                    df['tick_time'] = pd.to_datetime(df['tick_time'], errors='coerce')
                    df = df.sort_values('tick_time').reset_index(drop=True)
                
                return df
                
        except Exception as e:
            logger.error(f"获取tick数据失败: {e}")
            return pd.DataFrame()
    
    def insert_tick_data(self, symbol: str, tick_time: str, price: float, volume: int, side: str = None, raw: str = None) -> bool:
        """插入tick数据"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR IGNORE INTO ticks (symbol, tick_time, price, volume, side, raw)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (symbol, tick_time, price, volume, side, raw))
                
                # 更新最后更新时间
                conn.execute("""
                    INSERT OR REPLACE INTO last_ticks (symbol, last_time)
                    VALUES (?, ?)
                """, (symbol, tick_time))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"插入tick数据失败: {e}")
            return False
    
    def batch_insert_ticks(self, data: List[Tuple]) -> int:
        """批量插入tick数据"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 获取插入前的记录数
                before_count = cursor.execute("SELECT COUNT(*) FROM ticks").fetchone()[0]
                
                # 批量插入
                cursor.executemany("""
                    INSERT OR IGNORE INTO ticks (symbol, tick_time, price, volume, side, raw)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, data)
                
                # 获取插入后的记录数
                after_count = cursor.execute("SELECT COUNT(*) FROM ticks").fetchone()[0]
                
                # 更新最后更新时间
                if data:
                    symbol = data[0][0]
                    max_time = max(row[1] for row in data)
                    cursor.execute("""
                        INSERT OR REPLACE INTO last_ticks (symbol, last_time)
                        VALUES (?, ?)
                    """, (symbol, max_time))
                
                conn.commit()
                inserted_count = after_count - before_count
                logger.info(f"批量插入完成: {inserted_count} 条新记录")
                return inserted_count
                
        except Exception as e:
            logger.error(f"批量插入tick数据失败: {e}")
            return 0
    
    def get_data_statistics(self, symbol: str = None) -> Dict[str, Any]:
        """获取数据统计信息"""
        try:
            with self.get_connection() as conn:
                stats = {}
                
                if symbol:
                    # 单个标的统计
                    cursor = conn.cursor()
                    
                    # 总记录数
                    total_count = cursor.execute(
                        "SELECT COUNT(*) FROM ticks WHERE symbol = ?", (symbol,)
                    ).fetchone()[0]
                    
                    # 最早和最晚时间
                    time_range = cursor.execute("""
                        SELECT MIN(tick_time), MAX(tick_time) 
                        FROM ticks WHERE symbol = ?
                    """, (symbol,)).fetchone()
                    
                    # 最新价格
                    latest_price = cursor.execute("""
                        SELECT price FROM ticks 
                        WHERE symbol = ? 
                        ORDER BY tick_time DESC LIMIT 1
                    """, (symbol,)).fetchone()
                    
                    stats = {
                        'symbol': symbol,
                        'total_records': total_count,
                        'start_time': time_range[0] if time_range[0] else None,
                        'end_time': time_range[1] if time_range[1] else None,
                        'latest_price': latest_price[0] if latest_price else None,
                        'data_quality': 'good' if total_count > 1000 else 'limited'
                    }
                else:
                    # 全局统计
                    cursor = conn.cursor()
                    
                    # 各标的记录数
                    symbol_stats = cursor.execute("""
                        SELECT symbol, COUNT(*) as count, 
                               MIN(tick_time) as start_time,
                               MAX(tick_time) as end_time
                        FROM ticks 
                        GROUP BY symbol
                    """).fetchall()
                    
                    stats = {
                        'total_symbols': len(symbol_stats),
                        'symbol_details': [dict(row) for row in symbol_stats]
                    }
                
                return stats
                
        except Exception as e:
            logger.error(f"获取数据统计失败: {e}")
            return {}
    
    def cleanup_old_data(self, days_to_keep: int = 30) -> int:
        """清理过期数据"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days_to_keep)
            cutoff_str = cutoff_time.isoformat()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 删除过期数据
                cursor.execute("""
                    DELETE FROM ticks 
                    WHERE tick_time < ?
                """, (cutoff_str,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"清理完成: 删除 {deleted_count} 条过期记录")
                return deleted_count
                
        except Exception as e:
            logger.error(f"清理过期数据失败: {e}")
            return 0
    
    def backup_database(self, backup_path: str = None) -> bool:
        """备份数据库"""
        try:
            if backup_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"ticks_backup_{timestamp}.db"
            
            backup_path = Path(backup_path)
            
            with self.get_connection() as source:
                backup_conn = sqlite3.connect(backup_path)
                source.backup(backup_conn)
                backup_conn.close()
            
            logger.info(f"数据库备份完成: {backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"数据库备份失败: {e}")
            return False

    def update_system_status(self, module: str, status_data: dict) -> bool:
        """更新系统状态到数据库"""
        try:
            import json
            status_json = json.dumps(status_data, default=str)
            
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO system_status (module, status_data, updated_at)
                    VALUES (?, ?, CURRENT_TIMESTAMP)
                """, (module, status_json))
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"更新系统状态失败: {e}")
            return False
    
    def get_system_status(self, module: str) -> dict:
        """从数据库获取系统状态"""
        try:
            import json
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                result = cursor.execute("""
                    SELECT status_data, updated_at FROM system_status 
                    WHERE module = ?
                """, (module,)).fetchone()
                
                if result:
                    status_data = json.loads(result[0])
                    status_data['db_updated_at'] = result[1]
                    return status_data
                else:
                    return {}
                    
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def clear_system_status(self, module: str = None) -> bool:
        """清理系统状态"""
        try:
            with self.get_connection() as conn:
                if module:
                    conn.execute("DELETE FROM system_status WHERE module = ?", (module,))
                else:
                    conn.execute("DELETE FROM system_status")
                
                conn.commit()
                return True
                
        except Exception as e:
            logger.error(f"清理系统状态失败: {e}")
            return False

# 全局数据库管理器实例
db_manager = DatabaseManager()

# 便捷函数
def get_tick_data(symbol: str, **kwargs) -> pd.DataFrame:
    """获取tick数据的便捷函数"""
    return db_manager.get_tick_data(symbol, **kwargs)

def insert_tick(symbol: str, tick_time: str, price: float, volume: int, **kwargs) -> bool:
    """插入单条tick数据的便捷函数"""
    return db_manager.insert_tick_data(symbol, tick_time, price, volume, **kwargs)

def get_data_stats(symbol: str = None) -> Dict[str, Any]:
    """获取数据统计的便捷函数"""
    return db_manager.get_data_statistics(symbol)