#!/usr/bin/env python3
"""
配置管理页面 - 精简版
策略参数和系统设置管理
"""

import streamlit as st
import sys
import os
from pathlib import Path
import pandas as pd
import json
from datetime import datetime

# 添加路径
current_dir = Path(__file__).parent.parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# 导入组件
from components.sidebar import render_sidebar, render_page_header, render_status_indicators, render_refresh_button
from utils.session_state import init_session_state, auto_refresh_session
from config.app_config import get_config

# 页面配置
st.set_page_config(
    page_title="配置管理 - ETF套利系统", 
    page_icon="⚙️",
    layout="wide"
)

@auto_refresh_session
def main():
    """配置管理主页面"""
    
    # 初始化
    init_session_state()
    render_sidebar()
    render_page_header("配置管理", "策略参数和系统设置统一管理", "⚙️")
    
    # 状态指示器
    render_status_indicators()
    render_refresh_button("config_refresh", "刷新配置信息")
    
    st.markdown("---")
    
    # 配置导航
    config_tabs = st.tabs(["🎯 策略参数", "⚙️ 系统设置", "💾 配置管理"])
    
    with config_tabs[0]:
        render_strategy_config()
    
    with config_tabs[1]:
        render_system_config()
    
    with config_tabs[2]:
        render_config_management()

def render_strategy_config():
    """策略参数配置"""
    st.subheader("🎯 策略参数配置")
    
    symbol = st.session_state.get('current_symbol', '159740')
    st.info(f"📊 当前配置标的: {symbol}")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("**交易参数**")
        buy_trigger = st.number_input("买入触发跌幅 (%)", -10.0, -0.01, -0.2, 0.01, key="buy_trigger")
        profit_target = st.number_input("止盈目标 (%)", 0.01, 5.0, 0.25, 0.01, key="profit_target")
        stop_loss = st.number_input("止损线 (%)", -20.0, -0.1, -2.0, 0.1, key="stop_loss")
    
    with col2:
        st.markdown("**仓位管理**")
        position_size = st.number_input("单次买入数量", 1000, 1000000, 100000, 1000, key="position_size")
        max_positions = st.number_input("最大持仓层数", 1, 10, 3, key="max_positions")
        max_hold_time = st.number_input("最大持仓时间(分钟)", 5, 1440, 60, 5, key="max_hold_time")
    
    with col3:
        st.markdown("**风险控制**")
        daily_loss_limit = st.number_input("日亏损限制 (%)", -50.0, -1.0, -5.0, 0.5, key="daily_loss")
        signal_window = st.number_input("信号计算窗口", 10, 200, 50, 5, key="signal_window")
    
    if st.button("💾 保存策略配置", width='stretch'):
        st.success("✅ 策略配置保存成功")

def render_system_config():
    """系统设置配置"""
    st.subheader("⚙️ 系统设置配置")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**数据采集**")
        fetch_interval = st.number_input("采集间隔(秒)", 1, 60, 1, key="fetch_interval")
        batch_size = st.number_input("批次大小", 10, 1000, 100, key="batch_size")
        enable_validation = st.checkbox("启用数据验证", True, key="enable_validation")
        
        st.markdown("**监控设置**")
        max_cpu_usage = st.slider("CPU阈值 (%)", 50, 95, 80, key="max_cpu")
        max_memory_usage = st.slider("内存阈值 (%)", 50, 95, 80, key="max_memory")
    
    with col2:
        st.markdown("**界面设置**")
        theme = st.selectbox("界面主题", ["浅色", "深色"], key="theme")
        auto_refresh = st.checkbox("自动刷新", True, key="auto_refresh")
        if auto_refresh:
            refresh_interval = st.slider("刷新间隔(秒)", 5, 60, 10, key="refresh_interval")
        
        st.markdown("**通知设置**")
        enable_notifications = st.checkbox("启用通知", True, key="notifications")
        notification_sound = st.checkbox("通知声音", False, key="notification_sound")
    
    if st.button("💾 保存系统配置", width='stretch'):
        st.success("✅ 系统配置保存成功")

def render_config_management():
    """配置管理"""
    st.subheader("💾 配置管理")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**配置导出**")
        export_scope = st.selectbox("导出范围", ["所有配置", "策略参数", "系统设置"], key="export_scope")
        if st.button("📤 导出配置", width='stretch'):
            config_data = {"timestamp": datetime.now().isoformat(), "scope": export_scope}
            st.download_button("💾 下载配置文件", json.dumps(config_data, indent=2), 
                             f"config_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    
    with col2:
        st.markdown("**配置导入**")
        uploaded_file = st.file_uploader("选择配置文件", type=['json'])
        if st.button("📥 导入配置", width='stretch'):
            st.success("✅ 配置导入成功")
    
    # 预设方案
    st.markdown("### 📊 预设方案")
    
    preset_schemes = {
        "保守型策略": {"buy_trigger": -0.1, "profit_target": 0.2, "stop_loss": -1.0},
        "平衡型策略": {"buy_trigger": -0.2, "profit_target": 0.25, "stop_loss": -1.5},
        "激进型策略": {"buy_trigger": -0.4, "profit_target": 0.4, "stop_loss": -2.5}
    }
    
    selected_scheme = st.selectbox("选择预设方案", list(preset_schemes.keys()))
    
    col1, col2 = st.columns(2)
    with col1:
        st.markdown("**方案参数**")
        scheme_params = preset_schemes[selected_scheme]
        for key, value in scheme_params.items():
            st.markdown(f"- {key}: {value}%")
    
    with col2:
        if st.button("🎯 应用此方案", width='stretch'):
            st.success(f"✅ 已应用 {selected_scheme}")

if __name__ == "__main__":
    main()