#!/usr/bin/env python3
"""
网格搜索优化算法
系统性地搜索参数空间，找到最优参数组合
"""

import itertools
import logging
import asyncio
from typing import Dict, List, Tuple, Callable, Any
import numpy as np

logger = logging.getLogger(__name__)

class GridSearch:
    """网格搜索优化器"""
    
    def __init__(self, param_space: Dict, max_combinations: int = 1000):
        """
        初始化网格搜索优化器
        
        Args:
            param_space: 参数空间定义，格式: {param_name: (min, max, step)}
            max_combinations: 最大搜索组合数，防止搜索空间过大
        """
        self.param_space = param_space
        self.max_combinations = max_combinations
        self.results = []
        
    def generate_parameter_grid(self) -> List[Dict]:
        """生成参数网格"""
        logger.info("生成参数网格...")
        
        # 为每个参数生成取值范围
        param_ranges = {}
        for param_name, (min_val, max_val, step) in self.param_space.items():
            if step <= 0:
                raise ValueError(f"参数 {param_name} 的步长必须大于0")
            
            # 生成参数取值序列
            values = []
            current = min_val
            while current <= max_val:
                values.append(current)
                current += step
            
            param_ranges[param_name] = values
            logger.info(f"参数 {param_name}: {len(values)} 个取值 ({min_val} 到 {max_val}, 步长 {step})")
        
        # 计算总组合数
        total_combinations = 1
        for values in param_ranges.values():
            total_combinations *= len(values)
        
        logger.info(f"总参数组合数: {total_combinations}")
        
        if total_combinations > self.max_combinations:
            logger.warning(f"参数组合数 ({total_combinations}) 超过最大限制 ({self.max_combinations})")
            # 自动调整参数范围
            param_ranges = self._reduce_parameter_space(param_ranges, total_combinations)
        
        # 生成所有参数组合
        param_names = list(param_ranges.keys())
        param_values = list(param_ranges.values())
        
        combinations = []
        for combination in itertools.product(*param_values):
            param_dict = dict(zip(param_names, combination))
            combinations.append(param_dict)
        
        logger.info(f"实际生成 {len(combinations)} 个参数组合")
        return combinations
    
    def _reduce_parameter_space(self, param_ranges: Dict, total_combinations: int) -> Dict:
        """减少参数空间以控制组合数量"""
        logger.info("自动减少参数空间...")
        
        # 计算需要减少的倍数
        reduction_factor = (total_combinations / self.max_combinations) ** (1 / len(param_ranges))
        
        reduced_ranges = {}
        for param_name, values in param_ranges.items():
            # 保持参数范围，但减少取值点数
            new_count = max(3, int(len(values) / reduction_factor))  # 至少保留3个点
            
            if new_count < len(values):
                # 均匀采样
                indices = np.linspace(0, len(values) - 1, new_count, dtype=int)
                reduced_values = [values[i] for i in indices]
                reduced_ranges[param_name] = reduced_values
                logger.info(f"参数 {param_name}: 从 {len(values)} 个值减少到 {len(reduced_values)} 个值")
            else:
                reduced_ranges[param_name] = values
        
        return reduced_ranges
    
    async def optimize(self, symbol: str, days: int, evaluate_func: Callable) -> List[Dict]:
        """
        执行网格搜索优化
        
        Args:
            symbol: 交易标的
            days: 回测天数
            evaluate_func: 评估函数，接收参数配置并返回评估结果
            
        Returns:
            按适应度排序的最优配置列表
        """
        logger.info(f"开始网格搜索优化: {symbol}, 回测天数: {days}")
        
        # 生成参数网格
        parameter_combinations = self.generate_parameter_grid()
        
        # 评估所有参数组合
        self.results = []
        total_combinations = len(parameter_combinations)
        
        logger.info(f"开始评估 {total_combinations} 个参数组合...")
        
        # 批量处理以提高效率
        batch_size = 10
        for i in range(0, total_combinations, batch_size):
            batch = parameter_combinations[i:i + batch_size]
            batch_results = await self._evaluate_batch(batch, symbol, days, evaluate_func)
            self.results.extend(batch_results)
            
            # 显示进度
            progress = (i + len(batch)) / total_combinations * 100
            logger.info(f"评估进度: {progress:.1f}% ({i + len(batch)}/{total_combinations})")
        
        # 按适应度排序
        valid_results = [r for r in self.results if r['metrics']['fitness'] > -999]
        valid_results.sort(key=lambda x: x['metrics']['fitness'], reverse=True)
        
        logger.info(f"网格搜索完成，有效结果: {len(valid_results)}/{total_combinations}")
        
        # 返回前10个最优结果
        top_results = valid_results[:10]
        
        if top_results:
            best_result = top_results[0]
            logger.info(f"最优配置适应度: {best_result['metrics']['fitness']:.4f}")
            logger.info(f"最优参数: {best_result['config']}")
        
        return top_results
    
    async def _evaluate_batch(self, batch: List[Dict], symbol: str, days: int, 
                            evaluate_func: Callable) -> List[Dict]:
        """批量评估参数组合"""
        batch_results = []
        
        # 并发评估（限制并发数以避免资源耗尽）
        semaphore = asyncio.Semaphore(5)  # 最多5个并发评估
        
        async def evaluate_single(config):
            async with semaphore:
                try:
                    metrics = await evaluate_func(config, symbol, days)
                    return {
                        'config': config,
                        'metrics': metrics
                    }
                except Exception as e:
                    logger.error(f"评估配置失败 {config}: {e}")
                    return {
                        'config': config,
                        'metrics': {'fitness': -999, 'error': str(e)}
                    }
        
        # 并发执行评估
        tasks = [evaluate_single(config) for config in batch]
        batch_results = await asyncio.gather(*tasks)
        
        return batch_results
    
    def get_optimization_summary(self) -> Dict:
        """获取优化摘要"""
        if not self.results:
            return {'status': 'no_results'}
        
        valid_results = [r for r in self.results if r['metrics']['fitness'] > -999]
        failed_results = [r for r in self.results if r['metrics']['fitness'] <= -999]
        
        if not valid_results:
            return {
                'status': 'all_failed',
                'total_combinations': len(self.results),
                'failed_count': len(failed_results)
            }
        
        fitness_scores = [r['metrics']['fitness'] for r in valid_results]
        
        return {
            'status': 'success',
            'total_combinations': len(self.results),
            'valid_count': len(valid_results),
            'failed_count': len(failed_results),
            'success_rate': len(valid_results) / len(self.results),
            'fitness_stats': {
                'best': max(fitness_scores),
                'worst': min(fitness_scores),
                'mean': np.mean(fitness_scores),
                'std': np.std(fitness_scores)
            },
            'best_config': valid_results[0]['config'] if valid_results else None
        }
    
    def analyze_parameter_sensitivity(self) -> Dict:
        """分析参数敏感性"""
        if not self.results:
            return {}
        
        valid_results = [r for r in self.results if r['metrics']['fitness'] > -999]
        if len(valid_results) < 10:
            return {'error': 'insufficient_data'}
        
        # 分析每个参数对适应度的影响
        param_analysis = {}
        
        for param_name in self.param_space.keys():
            param_values = [r['config'][param_name] for r in valid_results]
            fitness_values = [r['metrics']['fitness'] for r in valid_results]
            
            # 计算相关性
            correlation = np.corrcoef(param_values, fitness_values)[0, 1]
            
            # 分析参数范围
            param_min = min(param_values)
            param_max = max(param_values)
            
            # 找出最优参数值
            best_idx = np.argmax(fitness_values)
            optimal_value = param_values[best_idx]
            
            param_analysis[param_name] = {
                'correlation_with_fitness': float(correlation) if not np.isnan(correlation) else 0.0,
                'value_range': (float(param_min), float(param_max)),
                'optimal_value': float(optimal_value),
                'sensitivity': abs(correlation) if not np.isnan(correlation) else 0.0
            }
        
        # 按敏感性排序
        sorted_params = sorted(param_analysis.items(), 
                             key=lambda x: x[1]['sensitivity'], 
                             reverse=True)
        
        return {
            'parameter_sensitivity': dict(sorted_params),
            'most_sensitive_param': sorted_params[0][0] if sorted_params else None,
            'least_sensitive_param': sorted_params[-1][0] if sorted_params else None
        }

# 测试函数
async def test_grid_search():
    """测试网格搜索算法"""
    logger.info("开始测试网格搜索算法...")
    
    # 定义测试参数空间
    param_space = {
        'buy_trigger_drop': (-0.010, -0.005, 0.001),
        'profit_target': (0.003, 0.008, 0.001),
        'stop_loss': (-0.025, -0.015, 0.002)
    }
    
    # 创建网格搜索实例
    grid_search = GridSearch(param_space, max_combinations=100)
    
    # 模拟评估函数
    async def mock_evaluate_func(config: Dict, symbol: str, days: int) -> Dict:
        """模拟评估函数"""
        await asyncio.sleep(0.01)  # 模拟计算时间
        
        # 简单的适应度计算（实际应该是回测结果）
        fitness = (
            abs(config['buy_trigger_drop']) * 10 +
            config['profit_target'] * 100 +
            abs(config['stop_loss']) * 5 +
            np.random.normal(0, 0.1)  # 添加一些随机性
        )
        
        return {
            'fitness': fitness,
            'total_return': fitness * 0.01,
            'max_drawdown': -fitness * 0.005,
            'sharpe_ratio': fitness * 0.1
        }
    
    try:
        # 执行优化
        results = await grid_search.optimize("TEST", 30, mock_evaluate_func)
        
        logger.info(f"✅ 网格搜索完成，获得 {len(results)} 个结果")
        
        if results:
            best_result = results[0]
            logger.info(f"最优适应度: {best_result['metrics']['fitness']:.4f}")
            logger.info(f"最优配置: {best_result['config']}")
        
        # 获取优化摘要
        summary = grid_search.get_optimization_summary()
        logger.info(f"优化摘要: 成功率 {summary['success_rate']:.2%}")
        
        # 分析参数敏感性
        sensitivity = grid_search.analyze_parameter_sensitivity()
        if 'parameter_sensitivity' in sensitivity:
            logger.info(f"最敏感参数: {sensitivity['most_sensitive_param']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 网格搜索测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 测试网格搜索算法...")
    print("=" * 50)
    
    success = asyncio.run(test_grid_search())
    
    if success:
        print("\n🎉 网格搜索算法测试成功！")
        print("✅ 参数网格生成正常")
        print("✅ 批量评估功能正常")
        print("✅ 结果排序功能正常")
        print("✅ 敏感性分析功能正常")
    else:
        print("\n❌ 网格搜索算法测试失败")
    
    print("=" * 50)
