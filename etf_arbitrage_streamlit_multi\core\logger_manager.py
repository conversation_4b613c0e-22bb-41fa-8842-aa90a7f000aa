"""
统一日志管理器
提供统一的日志配置、格式化和管理功能
"""

import logging
import logging.handlers
import sys
from typing import Dict, Optional, Union
from pathlib import Path
from datetime import datetime
import threading

class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)

class LoggerManager:
    """统一日志管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._loggers: Dict[str, logging.Logger] = {}
        self._log_dir = Path('logs')
        self._log_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self._default_level = logging.INFO
        self._default_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self._file_format = '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        
        # 初始化根日志器
        self._setup_root_logger()
    
    def _setup_root_logger(self):
        """设置根日志器"""
        root_logger = logging.getLogger()
        root_logger.setLevel(self._default_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self._default_level)
        console_formatter = ColoredFormatter(self._default_format)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = self._log_dir / f"app_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(self._default_level)
        file_formatter = logging.Formatter(self._file_format)
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)
    
    def get_logger(self, name: str, level: Optional[Union[int, str]] = None) -> logging.Logger:
        """获取指定名称的日志器"""
        if name in self._loggers:
            return self._loggers[name]
        
        logger = logging.getLogger(name)
        
        if level is not None:
            if isinstance(level, str):
                level = getattr(logging, level.upper())
            logger.setLevel(level)
        
        self._loggers[name] = logger
        return logger
    
    def create_module_logger(self, module_name: str, 
                           log_to_file: bool = True,
                           file_level: Union[int, str] = logging.DEBUG,
                           console_level: Union[int, str] = logging.INFO) -> logging.Logger:
        """为模块创建专用日志器"""
        
        logger = logging.getLogger(module_name)
        logger.setLevel(logging.DEBUG)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        if isinstance(console_level, str):
            console_level = getattr(logging, console_level.upper())
        console_handler.setLevel(console_level)
        
        console_formatter = ColoredFormatter(
            f'%(asctime)s - {module_name} - %(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        # 文件处理器
        if log_to_file:
            log_file = self._log_dir / f"{module_name}_{datetime.now().strftime('%Y%m%d')}.log"
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=5*1024*1024,  # 5MB
                backupCount=3,
                encoding='utf-8'
            )
            
            if isinstance(file_level, str):
                file_level = getattr(logging, file_level.upper())
            file_handler.setLevel(file_level)
            
            file_formatter = logging.Formatter(
                f'%(asctime)s - {module_name} - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        # 防止日志传播到根日志器
        logger.propagate = False
        
        self._loggers[module_name] = logger
        return logger
    
    def create_trading_logger(self) -> logging.Logger:
        """创建交易专用日志器"""
        return self.create_module_logger(
            'trading',
            log_to_file=True,
            file_level=logging.DEBUG,
            console_level=logging.INFO
        )
    
    def create_data_logger(self) -> logging.Logger:
        """创建数据采集专用日志器"""
        return self.create_module_logger(
            'data_collection',
            log_to_file=True,
            file_level=logging.DEBUG,
            console_level=logging.WARNING
        )
    
    def create_monitoring_logger(self) -> logging.Logger:
        """创建监控专用日志器"""
        return self.create_module_logger(
            'monitoring',
            log_to_file=True,
            file_level=logging.INFO,
            console_level=logging.WARNING
        )
    
    def create_backtest_logger(self) -> logging.Logger:
        """创建回测专用日志器"""
        return self.create_module_logger(
            'backtest',
            log_to_file=True,
            file_level=logging.DEBUG,
            console_level=logging.INFO
        )
    
    def set_global_level(self, level: Union[int, str]):
        """设置全局日志级别"""
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        
        self._default_level = level
        
        # 更新所有现有日志器
        for logger in self._loggers.values():
            logger.setLevel(level)
            for handler in logger.handlers:
                handler.setLevel(level)
    
    def add_file_handler(self, logger_name: str, file_path: Union[str, Path],
                        level: Union[int, str] = logging.DEBUG,
                        max_bytes: int = 10*1024*1024,
                        backup_count: int = 5):
        """为指定日志器添加文件处理器"""
        
        logger = self.get_logger(logger_name)
        
        if isinstance(level, str):
            level = getattr(logging, level.upper())
        
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        
        formatter = logging.Formatter(self._file_format)
        file_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
    
    def cleanup_old_logs(self, days_to_keep: int = 7):
        """清理旧日志文件"""
        cutoff_date = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
        
        cleaned_count = 0
        for log_file in self._log_dir.glob('*.log*'):
            try:
                if log_file.stat().st_mtime < cutoff_date:
                    log_file.unlink()
                    cleaned_count += 1
            except Exception as e:
                logging.error(f"清理日志文件失败 {log_file}: {e}")
        
        if cleaned_count > 0:
            logging.info(f"清理了 {cleaned_count} 个旧日志文件")
        
        return cleaned_count
    
    def get_log_stats(self) -> Dict[str, any]:
        """获取日志统计信息"""
        stats = {
            'log_directory': str(self._log_dir),
            'total_loggers': len(self._loggers),
            'logger_names': list(self._loggers.keys()),
            'log_files': []
        }
        
        # 统计日志文件
        for log_file in self._log_dir.glob('*.log*'):
            try:
                file_stat = log_file.stat()
                stats['log_files'].append({
                    'name': log_file.name,
                    'size_mb': round(file_stat.st_size / (1024*1024), 2),
                    'modified': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                })
            except Exception:
                pass
        
        return stats

# 全局日志管理器实例
logger_manager = LoggerManager()

# 便捷函数
def get_logger(name: str) -> logging.Logger:
    """获取日志器的便捷函数"""
    return logger_manager.get_logger(name)

def get_trading_logger() -> logging.Logger:
    """获取交易日志器的便捷函数"""
    return logger_manager.create_trading_logger()

def get_data_logger() -> logging.Logger:
    """获取数据日志器的便捷函数"""
    return logger_manager.create_data_logger()

def get_monitoring_logger() -> logging.Logger:
    """获取监控日志器的便捷函数"""
    return logger_manager.create_monitoring_logger()

def get_backtest_logger() -> logging.Logger:
    """获取回测日志器的便捷函数"""
    return logger_manager.create_backtest_logger()

def get_ui_logger() -> logging.Logger:
    """获取UI日志器的便捷函数"""
    return logger_manager.create_module_logger(
        'ui',
        log_to_file=True,
        file_level=logging.INFO,
        console_level=logging.WARNING
    )
