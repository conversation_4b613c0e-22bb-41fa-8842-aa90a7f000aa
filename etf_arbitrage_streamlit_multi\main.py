#!/usr/bin/env python3
"""
ETF套利交易系统 - 多页面Streamlit应用主入口
整合数据采集、回测分析、实时交易、系统监控等功能为统一的多页面应用
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
current_dir = Path(__file__).parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(current_dir))

# 设置页面配置
st.set_page_config(
    page_title="ETF套利交易系统",
    page_icon="🎯",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 导入共享组件
try:
    from components.sidebar import render_sidebar
    from utils.session_state import init_session_state
    from config.app_config import APP_CONFIG
except ImportError:
    # 如果导入失败，使用简单的默认配置
    APP_CONFIG = {
        'title': 'ETF套利交易系统',
        'description': '专业的ETF套利交易解决方案',
        'version': '2.0.0'
    }
    
    def init_session_state():
        """初始化会话状态"""
        if 'system_initialized' not in st.session_state:
            st.session_state.system_initialized = True
            st.session_state.current_symbol = "159740"
            st.session_state.user_settings = {}
    
    def render_sidebar():
        """渲染侧边栏"""
        with st.sidebar:
            st.image("https://via.placeholder.com/200x80/1f77b4/white?text=ETF+系统", width=200)
            st.markdown("---")
            
            # 用户设置
            st.subheader("🔧 系统设置")
            
            symbol = st.selectbox(
                "选择ETF标的",
                ["159740", "159915", "159919", "512880"],
                index=0,
                key="sidebar_symbol"
            )
            st.session_state.current_symbol = symbol
            
            # 主题设置
            theme = st.selectbox(
                "界面主题",
                ["浅色", "深色"],
                index=0,
                key="sidebar_theme"
            )
            
            # 自动刷新设置
            auto_refresh = st.checkbox(
                "自动刷新数据",
                value=True,
                key="sidebar_auto_refresh"
            )
            
            if auto_refresh:
                refresh_interval = st.slider(
                    "刷新间隔(秒)",
                    min_value=5,
                    max_value=60,
                    value=10,
                    key="sidebar_refresh_interval"
                )
                st.session_state.refresh_interval = refresh_interval

def main():
    """主页面内容"""
    # 初始化会话状态
    init_session_state()
    
    # 渲染侧边栏
    render_sidebar()
    
    # 主页面标题
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.title("🎯 ETF套利交易系统")
        st.markdown(f"**版本**: {APP_CONFIG.get('version', '2.0.0')}")
        
    st.markdown("---")
    
    # 欢迎信息
    st.markdown("""
    ## 🌟 欢迎使用ETF套利交易系统
    
    这是一个专业的ETF套利交易解决方案，提供完整的数据采集、策略回测、实时交易和风险控制功能。
    
    ### 📋 功能模块
    
    """)
    
    # 功能模块网格
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        #### 📊 数据管理
        - **数据采集**: 实时获取ETF价格数据
        - **数据存储**: 本地SQLite数据库管理  
        - **数据监控**: 数据质量和完整性检查
        
        #### 🔬 回测分析  
        - **策略回测**: 历史数据验证策略效果
        - **参数优化**: 智能寻找最优参数组合
        - **绩效分析**: 详细的收益风险分析报告
        """)
        
    with col2:
        st.markdown("""
        #### 🚀 实时交易
        - **信号监控**: 实时套利信号捕捉
        - **模拟交易**: 安全的策略验证环境
        - **风险控制**: 多层次风险管理机制
        
        #### 📈 系统监控
        - **性能监控**: 系统资源使用情况
        - **业务监控**: 交易策略运行状态  
        - **告警系统**: 异常情况及时通知
        """)
    
    st.markdown("---")
    
    # 快速状态概览
    st.subheader("📋 系统状态概览")
    
    col1, col2, col3, col4 = st.columns(4)
    
    # 获取系统状态（这里使用模拟数据，实际应用中需要连接真实的监控系统）
    with col1:
        st.metric(
            label="📊 数据连接状态",
            value="正常",
            delta="延迟: 50ms"
        )
    
    with col2:
        st.metric(
            label="💰 当前持仓",
            value="0股",
            delta="盈亏: ¥0"
        )
    
    with col3:
        st.metric(
            label="⚡ 活跃信号",
            value="2个",
            delta="置信度: 85%"
        )
    
    with col4:
        st.metric(
            label="🎯 今日收益率",
            value="0.00%",
            delta="相比昨日"
        )
    
    st.markdown("---")
    
    # 导航提示
    st.info("""
    💡 **使用提示**
    
    - 使用左侧导航栏切换到不同功能页面
    - 在侧边栏可以选择ETF标的和调整系统设置  
    - 建议先在"数据采集"页面启动数据收集，再进行回测和实时交易
    - 所有操作都会自动保存，可以安全退出和重新进入
    """)
    
    # 快速链接
    st.subheader("🚀 快速开始")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("📊 开始数据采集"):
            st.switch_page("pages/1_📊_数据采集.py")
            
    with col2:
        if st.button("🔬 运行策略回测"):
            st.switch_page("pages/2_🔬_回测分析.py")
            
    with col3:
        if st.button("🚀 原版逻辑回测"):
            st.switch_page("pages/6_🚀_回测分析(原版逻辑).py")
            
    with col4:
        if st.button("📈 启动实时监控"):
            st.switch_page("pages/3_🚀_实时交易.py")
    
    # 页脚信息
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: gray; margin-top: 50px;'>
        <small>ETF套利交易系统 v2.0.0 | 基于Streamlit多页面架构 | © 2024</small>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()