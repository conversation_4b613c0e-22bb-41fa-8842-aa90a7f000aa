#!/usr/bin/env python3
"""
性能优化测试和验证工具
测试各个优化模块的性能提升效果
"""

import sys
import time
import asyncio
import logging
from pathlib import Path
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from typing import Dict, List, Any

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_optimized_database_manager():
    """测试优化数据库管理器"""
    print("🔍 测试优化数据库管理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.optimized_database_manager import (
            get_optimized_db_manager, execute_optimized_query, read_optimized_dataframe
        )
        
        db_manager = get_optimized_db_manager()
        print("✅ 优化数据库管理器初始化成功")
        
        # 测试缓存查询
        start_time = time.time()
        query = "SELECT COUNT(*) as count FROM ticks WHERE symbol = ?"
        
        # 第一次查询（无缓存）
        result1 = execute_optimized_query(query, ('159740',))
        first_query_time = time.time() - start_time
        
        # 第二次查询（有缓存）
        start_time = time.time()
        result2 = execute_optimized_query(query, ('159740',))
        second_query_time = time.time() - start_time
        
        print(f"✅ 第一次查询时间: {first_query_time:.4f}秒")
        print(f"✅ 第二次查询时间: {second_query_time:.4f}秒")

        if second_query_time > 0 and second_query_time < first_query_time:
            speedup = first_query_time / second_query_time
            print(f"✅ 缓存加速比: {speedup:.2f}x")
        elif second_query_time == 0:
            print(f"✅ 缓存效果: 第二次查询几乎瞬时完成（缓存命中）")
        
        # 测试DataFrame查询
        df_query = "SELECT * FROM ticks WHERE symbol = ? LIMIT 100"
        df = read_optimized_dataframe(df_query, ('159740',))
        print(f"✅ DataFrame查询成功，返回 {len(df)} 行数据")
        
        # 获取性能报告
        report = db_manager.get_performance_report()
        print(f"✅ 性能报告: 缓存命中率 {report.get('query_performance', {}).get('cache_hit_rate', 0):.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化数据库管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vectorized_computing():
    """测试向量化计算"""
    print("🔍 测试向量化计算...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.vectorized_computing import (
            get_vectorized_computer, VectorizedComputing
        )
        
        computer = get_vectorized_computer()
        print("✅ 向量化计算引擎初始化成功")
        
        # 生成测试数据
        np.random.seed(42)
        test_data = pd.DataFrame({
            'price': 100 + np.cumsum(np.random.randn(1000) * 0.01),
            'volume': np.random.randint(1000, 10000, 1000),
            'timestamp': pd.date_range('2025-01-01', periods=1000, freq='1min')
        })
        
        # 测试技术指标计算
        start_time = time.time()
        indicators_df = computer.calculate_technical_indicators(
            test_data, 
            ['sma_20', 'ema_12', 'rsi_14', 'bollinger']
        )
        calculation_time = time.time() - start_time
        
        print(f"✅ 技术指标计算完成，耗时: {calculation_time:.4f}秒")
        print(f"✅ 计算了 {len(indicators_df.columns) - len(test_data.columns)} 个新指标")
        
        # 测试信号生成
        strategy_params = {
            'buy_trigger_drop': -0.002,
            'profit_target': 0.0025
        }
        
        start_time = time.time()
        signals_df = computer.generate_signals_vectorized(indicators_df, strategy_params)
        signal_time = time.time() - start_time
        
        print(f"✅ 信号生成完成，耗时: {signal_time:.4f}秒")
        
        buy_signals = signals_df['buy_signal'].sum()
        sell_signals = signals_df['sell_signal'].sum()
        print(f"✅ 生成买入信号: {buy_signals} 个，卖出信号: {sell_signals} 个")
        
        # 获取性能统计
        stats = computer.get_performance_stats()
        print(f"✅ 使用Numba加速: {stats['use_numba']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 向量化计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_optimizer():
    """测试内存优化器"""
    print("🔍 测试内存优化器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.memory_optimizer import (
            get_memory_optimizer, memory_optimized
        )
        
        optimizer = get_memory_optimizer()
        optimizer.start()
        print("✅ 内存优化器启动成功")
        
        # 测试DataFrame优化
        test_df = pd.DataFrame({
            'int_col': np.random.randint(0, 100, 10000),
            'float_col': np.random.random(10000),
            'str_col': ['test_string'] * 10000
        })
        
        original_memory = test_df.memory_usage(deep=True).sum() / 1024 / 1024
        print(f"✅ 原始DataFrame内存使用: {original_memory:.2f}MB")
        
        optimized_df = optimizer.optimize_dataframe(test_df)
        optimized_memory = optimized_df.memory_usage(deep=True).sum() / 1024 / 1024
        print(f"✅ 优化后DataFrame内存使用: {optimized_memory:.2f}MB")
        
        memory_savings = (1 - optimized_memory / original_memory) * 100
        print(f"✅ 内存节省: {memory_savings:.1f}%")
        
        # 测试缓存功能
        test_data = {'large_array': np.random.random(100000)}
        optimizer.cache_data('test_key', test_data)
        
        cached_data = optimizer.get_cached_data('test_key')
        print(f"✅ 缓存测试: {'成功' if cached_data is not None else '失败'}")
        
        # 测试装饰器
        @memory_optimized
        def test_function():
            return pd.DataFrame(np.random.random((1000, 10)))
        
        result_df = test_function()
        print(f"✅ 内存优化装饰器测试: 返回 {len(result_df)} 行数据")
        
        # 获取优化报告
        report = optimizer.get_optimization_report()
        print(f"✅ 优化报告生成成功")
        
        optimizer.stop()
        return True
        
    except Exception as e:
        print(f"❌ 内存优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_async_data_processor():
    """测试异步数据处理器"""
    print("🔍 测试异步数据处理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.async_data_processor import (
            get_async_processor, DataPoint
        )
        
        processor = await get_async_processor()
        await processor.start()
        print("✅ 异步数据处理器启动成功")
        
        # 创建数据流
        stream = processor.create_data_stream('test_stream', buffer_size=1000)
        print("✅ 数据流创建成功")
        
        # 测试数据发布和订阅
        received_data = []
        
        async def data_handler(data: DataPoint):
            received_data.append(data)
        
        await stream.subscribe(data_handler)
        
        # 发布测试数据
        test_data_points = []
        for i in range(10):
            data_point = DataPoint(
                symbol='TEST',
                timestamp=datetime.now(),
                price=100 + i,
                volume=1000 + i * 100,
                source='test'
            )
            test_data_points.append(data_point)
            await stream.publish(data_point)
        
        # 等待处理
        await asyncio.sleep(0.1)
        
        print(f"✅ 数据流测试: 发布 {len(test_data_points)} 个数据点，接收 {len(received_data)} 个")
        
        # 测试自定义处理器
        processed_count = 0
        
        async def custom_processor(data: DataPoint):
            nonlocal processed_count
            processed_count += 1
            await asyncio.sleep(0.01)  # 模拟处理时间
        
        processor.register_processor('custom_test', custom_processor)
        
        # 提交处理任务
        for data_point in test_data_points:
            await processor.process_data_async(data_point, 'custom_test')
        
        # 等待处理完成
        await asyncio.sleep(1)
        
        print(f"✅ 自定义处理器测试: 处理了 {processed_count} 个数据点")
        
        # 获取统计信息
        stats = processor.get_comprehensive_stats()
        print(f"✅ 统计信息获取成功")
        
        await processor.stop()
        return True
        
    except Exception as e:
        print(f"❌ 异步数据处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_comparison():
    """性能对比测试"""
    print("🔍 性能对比测试...")
    
    try:
        # 生成大量测试数据
        data_size = 50000
        test_data = pd.DataFrame({
            'price': 100 + np.cumsum(np.random.randn(data_size) * 0.01),
            'volume': np.random.randint(1000, 10000, data_size),
            'timestamp': pd.date_range('2025-01-01', periods=data_size, freq='1s')
        })
        
        print(f"✅ 生成测试数据: {data_size} 行")
        
        # 传统方式计算移动平均
        start_time = time.time()
        traditional_sma = test_data['price'].rolling(window=20).mean()
        traditional_time = time.time() - start_time
        
        print(f"✅ 传统方式SMA计算: {traditional_time:.4f}秒")
        
        # 向量化方式计算移动平均
        try:
            from etf_arbitrage_streamlit_multi.core.vectorized_computing import get_vectorized_computer
            
            computer = get_vectorized_computer()
            start_time = time.time()
            vectorized_result = computer.calculate_technical_indicators(test_data, ['sma_20'])
            vectorized_time = time.time() - start_time
            
            print(f"✅ 向量化方式SMA计算: {vectorized_time:.4f}秒")
            
            if vectorized_time > 0:
                speedup = traditional_time / vectorized_time
                print(f"✅ 向量化加速比: {speedup:.2f}x")
        
        except Exception as e:
            print(f"⚠️ 向量化计算测试跳过: {e}")
        
        # 内存使用对比
        try:
            from etf_arbitrage_streamlit_multi.core.memory_optimizer import get_memory_optimizer
            
            optimizer = get_memory_optimizer()
            
            original_memory = test_data.memory_usage(deep=True).sum() / 1024 / 1024
            optimized_data = optimizer.optimize_dataframe(test_data)
            optimized_memory = optimized_data.memory_usage(deep=True).sum() / 1024 / 1024
            
            memory_reduction = (1 - optimized_memory / original_memory) * 100
            print(f"✅ 内存优化效果: 减少 {memory_reduction:.1f}%")
        
        except Exception as e:
            print(f"⚠️ 内存优化测试跳过: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_performance_report():
    """生成性能优化报告"""
    print("📊 生成性能优化报告...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_results': {},
        'recommendations': []
    }
    
    try:
        # 数据库性能
        from etf_arbitrage_streamlit_multi.core.optimized_database_manager import get_optimized_db_manager
        db_manager = get_optimized_db_manager()
        db_report = db_manager.get_performance_report()
        report['test_results']['database'] = db_report
        
        if db_report.get('query_performance', {}).get('cache_hit_rate', 0) < 50:
            report['recommendations'].append("数据库缓存命中率较低，建议调整缓存策略")
    
    except Exception as e:
        print(f"⚠️ 数据库性能报告生成失败: {e}")
    
    try:
        # 计算性能
        from etf_arbitrage_streamlit_multi.core.vectorized_computing import get_vectorized_computer
        computer = get_vectorized_computer()
        compute_stats = computer.get_performance_stats()
        report['test_results']['computing'] = compute_stats
        
        if not compute_stats.get('use_numba', False):
            report['recommendations'].append("建议安装Numba以获得更好的计算性能")
    
    except Exception as e:
        print(f"⚠️ 计算性能报告生成失败: {e}")
    
    try:
        # 内存性能
        from etf_arbitrage_streamlit_multi.core.memory_optimizer import get_memory_optimizer
        optimizer = get_memory_optimizer()
        memory_report = optimizer.get_optimization_report()
        report['test_results']['memory'] = memory_report
        
        current_metrics = memory_report.get('memory_metrics', {})
        if current_metrics.get('memory_percent', 0) > 80:
            report['recommendations'].append("内存使用率过高，建议增加内存或优化数据结构")
    
    except Exception as e:
        print(f"⚠️ 内存性能报告生成失败: {e}")
    
    # 保存报告
    report_file = Path('performance_optimization_report.json')
    import json
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"✅ 性能优化报告已保存到: {report_file}")
    
    # 显示摘要
    print("\n📋 性能优化摘要:")
    for category, results in report['test_results'].items():
        print(f"  {category}: {'✅ 正常' if results else '❌ 异常'}")
    
    if report['recommendations']:
        print("\n💡 优化建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"  {i}. {rec}")
    
    return report

async def main():
    """主测试函数"""
    print("🚀 开始性能优化测试")
    print("=" * 60)
    
    tests = [
        ("数据库优化", test_optimized_database_manager),
        ("向量化计算", test_vectorized_computing),
        ("内存优化", test_memory_optimizer),
        ("异步处理", test_async_data_processor),
        ("性能对比", test_performance_comparison)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        print("-" * 40)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    # 生成性能报告
    print("\n" + "=" * 60)
    generate_performance_report()
    
    if passed == total:
        print("\n🎉 所有性能优化测试通过！")
        print("✅ 性能优化模块工作正常")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
