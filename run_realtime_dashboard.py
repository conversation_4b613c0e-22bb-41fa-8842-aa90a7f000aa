#!/usr/bin/env python3
"""
启动增强版实时交易模拟仪表板
"""

import subprocess
import sys
import os

def check_streamlit():
    """检查streamlit是否安装"""
    try:
        import streamlit
        return True
    except ImportError:
        return False

def install_streamlit():
    """安装streamlit"""
    print("正在安装 Streamlit...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "streamlit"])
    print("Streamlit 安装完成！")

def main():
    """主函数"""
    print("🚀 启动增强版实时交易模拟仪表板...")
    
    # 检查streamlit
    if not check_streamlit():
        print("未检测到 Streamlit，正在安装...")
        try:
            install_streamlit()
        except Exception as e:
            print(f"安装 Streamlit 失败: {e}")
            print("请手动安装: pip install streamlit")
            return
    
    # 启动仪表板
    # dashboard_file = "app_simple_realtime_dashboard.py"
    dashboard_file = "app_enhanced_realtime_dashboard.py"
    
    if not os.path.exists(dashboard_file):
        print(f"错误: 找不到文件 {dashboard_file}")
        return
    
    print(f"启动实时交易模拟仪表板: {dashboard_file}")
    print("浏览器将自动打开，如果没有请访问: http://localhost:8502")
    
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            dashboard_file,
            "--server.port", "8502",
            "--server.address", "localhost"
        ])
    except KeyboardInterrupt:
        print("\n实时交易模拟仪表板已停止")
    except Exception as e:
        print(f"启动失败: {e}")

if __name__ == "__main__":
    main()