#!/usr/bin/env python3
"""
启动增强版面板（包含智能优化功能）
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    required_files = [
        "app_enhanced_backtest_dashboard.py",
        "app_enhanced_realtime_dashboard.py",
        "ticks.db",
        "database/connection_pool.py",
        "test_market_analyzer.py",
        "test_rule_engine_simple.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"  - {file}")
        return False
    
    print("✅ 所有依赖文件存在")
    return True

def check_database():
    """检查数据库"""
    print("🔍 检查数据库...")
    
    try:
        import sqlite3
        conn = sqlite3.connect("ticks.db")
        cursor = conn.cursor()
        
        # 检查新表是否存在
        tables = ['optimal_configs', 'alert_rules', 'alert_history']
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ 表 {table}: {count} 条记录")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def run_backtest_dashboard():
    """启动回测面板"""
    print("🚀 启动增强版回测面板（包含智能优化）...")
    
    try:
        # 使用streamlit运行面板
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "app_enhanced_backtest_dashboard.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("📱 面板将在 http://localhost:8501 启动")
        print("🎯 新功能:")
        print("  - 智能参数优化（在预设配置中选择'智能优化'）")
        print("  - 市场特征分析")
        print("  - 参数敏感性分析")
        print("  - 优化结果保存和加载")
        
        # 启动面板
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出面板")
    except Exception as e:
        print(f"❌ 启动面板失败: {e}")

def run_realtime_dashboard():
    """启动实时面板"""
    print("🚀 启动增强版实时面板（包含预警系统）...")
    
    try:
        # 使用streamlit运行面板
        cmd = [
            sys.executable, "-m", "streamlit", "run", 
            "app_enhanced_realtime_dashboard.py",
            "--server.port", "8502",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("📱 面板将在 http://localhost:8502 启动")
        print("🎯 新功能:")
        print("  - 智能预警系统")
        print("  - 自定义预警规则")
        print("  - 预警历史记录")
        print("  - 实时条件监控")
        
        # 启动面板
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出面板")
    except Exception as e:
        print(f"❌ 启动面板失败: {e}")

def main():
    """主函数"""
    print("🎉 ETF套利系统 - 增强版面板启动器")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请确保所有文件存在")
        return
    
    # 检查数据库
    if not check_database():
        print("❌ 数据库检查失败，请先运行数据库初始化")
        return
    
    print("\n🎯 选择要启动的面板:")
    print("1. 回测面板（包含智能参数优化）")
    print("2. 实时面板（包含预警系统）")
    print("3. 同时启动两个面板")
    print("0. 退出")
    
    try:
        choice = input("\n请选择 (0-3): ").strip()
        
        if choice == "1":
            run_backtest_dashboard()
        elif choice == "2":
            run_realtime_dashboard()
        elif choice == "3":
            print("🚀 同时启动两个面板...")
            print("回测面板: http://localhost:8501")
            print("实时面板: http://localhost:8502")
            
            # 启动回测面板（后台）
            import threading
            backtest_thread = threading.Thread(target=run_backtest_dashboard)
            backtest_thread.daemon = True
            backtest_thread.start()
            
            # 启动实时面板（前台）
            run_realtime_dashboard()
            
        elif choice == "0":
            print("👋 退出")
        else:
            print("❌ 无效选择")
            
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
