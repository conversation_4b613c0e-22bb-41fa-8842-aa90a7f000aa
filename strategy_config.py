"""
策略参数统一配置文件
所有策略、回测、实时监控的参数都在此文件中统一管理
确保参数的一致性和正负符号的规范性
"""

from typing import Dict, Any, Tuple
from dataclasses import dataclass

@dataclass
class ParameterConfig:
    """参数配置类"""
    min_value: float
    max_value: float
    default_value: float
    step: float
    description: str

class StrategyConfig:
    """策略参数统一配置管理类"""
    
    # ==================== 核心策略参数 ====================
    
    # 交易信号参数
    SIGNAL_PARAMS = {
        'buy_trigger_drop': ParameterConfig(
            min_value=-0.01,      # 最小触发跌幅 -25%
            max_value=-0.0001,     # 最大触发跌幅 -0.01%
            default_value=-0.006, # 默认触发跌幅 -0.2%
            step=0.0001,
            description="买入触发跌幅（负值）"
        ),
        'signal_window': ParameterConfig(
            min_value=10,
            max_value=200,
            default_value=30,
            step=5,
            description="信号计算窗口（tick数）"
        )
    }
    
    # 风险控制参数
    RISK_PARAMS = {
        'stop_loss': ParameterConfig(
            min_value=-0.10,      # 最大止损 -10%
            max_value=-0.005,     # 最小止损 -0.5%
            default_value=-0.02,  # 默认止损 -2%
            step=0.001,
            description="止损线（负值）"
        ),
        'profit_target': ParameterConfig(
            min_value=0.0001,      # 最小止盈 0.01%
            max_value=0.05,       # 最大止盈 5%
            default_value=0.0050,  # 默认止盈 0.25%
            step=0.0001,
            description="基础止盈目标（正值）"
        ),
        'daily_loss_limit': ParameterConfig(
            min_value=-0.20,      # 最大日亏损 -20%
            max_value=-0.01,      # 最小日亏损 -1%
            default_value=-0.05,  # 默认日亏损 -5%
            step=0.01,
            description="日损失限制（负值）"
        ),
        'max_drawdown_limit': ParameterConfig(
            min_value=-0.30,      # 最大回撤 -30%
            max_value=-0.02,      # 最小回撤 -2%
            default_value=-0.10,  # 默认回撤 -10%
            step=0.01,
            description="最大回撤限制（负值）"
        )
    }
    
    # 仓位管理参数
    POSITION_PARAMS = {
        'max_position': ParameterConfig(
            min_value=10000,
            max_value=10000000,
            default_value=1000000,
            step=10000,
            description="最大持仓数量"
        ),
        'position_size': ParameterConfig(
            min_value=10000,
            max_value=1000000,
            default_value=100000,
            step=10000,
            description="单次买入仓位大小"
        ),
        'fund_buffer_ratio': ParameterConfig(
            min_value=0.01,
            max_value=0.20,
            default_value=0.05,
            step=0.01,
            description="资金缓冲比例（保留资金比例）"
        ),
        'layer1_ratio': ParameterConfig(
            min_value=0.1,
            max_value=0.8,
            default_value=0.4,
            step=0.05,
            description="第一层买入比例"
        ),
        'layer2_ratio': ParameterConfig(
            min_value=0.1,
            max_value=0.8,
            default_value=0.35,
            step=0.05,
            description="第二层买入比例"
        ),
        'layer3_ratio': ParameterConfig(
            min_value=0.1,
            max_value=0.8,
            default_value=0.25,
            step=0.05,
            description="第三层买入比例"
        )
    }
    
    # 时间控制参数
    TIME_PARAMS = {
        'enable_time_control': ParameterConfig(
            min_value=0,          # 布尔值
            max_value=1,          # 布尔值
            default_value=True,   # 默认开启时间控制
            step=1,
            description="是否启用时间控制功能"
        ),
        'max_hold_time': ParameterConfig(
            min_value=0,          # 0表示禁用
            max_value=86400,      # 最长24小时
            default_value=86400,  # 默认24小时
            step=300,
            description="最大持仓时间（秒）- 短期时间控制，0表示禁用"
        ),
        'min_hold_time': ParameterConfig(
            min_value=10,         # 最短10秒
            max_value=300,        # 最长5分钟
            default_value=30,     # 默认30秒
            step=10,
            description="最小持仓时间保护（秒）"
        ),
        'max_holding_days': ParameterConfig(
            min_value=0,          # 0表示禁用
            max_value=30,         # 最长30天
            default_value=7,      # 默认7天
            step=1,
            description="最大持仓天数 - 长期时间控制，0表示禁用"
        ),
        # 新增：交易时段控制参数
        'trading_session': ParameterConfig(
            min_value=0,          # 不使用数值范围
            max_value=2,          # 不使用数值范围
            default_value="全天",  # 默认全天交易
            step=1,
            description="允许交易时段（全天/上午/下午）"
        ),
        # 新增：平仓时间控制参数
        'close_before_morning': ParameterConfig(
            min_value=0,          # 布尔值
            max_value=1,          # 布尔值
            default_value=False,  # 默认不在上午收盘前平仓
            step=1,
            description="是否在上午收盘前（11:25）自动平仓"
        ),
        'close_before_afternoon': ParameterConfig(
            min_value=0,          # 布尔值
            max_value=1,          # 布尔值
            default_value=True,   # 默认在下午收盘前平仓
            step=1,
            description="是否在下午收盘前（14:55）自动平仓"
        )
    }
    
    # 分批交易参数
    PARTIAL_TRADE_PARAMS = {
        'partial_profit_multiplier1': ParameterConfig(
            min_value=0.2,
            max_value=2.0,
            default_value=0.5,
            step=0.1,
            description="第一次止盈倍数"
        ),
        'partial_profit_multiplier2': ParameterConfig(
            min_value=0.5,
            max_value=3.0,
            default_value=1.0,
            step=0.1,
            description="第二次止盈倍数"
        ),
        'partial_profit_multiplier3': ParameterConfig(
            min_value=1.0,
            max_value=5.0,
            default_value=2.0,
            step=0.1,
            description="第三次止盈倍数"
        ),
        'partial_sell_ratio1': ParameterConfig(
            min_value=0.1,
            max_value=1.0,
            default_value=0.3,
            step=0.05,
            description="第一次卖出比例"
        ),
        'partial_sell_ratio2': ParameterConfig(
            min_value=0.1,
            max_value=1.0,
            default_value=0.4,
            step=0.05,
            description="第二次卖出比例"
        ),
        'partial_sell_ratio3': ParameterConfig(
            min_value=0.1,
            max_value=1.0,
            default_value=0.3,
            step=0.05,
            description="第三次卖出比例"
        )
    }
    
    # 基础交易参数
    BASIC_PARAMS = {
        'initial_capital': ParameterConfig(
            min_value=10000.0,
            max_value=10000000.0,
            default_value=100000.0,
            step=10000.0,
            description="初始资金"
        ),
        'commission_rate': ParameterConfig(
            min_value=0.0,
            max_value=0.01,
            default_value=0.0003,
            step=0.0001,
            description="手续费率"
        ),
        'slippage': ParameterConfig(
            min_value=0.0,
            max_value=0.01,
            default_value=0.0001,
            step=0.0001,
            description="滑点"
        )
    }
    
    # 技术指标参数配置（保留原有的，以备扩展）
    TECHNICAL_PARAMS = {
        'ma_short': ParameterConfig(
            min_value=1,
            max_value=100,
            default_value=5,
            step=1,
            description="短期移动平均线周期"
        ),
        'ma_long': ParameterConfig(
            min_value=10,
            max_value=200,
            default_value=20,
            step=1,
            description="长期移动平均线周期"
        ),
        'rsi_period': ParameterConfig(
            min_value=2,
            max_value=50,
            default_value=14,
            step=1,
            description="RSI周期"
        )
    }
    
    # 收盘管理参数
    CLOSE_PARAMS = {
        'exit_at_close': ParameterConfig(
            min_value=0,
            max_value=1,
            default_value=1,
            step=1,
            description="是否在收盘前平仓 (0=否, 1=是)"
        )
    }
    
    # ==================== 预设配置 ====================
    
    STRATEGY_PRESETS = {
        "保守型": {
            'buy_trigger_drop': -0.004,
            'stop_loss': -0.015,
            'profit_target': 0.008,
            'max_hold_time': 1800,
            'max_position': 500000,
            'partial_profit_multiplier1': 0.3,
            'partial_profit_multiplier2': 0.6,
            'partial_profit_multiplier3': 1.2
        },
        "平衡型": {
            'buy_trigger_drop': -0.006,
            'stop_loss': -0.02,
            'profit_target': 0.006,
            'max_hold_time': 3600,
            'max_position': 1000000,
            'partial_profit_multiplier1': 0.5,
            'partial_profit_multiplier2': 1.0,
            'partial_profit_multiplier3': 2.0
        },
        "激进型": {
            'buy_trigger_drop': -0.008,
            'stop_loss': -0.025,
            'profit_target': 0.004,
            'max_hold_time': 7200,
            'max_position': 2000000,
            'partial_profit_multiplier1': 0.8,
            'partial_profit_multiplier2': 1.5,
            'partial_profit_multiplier3': 3.0
        }
    }
    
    # ==================== 统一参数获取方法 ====================
    
    @classmethod
    def get_all_params(cls) -> Dict[str, ParameterConfig]:
        """获取所有参数配置"""
        all_params = {}
        all_params.update(cls.SIGNAL_PARAMS)
        all_params.update(cls.RISK_PARAMS)
        all_params.update(cls.POSITION_PARAMS)
        all_params.update(cls.TIME_PARAMS)
        all_params.update(cls.PARTIAL_TRADE_PARAMS)
        all_params.update(cls.BASIC_PARAMS)
        all_params.update(cls.TECHNICAL_PARAMS)
        all_params.update(cls.CLOSE_PARAMS)
        return all_params
    
    @classmethod
    def get_param_config(cls, param_name: str) -> ParameterConfig:
        """获取指定参数的配置"""
        all_params = cls.get_all_params()
        if param_name not in all_params:
            raise ValueError(f"未知参数: {param_name}")
        return all_params[param_name]
    
    @classmethod
    def get_default_values(cls) -> Dict[str, float]:
        """获取所有参数的默认值"""
        all_params = cls.get_all_params()
        return {name: config.default_value for name, config in all_params.items()}
    
    @classmethod
    def get_preset_config(cls, preset_name: str) -> Dict[str, float]:
        """获取预设配置"""
        if preset_name not in cls.STRATEGY_PRESETS:
            raise ValueError(f"未知预设: {preset_name}")
        
        # 从默认值开始
        config = cls.get_default_values()
        # 应用预设覆盖
        config.update(cls.STRATEGY_PRESETS[preset_name])
        return config
    
    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> Dict[str, str]:
        """验证配置参数，返回错误信息"""
        errors = {}
        all_params = cls.get_all_params()

        for param_name, value in config.items():
            if param_name in all_params:
                param_config = all_params[param_name]

                # 特殊处理非数值参数
                if param_name == 'trading_session':
                    # 交易时段参数验证
                    if value not in ["全天", "上午", "下午"]:
                        errors[param_name] = f"交易时段值 '{value}' 无效，必须是：全天、上午、下午"
                elif param_name in ['close_before_morning', 'close_before_afternoon']:
                    # 布尔参数验证
                    if not isinstance(value, bool):
                        errors[param_name] = f"参数 '{param_name}' 必须是布尔值，当前值: {value}"
                else:
                    # 数值参数验证
                    try:
                        if isinstance(value, (int, float)):
                            if value < param_config.min_value:
                                errors[param_name] = f"值 {value} 小于最小值 {param_config.min_value}"
                            elif value > param_config.max_value:
                                errors[param_name] = f"值 {value} 大于最大值 {param_config.max_value}"
                    except TypeError:
                        # 如果无法比较，跳过验证
                        pass

        return errors
    
    @classmethod
    def get_streamlit_config(cls, param_name: str) -> Dict[str, Any]:
        """获取Streamlit组件配置"""
        param_config = cls.get_param_config(param_name)
        return {
            'min_value': param_config.min_value,
            'max_value': param_config.max_value,
            'value': param_config.default_value,
            'step': param_config.step,
            'help': param_config.description
        }


# ==================== 向后兼容的常量定义 ====================
# 为了保持与现有代码的兼容性，定义常量

# 获取默认配置
_DEFAULT_CONFIG = StrategyConfig.get_default_values()

# 导出常量（与strategy_engine_enhanced.py保持一致）
BUY_TRIGGER_DROP = _DEFAULT_CONFIG['buy_trigger_drop']
PROFIT_TARGET = _DEFAULT_CONFIG['profit_target']
STOP_LOSS = _DEFAULT_CONFIG['stop_loss']
MAX_HOLD_TIME = int(_DEFAULT_CONFIG['max_hold_time'])
MAX_POSITION = int(_DEFAULT_CONFIG['max_position'])
DAILY_LOSS_LIMIT = _DEFAULT_CONFIG['daily_loss_limit']
MAX_DRAWDOWN_LIMIT = _DEFAULT_CONFIG['max_drawdown_limit']

# 分层买入比例
LAYERS = (
    _DEFAULT_CONFIG['layer1_ratio'],
    _DEFAULT_CONFIG['layer2_ratio'],
    _DEFAULT_CONFIG['layer3_ratio']
)

# 分批卖出参数
PARTIAL_SELL_LEVELS = [
    _DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier1'],
    _DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier2'],
    _DEFAULT_CONFIG['profit_target'] * _DEFAULT_CONFIG['partial_profit_multiplier3']
]

PARTIAL_SELL_RATIOS = [
    _DEFAULT_CONFIG['partial_sell_ratio1'],
    _DEFAULT_CONFIG['partial_sell_ratio2'],
    _DEFAULT_CONFIG['partial_sell_ratio3']
]

# 时间参数
MIN_HOLD_TIME = int(_DEFAULT_CONFIG['min_hold_time'])

# 基础交易参数
INITIAL_CAPITAL = _DEFAULT_CONFIG['initial_capital']
COMMISSION_RATE = _DEFAULT_CONFIG['commission_rate']
SLIPPAGE = _DEFAULT_CONFIG['slippage']