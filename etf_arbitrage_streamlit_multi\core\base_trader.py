"""
统一交易器基类
提供所有交易器的通用接口和基础功能
"""

import threading
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from .database_manager import DatabaseManager
from .config_manager import ConfigManager
from .logger_manager import LoggerManager
from .exception_handler import ExceptionHandler, ErrorCategory, ErrorSeverity

# 获取核心服务
db_manager = DatabaseManager.get_instance()
config_manager = ConfigManager()
logger_manager = LoggerManager()
exception_handler = ExceptionHandler()

# 创建交易专用日志器
logger = logger_manager.create_trading_logger()

class TradeType(Enum):
    """交易类型"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

class PositionStatus(Enum):
    """持仓状态"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIAL = "PARTIAL"

@dataclass
class TradeSignal:
    """交易信号"""
    symbol: str
    signal_type: TradeType
    price: float
    quantity: int
    confidence: float
    reason: str
    timestamp: datetime
    signal_strength: float = 0.0
    volume: int = 0
    prev_close: float = 0.0

@dataclass
class TradeRecord:
    """交易记录"""
    id: str
    timestamp: datetime
    symbol: str
    action: TradeType
    quantity: int
    price: float
    amount: float
    commission: float
    reason: str
    pnl: float = 0.0

@dataclass
class Position:
    """持仓信息"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    status: PositionStatus
    open_time: datetime
    last_update: datetime

class BaseTrader(ABC):
    """交易器基类"""
    
    def __init__(self, initial_capital: float = None):
        # 使用配置管理器获取初始资金
        trading_config = config_manager.get_trading_config()
        self.initial_capital = initial_capital or trading_config.default_position_size
        
        # 基础状态
        self.is_running = False
        self.current_symbol = None
        self.stop_event = threading.Event()
        self.trader_thread = None
        
        # 交易数据
        self.positions: Dict[str, Position] = {}
        self.signals: List[TradeSignal] = []
        self.trades: List[TradeRecord] = []
        
        # 状态回调
        self.status_callbacks: List[Callable] = []
        
        # 统计信息
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'start_time': None,
            'last_signal_time': None,
            'signals_today': 0,
            'valid_signals_today': 0
        }
        
        logger.info(f"交易器初始化完成，初始资金: {self.initial_capital:,.2f}")
    
    @abstractmethod
    def _generate_signal(self, market_data: Dict) -> Optional[TradeSignal]:
        """生成交易信号（子类实现）"""
        pass
    
    @abstractmethod
    def _execute_signal(self, signal: TradeSignal) -> bool:
        """执行交易信号（子类实现）"""
        pass
    
    @abstractmethod
    def _trading_loop(self, symbol: str):
        """交易主循环（子类实现）"""
        pass
    
    def start_trading(self, symbol: str) -> bool:
        """启动交易"""
        if self.is_running:
            logger.warning("交易已在运行中")
            return False
        
        try:
            self.current_symbol = symbol
            self.is_running = True
            self.stop_event.clear()
            
            # 启动交易线程
            self.trader_thread = threading.Thread(
                target=self._trading_loop,
                args=(symbol,),
                daemon=True
            )
            self.trader_thread.start()
            
            # 更新统计信息
            self.stats['start_time'] = datetime.now()
            
            logger.info(f"开始交易: {symbol}")
            self._notify_status_change({
                'status': 'started',
                'symbol': symbol,
                'timestamp': datetime.now()
            })
            
            return True
            
        except Exception as e:
            exception_handler.handle_exception(
                e, ErrorCategory.TRADING_ERROR, ErrorSeverity.HIGH
            )
            self.is_running = False
            return False
    
    def stop_trading(self) -> bool:
        """停止交易"""
        if not self.is_running:
            logger.warning("交易未在运行")
            return False
        
        try:
            self.stop_event.set()
            self.is_running = False
            
            # 等待交易线程结束
            if self.trader_thread and self.trader_thread.is_alive():
                self.trader_thread.join(timeout=5)
            
            logger.info("交易已停止")
            self._notify_status_change({
                'status': 'stopped',
                'timestamp': datetime.now()
            })
            
            return True
            
        except Exception as e:
            exception_handler.handle_exception(
                e, ErrorCategory.TRADING_ERROR, ErrorSeverity.MEDIUM
            )
            return False
    
    def add_status_callback(self, callback: Callable):
        """添加状态回调"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, status_data: Dict):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(status_data)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取交易状态"""
        return {
            'is_running': self.is_running,
            'current_symbol': self.current_symbol,
            'positions': {k: v.__dict__ for k, v in self.positions.items()},
            'stats': self.stats.copy(),
            'recent_signals': [s.__dict__ for s in self.signals[-10:]],
            'recent_trades': [t.__dict__ for t in self.trades[-10:]]
        }
    
    def update_strategy_params(self, params: Dict):
        """更新策略参数"""
        # 子类可以重写此方法
        logger.info(f"更新策略参数: {params}")
    
    def save_trade_record(self, trade: TradeRecord):
        """保存交易记录到数据库"""
        try:
            query = '''
                INSERT INTO trade_records 
                (id, timestamp, symbol, action, quantity, price, amount, commission, reason, pnl)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            '''
            params = (
                trade.id, trade.timestamp.isoformat(), trade.symbol,
                trade.action.value, trade.quantity, trade.price,
                trade.amount, trade.commission, trade.reason, trade.pnl
            )
            
            db_manager.execute_update(query, params, 'backtest')
            logger.debug(f"交易记录已保存: {trade.id}")
            
        except Exception as e:
            logger.error(f"保存交易记录失败: {e}")
    
    def get_position_summary(self) -> Dict[str, Any]:
        """获取持仓汇总"""
        total_value = 0.0
        total_pnl = 0.0
        
        for position in self.positions.values():
            total_value += position.quantity * position.current_price
            total_pnl += position.unrealized_pnl + position.realized_pnl
        
        return {
            'total_positions': len(self.positions),
            'total_value': total_value,
            'total_pnl': total_pnl,
            'available_capital': self.initial_capital - total_value + total_pnl
        }
    
    def calculate_performance_metrics(self) -> Dict[str, float]:
        """计算性能指标"""
        if not self.trades:
            return {}
        
        # 计算基础指标
        total_trades = len(self.trades)
        winning_trades = sum(1 for t in self.trades if t.pnl > 0)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        
        total_pnl = sum(t.pnl for t in self.trades)
        avg_pnl = total_pnl / total_trades if total_trades > 0 else 0
        
        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'total_pnl': total_pnl,
            'avg_pnl': avg_pnl,
            'total_return': total_pnl / self.initial_capital if self.initial_capital > 0 else 0
        }
