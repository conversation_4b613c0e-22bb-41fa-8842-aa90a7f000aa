"""
侧边栏组件
"""

import streamlit as st
from datetime import datetime
from typing import Dict, Any

def render_sidebar():
    """渲染统一的侧边栏"""
    with st.sidebar:
        # 应用Logo和标题
        st.markdown("""
        <div style='text-align: center; padding: 20px 0;'>
            <h2 style='color: #1f77b4; margin: 0;'>🎯 ETF系统</h2>
            <p style='margin: 5px 0; color: #666; font-size: 14px;'>专业交易解决方案</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        # 标的选择
        st.subheader("📊 交易标的")
        symbol_options = {
            "159740": "恒生科技ETF",
            "159915": "创业板ETF", 
            "159919": "沪深300ETF",
            "512880": "券商ETF"
        }
        
        current_symbol = st.selectbox(
            "选择ETF标的",
            options=list(symbol_options.keys()),
            format_func=lambda x: f"{x} - {symbol_options[x]}",
            index=0 if st.session_state.get('current_symbol', '159740') not in symbol_options else list(symbol_options.keys()).index(st.session_state.get('current_symbol', '159740')),
            key="sidebar_symbol_select"
        )
        
        # 更新session state
        if current_symbol != st.session_state.get('current_symbol'):
            st.session_state.current_symbol = current_symbol
            st.rerun()
        
        st.markdown("---")
        
        # 系统设置
        st.subheader("⚙️ 系统设置")
        
        # 自动刷新
        auto_refresh = st.checkbox(
            "自动刷新数据",
            value=st.session_state.get('user_settings', {}).get('auto_refresh', True),
            key="sidebar_auto_refresh"
        )
        
        if auto_refresh:
            refresh_interval = st.slider(
                "刷新间隔(秒)",
                min_value=5,
                max_value=60,
                value=st.session_state.get('user_settings', {}).get('refresh_interval', 10),
                key="sidebar_refresh_interval"
            )
        else:
            refresh_interval = 10
        
        # 通知设置
        notifications = st.checkbox(
            "启用通知",
            value=st.session_state.get('user_settings', {}).get('notification_enabled', True),
            key="sidebar_notifications"
        )
        
        # 更新用户设置
        if 'user_settings' not in st.session_state:
            st.session_state.user_settings = {}
            
        st.session_state.user_settings.update({
            'auto_refresh': auto_refresh,
            'refresh_interval': refresh_interval,
            'notification_enabled': notifications
        })
        
        st.markdown("---")
        
        # 系统状态概览
        st.subheader("📈 系统状态")
        
        # 获取系统状态
        data_status = st.session_state.get('data_collection_status', {})
        trading_status = st.session_state.get('trading_status', {}) 
        monitoring_status = st.session_state.get('monitoring_status', {})
        
        # 数据采集状态
        data_running = data_status.get('is_running', False)
        st.markdown(f"""
        **数据采集**: {"🟢 运行中" if data_running else "🔴 已停止"}  
        **记录数**: {data_status.get('total_records', 0):,}
        """)
        
        # 交易状态
        trading_running = trading_status.get('is_running', False)
        st.markdown(f"""
        **实时交易**: {"🟢 监控中" if trading_running else "🔴 未启动"}  
        **今日P&L**: ¥{trading_status.get('today_pnl', 0):,.2f}
        """)
        
        # 监控状态  
        monitoring_running = monitoring_status.get('is_monitoring', False)
        health = monitoring_status.get('system_health', 'good')
        health_icon = {"good": "🟢", "warning": "🟡", "error": "🔴"}.get(health, "🔴")
        
        st.markdown(f"""
        **系统监控**: {"🟢 正常" if monitoring_running else "🔴 离线"}  
        **健康状态**: {health_icon} {health.upper()}
        """)
        
        st.markdown("---")
        
        # 快速操作
        st.subheader("🚀 快速操作")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📊 数据", help="跳转到数据采集页面"):
                st.switch_page("pages/1_📊_数据采集.py")
                
            if st.button("🚀 交易", help="跳转到实时交易页面"):
                st.switch_page("pages/3_🚀_实时交易.py")
        
        with col2:
            if st.button("🔬 回测", help="跳转到回测分析页面"):
                st.switch_page("pages/2_🔬_回测分析.py")
                
            if st.button("📈 监控", help="跳转到系统监控页面"):
                st.switch_page("pages/4_📈_系统监控.py")
        
        st.markdown("---")
        
        # 页脚信息
        st.markdown("""
        <div style='text-align: center; color: #666; font-size: 12px; margin-top: 30px;'>
            <p>ETF套利系统 v2.0</p>
            <p>多页面Streamlit架构</p>
        </div>
        """, unsafe_allow_html=True)

def render_status_indicators(show_details: bool = True):
    """渲染状态指示器"""
    col1, col2, col3, col4 = st.columns(4)
    
    # 获取状态数据
    data_status = st.session_state.get('data_collection_status', {})
    trading_status = st.session_state.get('trading_status', {})
    backtest_status = st.session_state.get('backtest_status', {})
    monitoring_status = st.session_state.get('monitoring_status', {})
    
    with col1:
        status = "运行" if data_status.get('is_running', False) else "停止"
        color = "normal" if data_status.get('is_running', False) else "inverse"
        st.metric(
            label="📊 数据采集",
            value=status,
            delta=f"{data_status.get('total_records', 0)} 条记录" if show_details else None
        )
    
    with col2:
        status = "监控" if trading_status.get('is_running', False) else "离线"
        pnl = trading_status.get('today_pnl', 0)
        st.metric(
            label="🚀 实时交易",
            value=status,
            delta=f"¥{pnl:+.2f}" if show_details and pnl != 0 else None
        )
    
    with col3:
        status = "运行" if backtest_status.get('is_running', False) else "空闲"
        st.metric(
            label="🔬 回测引擎",
            value=status,
            delta="优化中" if backtest_status.get('optimization_running', False) and show_details else None
        )
    
    with col4:
        health = monitoring_status.get('system_health', 'unknown')
        health_map = {'good': '良好', 'warning': '警告', 'error': '错误', 'unknown': '未知'}
        alert_count = len(monitoring_status.get('alerts', []))
        st.metric(
            label="📈 系统监控", 
            value=health_map.get(health, '未知'),
            delta=f"{alert_count} 个告警" if show_details and alert_count > 0 else None
        )

def render_page_header(title: str, description: str = None, icon: str = None):
    """渲染页面标题头部"""
    col1, col2, col3 = st.columns([1, 8, 1])
    
    with col2:
        if icon:
            st.markdown(f"# {icon} {title}")
        else:
            st.markdown(f"# {title}")
            
        if description:
            st.markdown(f"*{description}*")
    
    st.markdown("---")

def render_refresh_button(key: str = "refresh", help_text: str = "刷新当前页面数据"):
    """渲染刷新按钮"""
    col1, col2, col3 = st.columns([10, 1, 1])
    
    with col2:
        if st.button("🔄", key=key, help=help_text):
            st.rerun()
    
    with col3:
        auto_refresh = st.session_state.get('user_settings', {}).get('auto_refresh', True)
        if auto_refresh:
            interval = st.session_state.get('user_settings', {}).get('refresh_interval', 10)
            st.markdown(f"<small>自动刷新: {interval}s</small>", unsafe_allow_html=True)