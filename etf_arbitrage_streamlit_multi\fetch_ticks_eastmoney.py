# fetch_ticks_eastmoney.py
"""
从东方财富逐笔页面抓取逐笔（tick）数据并写入本地 SQLite。
目标页面: https://quote.eastmoney.com/sz159740.html
实现方法:
- 使用 Playwright headless 浏览器加载页面并监听网络请求
- 捕获逐笔数据对应的接口（通常是 websockets 或XHR返回的JSON）
- 解析并写入本地 SQLite ticks 表

注意: 需要安装 playwright 并运行 `playwright install`

用法:
python fetch_ticks_eastmoney.py --symbol 159740
"""
import asyncio
import json
import sqlite3
import argparse
from pathlib import Path
from datetime import datetime, time as dt_time, timedelta
from playwright.async_api import async_playwright
import time
import requests
import logging
from logging.handlers import TimedRotatingFileHandler
from typing import List, Optional, Tuple, Dict
# fetch_ticks_eastmoney.py
"""
从东方财富逐笔页面抓取逐笔（tick）数据并写入本地 SQLite。
目标页面: https://quote.eastmoney.com/sz159740.html
实现方法:
- 使用 Playwright headless 浏览器加载页面并监听网络请求
- 捕获逐笔数据对应的接口（通常是 websockets 或XHR返回的JSON）
- 解析并写入本地 SQLite ticks 表

注意: 需要安装 playwright 并运行 `playwright install`

用法:
python fetch_ticks_eastmoney.py --symbol 159740
"""

# 日志配置：每天轮转，保留 7 天
LOG_PATH = Path(__file__).resolve().parent / 'fetch_ticks.log'
logger = logging.getLogger('fetch_ticks')
if not logger.handlers:
    logger.setLevel(logging.INFO)
    handler = TimedRotatingFileHandler(LOG_PATH, when='midnight', backupCount=7, encoding='utf-8')
    fmt = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(fmt)
    logger.addHandler(handler)
    # 也输出到控制台
    console = logging.StreamHandler()
    console.setFormatter(fmt)
    logger.addHandler(console)

DB_PATH = Path(__file__).resolve().parent / "ticks.db"


def market_of(code: str) -> str:
    """
    输入 6 位纯数字代码，返回 'sh' / 'sz' / 'bj' / 'UNKNOWN'
    先判断基金，再判断股票
    """
    code = str(code).zfill(6)

    # 1) 基金
    if code.startswith('5'):
        return 'sh'
    if code.startswith(('1', '159', '16', '168', '169')):
        return 'sz'

    # 2) 股票
    if code.startswith(('60', '688', '900')):
        return 'sh'
    if code.startswith(('000', '001', '002', '300', '200')):
        return 'sz'

    # 3) 北交所 & 其他
    if code.startswith(('83', '87', '43')):
        return 'bj'

    return 'UNKNOWN'


def secid_prefix_for_market(market: str) -> str:
    """将 market ('sh'/'sz'/'bj') 转换为 push2 接口使用的 secid 前缀数字。
    默认为 '0' (深圳)。
    """
    if market == 'sh':
        return '1'
    if market == 'sz':
        return '0'
    if market == 'bj':
        return '2'
    return '0'


# 插入 rows: list of tuples(symbol, tick_time_iso, price, volume, side, raw)

def insert_ticks(rows: List[Tuple[str, str, float, int, Optional[str], str]]) -> None:
    if not rows:
        return
    conn = sqlite3.connect(DB_PATH)
    c = conn.cursor()
    try:
        before = conn.total_changes
    except Exception:
        before = 0
    # 使用 INSERT OR IGNORE 来避免违反唯一索引的重复插入
    c.executemany("INSERT OR IGNORE INTO ticks(symbol, tick_time, price, volume, side, raw) VALUES(?,?,?,?,?,?)", rows)
    try:
        after = conn.total_changes
    except Exception:
        after = before
    inserted = after - before
    duplicates = len(rows) - inserted
    # 更新 last_ticks 表中的 last_time 为这批 rows 中的最大时间
    try:
        times = [r[1] for r in rows]
        max_time = max(times)
        symbol = rows[0][0]
        c.execute("INSERT OR REPLACE INTO last_ticks(symbol, last_time) VALUES(?,?)", (symbol, max_time))
    except Exception:
        pass
    conn.commit()
    conn.close()
    msg = f"inserted: {inserted}, duplicates ignored: {duplicates}, symbol: {rows[0][0]}"
    print(msg)
    logger.info(msg)


def is_trading_time() -> bool:
    """
    判断当前是否是交易时间
    交易时间: 周一至周五 9:30-11:30, 13:00-15:00
    """
    now = datetime.now()
    # 周末不交易
    if now.weekday() >= 5:  # 5=周六, 6=周日
        return False
    
    # 判断时间段
    current_time = now.time()
    morning_start = dt_time(9, 30)
    morning_end = dt_time(11, 30)
    afternoon_start = dt_time(13, 0)
    afternoon_end = dt_time(15, 0)
    
    # 早盘或午盘交易时间
    return ((current_time >= morning_start and current_time <= morning_end) or
            (current_time >= afternoon_start and current_time <= afternoon_end))


def get_next_trading_session_wait_seconds() -> int:
    """
    计算距离下一个交易时段开始还有多少秒
    """
    now = datetime.now()
    current_time = now.time()
    today = now.date()
    
    # 定义交易时间段
    morning_start = dt_time(9, 30)
    morning_end = dt_time(11, 30)
    afternoon_start = dt_time(13, 0)
    afternoon_end = dt_time(15, 0)
    
    # 计算今天的各个时间点
    today_morning_start = datetime.combine(today, morning_start)
    today_morning_end = datetime.combine(today, morning_end)
    today_afternoon_start = datetime.combine(today, afternoon_start)
    today_afternoon_end = datetime.combine(today, afternoon_end)
    
    # 如果当前是工作日
    if now.weekday() < 5:  # 0-4 表示周一至周五
        # 如果当前时间在早盘开始前
        if current_time < morning_start:
            return int((today_morning_start - now).total_seconds())
        # 如果当前时间在早盘结束后，午盘开始前
        elif morning_end < current_time < afternoon_start:
            return int((today_afternoon_start - now).total_seconds())
        # 如果当前时间在午盘结束后
        elif current_time > afternoon_end:
            # 计算到下一个工作日早盘的时间
            next_day = today + timedelta(days=1)
            # 如果下一天是周末，则计算到下周一
            if next_day.weekday() >= 5:
                next_day = today + timedelta(days=(7 - today.weekday()))
            next_morning_start = datetime.combine(next_day, morning_start)
            return int((next_morning_start - now).total_seconds())
    else:  # 如果当前是周末
        # 计算到下周一早盘的时间（周六=2天，周日=1天）
        days_to_monday = (7 - now.weekday()) % 7
        if days_to_monday == 0:
            days_to_monday = 1
        next_monday = today + timedelta(days=days_to_monday)
        next_morning_start = datetime.combine(next_monday, morning_start)
        return int((next_morning_start - now).total_seconds())
    
    # 默认情况，如果已经在交易时间内，返回0
    return 0


async def run(symbol: str, dump_dir: str = None, listen_seconds: int = 65, continuous: bool = False):
    """
    使用Playwright监听东方财富网页获取逐笔数据
    
    Args:
        symbol: 股票/ETF代码
        dump_dir: 保存响应样本的目录
        listen_seconds: 单次监听的秒数
        continuous: 是否持续监听（在交易时间内）
    """
    market = market_of(symbol)
    if market == 'UNKNOWN':
        logger.warning(f"market_of returned UNKNOWN for {symbol}, defaulting to 'sz'")
        market = 'sz'
    url = f"https://quote.eastmoney.com/{market}{symbol}.html"
    
    while True:
        # 检查是否在交易时间内
        if continuous and not is_trading_time():
            wait_seconds = get_next_trading_session_wait_seconds()
            logger.info(f"当前不是交易时间，等待 {wait_seconds} 秒后重试")
            print(f"当前不是交易时间，等待 {wait_seconds} 秒后重试")
            await asyncio.sleep(min(wait_seconds, 300))  # 最多等待5分钟，然后重新检查
            continue
            
        logger.info(f"开始监听 {symbol} 的逐笔数据")
        try:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()

                dump_path = None
                if dump_dir:
                    dump_path = Path(dump_dir)
                    dump_path.mkdir(parents=True, exist_ok=True)

                async def on_request(request):
                    # 可用于调试请求
                    return

                async def on_response(response):
                    try:
                        req = response.request
                        resp_url = req.url if req else response.url
                        # 只处理可能包含逐笔的请求
                        if not any(x in resp_url for x in ("push2","tick","trades","l2","trade")):
                            return

                        # 读取响应内容，兼容 text() 或 body()
                        txt = None
                        try:
                            txt = await response.text()
                        except Exception:
                            try:
                                b = await response.body()
                                txt = b.decode('utf-8', errors='ignore') if b else None
                            except Exception:
                                return

                        if not txt:
                            return

                        # 将原始响应写入样本目录（如用户需要），便于贴样本到我这儿进行定制解析
                        if dump_path:
                            try:
                                # Windows 文件名不能包含某些字符（:?<>\\|*"/），使用 URL 的短哈希作为文件名，避免非法字符
                                import hashlib
                                h = hashlib.sha256(resp_url.encode('utf-8')).hexdigest()[:12]
                                fn = dump_path / f"{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}_{h}.txt"
                                fn.write_text(txt, encoding='utf-8')
                                print(f"dumped response -> {fn}")
                            except Exception as e:
                                print("dump write error:", e)

                        # 尝试解析 JSON
                        try:
                            # 兼容 JSONP 回调（如 jQueryxxxx({...});），去掉回调包装再解析
                            import re
                            m = re.search(r'^[^(]*\((.*)\)\s*;?\s*$', txt, flags=re.S)
                            payload_text = m.group(1) if m else txt
                            data = json.loads(payload_text)
                        except Exception:
                            return

                        # 首先初始化 rows
                        # 只在解析到 data.details 时写入 ticks，忽略所有 fallback 解析，保证写入高置信度逐笔数据
                        rows = []
                        details_parsed = False
                        try:
                            if isinstance(data, dict) and 'data' in data and isinstance(data['data'], dict) and 'details' in data['data']:
                                details = data['data']['details']
                                if isinstance(details, list) and details:
                                    for item in details:
                                        try:
                                            parts = item.split(',')
                                            if len(parts) >= 3:
                                                t = parts[0].strip()
                                                price = float(parts[1])
                                                vol = int(parts[2])
                                                flag = parts[4].strip() if len(parts) > 4 else None
                                                side = None
                                                if flag == '1':
                                                    side = 'B'
                                                elif flag == '2':
                                                    side = 'S'
                                                ts = datetime.now().strftime('%Y-%m-%d ') + t if isinstance(t, str) and ':' in t else (t or datetime.utcnow().isoformat())
                                                rows.append((symbol, ts, price, vol, side, json.dumps({'detail': item}, ensure_ascii=False)))
                                                details_parsed = True
                                        except Exception:
                                            continue
                        except Exception:
                            details_parsed = False

                        # 仅当通过 details 成功解析到逐笔时写入数据库
                        if not details_parsed:
                            rows = []
                        
                        if rows:
                            insert_ticks(rows)
                            print(f"inserted {len(rows)} rows from {resp_url}")

                    except Exception as exc:
                        print("on_response parse error:", exc)

                page.on("request", on_request)
                page.on("response", on_response)

                await page.goto(url)
                print("loaded", url)
                # 等待并监听一段时间以捕获逐笔请求
                await page.wait_for_timeout(5000)
                
                # 计算实际监听时间
                actual_listen_seconds = listen_seconds
                if continuous:
                    # 如果是持续监听模式，计算到当前交易时段结束还有多少秒
                    now = datetime.now()
                    current_time = now.time()
                    
                    if current_time < dt_time(11, 30) and current_time >= dt_time(9, 30):
                        # 早盘
                        end_time = datetime.combine(now.date(), dt_time(11, 30))
                        remaining_seconds = int((end_time - now).total_seconds())
                        actual_listen_seconds = min(remaining_seconds, listen_seconds)
                    elif current_time < dt_time(15, 0) and current_time >= dt_time(13, 0):
                        # 午盘
                        end_time = datetime.combine(now.date(), dt_time(15, 0))
                        remaining_seconds = int((end_time - now).total_seconds())
                        actual_listen_seconds = min(remaining_seconds, listen_seconds)
                
                logger.info(f"监听 {actual_listen_seconds} 秒")
                await page.wait_for_timeout(actual_listen_seconds * 1000)
                await browser.close()
                
                # 如果不是持续监听模式，退出循环
                if not continuous:
                    break
                
                # 如果已经不在交易时间，等待下一个交易时段
                if not is_trading_time():
                    wait_seconds = get_next_trading_session_wait_seconds()
                    logger.info(f"交易时段结束，等待 {wait_seconds} 秒后继续")
                    print(f"交易时段结束，等待 {wait_seconds} 秒后继续")
                    await asyncio.sleep(min(wait_seconds, 300))  # 最多等待5分钟，然后重新检查
        
        except Exception as e:
            logger.error(f"监听过程中发生错误: {e}")
            print(f"监听过程中发生错误: {e}")
            # 如果是持续监听模式，等待一段时间后重试
            if continuous:
                await asyncio.sleep(30)  # 等待30秒后重试
            else:
                break


def get_last_time(symbol: str) -> Optional[str]:
    try:
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        row = c.execute("SELECT last_time FROM last_ticks WHERE symbol=?", (symbol,)).fetchone()
        conn.close()
        if not row:
            return None
        return row[0]
    except Exception:
        return None


def parse_detail_to_row(symbol: str, detail: str, server_date: Optional[int] = None) -> Optional[Tuple[str, str, float, int, Optional[str], str]]:
    # detail: "HH:MM:SS,price,volume,?,flag"
    parts = detail.split(',')
    if len(parts) < 3:
        return None
    t = parts[0].strip()
    try:
        price = float(parts[1])
    except Exception:
        return None
    try:
        vol = int(parts[2])
    except Exception:
        vol = 0
    flag = parts[4].strip() if len(parts) > 4 else None
    side = None
    if flag == '1':
        side = 'B'
    elif flag == '2':
        side = 'S'
    # combine with server_date if provided,否则用本地日期
    if server_date is not None:
        try:
            ts_int = int(server_date)
            if ts_int > 1_000_000_000_000:
                ts_int = ts_int // 1000
            date_str = datetime.fromtimestamp(ts_int).strftime('%Y-%m-%d')
        except Exception:
            date_str = datetime.now().strftime('%Y-%m-%d')
    else:
        date_str = datetime.now().strftime('%Y-%m-%d')
    ts = date_str + ' ' + t if ':' in t else (t or datetime.utcnow().isoformat())
    raw = json.dumps({'detail': detail}, ensure_ascii=False)
    return (symbol, ts, price, vol, side, raw)


def fetch_details_api(symbol: str, count: int = 200) -> Tuple[Optional[List[str]], Optional[int]]:
    # 使用 push2 API 拉取 recent details，返回 parsed detail strings list
    # secid 0.{symbol} for SZ
    url = 'https://push2.eastmoney.com/api/qt/stock/details/get'
    market = market_of(symbol)
    prefix = secid_prefix_for_market(market)
    params = {
        'secid': f'{prefix}.{symbol}',
        'fields1': 'f1,f2,f3,f4',
        'fields2': 'f51,f52,f53,f54,f55',
        'pos': f'-{count}',
        'fltt': '2',
    }
    try:
        r = requests.get(url, params=params, timeout=10)
        txt = r.text
        # strip jsonp
        import re
        m = re.search(r'^[^(]*\((.*)\)\s*;?\s*$', txt, flags=re.S)
        payload_text = m.group(1) if m else txt
        data = json.loads(payload_text)
        if isinstance(data, dict) and 'data' in data and isinstance(data['data'], dict) and 'details' in data['data']:
            # 尝试提取服务器时间戳（常见字段如 f86 或 t 别名）
            server_ts = None
            d = data['data']
            for k in ('f86','t','serverTime','time'):
                if k in d:
                    server_ts = d.get(k)
                    break
            return d.get('details'), server_ts
    except Exception as e:
        print('api fetch error:', e)
    return None, None


def api_poll_loop(symbol: str, poll_seconds: int = 5, count: int = 200, continuous: bool = False) -> None:
    """
    使用API轮询获取逐笔数据
    
    Args:
        symbol: 股票/ETF代码
        poll_seconds: 轮询间隔秒数
        count: 每次获取的记录数
        continuous: 是否持续监听（在交易时间内）
    """
    print('start api poll for', symbol)
    
    while True:
        # 检查是否在交易时间内
        if continuous and not is_trading_time():
            wait_seconds = get_next_trading_session_wait_seconds()
            logger.info(f"当前不是交易时间，等待 {wait_seconds} 秒后重试")
            print(f"当前不是交易时间，等待 {wait_seconds} 秒后重试")
            time.sleep(min(wait_seconds, 300))  # 最多等待5分钟，然后重新检查
            continue
            
        try:
            details, server_ts = fetch_details_api(symbol, count=count)
            if not details:
                time.sleep(poll_seconds)
                continue
            last_time = get_last_time(symbol)
            # parse last_time to datetime
            last_dt = None
            if last_time:
                try:
                    last_dt = datetime.fromisoformat(last_time)
                except Exception:
                    last_dt = None
            rows_to_insert = []
            for d in details:
                row = parse_detail_to_row(symbol, d, server_date=server_ts)
                if not row:
                    continue
                # compare time
                try:
                    row_dt = datetime.fromisoformat(row[1])
                except Exception:
                    row_dt = None
                if last_dt and row_dt and row_dt <= last_dt:
                    continue
                rows_to_insert.append(row)
            if rows_to_insert:
                insert_ticks(rows_to_insert)
                print(f'api inserted {len(rows_to_insert)} rows')
        except Exception as e:
            print('api_poll_loop error:', e)
            logger.error(f"API轮询错误: {e}")
            
        # 如果不是持续监听模式，退出循环
        if not continuous:
            break
            
        time.sleep(poll_seconds)


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--symbol', default='159740')
    parser.add_argument('--dump-dir', default=None, help='保存捕获响应样本的目录')
    parser.add_argument('--listen-seconds', type=int, default=65, help='页面监听秒数')
    parser.add_argument('--api-poll', action='store_true', help='Use direct API polling instead of Playwright')
    parser.add_argument('--poll-seconds', type=int, default=5, help='API poll interval seconds')
    parser.add_argument('--count', type=int, default=200, help='Number of recent ticks to fetch per API call')
    parser.add_argument('--continuous', action='store_true', help='在交易时间内持续监听')
    args = parser.parse_args()
    
    # 确保数据库表存在
    try:
        conn = sqlite3.connect(DB_PATH)
        c = conn.cursor()
        c.execute('''CREATE TABLE IF NOT EXISTS ticks
                     (symbol TEXT, tick_time TEXT, price REAL, volume INTEGER, side TEXT, raw TEXT,
                      PRIMARY KEY (symbol, tick_time))''')
        c.execute('''CREATE TABLE IF NOT EXISTS last_ticks
                     (symbol TEXT PRIMARY KEY, last_time TEXT)''')
        conn.commit()
        conn.close()
    except Exception as e:
        logger.error(f"初始化数据库失败: {e}")
        print(f"初始化数据库失败: {e}")
    
    if args.api_poll:
        api_poll_loop(args.symbol, poll_seconds=args.poll_seconds, count=args.count, continuous=args.continuous)
    else:
        asyncio.run(run(args.symbol, args.dump_dir, args.listen_seconds, continuous=args.continuous))
