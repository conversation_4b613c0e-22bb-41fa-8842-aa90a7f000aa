"""
服务注册器
提供统一的服务注册、发现和管理功能
"""

import logging
import threading
from typing import Dict, Any, Optional, Type, Callable, List
from datetime import datetime
from enum import Enum
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class ServiceStatus(Enum):
    """服务状态"""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"

class ServiceType(Enum):
    """服务类型"""
    DATA_COLLECTOR = "data_collector"
    TRADER = "trader"
    MONITOR = "monitor"
    DATABASE = "database"
    CONFIG = "config"
    LOGGER = "logger"
    STRATEGY = "strategy"
    RISK_MANAGER = "risk_manager"

class BaseService(ABC):
    """基础服务接口"""
    
    def __init__(self, name: str, service_type: ServiceType):
        self.name = name
        self.service_type = service_type
        self.status = ServiceStatus.STOPPED
        self.created_at = datetime.now()
        self.started_at = None
        self.stopped_at = None
        self.error_message = None
        self.health_check_interval = 60  # 秒
        self.last_health_check = None
        self.health_status = True
    
    @abstractmethod
    def start(self) -> bool:
        """启动服务"""
        pass
    
    @abstractmethod
    def stop(self) -> bool:
        """停止服务"""
        pass
    
    @abstractmethod
    def health_check(self) -> bool:
        """健康检查"""
        pass
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取服务状态信息"""
        return {
            'name': self.name,
            'type': self.service_type.value,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'stopped_at': self.stopped_at.isoformat() if self.stopped_at else None,
            'error_message': self.error_message,
            'health_status': self.health_status,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None
        }

class ServiceRegistry:
    """服务注册器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._services: Dict[str, BaseService] = {}
        self._service_dependencies: Dict[str, List[str]] = {}
        self._startup_order: List[str] = []
        self._shutdown_order: List[str] = []
        self._health_check_thread = None
        self._health_check_running = False
        self._status_callbacks: List[Callable] = []
    
    def register_service(self, service: BaseService, dependencies: List[str] = None) -> bool:
        """注册服务"""
        try:
            if service.name in self._services:
                logger.warning(f"服务 {service.name} 已存在，将被替换")
            
            self._services[service.name] = service
            self._service_dependencies[service.name] = dependencies or []
            
            # 更新启动顺序
            self._update_startup_order()
            
            logger.info(f"服务 {service.name} 注册成功")
            self._notify_status_change(service.name, 'registered')
            
            return True
            
        except Exception as e:
            logger.error(f"注册服务 {service.name} 失败: {e}")
            return False
    
    def unregister_service(self, service_name: str) -> bool:
        """注销服务"""
        try:
            if service_name not in self._services:
                logger.warning(f"服务 {service_name} 不存在")
                return False
            
            # 停止服务
            service = self._services[service_name]
            if service.status == ServiceStatus.RUNNING:
                self.stop_service(service_name)
            
            # 移除服务
            del self._services[service_name]
            if service_name in self._service_dependencies:
                del self._service_dependencies[service_name]
            
            # 更新启动顺序
            self._update_startup_order()
            
            logger.info(f"服务 {service_name} 注销成功")
            self._notify_status_change(service_name, 'unregistered')
            
            return True
            
        except Exception as e:
            logger.error(f"注销服务 {service_name} 失败: {e}")
            return False
    
    def start_service(self, service_name: str) -> bool:
        """启动服务"""
        try:
            if service_name not in self._services:
                logger.error(f"服务 {service_name} 不存在")
                return False
            
            service = self._services[service_name]
            
            if service.status == ServiceStatus.RUNNING:
                logger.info(f"服务 {service_name} 已在运行")
                return True
            
            # 检查依赖服务
            dependencies = self._service_dependencies.get(service_name, [])
            for dep_name in dependencies:
                if dep_name not in self._services:
                    logger.error(f"依赖服务 {dep_name} 不存在")
                    return False
                
                dep_service = self._services[dep_name]
                if dep_service.status != ServiceStatus.RUNNING:
                    logger.info(f"启动依赖服务: {dep_name}")
                    if not self.start_service(dep_name):
                        logger.error(f"启动依赖服务 {dep_name} 失败")
                        return False
            
            # 启动服务
            logger.info(f"启动服务: {service_name}")
            service.status = ServiceStatus.STARTING
            self._notify_status_change(service_name, 'starting')
            
            if service.start():
                service.status = ServiceStatus.RUNNING
                service.started_at = datetime.now()
                service.error_message = None
                
                logger.info(f"服务 {service_name} 启动成功")
                self._notify_status_change(service_name, 'started')
                
                return True
            else:
                service.status = ServiceStatus.ERROR
                service.error_message = "启动失败"
                
                logger.error(f"服务 {service_name} 启动失败")
                self._notify_status_change(service_name, 'start_failed')
                
                return False
                
        except Exception as e:
            logger.error(f"启动服务 {service_name} 异常: {e}")
            
            if service_name in self._services:
                self._services[service_name].status = ServiceStatus.ERROR
                self._services[service_name].error_message = str(e)
                self._notify_status_change(service_name, 'error')
            
            return False
    
    def stop_service(self, service_name: str) -> bool:
        """停止服务"""
        try:
            if service_name not in self._services:
                logger.error(f"服务 {service_name} 不存在")
                return False
            
            service = self._services[service_name]
            
            if service.status == ServiceStatus.STOPPED:
                logger.info(f"服务 {service_name} 已停止")
                return True
            
            # 停止依赖此服务的其他服务
            dependent_services = self._get_dependent_services(service_name)
            for dep_name in dependent_services:
                if self._services[dep_name].status == ServiceStatus.RUNNING:
                    logger.info(f"停止依赖服务: {dep_name}")
                    self.stop_service(dep_name)
            
            # 停止服务
            logger.info(f"停止服务: {service_name}")
            service.status = ServiceStatus.STOPPING
            self._notify_status_change(service_name, 'stopping')
            
            if service.stop():
                service.status = ServiceStatus.STOPPED
                service.stopped_at = datetime.now()
                service.error_message = None
                
                logger.info(f"服务 {service_name} 停止成功")
                self._notify_status_change(service_name, 'stopped')
                
                return True
            else:
                service.status = ServiceStatus.ERROR
                service.error_message = "停止失败"
                
                logger.error(f"服务 {service_name} 停止失败")
                self._notify_status_change(service_name, 'stop_failed')
                
                return False
                
        except Exception as e:
            logger.error(f"停止服务 {service_name} 异常: {e}")
            
            if service_name in self._services:
                self._services[service_name].status = ServiceStatus.ERROR
                self._services[service_name].error_message = str(e)
                self._notify_status_change(service_name, 'error')
            
            return False
    
    def start_all_services(self) -> bool:
        """按依赖顺序启动所有服务"""
        logger.info("开始启动所有服务")
        
        success_count = 0
        for service_name in self._startup_order:
            if self.start_service(service_name):
                success_count += 1
            else:
                logger.error(f"启动服务 {service_name} 失败，停止启动流程")
                break
        
        total_services = len(self._startup_order)
        logger.info(f"服务启动完成: {success_count}/{total_services}")
        
        return success_count == total_services
    
    def stop_all_services(self) -> bool:
        """按反向依赖顺序停止所有服务"""
        logger.info("开始停止所有服务")
        
        success_count = 0
        for service_name in reversed(self._startup_order):
            if service_name in self._services:
                if self.stop_service(service_name):
                    success_count += 1
        
        logger.info(f"服务停止完成: {success_count} 个服务已停止")
        
        return True
    
    def get_service(self, service_name: str) -> Optional[BaseService]:
        """获取服务实例"""
        return self._services.get(service_name)
    
    def get_service_status(self, service_name: str) -> Optional[Dict[str, Any]]:
        """获取服务状态"""
        service = self._services.get(service_name)
        return service.get_status_info() if service else None
    
    def get_all_services_status(self) -> Dict[str, Dict[str, Any]]:
        """获取所有服务状态"""
        return {
            name: service.get_status_info()
            for name, service in self._services.items()
        }

    def list_services(self) -> List[str]:
        """获取所有已注册的服务名称列表"""
        return list(self._services.keys())
    
    def _update_startup_order(self):
        """更新启动顺序（拓扑排序）"""
        # 简单的拓扑排序实现
        visited = set()
        temp_visited = set()
        order = []
        
        def visit(service_name):
            if service_name in temp_visited:
                raise ValueError(f"检测到循环依赖: {service_name}")
            
            if service_name not in visited:
                temp_visited.add(service_name)
                
                dependencies = self._service_dependencies.get(service_name, [])
                for dep in dependencies:
                    if dep in self._services:
                        visit(dep)
                
                temp_visited.remove(service_name)
                visited.add(service_name)
                order.append(service_name)
        
        try:
            for service_name in self._services:
                if service_name not in visited:
                    visit(service_name)
            
            self._startup_order = order
            logger.debug(f"服务启动顺序: {order}")
            
        except ValueError as e:
            logger.error(f"更新启动顺序失败: {e}")
    
    def _get_dependent_services(self, service_name: str) -> List[str]:
        """获取依赖指定服务的服务列表"""
        dependents = []
        for name, dependencies in self._service_dependencies.items():
            if service_name in dependencies:
                dependents.append(name)
        return dependents
    
    def add_status_callback(self, callback: Callable):
        """添加状态变化回调"""
        self._status_callbacks.append(callback)
    
    def _notify_status_change(self, service_name: str, event: str):
        """通知状态变化"""
        for callback in self._status_callbacks:
            try:
                callback(service_name, event)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")

# 全局服务注册器实例
service_registry = ServiceRegistry()
