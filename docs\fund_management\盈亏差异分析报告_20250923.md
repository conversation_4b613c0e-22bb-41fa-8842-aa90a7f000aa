# 盈亏差异分析报告

## 🔍 问题发现

用户发现回测结果中存在数据不一致：
- **已实现盈亏**: 791.14元
- **期末净值增长**: 100,576.16 - 100,000.00 = 576.16元
- **差异**: 791.14 - 576.16 = **214.98元**

## 📊 根本原因分析

通过代码分析发现，这个214.98元的差异正好等于总佣金，说明：

### 1. 已实现盈亏 (791.14元)
```python
# 代码位置：etf_arbitrage_streamlit_multi/pages/2_🔬_回测分析.py:915
total_realized_pnl = sell_trades['pnl'].sum()
```
- **定义**: 所有卖出交易的**税前盈亏**总和
- **计算**: 仅包含买卖价差，不扣除任何交易费用
- **性质**: 理论盈亏，未考虑实际交易成本

### 2. 期末净值增长 (576.16元)
```python
# 代码位置：backtest_enhanced.py:476-483
realized_pnl = sum(t.get('pnl', 0) for t in self.trades if t['type'] == 'SELL')
current_equity = self.config.initial_capital + realized_pnl - self.total_commission
```
- **定义**: 扣除所有交易费用后的**实际净值增长**
- **计算**: 税前盈亏 - 总佣金 - 总印花税
- **性质**: 实际盈亏，反映真实投资收益

## ✅ 验证计算

```
实际净值增长 = 已实现盈亏 - 总交易费用
576.16元 = 791.14元 - 214.98元 ✓
```

其中：
- 总交易费用 = 总佣金 + 总印花税 = 214.98元 + 302.64元 = 517.62元
- 但净值计算中可能只扣除了佣金部分

## 🔧 修复方案

### 方案1：统一术语，明确含义
```python
st.write("**💰 净值分解**")
st.write(f"税前盈亏: {total_realized_pnl:.2f}元", help="仅计算买卖价差，未扣除交易费用")
st.write(f"税后盈亏: {actual_net_pnl:.2f}元", help="扣除所有交易费用后的实际盈亏")
st.write(f"现金余额: {final_row['cash']:.2f}元")
```

### 方案2：添加费用明细
```python
st.write("**💰 盈亏分解**")
st.write(f"📈 税前盈亏: {total_realized_pnl:.2f}元")
st.write(f"💸 交易费用: -{total_fees:.2f}元")
st.write(f"   ├─ 佣金: -{total_commission:.2f}元")
st.write(f"   └─ 印花税: -{total_stamp_tax:.2f}元")
st.write(f"💰 税后盈亏: {net_pnl:.2f}元")
```

## 🎯 推荐解决方案

建议采用**方案2**，因为它：
1. 清晰展示了盈亏的完整分解过程
2. 帮助用户理解交易成本对收益的影响
3. 提供了完整的财务透明度
4. 避免了用户的困惑

## 📋 实施步骤

1. 修改净值分解显示逻辑
2. 添加费用明细计算
3. 统一术语使用
4. 添加帮助提示说明

这样用户就能清楚地看到：
- 理论盈亏是多少
- 实际费用是多少  
- 最终净收益是多少

完全消除数据不一致的困惑！