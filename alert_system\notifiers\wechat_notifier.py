#!/usr/bin/env python3
"""
微信通知器
支持企业微信机器人和个人微信通知
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class WeChatNotifier:
    """微信通知器"""
    
    def __init__(self, 
                 webhook_url: str = None,
                 mentioned_list: List[str] = None,
                 mentioned_mobile_list: List[str] = None):
        """
        初始化微信通知器
        
        Args:
            webhook_url: 企业微信机器人webhook地址
            mentioned_list: @的用户列表
            mentioned_mobile_list: @的手机号列表
        """
        self.webhook_url = webhook_url or os.getenv('WECHAT_WEBHOOK_URL')
        self.mentioned_list = mentioned_list or self._load_mentioned_list()
        self.mentioned_mobile_list = mentioned_mobile_list or self._load_mentioned_mobile_list()
        
        # 消息模板
        self.templates = {
            'alert': self._get_alert_template(),
            'summary': self._get_summary_template(),
            'error': self._get_error_template()
        }
        
        logger.info("微信通知器初始化完成")
    
    def _load_mentioned_list(self) -> List[str]:
        """加载@用户列表"""
        mentioned_str = os.getenv('WECHAT_MENTIONED_LIST', '')
        if mentioned_str:
            return [user.strip() for user in mentioned_str.split(',')]
        return []
    
    def _load_mentioned_mobile_list(self) -> List[str]:
        """加载@手机号列表"""
        mobile_str = os.getenv('WECHAT_MENTIONED_MOBILE_LIST', '')
        if mobile_str:
            return [mobile.strip() for mobile in mobile_str.split(',')]
        return []
    
    def _get_alert_template(self) -> str:
        """获取预警消息模板"""
        return """🚨 **ETF套利预警通知**

**交易标的:** {symbol}
**预警类型:** {alert_type}
**触发条件:** {condition}
**当前价格:** {price}
**信号强度:** {signal}
**触发时间:** {timestamp}

**详细信息:**
{details}

**建议操作:**
{recommendation}

---
*ETF套利系统自动发送*"""
    
    def _get_summary_template(self) -> str:
        """获取总结消息模板"""
        return """📊 **ETF套利系统日报**

**今日交易总结:**
• 总收益率: {total_return}
• 交易次数: {trade_count}
• 胜率: {win_rate}
• 最大回撤: {max_drawdown}

**预警统计:**
• 触发预警: {alert_count} 次
• 成功交易: {successful_trades} 次

**系统状态:**
• 运行时间: {uptime}
• 数据更新: {last_update}

---
*{send_time} 自动生成*"""
    
    def _get_error_template(self) -> str:
        """获取错误消息模板"""
        return """⚠️ **ETF套利系统错误通知**

**错误详情:**
• 错误类型: {error_type}
• 错误信息: {error_message}
• 发生时间: {timestamp}
• 影响模块: {module}

**建议处理:**
{suggestion}

---
*请及时处理系统错误*"""
    
    async def send(self, message: str, message_type: str = 'text',
                   mentioned_list: List[str] = None,
                   mentioned_mobile_list: List[str] = None,
                   **kwargs) -> bool:
        """
        发送微信消息
        
        Args:
            message: 消息内容
            message_type: 消息类型 ('text', 'markdown')
            mentioned_list: @的用户列表
            mentioned_mobile_list: @的手机号列表
            **kwargs: 模板参数
            
        Returns:
            发送是否成功
        """
        if not self.webhook_url:
            logger.warning("微信webhook未配置，跳过发送")
            return False
        
        try:
            # 构建消息体
            payload = self._build_payload(
                message, message_type, 
                mentioned_list or self.mentioned_list,
                mentioned_mobile_list or self.mentioned_mobile_list,
                **kwargs
            )
            
            # 发送消息
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        if result.get('errcode') == 0:
                            logger.info("微信消息发送成功")
                            return True
                        else:
                            logger.error(f"微信消息发送失败: {result}")
                            return False
                    else:
                        logger.error(f"微信API请求失败: {response.status}")
                        return False
            
        except Exception as e:
            logger.error(f"微信消息发送异常: {e}")
            return False
    
    def _build_payload(self, message: str, message_type: str,
                      mentioned_list: List[str], mentioned_mobile_list: List[str],
                      **kwargs) -> Dict:
        """构建消息载荷"""
        if message_type == 'markdown':
            payload = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }
        else:
            # 文本消息
            payload = {
                "msgtype": "text",
                "text": {
                    "content": message,
                    "mentioned_list": mentioned_list,
                    "mentioned_mobile_list": mentioned_mobile_list
                }
            }
        
        return payload
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> bool:
        """发送预警消息"""
        template = self.templates['alert']
        message = template.format(
            symbol=symbol,
            alert_type=alert_type,
            condition=condition,
            price=f"{price:.4f}",
            signal=f"{signal:.4f}",
            details=details or "无额外信息",
            recommendation=recommendation or "请根据策略执行相应操作",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        
        return await self.send(message, message_type='markdown')
    
    async def send_summary(self, total_return: float, trade_count: int,
                          win_rate: float, max_drawdown: float,
                          alert_count: int, successful_trades: int) -> bool:
        """发送日报消息"""
        template = self.templates['summary']
        message = template.format(
            total_return=f"{total_return:.2%}",
            trade_count=trade_count,
            win_rate=f"{win_rate:.2%}",
            max_drawdown=f"{max_drawdown:.2%}",
            alert_count=alert_count,
            successful_trades=successful_trades,
            uptime="24小时",
            last_update=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            send_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        
        return await self.send(message, message_type='markdown')
    
    async def send_error(self, error_type: str, error_message: str,
                        module: str, suggestion: str = "") -> bool:
        """发送错误通知消息"""
        template = self.templates['error']
        message = template.format(
            error_type=error_type,
            error_message=error_message,
            module=module,
            suggestion=suggestion or "请检查系统日志并联系技术支持",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
        
        return await self.send(message, message_type='markdown')
    
    async def send_simple(self, text: str, mention_all: bool = False) -> bool:
        """发送简单文本消息"""
        mentioned_list = ["@all"] if mention_all else self.mentioned_list
        return await self.send(text, mentioned_list=mentioned_list)
    
    async def test_connection(self) -> bool:
        """测试微信连接"""
        if not self.webhook_url:
            logger.warning("微信webhook未配置")
            return False
        
        test_message = f"🔔 ETF套利系统连接测试\n时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        return await self.send_simple(test_message)


class WeChatWorkNotifier(WeChatNotifier):
    """企业微信通知器（继承自微信通知器）"""
    
    def __init__(self, webhook_url: str = None, **kwargs):
        super().__init__(webhook_url, **kwargs)
        logger.info("企业微信通知器初始化完成")
    
    async def send_card(self, title: str, description: str, 
                       url: str = None, btntxt: str = "详情") -> bool:
        """发送卡片消息（企业微信特有）"""
        if not self.webhook_url:
            logger.warning("企业微信webhook未配置，跳过发送")
            return False
        
        payload = {
            "msgtype": "textcard",
            "textcard": {
                "title": title,
                "description": description,
                "url": url or "https://github.com",
                "btntxt": btntxt
            }
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.webhook_url,
                    json=payload,
                    headers={'Content-Type': 'application/json'}
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        if result.get('errcode') == 0:
                            logger.info("企业微信卡片消息发送成功")
                            return True
                        else:
                            logger.error(f"企业微信卡片消息发送失败: {result}")
                            return False
                    else:
                        logger.error(f"企业微信API请求失败: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"企业微信卡片消息发送异常: {e}")
            return False


# 测试函数
async def test_wechat_notifier():
    """测试微信通知器"""
    logger.info("开始测试微信通知器...")
    
    # 创建微信通知器实例
    notifier = WeChatNotifier()
    
    try:
        # 测试连接
        connection_ok = await notifier.test_connection()
        if not connection_ok:
            logger.warning("微信连接测试失败，跳过发送测试")
            return False
        
        # 测试预警消息
        success1 = await notifier.send_alert(
            symbol="159740",
            alert_type="买入信号",
            condition="signal <= -0.006",
            price=0.7523,
            signal=-0.0078,
            details="信号强度较强，建议关注",
            recommendation="考虑买入操作"
        )
        
        # 测试简单消息
        success2 = await notifier.send_simple("这是一条测试消息")
        
        logger.info(f"微信测试结果: 预警消息={success1}, 简单消息={success2}")
        return success1 or success2
        
    except Exception as e:
        logger.error(f"微信测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_wechat_notifier())
