#!/usr/bin/env python3
"""
增强版实时交易引擎 V2
使用统一的核心基础设施和BaseTrader基类
"""

import threading
import time
import json
from typing import Dict, List, Optional, Callable, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import numpy as np
import pandas as pd

# 导入核心基础设施
try:
    from ..core.base_trader import BaseTrader, TradeSignal, TradeRecord, Position, TradeType, PositionStatus
    from ..core.database_manager import DatabaseManager
    from ..core.config_manager import ConfigManager
    from ..core.logger_manager import LoggerManager
    from ..core.exception_handler import ExceptionHandler, ErrorCategory, ErrorSeverity
    CORE_INFRASTRUCTURE_AVAILABLE = True
except ImportError:
    CORE_INFRASTRUCTURE_AVAILABLE = False
    import logging

# 初始化核心服务
if CORE_INFRASTRUCTURE_AVAILABLE:
    db_manager = DatabaseManager.get_instance()
    config_manager = ConfigManager()
    logger_manager = LoggerManager()
    exception_handler = ExceptionHandler()
    logger = logger_manager.create_module_logger('enhanced_real_time_trader_v2')
else:
    logger = logging.getLogger(__name__)

# 导入策略引擎和数据连接器
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from strategy_engine_enhanced import Position as StrategyPosition, RiskManager
    from strategy_config import StrategyConfig
    STRATEGY_AVAILABLE = True
except ImportError:
    STRATEGY_AVAILABLE = False
    logger.warning("策略引擎不可用，将使用模拟模式")

# 导入真实数据连接器
try:
    from .real_data_connector import RealDataConnector
    DATA_CONNECTOR_AVAILABLE = True
except ImportError:
    DATA_CONNECTOR_AVAILABLE = False
    logger.warning("数据连接器不可用，将使用内置数据源")

# 导入策略管理器
try:
    from .trading_strategies import get_strategy_manager, SignalResult
    STRATEGY_MANAGER_AVAILABLE = True
except ImportError:
    STRATEGY_MANAGER_AVAILABLE = False
    logger.warning("策略管理器不可用")

# 导入A股交易规则管理器
try:
    from .trading_rules_manager import get_trading_rules_manager
    TRADING_RULES_AVAILABLE = True
except ImportError:
    TRADING_RULES_AVAILABLE = False
    logger.warning("A股交易规则管理器不可用")

class RealTimeDataFeed:
    """实时数据源"""
    
    def __init__(self, db_path: str = None):
        # 使用核心基础设施获取数据库路径
        if CORE_INFRASTRUCTURE_AVAILABLE and db_path is None:
            db_config = config_manager.get_database_config()
            self.db_path = db_config.ticks_db
        else:
            self.db_path = db_path or "ticks.db"
            
        self.subscribers = []
        self.is_active = False
        self.current_symbol = None
        self.feed_thread = None
        self.stop_event = threading.Event()
        
        # 初始化真实数据连接器
        if DATA_CONNECTOR_AVAILABLE:
            self.data_connector = RealDataConnector(self.db_path)
            logger.info("使用真实数据连接器")
        else:
            self.data_connector = None
            logger.warning("使用内置数据源")
    
    def subscribe(self, callback: Callable):
        """订阅数据更新"""
        self.subscribers.append(callback)
    
    def start_feed(self, symbol: str):
        """启动实时数据推送"""
        if self.is_active:
            logger.warning("数据源已在运行中")
            return False
            
        self.current_symbol = symbol
        self.is_active = True
        self.stop_event.clear()
        
        self.feed_thread = threading.Thread(target=self._feed_loop, daemon=True)
        self.feed_thread.start()
        
        logger.info(f"启动实时数据源: {symbol}")
        return True
    
    def stop_feed(self):
        """停止数据推送"""
        if not self.is_active:
            return False
            
        self.stop_event.set()
        self.is_active = False
        
        if self.feed_thread and self.feed_thread.is_alive():
            self.feed_thread.join(timeout=5)
            
        logger.info("实时数据源已停止")
        return True
    
    def _feed_loop(self):
        """数据推送循环"""
        while not self.stop_event.is_set():
            try:
                # 获取最新数据
                tick_data = self._get_latest_tick()
                
                if tick_data:
                    # 通知所有订阅者
                    for callback in self.subscribers:
                        try:
                            callback(tick_data)
                        except Exception as e:
                            logger.error(f"数据推送回调失败: {e}")
                
                # 等待下一次更新
                time.sleep(1)  # 1秒更新一次
                
            except Exception as e:
                logger.error(f"数据推送循环出错: {e}")
                time.sleep(5)  # 出错后等待5秒再重试
    
    def _get_latest_tick(self) -> Optional[dict]:
        """获取最新tick数据"""
        try:
            # 使用真实数据连接器
            if self.data_connector:
                market_data = self.data_connector.get_current_market_data(self.current_symbol)
                if market_data:
                    return market_data
            
            # 备用方案：从数据库获取
            return self._get_tick_from_database()
                
        except Exception as e:
            logger.error(f"获取tick数据失败: {e}")
            return None
    
    def _get_tick_from_database(self) -> Optional[dict]:
        """从数据库获取tick数据（备用方案）"""
        try:
            # 使用核心DatabaseManager或传统方式
            if CORE_INFRASTRUCTURE_AVAILABLE:
                query = """
                SELECT tick_time, price, volume
                FROM ticks 
                WHERE symbol = ? AND tick_time >= datetime('now', '-5 minutes')
                ORDER BY tick_time DESC 
                LIMIT 1
                """
                
                df = db_manager.read_dataframe(query, (self.current_symbol,), 'ticks')
                
                if not df.empty:
                    return {
                        'symbol': self.current_symbol,
                        'timestamp': pd.to_datetime(df.iloc[0]['tick_time']),
                        'price': float(df.iloc[0]['price']),
                        'volume': int(df.iloc[0]['volume'])
                    }
            
            # 如果没有最新数据，生成模拟数据
            return self._generate_simulated_tick()
                
        except Exception as e:
            logger.error(f"从数据库获取tick数据失败: {e}")
            return None
    
    def _generate_simulated_tick(self) -> dict:
        """生成模拟tick数据"""
        # 基础价格（可以从配置或历史数据获取）
        base_price = 1.0
        
        # 添加随机波动
        price_change = np.random.normal(0, base_price * 0.001)
        current_price = max(0.01, base_price + price_change)
        
        return {
            'symbol': self.current_symbol,
            'timestamp': datetime.now(),
            'price': current_price,
            'volume': np.random.randint(1000, 10000)
        }

class EnhancedRiskManager:
    """增强版风险管理器"""
    
    def __init__(self, initial_capital: float):
        self.initial_capital = initial_capital
        self.emergency_stop = False
        
        # 使用配置管理器获取风险参数
        if CORE_INFRASTRUCTURE_AVAILABLE:
            trading_config = config_manager.get_trading_config()
            self.max_positions = trading_config.max_positions
            self.commission_rate = trading_config.commission_rate
        else:
            self.max_positions = 5
            self.commission_rate = 0.0003
    
    def check_risk(self, signal: TradeSignal, positions: Dict, market_data: Dict) -> Tuple[bool, str]:
        """风险检查"""
        if self.emergency_stop:
            return False, "紧急停止状态"
        
        # 检查持仓数量
        if len(positions) >= self.max_positions and signal.signal_type == TradeType.BUY:
            return False, f"持仓数量超限 ({len(positions)}/{self.max_positions})"
        
        # 检查资金充足性
        required_capital = signal.quantity * signal.price
        if required_capital > self.initial_capital * 0.2:  # 单笔不超过20%
            return False, f"单笔投资过大: {required_capital:,.2f}"
        
        return True, "风险检查通过"
    
    def reset_emergency_stop(self):
        """重置紧急停止状态"""
        self.emergency_stop = False
        logger.info("紧急停止状态已重置")

class EnhancedRealTimeTrader(BaseTrader):
    """增强版实时交易引擎 - 继承BaseTrader"""
    
    def __init__(self, initial_capital: float = None):
        # 使用配置管理器获取初始资金
        if CORE_INFRASTRUCTURE_AVAILABLE and initial_capital is None:
            trading_config = config_manager.get_trading_config()
            initial_capital = trading_config.default_position_size
        
        super().__init__(initial_capital or 1000000)
        
        # 核心组件
        self.data_feed = RealTimeDataFeed()
        self.risk_manager = EnhancedRiskManager(self.initial_capital)
        
        # A股交易规则管理器
        self.trading_rules = get_trading_rules_manager() if TRADING_RULES_AVAILABLE else None
        
        # 策略管理器
        self.strategy_manager = None
        if STRATEGY_MANAGER_AVAILABLE:
            try:
                self.strategy_manager = get_strategy_manager()
                logger.info("策略管理器初始化成功")
            except Exception as e:
                logger.error(f"策略管理器初始化失败: {e}")
                self.strategy_manager = None
        
        # 策略参数
        self.strategy_params = StrategyConfig.get_default_values() if STRATEGY_AVAILABLE else {
            'buy_trigger_drop': -0.002,
            'profit_target': 0.0025,
            'stop_loss': -0.02,
            'max_hold_time': 3600,
            'position_size': 100000,
            'commission_rate': 0.0003
        }
        
        # 订阅数据更新
        self.data_feed.subscribe(self._on_tick_data)
        
        logger.info(f"增强版交易引擎初始化完成，初始资金: {self.initial_capital:,.2f}")
    
    def _generate_signal(self, market_data: Dict) -> Optional[TradeSignal]:
        """生成交易信号（实现基类抽象方法）"""
        try:
            # 优先使用策略管理器
            if self.strategy_manager:
                current_strategy = self.strategy_manager.get_current_strategy()
                if current_strategy:
                    # 准备策略所需的数据
                    full_data = self._prepare_strategy_data(market_data)
                    
                    # 生成信号
                    signal_result = current_strategy.generate_signal(full_data)
                    
                    if signal_result and abs(signal_result.signal) > 0.1:
                        # 转换SignalResult为TradeSignal
                        signal_type = TradeType.BUY if signal_result.signal > 0 else TradeType.SELL
                        
                        return TradeSignal(
                            symbol=market_data['symbol'],
                            signal_type=signal_type,
                            price=market_data['price'],
                            quantity=int(self.strategy_params['position_size'] / market_data['price']),
                            confidence=signal_result.confidence,
                            reason=f"策略信号: {signal_result.reason}",
                            timestamp=market_data['timestamp'],
                            signal_strength=abs(signal_result.signal)
                        )
            
            # 备用信号生成逻辑
            return self._generate_fallback_signal(market_data)

        except Exception as e:
            if CORE_INFRASTRUCTURE_AVAILABLE:
                exception_handler.handle_exception(
                    e, ErrorCategory.TRADING_ERROR, ErrorSeverity.MEDIUM
                )
            else:
                logger.error(f"信号生成失败: {e}")
            return None

    def _prepare_strategy_data(self, market_data: Dict) -> pd.DataFrame:
        """准备策略所需的数据"""
        try:
            # 获取历史数据
            if CORE_INFRASTRUCTURE_AVAILABLE:
                query = """
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol = ?
                ORDER BY tick_time DESC
                LIMIT 100
                """
                df = db_manager.read_dataframe(query, (market_data['symbol'],), 'ticks')
            else:
                # 创建模拟数据
                df = pd.DataFrame({
                    'tick_time': [datetime.now()],
                    'price': [market_data['price']],
                    'volume': [market_data['volume']]
                })

            if df.empty:
                # 如果没有历史数据，创建当前数据点
                df = pd.DataFrame({
                    'tick_time': [market_data['timestamp']],
                    'price': [market_data['price']],
                    'volume': [market_data['volume']]
                })

            return df

        except Exception as e:
            logger.error(f"准备策略数据失败: {e}")
            return pd.DataFrame()

    def _generate_fallback_signal(self, market_data: Dict) -> Optional[TradeSignal]:
        """备用信号生成逻辑"""
        try:
            # 简单的价格变化信号
            symbol = market_data['symbol']
            current_price = market_data['price']

            # 获取最近的价格
            if symbol in self.positions:
                position = self.positions[symbol]
                price_change = (current_price - position.avg_price) / position.avg_price

                # 简单的止盈止损逻辑
                if price_change > self.strategy_params['profit_target']:
                    return TradeSignal(
                        symbol=symbol,
                        signal_type=TradeType.SELL,
                        price=current_price,
                        quantity=position.quantity,
                        confidence=0.8,
                        reason=f"止盈信号: 涨幅{price_change:.2%}",
                        timestamp=market_data['timestamp']
                    )
                elif price_change < self.strategy_params['stop_loss']:
                    return TradeSignal(
                        symbol=symbol,
                        signal_type=TradeType.SELL,
                        price=current_price,
                        quantity=position.quantity,
                        confidence=0.9,
                        reason=f"止损信号: 跌幅{price_change:.2%}",
                        timestamp=market_data['timestamp']
                    )

            return None

        except Exception as e:
            logger.error(f"备用信号生成失败: {e}")
            return None

    def _execute_signal(self, signal: TradeSignal) -> bool:
        """执行交易信号（实现基类抽象方法）"""
        try:
            # 风险检查
            risk_ok, risk_reason = self.risk_manager.check_risk(
                signal, self.positions, {'price': signal.price}
            )

            if not risk_ok:
                logger.warning(f"风险检查失败: {risk_reason}")
                return False

            # A股规则验证
            if self.trading_rules:
                market_data = {
                    'timestamp': signal.timestamp,
                    'symbol': signal.symbol,
                    'price': signal.price,
                    'volume': getattr(signal, 'volume', 1000000),
                    'prev_close': getattr(signal, 'prev_close', signal.price * 0.99)
                }

                compliance_result = self.trading_rules.validate_trade_compliance(
                    signal.symbol, signal.signal_type.value, signal.quantity,
                    signal.price, market_data
                )

                if not compliance_result['is_compliant']:
                    violations = '; '.join(compliance_result.get('violations', []))
                    logger.warning(f"A股规则验证失败: {violations}")
                    return False

            # 执行交易
            trade_record = self._create_trade_record(signal)

            if trade_record:
                self.trades.append(trade_record)
                self._update_position(signal, trade_record)
                self.save_trade_record(trade_record)

                logger.info(f"交易执行成功: {signal.signal_type.value} {signal.quantity}@{signal.price:.4f}")
                return True

            return False

        except Exception as e:
            if CORE_INFRASTRUCTURE_AVAILABLE:
                exception_handler.handle_exception(
                    e, ErrorCategory.TRADING_ERROR, ErrorSeverity.HIGH
                )
            else:
                logger.error(f"执行交易信号失败: {e}")
            return False

    def _create_trade_record(self, signal: TradeSignal) -> Optional[TradeRecord]:
        """创建交易记录"""
        try:
            trade_id = f"{signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.trades)}"
            amount = signal.quantity * signal.price
            commission = amount * self.strategy_params['commission_rate']

            return TradeRecord(
                id=trade_id,
                timestamp=signal.timestamp,
                symbol=signal.symbol,
                action=signal.signal_type,
                quantity=signal.quantity,
                price=signal.price,
                amount=amount,
                commission=commission,
                reason=signal.reason
            )

        except Exception as e:
            logger.error(f"创建交易记录失败: {e}")
            return None

    def _update_position(self, signal: TradeSignal, trade: TradeRecord):
        """更新持仓"""
        try:
            symbol = signal.symbol

            if signal.signal_type == TradeType.BUY:
                if symbol in self.positions:
                    # 加仓
                    position = self.positions[symbol]
                    total_quantity = position.quantity + signal.quantity
                    total_cost = (position.quantity * position.avg_price +
                                 signal.quantity * signal.price)
                    position.avg_price = total_cost / total_quantity
                    position.quantity = total_quantity
                    position.last_update = datetime.now()
                else:
                    # 新建仓位
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=signal.quantity,
                        avg_price=signal.price,
                        current_price=signal.price,
                        unrealized_pnl=0.0,
                        realized_pnl=0.0,
                        status=PositionStatus.OPEN,
                        open_time=datetime.now(),
                        last_update=datetime.now()
                    )

            elif signal.signal_type == TradeType.SELL:
                if symbol in self.positions:
                    position = self.positions[symbol]

                    if signal.quantity >= position.quantity:
                        # 全部平仓
                        pnl = (signal.price - position.avg_price) * position.quantity
                        position.realized_pnl += pnl
                        position.status = PositionStatus.CLOSED
                        del self.positions[symbol]
                    else:
                        # 部分平仓
                        pnl = (signal.price - position.avg_price) * signal.quantity
                        position.realized_pnl += pnl
                        position.quantity -= signal.quantity
                        position.status = PositionStatus.PARTIAL

                    position.last_update = datetime.now()

        except Exception as e:
            logger.error(f"更新持仓失败: {e}")

    def _trading_loop(self, symbol: str):
        """交易主循环（实现基类抽象方法）"""
        try:
            logger.info(f"启动交易循环: {symbol}")

            # 启动数据源
            if not self.data_feed.start_feed(symbol):
                logger.error("启动数据源失败")
                return

            # 主循环
            while not self.stop_event.is_set():
                try:
                    time.sleep(1)  # 数据由回调处理，这里只需要保持循环
                except Exception as e:
                    logger.error(f"交易循环错误: {e}")
                    time.sleep(5)

            # 停止数据源
            self.data_feed.stop_feed()
            logger.info("交易循环已停止")

        except Exception as e:
            if CORE_INFRASTRUCTURE_AVAILABLE:
                exception_handler.handle_exception(
                    e, ErrorCategory.TRADING_ERROR, ErrorSeverity.HIGH
                )
            else:
                logger.error(f"交易循环失败: {e}")

    def _on_tick_data(self, tick_data: dict):
        """处理tick数据"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']

            # 更新持仓价格
            for position in self.positions.values():
                if position.symbol == symbol:
                    position.current_price = price
                    position.unrealized_pnl = (price - position.avg_price) * position.quantity
                    position.last_update = datetime.now()

            # 生成交易信号
            signal = self._generate_signal(tick_data)

            if signal and signal.signal_type != TradeType.HOLD:
                self.signals.append(signal)
                self.stats['signals_today'] += 1
                self.stats['last_signal_time'] = timestamp

                # 执行信号
                if self._execute_signal(signal):
                    self.stats['valid_signals_today'] += 1

        except Exception as e:
            logger.error(f"处理tick数据失败: {e}")

    def refresh_strategy_manager(self):
        """刷新策略管理器状态（与页面选择同步）"""
        if STRATEGY_MANAGER_AVAILABLE and self.strategy_manager:
            try:
                # 重新获取策略管理器以同步状态
                self.strategy_manager = get_strategy_manager()
                logger.info("策略管理器状态已刷新")
            except Exception as e:
                logger.error(f"刷新策略管理器失败: {e}")

    def get_enhanced_status(self) -> Dict[str, Any]:
        """获取增强状态信息"""
        base_status = self.get_status()

        # 添加增强信息
        enhanced_status = {
            **base_status,
            'strategy_manager_available': STRATEGY_MANAGER_AVAILABLE,
            'trading_rules_available': TRADING_RULES_AVAILABLE,
            'data_connector_available': DATA_CONNECTOR_AVAILABLE,
            'core_infrastructure_available': CORE_INFRASTRUCTURE_AVAILABLE,
            'strategy_params': self.strategy_params.copy(),
            'risk_manager_status': {
                'emergency_stop': self.risk_manager.emergency_stop,
                'max_positions': self.risk_manager.max_positions
            }
        }

        # 添加当前策略信息
        if self.strategy_manager:
            try:
                current_strategy = self.strategy_manager.get_current_strategy()
                if current_strategy:
                    enhanced_status['current_strategy'] = {
                        'name': getattr(current_strategy, 'name', 'Unknown'),
                        'description': getattr(current_strategy, 'description', 'No description')
                    }
            except Exception as e:
                logger.error(f"获取当前策略信息失败: {e}")

        return enhanced_status

# 全局实例
enhanced_trader = EnhancedRealTimeTrader()

# 便捷函数
def start_enhanced_trading(symbol: str, params: Dict = None) -> bool:
    """启动增强版实时交易"""
    if params:
        enhanced_trader.update_strategy_params(params)
    return enhanced_trader.start_trading(symbol)

def stop_enhanced_trading() -> bool:
    """停止增强版实时交易"""
    return enhanced_trader.stop_trading()

def get_enhanced_trading_status() -> Dict:
    """获取增强版交易状态"""
    return enhanced_trader.get_enhanced_status()

def close_all_enhanced_positions() -> bool:
    """关闭所有持仓"""
    try:
        for symbol, position in enhanced_trader.positions.items():
            if position.status == PositionStatus.OPEN:
                # 创建平仓信号
                signal = TradeSignal(
                    symbol=symbol,
                    signal_type=TradeType.SELL,
                    price=position.current_price,
                    quantity=position.quantity,
                    confidence=1.0,
                    reason="手动平仓",
                    timestamp=datetime.now()
                )
                enhanced_trader._execute_signal(signal)

        logger.info("所有持仓已关闭")
        return True

    except Exception as e:
        logger.error(f"关闭持仓失败: {e}")
        return False
