# 时间参数机制说明

## 概述

ETF套利回测系统支持双重时间控制机制，提供灵活的持仓时间管理。

## 参数说明

### 1. enable_time_control（时间控制总开关）
- **类型**: 布尔值
- **默认值**: True（启用）
- **用途**: 全局控制是否启用时间止损功能
- **说明**: 关闭后所有时间控制均失效

### 2. max_hold_time（最大持仓时间）
- **单位**: 秒
- **默认值**: 86400秒（24小时）
- **范围**: 0-86400秒（0表示禁用，最长24小时）
- **用途**: 短期时间控制，适合日内交易
- **优先级**: 高（优先检查）
- **禁用**: 设置为0即可禁用此项控制

### 3. max_holding_days（最大持仓天数）
- **单位**: 天
- **默认值**: 7天
- **范围**: 0-30天（0表示禁用，最长30天）
- **用途**: 长期时间控制，防止长期被套
- **优先级**: 低（作为补充保护）
- **禁用**: 设置为0即可禁用此项控制

### 4. min_hold_time（最小持仓时间）
- **单位**: 秒
- **默认值**: 30秒
- **范围**: 10-300秒
- **用途**: 防止频繁交易的保护机制
- **说明**: 此参数不可禁用，始终生效

## 工作机制

### 时间止损逻辑
```python
# 检查是否启用时间控制
enable_time_control = getattr(self.config, 'enable_time_control', True)

if enable_time_control:
    # 1. 秒级限制检查（优先级高）- 0表示禁用
    max_hold_time = getattr(self.config, 'max_hold_time', 0)
    if max_hold_time > 0 and hold_time >= max_hold_time:
        return True, 1.0, f"时间止损(秒): {hold_time}s >= {max_hold_time}s"
    
    # 2. 天级限制检查（补充保护）- 0表示禁用
    max_holding_days = getattr(self.config, 'max_holding_days', 0)
    if max_holding_days > 0:
        max_hold_seconds = max_holding_days * 24 * 3600
        if hold_time >= max_hold_seconds:
            return True, 1.0, f"时间止损(天): {hold_days:.1f}天 >= {max_holding_days}天"
```

### 执行顺序
1. **全局开关检查**: 检查 enable_time_control 是否启用
2. **最小持仓时间保护**: 防止过早止损（始终生效）
3. **秒级时间控制**: 优先检查 max_hold_time（可选）
4. **天级时间控制**: 检查 max_holding_days（可选）
5. **任一条件满足**: 立即触发时间止损

### 控制层级
- **Level 1**: 全局开关（enable_time_control）- 可以完全禁用时间控制
- **Level 2**: 单项开关（参数设为0）- 可以禁用特定时间控制
- **Level 3**: 参数调整 - 在启用状态下调整具体数值

## 使用场景

### 🔴 完全禁用时间控制
**适用策略**: 长期持有、价值投资
**配置特点**: `enable_time_control=False`
**风险特征**: 无时间限制，依赖其他止损机制
**适用市场**: 稳定上升趋势，低波动环境

### 🟡 仅秒级时间控制
**适用策略**: 日内交易、高频交易、套利交易
**配置特点**: `max_hold_time>0, max_holding_days=0`
**风险特征**: 严格控制持仓时间，快进快出
**适用市场**: 高波动、流动性好的市场

### 🟠 仅天级时间控制
**适用策略**: 波段交易、趋势跟踪
**配置特点**: `max_hold_time=0, max_holding_days>0`
**风险特征**: 允许日内波动，防止长期被套
**适用市场**: 中等波动、有明确趋势的市场

### 🟢 双重时间控制
**适用策略**: 综合交易策略、多时间框架策略
**配置特点**: `max_hold_time>0, max_holding_days>0`
**风险特征**: 多层次风险管理，灵活应对不同情况
**适用市场**: 复杂多变的市场环境

### 具体策略建议

#### 日内交易策略
- `enable_time_control=True`
- `max_hold_time=1800-14400`（30分钟-4小时）
- `max_holding_days=0`（禁用）或 `1-2`（兜底保护）

#### 短期波段策略
- `enable_time_control=True`
- `max_hold_time=14400-86400`（4-24小时）
- `max_holding_days=3-7`（防止长期被套）

#### 长期持有策略
- `enable_time_control=False`（完全禁用）
- 或 `max_hold_time=0, max_holding_days=30-90`（仅长期保护）

## 配置示例

### 完全禁用时间控制
```python
config = BacktestConfig(
    enable_time_control=False,  # 关闭所有时间控制
    # 其他时间参数将被忽略
)
```

### 仅启用秒级控制（日内交易）
```python
config = BacktestConfig(
    enable_time_control=True,
    max_hold_time=3600,         # 1小时
    max_holding_days=0,         # 禁用天级控制
    min_hold_time=30            # 30秒保护
)
```

### 仅启用天级控制（波段交易）
```python
config = BacktestConfig(
    enable_time_control=True,
    max_hold_time=0,            # 禁用秒级控制
    max_holding_days=7,         # 7天
    min_hold_time=30            # 30秒保护
)
```

### 双重时间控制（综合策略）
```python
config = BacktestConfig(
    enable_time_control=True,
    max_hold_time=7200,         # 2小时
    max_holding_days=14,        # 14天
    min_hold_time=30            # 30秒保护
)
```

### 保守型配置
```python
config = BacktestConfig(
    enable_time_control=True,
    max_hold_time=1800,         # 30分钟
    max_holding_days=3,         # 3天
    min_hold_time=30            # 30秒
)
```

### 激进型配置
```python
config = BacktestConfig(
    enable_time_control=True,
    max_hold_time=14400,        # 4小时
    max_holding_days=30,        # 30天
    min_hold_time=10            # 10秒
)
```

## 参数优化建议

### 优化策略
1. **先优化 max_hold_time**: 对短期收益影响更大
2. **再优化 max_holding_days**: 控制长期风险
3. **结合市场特征**: 不同市场环境使用不同参数

### 注意事项
- 两个参数应该协调设置，避免冲突
- max_hold_time 通常应小于 max_holding_days * 24 * 3600
- 根据交易频率和风险承受能力调整参数
- 定期回测验证参数有效性

## 时间重置机制

### 重置触发条件

#### 1. 全部卖出重置
```python
if qty >= self.total_quantity:
    # 全部卖出时重置所有状态
    self.first_buy_time = None
    self.total_quantity = 0
    self.total_cost = 0.0
```

#### 2. 大幅加仓重置
```python
# 当新买入占总仓位≥30%时，重置时间和止盈级别
if self._should_reset_profit_level(qty):
    self.profit_level_reached = 0
    self.first_buy_time = timestamp  # 重置为当前买入时间
```

#### 3. 小幅加仓保持
```python
# 当新买入占总仓位<30%时，保持原有时间
if self.first_buy_time is None:
    self.first_buy_time = timestamp  # 仅在首次买入时设置
```

### 重置逻辑说明

| 场景 | 时间重置 | 原因 |
|------|----------|------|
| 首次买入 | 设置为买入时间 | 开始计时 |
| 小幅加仓(<30%) | 保持最早时间 | 维持原有风险控制 |
| 大幅加仓(≥30%) | 重置为最新时间 | 避免新资金承担旧风险 |
| 部分卖出 | 保持不变 | 剩余仓位继续计时 |
| 全部卖出 | 重置为None | 清空所有状态 |

### 风险控制意义

1. **避免时间风险累积**: 大幅加仓时重置时间，防止新资金承担旧仓位的时间风险
2. **保持策略连续性**: 小幅加仓时保持时间，维持原有交易逻辑
3. **灵活风险管理**: 30%阈值平衡了风险控制和交易灵活性

## 技术实现

### 配置集成
- 参数已集成到 `StrategyConfig` 统一配置系统
- 支持 Streamlit 界面配置
- 支持参数优化算法

### 回测支持
- 完全集成到 `EnhancedBacktest` 回测引擎
- 支持实时时间计算和重置逻辑
- 提供详细的止损原因说明

### 界面展示
- 回测分析页面提供双重时间参数配置
- 清晰的参数说明和使用建议
- 实时参数验证和冲突检查

### 代码实现
```python
def add_position(self, qty: int, price: float, timestamp: datetime = None):
    """添加持仓"""
    if timestamp is None:
        timestamp = datetime.now()
    
    # 记录批次信息
    self.batches.append(PositionBatch(qty, price, timestamp))
    self.total_quantity += qty
    self.total_cost += qty * price
    
    # 首次买入设置时间
    if self.first_buy_time is None:
        self.first_buy_time = timestamp

    # 智能重置机制：大幅加仓时重置时间和止盈级别
    if self._should_reset_profit_level(qty):
        self.profit_level_reached = 0
        self.first_buy_time = timestamp  # 重置时间
```