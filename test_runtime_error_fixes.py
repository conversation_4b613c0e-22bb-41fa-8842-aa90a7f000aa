#!/usr/bin/env python3
"""
测试运行时错误修复
验证所有修复是否生效
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_database_manager_fix():
    """测试DatabaseManager修复"""
    print("🔍 测试DatabaseManager修复...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.database_manager import DatabaseManager
        
        # 测试get_instance方法
        db_manager = DatabaseManager.get_instance()
        print(f"✅ DatabaseManager.get_instance()方法可用")
        print(f"✅ 数据库管理器类型: {type(db_manager).__name__}")
        
        # 测试数据库连接
        with db_manager.get_connection('ticks') as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            print(f"✅ 数据库连接正常，表数量: {len(tables)}")
        
        return True
        
    except Exception as e:
        print(f"❌ DatabaseManager测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_trader_import():
    """测试增强版交易器导入"""
    print("🔍 测试增强版交易器导入...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import (
            enhanced_trader, start_enhanced_trading, stop_enhanced_trading,
            get_enhanced_trading_status, close_all_enhanced_positions
        )
        
        print("✅ 增强版交易器导入成功")
        
        # 测试状态获取
        status = get_enhanced_trading_status()
        print(f"✅ 状态获取成功: 运行状态 = {status.get('is_running', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 增强版交易器导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_level_fixes():
    """测试日志级别修复"""
    print("🔍 测试日志级别修复...")
    
    try:
        # 测试strategy_engine_enhanced
        import strategy_engine_enhanced
        print("✅ strategy_engine_enhanced导入成功")
        
        # 测试backtest_enhanced
        import backtest_enhanced
        print("✅ backtest_enhanced导入成功")
        
        # 检查日志级别设置
        import logging
        logger = logging.getLogger()
        print(f"✅ 当前日志级别: {logging.getLevelName(logger.level)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志级别测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_infrastructure():
    """测试核心基础设施"""
    print("🔍 测试核心基础设施...")
    
    try:
        from etf_arbitrage_streamlit_multi.core import (
            DatabaseManager, ConfigManager, LoggerManager, 
            ExceptionHandler, ServiceRegistry
        )
        
        print("✅ 核心基础设施导入成功")
        
        # 测试各个组件
        db_manager = DatabaseManager.get_instance()
        config_manager = ConfigManager()
        logger_manager = LoggerManager()
        exception_handler = ExceptionHandler()
        service_registry = ServiceRegistry()
        
        print("✅ 所有核心组件初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心基础设施测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_v2_trader():
    """测试V2版本交易器"""
    print("🔍 测试V2版本交易器...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader_v2 import (
            EnhancedRealTimeTrader, start_enhanced_trading, 
            get_enhanced_trading_status
        )
        
        print("✅ V2版本交易器导入成功")
        
        # 创建交易器实例
        trader = EnhancedRealTimeTrader()
        print(f"✅ V2交易器初始化成功，初始资金: {trader.initial_capital:,.2f}")
        
        # 测试状态获取
        status = trader.get_enhanced_status()
        print(f"✅ V2状态获取成功，包含 {len(status)} 个字段")
        
        return True
        
    except Exception as e:
        print(f"❌ V2版本交易器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_column_fixes():
    """测试数据库列修复"""
    print("🔍 测试数据库列修复...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.database_manager import DatabaseManager
        
        db_manager = DatabaseManager.get_instance()
        
        # 测试ticks表结构
        with db_manager.get_connection('ticks') as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(ticks)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            print(f"✅ ticks表列数: {len(columns)}")
            print(f"✅ 列名: {column_names}")
            
            # 检查索引
            cursor.execute("PRAGMA index_list(ticks)")
            indexes = cursor.fetchall()
            print(f"✅ 索引数量: {len(indexes)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库列测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_streamlit_compatibility():
    """测试Streamlit兼容性"""
    print("🔍 测试Streamlit兼容性...")
    
    try:
        # 模拟检查页面文件语法
        page_files = [
            "etf_arbitrage_streamlit_multi/pages/2_🔬_回测分析.py",
            "etf_arbitrage_streamlit_multi/pages/3_🚀_实时交易_增强版.py"
        ]
        
        for page_file in page_files:
            if Path(page_file).exists():
                # 简单的语法检查
                with open(page_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否还有st.write(..., help=...)的用法
                if 'st.write(' in content and 'help=' in content:
                    # 进一步检查是否在同一行
                    lines = content.split('\n')
                    problematic_lines = []
                    for i, line in enumerate(lines, 1):
                        if 'st.write(' in line and 'help=' in line:
                            problematic_lines.append((i, line.strip()))
                    
                    if problematic_lines:
                        print(f"⚠️ {page_file} 仍有st.write的help参数:")
                        for line_num, line in problematic_lines:
                            print(f"   第{line_num}行: {line}")
                    else:
                        print(f"✅ {page_file} st.write修复完成")
                else:
                    print(f"✅ {page_file} 无st.write的help参数问题")
            else:
                print(f"⚠️ {page_file} 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始运行时错误修复验证")
    print("=" * 60)
    
    tests = [
        test_database_manager_fix,
        test_enhanced_trader_import,
        test_log_level_fixes,
        test_core_infrastructure,
        test_v2_trader,
        test_database_column_fixes,
        test_streamlit_compatibility
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有运行时错误修复验证通过！")
        print("✅ 系统应该可以正常启动了")
        return True
    else:
        print("⚠️ 部分测试失败，可能还有问题需要解决")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
