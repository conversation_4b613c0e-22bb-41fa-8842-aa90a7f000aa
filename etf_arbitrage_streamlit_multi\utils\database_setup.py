#!/usr/bin/env python3
"""
数据库初始化模块
确保优化配置表存在
"""

import sqlite3
import os

def ensure_optimal_configs_table():
    """确保optimal_configs表存在"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.cursor()
        
        # 创建optimal_configs表（如果不存在）
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS optimal_configs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                config_name TEXT NOT NULL,
                parameters TEXT NOT NULL,
                performance_metrics TEXT NOT NULL,
                optimization_method TEXT,
                fitness_score REAL,
                created_at TEXT,
                updated_at TEXT,
                UNIQUE(symbol, config_name)
            )
        """)
        
        # 创建索引
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_optimal_configs_symbol 
            ON optimal_configs(symbol)
        """)
        
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_optimal_configs_fitness 
            ON optimal_configs(fitness_score DESC)
        """)
        
        conn.commit()
        conn.close()
        return True
        
    except Exception as e:
        print(f"数据库表创建失败: {e}")
        return False

if __name__ == "__main__":
    ensure_optimal_configs_table()