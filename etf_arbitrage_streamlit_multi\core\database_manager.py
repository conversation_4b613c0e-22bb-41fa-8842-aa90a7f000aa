"""
统一数据库管理器
提供统一的数据库连接、操作和管理功能
"""

import sqlite3
import threading
import logging
from typing import Dict, List, Optional, Any, Union
from contextlib import contextmanager
from pathlib import Path
from datetime import datetime
import pandas as pd

logger = logging.getLogger(__name__)

class DatabaseManager:
    """统一数据库管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return

        self._initialized = True
        self._connections = {}
        self._connection_lock = threading.Lock()
        self._db_configs = {
            'ticks': 'ticks.db',
            'monitoring': 'monitoring.db',
            'backtest': 'backtest.db'
        }

        # 初始化数据库
        self._initialize_databases()

    @classmethod
    def get_instance(cls):
        """获取单例实例"""
        return cls()
    
    def _initialize_databases(self):
        """初始化所有数据库"""
        try:
            # 初始化ticks数据库
            self._init_ticks_db()
            # 初始化监控数据库
            self._init_monitoring_db()
            # 初始化回测数据库
            self._init_backtest_db()
            
            logger.info("所有数据库初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _init_ticks_db(self):
        """初始化ticks数据库"""
        with self.get_connection('ticks') as conn:
            cursor = conn.cursor()

            # 检查现有表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ticks'")
            ticks_exists = cursor.fetchone()

            if not ticks_exists:
                # 创建新的ticks表
                cursor.execute('''
                    CREATE TABLE ticks (
                        symbol TEXT,
                        tick_time TEXT,
                        price REAL,
                        volume INTEGER,
                        side TEXT,
                        raw TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (symbol, tick_time)
                    )
                ''')
            else:
                # 检查是否需要添加created_at列
                cursor.execute("PRAGMA table_info(ticks)")
                columns = [col[1] for col in cursor.fetchall()]
                if 'created_at' not in columns:
                    try:
                        cursor.execute('ALTER TABLE ticks ADD COLUMN created_at TIMESTAMP')
                    except Exception as e:
                        logger.warning(f"添加created_at列失败: {e}")

            # 检查last_ticks表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='last_ticks'")
            last_ticks_exists = cursor.fetchone()

            if not last_ticks_exists:
                # 创建last_ticks表
                cursor.execute('''
                    CREATE TABLE last_ticks (
                        symbol TEXT PRIMARY KEY,
                        last_time TEXT,
                        last_price REAL,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
            else:
                # 检查是否需要添加列
                cursor.execute("PRAGMA table_info(last_ticks)")
                columns = [col[1] for col in cursor.fetchall()]
                if 'last_price' not in columns:
                    try:
                        cursor.execute('ALTER TABLE last_ticks ADD COLUMN last_price REAL')
                    except Exception as e:
                        logger.warning(f"添加last_price列失败: {e}")
                if 'updated_at' not in columns:
                    try:
                        cursor.execute('ALTER TABLE last_ticks ADD COLUMN updated_at TIMESTAMP')
                    except Exception as e:
                        logger.warning(f"添加updated_at列失败: {e}")

            # 创建索引（如果不存在）
            try:
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_ticks_symbol_time ON ticks(symbol, tick_time)')

                # 检查created_at列是否存在再创建索引
                cursor.execute("PRAGMA table_info(ticks)")
                columns = [col[1] for col in cursor.fetchall()]
                if 'created_at' in columns:
                    cursor.execute('CREATE INDEX IF NOT EXISTS idx_ticks_created_at ON ticks(created_at)')
            except Exception as e:
                logger.warning(f"创建索引失败: {e}")

            conn.commit()
    
    def _init_monitoring_db(self):
        """初始化监控数据库"""
        with self.get_connection('monitoring') as conn:
            cursor = conn.cursor()
            
            # 创建系统监控表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    cpu_usage REAL,
                    memory_usage REAL,
                    disk_usage REAL,
                    network_io TEXT
                )
            ''')
            
            # 创建业务监控表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS business_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    metric_name TEXT,
                    metric_value REAL,
                    metric_unit TEXT,
                    tags TEXT
                )
            ''')
            
            # 创建告警记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    alert_type TEXT,
                    severity TEXT,
                    message TEXT,
                    resolved BOOLEAN DEFAULT FALSE,
                    resolved_at TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def _init_backtest_db(self):
        """初始化回测数据库"""
        with self.get_connection('backtest') as conn:
            cursor = conn.cursor()
            
            # 创建回测结果表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS backtest_results (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id TEXT UNIQUE,
                    symbol TEXT,
                    start_date TEXT,
                    end_date TEXT,
                    strategy_name TEXT,
                    parameters TEXT,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    total_trades INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建交易记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS trade_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id TEXT,
                    timestamp TEXT,
                    symbol TEXT,
                    action TEXT,
                    quantity INTEGER,
                    price REAL,
                    amount REAL,
                    commission REAL,
                    pnl REAL,
                    FOREIGN KEY (run_id) REFERENCES backtest_results(run_id)
                )
            ''')
            
            conn.commit()
    
    @contextmanager
    def get_connection(self, db_name: str = 'ticks'):
        """获取数据库连接（上下文管理器）"""
        if db_name not in self._db_configs:
            raise ValueError(f"未知的数据库: {db_name}")
        
        db_path = self._db_configs[db_name]
        thread_id = threading.get_ident()
        conn_key = f"{db_name}_{thread_id}"
        
        with self._connection_lock:
            if conn_key not in self._connections:
                self._connections[conn_key] = sqlite3.connect(
                    db_path, 
                    check_same_thread=False,
                    timeout=30.0
                )
                self._connections[conn_key].row_factory = sqlite3.Row
        
        conn = self._connections[conn_key]
        try:
            yield conn
        except Exception as e:
            conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            # 连接保持打开状态，由连接池管理
            pass
    
    def execute_query(self, query: str, params: tuple = (), db_name: str = 'ticks') -> List[Dict]:
        """执行查询并返回结果"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
    
    def execute_update(self, query: str, params: tuple = (), db_name: str = 'ticks') -> int:
        """执行更新操作并返回影响的行数"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            return cursor.rowcount
    
    def execute_many(self, query: str, params_list: List[tuple], db_name: str = 'ticks') -> int:
        """批量执行操作"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
    
    def read_dataframe(self, query: str, params: tuple = (), db_name: str = 'ticks') -> pd.DataFrame:
        """读取数据为DataFrame"""
        with self.get_connection(db_name) as conn:
            return pd.read_sql_query(query, conn, params=params)
    
    def save_tick_data(self, symbol: str, tick_time: str, price: float, volume: int, side: str = None, raw: str = None) -> bool:
        """保存tick数据"""
        try:
            with self.get_connection('ticks') as conn:
                cursor = conn.cursor()

                # 检查表结构，决定插入语句
                cursor.execute("PRAGMA table_info(ticks)")
                columns = [col[1] for col in cursor.fetchall()]

                if 'created_at' in columns:
                    cursor.execute('''
                        INSERT OR REPLACE INTO ticks
                        (symbol, tick_time, price, volume, side, raw, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (symbol, tick_time, price, volume, side, raw))
                else:
                    cursor.execute('''
                        INSERT OR REPLACE INTO ticks
                        (symbol, tick_time, price, volume, side, raw)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ''', (symbol, tick_time, price, volume, side, raw))

                # 更新last_ticks
                cursor.execute("PRAGMA table_info(last_ticks)")
                last_columns = [col[1] for col in cursor.fetchall()]

                if 'last_price' in last_columns and 'updated_at' in last_columns:
                    cursor.execute('''
                        INSERT OR REPLACE INTO last_ticks
                        (symbol, last_time, last_price, updated_at)
                        VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                    ''', (symbol, tick_time, price))
                else:
                    cursor.execute('''
                        INSERT OR REPLACE INTO last_ticks
                        (symbol, last_time)
                        VALUES (?, ?)
                    ''', (symbol, tick_time))

                conn.commit()
                return True
        except Exception as e:
            logger.error(f"保存tick数据失败: {e}")
            return False

    def insert_ticks(self, ticks_data: List[Dict]) -> int:
        """批量插入tick数据"""
        if not ticks_data:
            return 0

        # 检查表结构
        with self.get_connection('ticks') as conn:
            cursor = conn.cursor()
            cursor.execute("PRAGMA table_info(ticks)")
            columns = [col[1] for col in cursor.fetchall()]

        if 'created_at' in columns:
            query = '''
                INSERT OR REPLACE INTO ticks
                (symbol, tick_time, price, volume, side, raw, created_at)
                VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            '''
        else:
            query = '''
                INSERT OR REPLACE INTO ticks
                (symbol, tick_time, price, volume, side, raw)
                VALUES (?, ?, ?, ?, ?, ?)
            '''

        params_list = [
            (
                tick['symbol'],
                tick['tick_time'],
                tick['price'],
                tick['volume'],
                tick.get('side', ''),
                tick.get('raw', '')
            )
            for tick in ticks_data
        ]

        return self.execute_many(query, params_list, 'ticks')
    
    def get_latest_ticks(self, symbol: str, limit: int = 100) -> pd.DataFrame:
        """获取最新的tick数据"""
        query = '''
            SELECT * FROM ticks 
            WHERE symbol = ? 
            ORDER BY tick_time DESC 
            LIMIT ?
        '''
        return self.read_dataframe(query, (symbol, limit), 'ticks')
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """清理旧数据"""
        cutoff_date = datetime.now().strftime('%Y-%m-%d')
        
        # 清理旧的tick数据
        query = '''
            DELETE FROM ticks 
            WHERE created_at < datetime('now', '-{} days')
        '''.format(days_to_keep)
        
        deleted_count = self.execute_update(query, (), 'ticks')
        logger.info(f"清理了 {deleted_count} 条旧tick数据")
        
        return deleted_count
    
    def close_all_connections(self):
        """关闭所有数据库连接"""
        with self._connection_lock:
            for conn in self._connections.values():
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接失败: {e}")
            self._connections.clear()
        
        logger.info("所有数据库连接已关闭")

# 全局数据库管理器实例
db_manager = DatabaseManager()
