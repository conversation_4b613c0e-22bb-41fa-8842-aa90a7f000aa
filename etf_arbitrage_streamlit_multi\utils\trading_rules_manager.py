#!/usr/bin/env python3
"""
A股交易规则管理器
实现完整的A股交易规则验证，包括T+0/T+1规则、涨跌停限制、交易时间验证等
"""

from datetime import datetime, time, date
from typing import Dict, List, Tuple, Optional, Any
import logging
import re

logger = logging.getLogger(__name__)

class TradingRulesManager:
    """A股交易规则管理器"""
    
    def __init__(self):
        """初始化交易规则管理器"""
        self.rules_version = "1.0.0"
        self.last_updated = datetime.now()
        
        # T+0 ETF白名单
        self.t0_etf_whitelist = {
            # 恒生科技ETF
            '159740': '恒生科技ETF',
            # 中概互联ETF
            '513050': '中概互联ETF',
            # 纳指ETF
            '513100': '纳指ETF',
            # 货币ETF系列
            '511010': '国债ETF',
            '511020': '活钱宝',
            '511030': '5年国债',
            '511220': '海富通货币',
            '511260': '上证10年期国债ETF',
            '511270': '海富通上证10年期地方政府债ETF',
            '511280': '华宝添益',
            '511290': '银华日利',
            '511380': '银华交易型货币',
            '511880': '银华日利ETF',
            '511990': '华宝添益ETF',
            # 黄金ETF
            '518880': '黄金ETF',
            '518800': '黄金基金',
            # 其他T+0 ETF
            '159001': '易方达保证金收益货币',
            '159003': '招商快线货币',
        }
        
        # 板块涨跌停规则
        self.price_limit_rules = {
            'main_board': 0.10,      # 主板10%
            'chinext': 0.20,         # 创业板20%
            'star_market': 0.20,     # 科创板20%
            'bse': 0.30,            # 北交所30%
            'st_stock': 0.05,       # ST股票5%
        }
        
        # 交易时间配置
        self.trading_hours = {
            'morning_start': time(9, 30),
            'morning_end': time(11, 30),
            'afternoon_start': time(13, 0),
            'afternoon_end': time(15, 0),
            'auction_morning': time(9, 15),  # 集合竞价开始
            'auction_end': time(9, 25),      # 集合竞价结束
            'auction_afternoon': time(14, 57), # 尾盘集合竞价
        }
        
        # 特殊规则配置
        self.special_rules = {
            'min_trade_unit': 100,           # 最小交易单位100股
            'max_single_order': 1000000,     # 单笔最大订单100万股
            'new_stock_no_limit_days': 5,    # 新股上市无涨跌停天数
        }
        
        logger.info(f"交易规则管理器初始化完成，版本: {self.rules_version}")
    
    def is_t0_tradable(self, symbol: str) -> Tuple[bool, str]:
        """
        判断股票是否支持T+0交易
        
        Args:
            symbol: 股票代码
            
        Returns:
            (是否支持T+0, 说明信息)
        """
        try:
            # 检查是否在T+0白名单中
            if symbol in self.t0_etf_whitelist:
                etf_name = self.t0_etf_whitelist[symbol]
                return True, f"{symbol}({etf_name})支持T+0交易"
            
            # 检查是否为货币ETF（511开头）
            if symbol.startswith('511'):
                return True, f"{symbol}为货币ETF，支持T+0交易"
            
            # 检查是否为债券ETF（某些特定规则）
            if self._is_bond_etf(symbol):
                return True, f"{symbol}为债券ETF，支持T+0交易"
            
            # 其他股票默认T+1
            return False, f"{symbol}为普通股票，仅支持T+1交易"
            
        except Exception as e:
            logger.error(f"判断T+0交易规则时出错: {e}")
            return False, f"判断T+0规则失败: {str(e)}"
    
    def _is_bond_etf(self, symbol: str) -> bool:
        """判断是否为债券ETF"""
        # 简化的债券ETF判断逻辑，实际应用中可能需要更复杂的规则
        bond_etf_patterns = ['511', '159']  # 部分债券ETF的代码模式
        return any(symbol.startswith(pattern) for pattern in bond_etf_patterns)

    def identify_board(self, symbol: str) -> str:
        """
        识别股票所属板块（公开方法）

        Args:
            symbol: 股票代码

        Returns:
            板块名称
        """
        return self._identify_board(symbol)

    def get_price_limit(self, symbol: str, prev_close: float = None) -> Dict[str, Any]:
        """
        获取股票的涨跌停限制
        
        Args:
            symbol: 股票代码
            prev_close: 前收盘价
            
        Returns:
            包含涨跌停信息的字典
        """
        try:
            board = self._identify_board(symbol)
            limit_ratio = self.price_limit_rules.get(board, 0.10)
            
            result = {
                'symbol': symbol,
                'board': board,
                'limit_ratio': limit_ratio,
                'limit_percentage': f"{limit_ratio*100:.0f}%"
            }
            
            # 如果提供了前收盘价，计算具体的涨跌停价格
            if prev_close is not None:
                upper_limit = prev_close * (1 + limit_ratio)
                lower_limit = prev_close * (1 - limit_ratio)
                result.update({
                    'prev_close': prev_close,
                    'upper_limit': round(upper_limit, 2),
                    'lower_limit': round(lower_limit, 2)
                })
            
            return result
            
        except Exception as e:
            logger.error(f"获取涨跌停限制时出错: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'limit_ratio': 0.10  # 默认10%
            }
    
    def _identify_board(self, symbol: str) -> str:
        """
        识别股票所属板块
        
        Args:
            symbol: 股票代码
            
        Returns:
            板块标识
        """
        try:
            # 创业板：300开头
            if symbol.startswith('300'):
                return 'chinext'
            
            # 科创板：688开头
            elif symbol.startswith('688'):
                return 'star_market'
            
            # 北交所：8开头或43开头
            elif symbol.startswith('8') or symbol.startswith('43'):
                return 'bse'
            
            # ST股票：包含ST字样（这里简化处理）
            elif 'ST' in symbol.upper():
                return 'st_stock'
            
            # 主板：000、001、002、600、601、603开头
            elif any(symbol.startswith(prefix) for prefix in ['000', '001', '002', '600', '601', '603']):
                return 'main_board'
            
            # ETF等其他：默认按主板处理
            else:
                return 'main_board'
                
        except Exception as e:
            logger.error(f"识别板块时出错: {e}")
            return 'main_board'  # 默认主板

    def is_valid_trading_time(self, current_time: datetime = None) -> Tuple[bool, str]:
        """
        检查当前是否为有效交易时间

        Args:
            current_time: 当前时间，如果为None则使用系统当前时间

        Returns:
            (是否为交易时间, 状态说明)
        """
        if current_time is None:
            current_time = datetime.now()

        try:
            # 检查是否为工作日（简化处理，实际应考虑节假日）
            if current_time.weekday() >= 5:  # 周六、周日
                return False, "非交易日（周末）"

            current_time_only = current_time.time()

            # 检查是否在交易时间内
            morning_trading = (self.trading_hours['morning_start'] <= current_time_only <= self.trading_hours['morning_end'])
            afternoon_trading = (self.trading_hours['afternoon_start'] <= current_time_only <= self.trading_hours['afternoon_end'])

            if morning_trading:
                return True, "上午交易时间"
            elif afternoon_trading:
                return True, "下午交易时间"
            else:
                return False, f"非交易时间（当前时间：{current_time_only.strftime('%H:%M:%S')}）"

        except Exception as e:
            logger.error(f"检查交易时间时出错: {e}")
            return False, f"交易时间检查失败: {str(e)}"

    def is_auction_time(self, current_time: datetime = None) -> Tuple[bool, str]:
        """
        检查当前是否为集合竞价时间

        Args:
            current_time: 当前时间

        Returns:
            (是否为集合竞价时间, 说明)
        """
        if current_time is None:
            current_time = datetime.now()

        try:
            current_time_only = current_time.time()

            # 早盘集合竞价
            if self.trading_hours['auction_morning'] <= current_time_only <= self.trading_hours['auction_end']:
                return True, "早盘集合竞价时间"

            # 尾盘集合竞价
            if current_time_only >= self.trading_hours['auction_afternoon']:
                return True, "尾盘集合竞价时间"

            return False, "非集合竞价时间"

        except Exception as e:
            logger.error(f"检查集合竞价时间时出错: {e}")
            return False, f"集合竞价时间检查失败: {str(e)}"

    def normalize_quantity(self, quantity: int) -> int:
        """
        标准化交易数量为100股整数倍

        Args:
            quantity: 原始数量

        Returns:
            标准化后的数量
        """
        try:
            min_unit = self.special_rules['min_trade_unit']
            normalized = (quantity // min_unit) * min_unit

            if normalized != quantity:
                logger.info(f"数量标准化: {quantity} -> {normalized}")

            return normalized

        except Exception as e:
            logger.error(f"标准化数量时出错: {e}")
            return quantity

    def validate_trade_compliance(self, symbol: str, trade_type: str, quantity: int,
                                 current_price: float, prev_close: float = None,
                                 current_time: datetime = None) -> Dict[str, Any]:
        """
        验证交易合规性

        Args:
            symbol: 股票代码
            trade_type: 交易类型 ('buy' 或 'sell')
            quantity: 交易数量
            current_price: 当前价格
            prev_close: 前收盘价
            current_time: 当前时间

        Returns:
            合规性检查结果
        """
        result = {
            'symbol': symbol,
            'trade_type': trade_type,
            'quantity': quantity,
            'current_price': current_price,
            'is_compliant': True,
            'violations': [],
            'warnings': [],
            'normalized_quantity': quantity
        }

        try:
            # 1. 交易时间检查
            is_trading_time, time_msg = self.is_valid_trading_time(current_time)
            if not is_trading_time:
                result['violations'].append(f"交易时间限制: {time_msg}")
                result['is_compliant'] = False

            # 2. 数量标准化检查
            normalized_qty = self.normalize_quantity(quantity)
            if normalized_qty != quantity:
                result['warnings'].append(f"数量已标准化: {quantity} -> {normalized_qty}")
                result['normalized_quantity'] = normalized_qty

            if normalized_qty <= 0:
                result['violations'].append("交易数量必须大于0")
                result['is_compliant'] = False

            # 3. 最大订单限制检查
            max_order = self.special_rules['max_single_order']
            if normalized_qty > max_order:
                result['violations'].append(f"单笔订单超过限制: {normalized_qty} > {max_order}")
                result['is_compliant'] = False

            # 4. 涨跌停限制检查
            if prev_close is not None:
                price_limit_info = self.get_price_limit(symbol, prev_close)
                if 'upper_limit' in price_limit_info and 'lower_limit' in price_limit_info:
                    upper_limit = price_limit_info['upper_limit']
                    lower_limit = price_limit_info['lower_limit']

                    if current_price >= upper_limit:
                        result['violations'].append(f"价格触及涨停: {current_price} >= {upper_limit}")
                        result['is_compliant'] = False
                    elif current_price <= lower_limit:
                        result['violations'].append(f"价格触及跌停: {current_price} <= {lower_limit}")
                        result['is_compliant'] = False

            # 5. T+0/T+1规则检查（针对卖出）
            if trade_type.lower() == 'sell':
                is_t0, t0_msg = self.is_t0_tradable(symbol)
                if not is_t0:
                    result['warnings'].append(f"T+1限制: {t0_msg}")

            # 6. 集合竞价时间特殊处理
            is_auction, auction_msg = self.is_auction_time(current_time)
            if is_auction:
                result['warnings'].append(f"集合竞价时间: {auction_msg}")

            return result

        except Exception as e:
            logger.error(f"验证交易合规性时出错: {e}")
            result['violations'].append(f"合规性检查失败: {str(e)}")
            result['is_compliant'] = False
            return result

    def check_price_limits(self, symbol: str, current_price: float, prev_close: float) -> Dict[str, Any]:
        """
        检查价格涨跌停限制

        Args:
            symbol: 股票代码
            current_price: 当前价格
            prev_close: 前收盘价

        Returns:
            价格限制检查结果
        """
        try:
            price_limit_info = self.get_price_limit(symbol, prev_close)

            result = {
                'symbol': symbol,
                'current_price': current_price,
                'prev_close': prev_close,
                'is_valid': True,
                'status': 'normal',
                'message': '价格正常'
            }

            if 'upper_limit' in price_limit_info and 'lower_limit' in price_limit_info:
                upper_limit = price_limit_info['upper_limit']
                lower_limit = price_limit_info['lower_limit']

                result.update({
                    'upper_limit': upper_limit,
                    'lower_limit': lower_limit,
                    'limit_ratio': price_limit_info['limit_ratio']
                })

                if current_price >= upper_limit:
                    result.update({
                        'is_valid': False,
                        'status': 'upper_limit',
                        'message': f'价格触及涨停限制: {current_price} >= {upper_limit}'
                    })
                elif current_price <= lower_limit:
                    result.update({
                        'is_valid': False,
                        'status': 'lower_limit',
                        'message': f'价格触及跌停限制: {current_price} <= {lower_limit}'
                    })
                else:
                    # 计算距离涨跌停的百分比
                    to_upper = (upper_limit - current_price) / prev_close
                    to_lower = (current_price - lower_limit) / prev_close
                    result.update({
                        'distance_to_upper': round(to_upper * 100, 2),
                        'distance_to_lower': round(to_lower * 100, 2)
                    })

            return result

        except Exception as e:
            logger.error(f"检查价格限制时出错: {e}")
            return {
                'symbol': symbol,
                'current_price': current_price,
                'is_valid': False,
                'status': 'error',
                'message': f'价格检查失败: {str(e)}'
            }

    def get_trading_status_summary(self, symbol: str, current_time: datetime = None) -> Dict[str, Any]:
        """
        获取交易状态摘要

        Args:
            symbol: 股票代码
            current_time: 当前时间

        Returns:
            交易状态摘要
        """
        try:
            if current_time is None:
                current_time = datetime.now()

            # 获取各种状态
            is_trading_time, time_msg = self.is_valid_trading_time(current_time)
            is_auction, auction_msg = self.is_auction_time(current_time)
            is_t0, t0_msg = self.is_t0_tradable(symbol)
            price_limit_info = self.get_price_limit(symbol)

            summary = {
                'symbol': symbol,
                'current_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'trading_time': {
                    'is_valid': is_trading_time,
                    'message': time_msg
                },
                'auction_time': {
                    'is_auction': is_auction,
                    'message': auction_msg
                },
                't0_trading': {
                    'is_supported': is_t0,
                    'message': t0_msg
                },
                'price_limits': price_limit_info,
                'rules_version': self.rules_version,
                'last_updated': self.last_updated.strftime('%Y-%m-%d %H:%M:%S')
            }

            return summary

        except Exception as e:
            logger.error(f"获取交易状态摘要时出错: {e}")
            return {
                'symbol': symbol,
                'error': str(e),
                'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

    def update_t0_whitelist(self, new_whitelist: Dict[str, str]) -> bool:
        """
        更新T+0白名单

        Args:
            new_whitelist: 新的白名单字典

        Returns:
            是否更新成功
        """
        try:
            old_count = len(self.t0_etf_whitelist)
            self.t0_etf_whitelist.update(new_whitelist)
            new_count = len(self.t0_etf_whitelist)

            self.last_updated = datetime.now()
            logger.info(f"T+0白名单已更新: {old_count} -> {new_count}")
            return True

        except Exception as e:
            logger.error(f"更新T+0白名单时出错: {e}")
            return False


# 全局实例
_trading_rules_manager = None

def get_trading_rules_manager() -> TradingRulesManager:
    """获取全局交易规则管理器实例"""
    global _trading_rules_manager
    if _trading_rules_manager is None:
        _trading_rules_manager = TradingRulesManager()
    return _trading_rules_manager


# 测试函数
def test_trading_rules_manager():
    """测试交易规则管理器功能"""
    print("🧪 测试交易规则管理器...")

    manager = get_trading_rules_manager()

    # 测试T+0判断
    test_symbols = ['159740', '513050', '000001', '300001', '688001']
    print("\n📊 T+0交易规则测试:")
    for symbol in test_symbols:
        is_t0, msg = manager.is_t0_tradable(symbol)
        print(f"  {symbol}: {'✅' if is_t0 else '❌'} {msg}")

    # 测试板块识别和涨跌停
    print("\n📈 涨跌停限制测试:")
    for symbol in test_symbols:
        limit_info = manager.get_price_limit(symbol, 10.0)
        print(f"  {symbol}: {limit_info['board']} - {limit_info['limit_percentage']}")

    # 测试交易时间
    print("\n⏰ 交易时间测试:")
    is_trading, time_msg = manager.is_valid_trading_time()
    print(f"  当前交易时间状态: {'✅' if is_trading else '❌'} {time_msg}")

    # 测试合规性检查
    print("\n✅ 合规性检查测试:")
    compliance = manager.validate_trade_compliance(
        symbol='159740',
        trade_type='buy',
        quantity=150,
        current_price=0.75,
        prev_close=0.74
    )
    print(f"  合规性: {'✅' if compliance['is_compliant'] else '❌'}")
    if compliance['violations']:
        print(f"  违规项: {compliance['violations']}")
    if compliance['warnings']:
        print(f"  警告项: {compliance['warnings']}")

    print("\n🎉 交易规则管理器测试完成!")


if __name__ == "__main__":
    test_trading_rules_manager()
