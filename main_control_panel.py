#!/usr/bin/env python3
"""
ETF套利交易系统 - 主控制面板
整合数据采集、回测、实时交易、监控等所有功能
"""

import streamlit as st
import subprocess
import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import logging
import time
import plotly.express as px
import plotly.graph_objects as go
from typing import Dict, List, Any, Optional
import json

# 设置页面配置
st.set_page_config(
    page_title="ETF套利交易系统 - 主控制面板",
    page_icon="🎛️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MonitoringDashboard:
    """监控结果查看面板"""
    
    def __init__(self):
        self.db_path = "monitoring.db"
        self.init_database()
    
    def init_database(self):
        """初始化监控数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建性能监控表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    cpu_percent REAL,
                    memory_percent REAL,
                    disk_io_read REAL,
                    disk_io_write REAL,
                    network_io_sent REAL,
                    network_io_recv REAL,
                    process_count INTEGER
                )
            """)
            
            # 创建业务监控表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS business_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    trade_count INTEGER,
                    signal_accuracy REAL
                )
            """)
            
            # 创建告警表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    source TEXT,
                    severity TEXT,
                    title TEXT,
                    message TEXT,
                    acknowledged BOOLEAN DEFAULT 0,
                    resolved BOOLEAN DEFAULT 0,
                    acknowledged_by TEXT,
                    resolved_by TEXT,
                    resolved_at DATETIME
                )
            """)
            
            conn.commit()
            conn.close()
            logger.info("监控数据库初始化完成")
            
        except Exception as e:
            logger.error(f"初始化监控数据库失败: {e}")
    
    def get_performance_data(self, hours: int = 24) -> pd.DataFrame:
        """获取性能监控数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
                SELECT * FROM performance_metrics 
                WHERE timestamp >= datetime('now', '-{hours} hours')
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取性能数据失败: {e}")
            return pd.DataFrame()
    
    def get_business_data(self, symbol: str = "159740", hours: int = 24) -> pd.DataFrame:
        """获取业务监控数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
                SELECT * FROM business_metrics 
                WHERE symbol = ? AND timestamp >= datetime('now', '-{hours} hours')
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            df = pd.read_sql_query(query, conn, params=(symbol,))
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取业务数据失败: {e}")
            return pd.DataFrame()
    
    def get_active_alerts(self) -> pd.DataFrame:
        """获取活跃告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = """
                SELECT * FROM alerts 
                WHERE resolved = 0 
                ORDER BY timestamp DESC
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取活跃告警失败: {e}")
            return pd.DataFrame()
    
    def acknowledge_alert(self, alert_id: int, user: str = "admin"):
        """确认告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute(
                "UPDATE alerts SET acknowledged = 1, acknowledged_by = ? WHERE id = ?",
                (user, alert_id)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"确认告警失败: {e}")
            return False
    
    def resolve_alert(self, alert_id: int, user: str = "admin"):
        """解决告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute(
                "UPDATE alerts SET resolved = 1, resolved_by = ?, resolved_at = CURRENT_TIMESTAMP WHERE id = ?",
                (user, alert_id)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False

class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.processes = {}
        
    def check_database_status(self):
        """检查数据库状态"""
        try:
            conn = sqlite3.connect("ticks.db")
            
            # 检查表是否存在
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ticks'")
            table_exists = cursor.fetchone() is not None
            
            if not table_exists:
                conn.close()
                return {
                    'total_records': 0,
                    'symbol_records': 0,
                    'latest_time': None,
                    'status': 'empty',
                    'error': '表不存在'
                }
            
            cursor = conn.execute("SELECT COUNT(*) FROM ticks")
            total_count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT COUNT(*) FROM ticks WHERE symbol='159740'")
            symbol_count = cursor.fetchone()[0]
            
            cursor = conn.execute("SELECT MAX(tick_time) FROM ticks WHERE symbol='159740'")
            latest_time_result = cursor.fetchone()[0]
            latest_time = latest_time_result if latest_time_result else None
            
            conn.close()
            
            return {
                'total_records': total_count,
                'symbol_records': symbol_count,
                'latest_time': latest_time,
                'status': 'healthy' if total_count > 0 else 'empty'
            }
        except Exception as e:
            return {
                'total_records': 0,
                'symbol_records': 0,
                'latest_time': None,
                'status': 'error',
                'error': str(e)
            }
    
    def get_data_summary(self):
        """获取数据摘要"""
        try:
            conn = sqlite3.connect("ticks.db")
            
            # 按日期统计数据
            query = """
            SELECT 
                DATE(tick_time) as date,
                COUNT(*) as count,
                MIN(price) as min_price,
                MAX(price) as max_price
            FROM ticks 
            WHERE symbol='159740'
            GROUP BY DATE(tick_time)
            ORDER BY date DESC
            LIMIT 7
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            return df
        except Exception as e:
            logger.error(f"获取数据摘要失败: {e}")
            return pd.DataFrame()

def create_performance_charts(df: pd.DataFrame):
    """创建性能监控图表"""
    if df.empty:
        st.warning("暂无性能监控数据")
        return
    
    # CPU和内存使用率
    fig1 = go.Figure()
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['cpu_percent'], 
                            name='CPU使用率', line=dict(color='blue')))
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['memory_percent'], 
                            name='内存使用率', line=dict(color='red')))
    fig1.update_layout(title='CPU和内存使用率', xaxis_title='时间', yaxis_title='百分比 (%)')
    st.plotly_chart(fig1, width='stretch')
    
    # 磁盘IO
    col1, col2 = st.columns(2)
    with col1:
        fig2 = px.line(df, x='timestamp', y='disk_io_read', 
                      title='磁盘读取速度 (MB/s)')
        st.plotly_chart(fig2, width='stretch')
    with col2:
        fig3 = px.line(df, x='timestamp', y='disk_io_write', 
                      title='磁盘写入速度 (MB/s)')
        st.plotly_chart(fig3, width='stretch')

def create_business_charts(df: pd.DataFrame):
    """创建业务监控图表"""
    if df.empty:
        st.warning("暂无业务监控数据")
        return
    
    # 收益率和夏普比率
    fig1 = go.Figure()
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['total_return'], 
                            name='总收益率', line=dict(color='green')))
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['sharpe_ratio'], 
                            name='夏普比率', line=dict(color='orange')))
    fig1.update_layout(title='收益表现', xaxis_title='时间', yaxis_title='数值')
    st.plotly_chart(fig1, width='stretch')
    
    # 风险指标
    col1, col2 = st.columns(2)
    with col1:
        fig2 = px.line(df, x='timestamp', y='max_drawdown', 
                      title='最大回撤')
        st.plotly_chart(fig2, width='stretch')
    with col2:
        fig3 = px.line(df, x='timestamp', y='win_rate', 
                      title='胜率')
        st.plotly_chart(fig3, width='stretch')

def display_alerts(alerts_df: pd.DataFrame, dashboard: MonitoringDashboard):
    """显示告警信息"""
    if alerts_df.empty:
        st.success("✅ 当前没有活跃告警")
        return
    
    st.subheader("🚨 活跃告警")
    
    for _, alert in alerts_df.iterrows():
        severity = alert['severity']
        
        with st.expander(f"{severity.upper()} - {alert['title']} - {alert['timestamp']}"):
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"**来源**: {alert['source']}")
                st.markdown(f"**严重程度**: {severity}")
                st.markdown(f"**时间**: {alert['timestamp']}")
            with col2:
                st.markdown(f"**消息**: {alert['message']}")
                if alert['acknowledged']:
                    st.success(f"✅ 已确认 by {alert['acknowledged_by']}")
                else:
                    if st.button("✅ 确认", key=f"ack_{alert['id']}"):
                        if dashboard.acknowledge_alert(alert['id'], "admin"):
                            st.success("告警已确认")
                            st.rerun()
                
                if alert['resolved']:
                    st.success(f"✅ 已解决 by {alert['resolved_by']}")
                else:
                    if st.button("✅ 解决", key=f"resolve_{alert['id']}"):
                        if dashboard.resolve_alert(alert['id'], "admin"):
                            st.success("告警已解决")
                            st.rerun()

def show_monitoring_page():
    """显示监控页面"""
    # 导入并运行独立的监控面板
    try:
        import sys
        import os
        
        # 添加monitoring目录到路径
        monitoring_path = os.path.join(os.getcwd(), 'monitoring')
        if monitoring_path not in sys.path:
            sys.path.append(monitoring_path)
        
        # 导入监控面板模块
        import monitoring_dashboard
        
        # 直接调用监控面板的main函数
        monitoring_dashboard.main()
        
    except ImportError as e:
        st.error(f"❌ 无法导入监控面板模块: {e}")
        st.info("💡 请确保 monitoring/monitoring_dashboard.py 文件存在")
        
        # 回退到内置监控面板
        show_fallback_monitoring_page()
        
    except Exception as e:
        st.error(f"❌ 监控面板运行错误: {e}")
        st.info("💡 请检查监控系统配置")
        
        # 回退到内置监控面板
        show_fallback_monitoring_page()

def show_fallback_monitoring_page():
    """显示回退监控页面"""
    st.title("📊 ETF套利系统 - 统一监控面板")
    st.markdown("---")
    
    # 初始化监控面板
    dashboard = MonitoringDashboard()
    
    # 侧边栏 - 时间范围选择
    st.sidebar.header("⏰ 时间范围")
    time_range = st.sidebar.selectbox(
        "选择时间范围",
        ["1小时", "6小时", "12小时", "24小时", "48小时"],
        index=3,
        key="fallback_monitoring_time_range"
    )
    hours_map = {"1小时": 1, "6小时": 6, "12小时": 12, "24小时": 24, "48小时": 48}
    selected_hours = hours_map[time_range]
    
    # 侧边栏 - 标的选择
    st.sidebar.header("🎯 监控标的")
    symbol = st.sidebar.text_input("交易标的", value="159740")
    
    # 侧边栏 - 刷新控制
    st.sidebar.header("🔄 刷新控制")
    if st.sidebar.button("🔄 刷新数据"):
        st.rerun()
    
    # 主界面 - 性能监控
    st.header("💻 系统性能监控")
    perf_data = dashboard.get_performance_data(selected_hours)
    create_performance_charts(perf_data)
    
    # 业务监控
    st.header("📈 业务性能监控")
    business_data = dashboard.get_business_data(symbol, selected_hours)
    create_business_charts(business_data)
    
    # 告警监控
    st.header("🚨 告警监控")
    active_alerts = dashboard.get_active_alerts()
    display_alerts(active_alerts, dashboard)
    
    # 系统状态
    st.sidebar.header("📋 系统状态")
    st.sidebar.metric("性能数据点", len(perf_data))
    st.sidebar.metric("业务数据点", len(business_data))
    st.sidebar.metric("活跃告警", len(active_alerts))

def main():
    """主函数"""
    # 页面导航
    st.sidebar.title("🎛️ ETF套利交易系统")
    page = st.sidebar.selectbox(
        "选择功能模块",
        ["🏠 主控制面板", "📊 监控面板", "📈 回测分析", "🎯 实时交易"],
        key="main_navigation_selectbox"
    )
    
    if page == "📊 监控面板":
        show_monitoring_page()
        return
    elif page == "📈 回测分析":
        st.title("📈 回测分析模块")
        st.info("回测分析功能正在开发中...")
        if st.button("🔍 启动回测分析面板"):
            st.info("将在新标签页中打开回测分析面板")
        return
    elif page == "🎯 实时交易":
        st.title("🎯 实时交易模块")
        st.info("实时交易功能正在开发中...")
        if st.button("🚀 启动实时交易面板"):
            st.info("将在新标签页中打开实时交易面板")
        return
    
    # 主控制面板
    st.title("🎛️ ETF套利交易系统 - 主控制面板")
    st.markdown("---")
    
    # 创建系统管理器
    manager = SystemManager()
    
    # 侧边栏 - 系统状态
    st.sidebar.header("📊 系统状态")
    
    # 检查数据库状态
    db_status = manager.check_database_status()
    
    if db_status['status'] == 'healthy':
        st.sidebar.success("✅ 数据库正常")
        st.sidebar.metric("总记录数", f"{db_status['total_records']:,}")
        st.sidebar.metric("159740记录数", f"{db_status['symbol_records']:,}")
        if db_status['latest_time']:
            st.sidebar.info(f"最新数据: {db_status['latest_time']}")
    elif db_status['status'] == 'empty':
        st.sidebar.warning("⚠️ 数据库为空")
    else:
        st.sidebar.error(f"❌ 数据库错误: {db_status.get('error', '未知错误')}")
    
    # 主界面 - 功能模块
    col1, col2 = st.columns(2)
    
    with col1:
        st.header("📡 数据采集模块")
        
        st.markdown("""
        **功能说明**：
        - 实时采集ETF价格数据
        - 存储到本地数据库
        - 支持多种数据源
        """)
        
        if st.button("🚀 启动数据采集", key="start_collector"):
            st.info("正在启动数据采集...")
            try:
                # 在新窗口启动数据采集
                if sys.platform == "win32":
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k", 
                        f"cd /d {os.getcwd()} && python data_collector.py"
                    ])
                else:
                    subprocess.Popen([
                        "gnome-terminal", "--", "python", "data_collector.py"
                    ])
                st.success("✅ 数据采集已在新窗口启动")
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")
        
        if st.button("📊 查看采集状态", key="check_collector"):
            # 显示最近的数据采集情况
            data_summary = manager.get_data_summary()
            if not data_summary.empty:
                st.subheader("📈 最近7天数据统计")
                st.dataframe(data_summary)
            else:
                st.warning("暂无数据")
    
    with col2:
        st.header("📈 回测分析模块")
        
        st.markdown("""
        **功能说明**：
        - 历史数据回测
        - 策略参数优化
        - 风险收益分析
        """)
        
        if st.button("🔍 启动回测分析", key="start_backtest"):
            st.info("正在启动回测分析...")
            try:
                # 在新窗口启动回测面板
                if sys.platform == "win32":
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k",
                        f"cd /d {os.getcwd()} && streamlit run app_enhanced_backtest_dashboard.py --server.port 8502"
                    ])
                else:
                    subprocess.Popen([
                        "gnome-terminal", "--", "streamlit", "run", "app_enhanced_backtest_dashboard.py --server.port 8502"
                    ])
                st.success("✅ 回测分析面板已在新窗口启动")
                st.info("💡 回测面板将在浏览器中打开: http://localhost:8502")
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")
        
        if st.button("📋 快速回测", key="quick_backtest"):
            # 显示快速回测选项
            with st.expander("⚙️ 快速回测设置"):
                col_a, col_b = st.columns(2)
                with col_a:
                    start_date = st.date_input("开始日期", datetime.now() - timedelta(days=7))
                    initial_capital = st.number_input("初始资金", value=1000000, step=100000)
                with col_b:
                    end_date = st.date_input("结束日期", datetime.now())
                    buy_trigger = st.number_input("买入触发", value=-0.006, step=0.001, format="%.3f")
                
                if st.button("▶️ 运行快速回测"):
                    st.info("快速回测功能开发中...")

    st.markdown("---")
    
    # 第二行功能模块
    col3, col4 = st.columns(2)
    
    with col3:
        st.header("🎯 实时交易模块")
        
        st.markdown("""
        **功能说明**：
        - 实时交易信号监控
        - 模拟交易执行
        - 风险控制管理
        """)
        
        if st.button("🚀 启动实时交易", key="start_realtime"):
            st.info("正在启动实时交易...")
            try:
                # 在新窗口启动实时交易面板
                if sys.platform == "win32":
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k",
                        f"cd /d {os.getcwd()} && streamlit run app_enhanced_realtime_dashboard.py --server.port 8503"
                    ])
                else:
                    subprocess.Popen([
                        "gnome-terminal", "--", "streamlit", "run", "app_enhanced_realtime_dashboard.py --server.port 8503"
                    ])
                st.success("✅ 实时交易面板已在新窗口启动")
                st.info("💡 实时交易面板将在浏览器中打开: http://localhost:8503")
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")
                st.success("✅ 实时交易面板已在新窗口启动")
                st.info("💡 实时交易面板将在浏览器中打开")
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")
        
        # 实时状态监控
        if st.button("📊 查看实时状态", key="check_realtime"):
            # 显示最新的价格和信号
            try:
                conn = sqlite3.connect("ticks.db")
                query = """
                SELECT tick_time, price, volume 
                FROM ticks 
                WHERE symbol='159740' 
                ORDER BY tick_time DESC 
                LIMIT 10
                """
                df = pd.read_sql_query(query, conn)
                conn.close()
                
                if not df.empty:
                    st.subheader("📈 最新价格数据")
                    st.dataframe(df)
                    
                    # 计算简单信号
                    latest_price = df['price'].iloc[0]
                    max_price = df['price'].max()
                    signal = (latest_price - max_price) / max_price
                    
                    col_a, col_b, col_c = st.columns(3)
                    with col_a:
                        st.metric("最新价格", f"{latest_price:.4f}")
                    with col_b:
                        st.metric("最高价格", f"{max_price:.4f}")
                    with col_c:
                        st.metric("信号强度", f"{signal*100:.4f}%")
                else:
                    st.warning("暂无实时数据")
            except Exception as e:
                st.error(f"获取实时数据失败: {e}")
    
    with col4:
        st.header("⚙️ 系统管理模块")
        
        st.markdown("""
        **功能说明**：
        - 系统配置管理
        - 日志查看
        - 性能监控
        """)
        
        # 系统配置
        with st.expander("🔧 系统配置"):
            st.subheader("数据库配置")
            db_path = st.text_input("数据库路径", value="ticks.db")
            
            st.subheader("交易配置")
            default_symbol = st.text_input("默认交易标的", value="159740")
            
            if st.button("💾 保存配置"):
                st.success("配置已保存")
        
        # 系统工具
        with st.expander("🛠️ 系统工具"):
            if st.button("🗑️ 清理日志"):
                try:
                    log_files = []
                    if os.path.exists("fetch_ticks.log"):
                        log_files.append("fetch_ticks.log")
                    
                    # 检查其他可能的日志文件
                    for log_file in ["error.log", "app.log", "system.log"]:
                        if os.path.exists(log_file):
                            log_files.append(log_file)
                    
                    if not log_files:
                        st.info("没有找到需要清理的日志文件")
                        return
                    
                    # 清理日志文件
                    cleaned_count = 0
                    for log_file in log_files:
                        try:
                            os.remove(log_file)
                            cleaned_count += 1
                        except Exception as e:
                            st.warning(f"无法删除 {log_file}: {e}")
                    
                    if cleaned_count > 0:
                        st.success(f"✅ 已清理 {cleaned_count} 个日志文件")
                    else:
                        st.info("没有日志文件被清理")
                        
                except Exception as e:
                    st.error(f"清理日志失败: {e}")
            
            if st.button("📊 性能报告"):
                try:
                    result = subprocess.run([
                        "python", "strategy_monitor.py", 
                        "--symbol", "159740", "--days", "7"
                    ], capture_output=True, text=True, encoding='utf-8', errors='ignore', cwd=os.getcwd())
                    
                    if result.returncode == 0:
                        st.success("✅ 性能报告生成完成")
                        st.code(result.stdout)
                    else:
                        st.error(f"生成报告失败: {result.stderr}")
                except Exception as e:
                    st.error(f"执行性能报告失败: {e}")
            
            # 监控功能集成
            if st.button("📈 系统性能监控", key="system_monitor"):
                try:
                    # 启动性能监控
                    if sys.platform == "win32":
                        subprocess.Popen([
                            "cmd", "/c", "start", "cmd", "/k",
                            f"cd /d {os.getcwd()} && python monitoring/monitoring_manager.py"
                        ])
                    else:
                        subprocess.Popen([
                            "gnome-terminal", "--", "python", "monitoring/monitoring_manager.py"
                        ])
                    st.success("✅ 系统性能监控已启动")
                    st.info("性能监控将在新窗口中运行")
                except Exception as e:
                    st.error(f"启动性能监控失败: {e}")
            
            if st.button("🚨 告警监控", key="alert_monitor"):
                try:
                    # 启动告警监控
                    if sys.platform == "win32":
                        subprocess.Popen([
                            "cmd", "/c", "start", "cmd", "/k",
                            f"cd /d {os.getcwd()} && python monitoring/alert_monitor.py"
                        ])
                    else:
                        subprocess.Popen([
                            "gnome-terminal", "--", "python", "monitoring/alert_monitor.py"
                        ])
                    st.success("✅ 告警监控已启动")
                    st.info("告警监控将在新窗口中运行")
                except Exception as e:
                    st.error(f"启动告警监控失败: {e}")
            
            if st.button("📊 业务监控", key="business_monitor"):
                try:
                    # 启动业务监控
                    if sys.platform == "win32":
                        subprocess.Popen([
                            "cmd", "/c", "start", "cmd", "/k",
                            f"cd /d {os.getcwd()} && python monitoring/business_monitor.py"
                        ])
                    else:
                        subprocess.Popen([
                            "gnome-terminal", "--", "python", "monitoring/business_monitor.py"
                        ])
                    st.success("✅ 业务监控已启动")
                    st.info("业务监控将在新窗口中运行")
                except Exception as e:
                    st.error(f"启动业务监控失败: {e}")
            

            
            if st.button("🔄 重启系统"):
                try:
                    st.info("🔄 正在重启系统...")
                    # 获取当前脚本路径
                    current_script = os.path.abspath(__file__)
                    # 重启当前streamlit应用
                    os.system(f"pkill -f \"streamlit run {current_script}\"")
                    time.sleep(2)
                    # 重新启动
                    if sys.platform == "win32":
                        subprocess.Popen([
                            "cmd", "/c", "start", "cmd", "/k",
                            f"cd /d {os.getcwd()} && streamlit run {current_script}"
                        ])
                    else:
                        subprocess.Popen([
                            "streamlit", "run", current_script
                        ])
                    st.success("✅ 系统重启命令已发送")
                    st.info("请等待几秒钟，系统将重新启动...")
                except Exception as e:
                    st.error(f"系统重启失败: {e}")
        
        # 快捷操作
        st.subheader("🚀 快捷操作")
        
        if st.button("🎯 一键启动全部", key="start_all"):
            st.info("正在启动所有模块...")
            try:
                # 启动数据采集
                if sys.platform == "win32":
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k", 
                        f"cd /d {os.getcwd()} && python data_collector.py"
                    ])
                    time.sleep(2)
                    # 启动回测面板
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k",
                        f"cd /d {os.getcwd()} && streamlit run app_enhanced_backtest_dashboard.py --server.port 8502"
                    ])
                    time.sleep(2)
                    # 启动实时交易面板
                    subprocess.Popen([
                        "cmd", "/c", "start", "cmd", "/k",
                        f"cd /d {os.getcwd()} && streamlit run app_enhanced_realtime_dashboard.py --server.port 8503"
                    ])
                
                st.success("✅ 所有模块启动完成")
                st.info("""
                💡 访问地址：
                - 回测分析: http://localhost:8502
                - 实时交易: http://localhost:8503
                - 主控制面板: http://localhost:8501
                """)
            except Exception as e:
                st.error(f"❌ 启动失败: {e}")

    st.markdown("---")
    
    # 底部状态栏
    st.subheader("📋 系统概览")
    
    col_status1, col_status2, col_status3, col_status4 = st.columns(4)
    
    with col_status1:
        st.metric(
            "数据库状态", 
            "正常" if db_status['status'] == 'healthy' else "异常",
            delta=None
        )
    
    with col_status2:
        st.metric(
            "数据记录", 
            f"{db_status['symbol_records']:,}" if db_status['symbol_records'] else "0"
        )
    
    with col_status3:
        current_time = datetime.now().strftime("%H:%M:%S")
        st.metric("当前时间", current_time)
    
    with col_status4:
        # 检查是否在交易时间
        now = datetime.now()
        is_trading_time = (
            (9 <= now.hour < 11) or 
            (now.hour == 11 and now.minute <= 30) or
            (13 <= now.hour < 15)
        )
        st.metric(
            "交易状态", 
            "交易时间" if is_trading_time else "非交易时间"
        )

    # 页脚信息
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: gray;'>
    <p>🎛️ ETF套利交易系统 v2.0 | 统一控制面板</p>
    <p>💡 使用说明：使用左侧导航栏切换功能模块，所有功能已集成到统一界面中</p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
