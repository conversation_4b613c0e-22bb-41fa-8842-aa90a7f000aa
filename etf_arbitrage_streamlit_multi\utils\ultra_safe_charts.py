#!/usr/bin/env python3
"""
超级安全的图表显示模块
使用最简化的配置避免所有JavaScript错误
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, Optional, Any

def ultra_safe_plotly_chart(fig: go.Figure, title: str = "", **kwargs):
    """超级安全的Plotly图表显示 - 使用最简配置"""
    try:
        # 完全最小化的配置
        config = {
            'displayModeBar': False,  # 完全禁用工具栏
            'displaylogo': False,
            'staticPlot': False,
            'responsive': True
        }
        
        # 最简化的布局设置
        fig.update_layout(
            template='simple_white',  # 使用最简单的模板
            showlegend=True,
            margin=dict(l=50, r=50, t=50, b=50),
            font=dict(size=12)
        )
        
        if title:
            st.subheader(title)
        
        # 使用最安全的显示方式
        st.plotly_chart(fig, width='stretch', config=config)
        
    except Exception as e:
        st.error(f"图表显示失败: {e}")
        
        # 超级简化的备用方案
        try:
            st.info("正在使用备用显示方案...")
            
            # 尝试显示数据表格作为备用
            if hasattr(fig, 'data') and fig.data:
                for i, trace in enumerate(fig.data):
                    if hasattr(trace, 'x') and hasattr(trace, 'y'):
                        df_backup = pd.DataFrame({
                            'X': trace.x if trace.x is not None else [],
                            'Y': trace.y if trace.y is not None else []
                        })
                        if not df_backup.empty:
                            st.write(f"**{trace.name or f'数据系列{i+1}'}**")
                            st.dataframe(df_backup.tail(10), width='stretch')
            else:
                st.warning("无法显示图表数据")
                
        except Exception as backup_error:
            st.error(f"备用显示也失败了: {backup_error}")
            st.info("请尝试刷新页面")

def create_ultra_safe_line_chart(x_data, y_data, title: str = "折线图", x_label: str = "X", y_label: str = "Y"):
    """创建超级安全的折线图"""
    try:
        if x_data is None or y_data is None:
            st.warning(f"{title}: 数据为空")
            return
        
        # 确保数据格式正确
        x_data = pd.Series(x_data).dropna()
        y_data = pd.Series(y_data).dropna()
        
        if len(x_data) == 0 or len(y_data) == 0:
            st.warning(f"{title}: 有效数据为空")
            return
        
        # 创建最简单的图表
        fig = go.Figure()
        fig.add_trace(
            go.Scatter(
                x=x_data,
                y=y_data,
                mode='lines',
                name=y_label,
                line=dict(width=2)
            )
        )
        
        fig.update_layout(
            title=title,
            xaxis_title=x_label,
            yaxis_title=y_label,
            height=400
        )
        
        ultra_safe_plotly_chart(fig, title)
        
    except Exception as e:
        st.error(f"{title} 生成失败: {e}")
        
        # 最后的备用方案：显示数据表格
        try:
            df = pd.DataFrame({x_label: x_data, y_label: y_data})
            st.subheader(f"{title} (表格形式)")
            st.dataframe(df.tail(20), width='stretch')
        except:
            st.error(f"无法显示 {title}")

def display_ultra_safe_metrics(metrics_dict: Dict, title: str = "性能指标"):
    """超级安全的指标显示"""
    try:
        if not metrics_dict:
            st.info(f"{title}: 暂无数据")
            return
        
        st.subheader(title)
        
        # 使用Streamlit原生组件显示指标
        cols = st.columns(min(len(metrics_dict), 4))
        
        for i, (key, value) in enumerate(metrics_dict.items()):
            with cols[i % len(cols)]:
                try:
                    # 尝试格式化数值
                    if isinstance(value, (int, float)):
                        if abs(value) < 1:
                            formatted_value = f"{value:.4f}"
                        elif abs(value) < 1000:
                            formatted_value = f"{value:.2f}"
                        else:
                            formatted_value = f"{value:,.0f}"
                    else:
                        formatted_value = str(value)
                    
                    st.metric(label=key, value=formatted_value)
                except:
                    st.metric(label=key, value=str(value))
                    
    except Exception as e:
        st.error(f"指标显示失败: {e}")
        
        # 备用方案：简单文本显示
        try:
            st.subheader(title)
            for key, value in metrics_dict.items():
                st.write(f"**{key}**: {value}")
        except:
            st.error("指标显示完全失败")

def emergency_data_display(data, title: str = "数据"):
    """紧急数据显示 - 当所有图表都失败时使用"""
    try:
        st.subheader(f"📊 {title}")
        
        if isinstance(data, pd.DataFrame):
            if not data.empty:
                st.dataframe(data.tail(20), width='stretch')
            else:
                st.info("DataFrame为空")
        elif isinstance(data, pd.Series):
            if not data.empty:
                st.write(data.tail(10))
            else:
                st.info("Series为空")
        elif isinstance(data, dict):
            for key, value in data.items():
                st.write(f"**{key}**: {value}")
        else:
            st.write(data)
            
    except Exception as e:
        st.error(f"紧急数据显示失败: {e}")
        st.write("数据类型:", type(data))
        st.write("数据内容:", str(data)[:200] + "..." if len(str(data)) > 200 else str(data))