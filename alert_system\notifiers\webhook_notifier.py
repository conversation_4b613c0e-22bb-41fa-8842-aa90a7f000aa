#!/usr/bin/env python3
"""
Webhook通知器
支持自定义webhook通知，可用于集成各种第三方服务
"""

import asyncio
import aiohttp
import json
import logging
from typing import Dict, List, Optional, Any
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class WebhookNotifier:
    """Webhook通知器"""
    
    def __init__(self, 
                 webhook_url: str = None,
                 headers: Dict[str, str] = None,
                 auth_token: str = None,
                 timeout: int = 30):
        """
        初始化Webhook通知器
        
        Args:
            webhook_url: Webhook地址
            headers: 自定义请求头
            auth_token: 认证令牌
            timeout: 请求超时时间
        """
        self.webhook_url = webhook_url or os.getenv('WEBHOOK_URL')
        self.auth_token = auth_token or os.getenv('WEBHOOK_AUTH_TOKEN')
        self.timeout = timeout
        
        # 设置默认请求头
        self.headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'ETF-Arbitrage-System/1.0'
        }
        
        # 添加自定义请求头
        if headers:
            self.headers.update(headers)
        
        # 添加认证头
        if self.auth_token:
            self.headers['Authorization'] = f'Bearer {self.auth_token}'
        
        logger.info("Webhook通知器初始化完成")
    
    async def send(self, payload: Dict[str, Any], 
                   webhook_url: str = None,
                   method: str = 'POST') -> bool:
        """
        发送Webhook消息
        
        Args:
            payload: 消息载荷
            webhook_url: 自定义webhook地址
            method: HTTP方法
            
        Returns:
            发送是否成功
        """
        url = webhook_url or self.webhook_url
        if not url:
            logger.warning("Webhook URL未配置，跳过发送")
            return False
        
        try:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.request(
                    method=method,
                    url=url,
                    json=payload,
                    headers=self.headers
                ) as response:
                    
                    if 200 <= response.status < 300:
                        logger.info(f"Webhook消息发送成功: {response.status}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"Webhook发送失败: {response.status} - {error_text}")
                        return False
            
        except asyncio.TimeoutError:
            logger.error(f"Webhook请求超时: {url}")
            return False
        except Exception as e:
            logger.error(f"Webhook发送异常: {e}")
            return False
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> bool:
        """发送预警消息"""
        payload = {
            "type": "alert",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "symbol": symbol,
                "alert_type": alert_type,
                "condition": condition,
                "price": price,
                "signal": signal,
                "details": details,
                "recommendation": recommendation
            },
            "metadata": {
                "source": "etf_arbitrage_system",
                "version": "1.0.0"
            }
        }
        
        return await self.send(payload)
    
    async def send_summary(self, total_return: float, trade_count: int,
                          win_rate: float, max_drawdown: float,
                          alert_count: int, successful_trades: int) -> bool:
        """发送日报消息"""
        payload = {
            "type": "summary",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "total_return": total_return,
                "trade_count": trade_count,
                "win_rate": win_rate,
                "max_drawdown": max_drawdown,
                "alert_count": alert_count,
                "successful_trades": successful_trades,
                "report_date": datetime.now().strftime('%Y-%m-%d')
            },
            "metadata": {
                "source": "etf_arbitrage_system",
                "version": "1.0.0"
            }
        }
        
        return await self.send(payload)
    
    async def send_error(self, error_type: str, error_message: str,
                        module: str, suggestion: str = "") -> bool:
        """发送错误通知消息"""
        payload = {
            "type": "error",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "error_type": error_type,
                "error_message": error_message,
                "module": module,
                "suggestion": suggestion,
                "severity": "high"
            },
            "metadata": {
                "source": "etf_arbitrage_system",
                "version": "1.0.0"
            }
        }
        
        return await self.send(payload)
    
    async def send_custom(self, message_type: str, data: Dict[str, Any],
                         metadata: Dict[str, Any] = None) -> bool:
        """发送自定义消息"""
        payload = {
            "type": message_type,
            "timestamp": datetime.now().isoformat(),
            "data": data,
            "metadata": metadata or {
                "source": "etf_arbitrage_system",
                "version": "1.0.0"
            }
        }
        
        return await self.send(payload)
    
    async def test_connection(self) -> bool:
        """测试Webhook连接"""
        if not self.webhook_url:
            logger.warning("Webhook URL未配置")
            return False
        
        test_payload = {
            "type": "test",
            "timestamp": datetime.now().isoformat(),
            "data": {
                "message": "ETF套利系统连接测试",
                "test_id": f"test_{int(datetime.now().timestamp())}"
            },
            "metadata": {
                "source": "etf_arbitrage_system",
                "version": "1.0.0"
            }
        }
        
        return await self.send(test_payload)


class SlackNotifier(WebhookNotifier):
    """Slack通知器（继承自Webhook通知器）"""
    
    def __init__(self, webhook_url: str = None, channel: str = None, 
                 username: str = "ETF套利系统", **kwargs):
        super().__init__(webhook_url, **kwargs)
        self.channel = channel
        self.username = username
        logger.info("Slack通知器初始化完成")
    
    async def send_slack_message(self, text: str, channel: str = None,
                                username: str = None, icon_emoji: str = ":robot_face:") -> bool:
        """发送Slack消息"""
        payload = {
            "text": text,
            "channel": channel or self.channel,
            "username": username or self.username,
            "icon_emoji": icon_emoji
        }
        
        return await self.send(payload)
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> bool:
        """发送Slack预警消息"""
        text = f"""🚨 *ETF套利预警通知*

*交易标的:* {symbol}
*预警类型:* {alert_type}
*触发条件:* {condition}
*当前价格:* {price:.4f}
*信号强度:* {signal:.4f}
*触发时间:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

*详细信息:*
{details or '无额外信息'}

*建议操作:*
{recommendation or '请根据策略执行相应操作'}"""
        
        return await self.send_slack_message(text, icon_emoji=":warning:")


class DiscordNotifier(WebhookNotifier):
    """Discord通知器（继承自Webhook通知器）"""
    
    def __init__(self, webhook_url: str = None, username: str = "ETF套利系统", **kwargs):
        super().__init__(webhook_url, **kwargs)
        self.username = username
        logger.info("Discord通知器初始化完成")
    
    async def send_discord_message(self, content: str, username: str = None,
                                  avatar_url: str = None, embeds: List[Dict] = None) -> bool:
        """发送Discord消息"""
        payload = {
            "content": content,
            "username": username or self.username
        }
        
        if avatar_url:
            payload["avatar_url"] = avatar_url
        
        if embeds:
            payload["embeds"] = embeds
        
        return await self.send(payload)
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> bool:
        """发送Discord预警消息"""
        embed = {
            "title": "🚨 ETF套利预警通知",
            "color": 15158332,  # 红色
            "fields": [
                {"name": "交易标的", "value": symbol, "inline": True},
                {"name": "预警类型", "value": alert_type, "inline": True},
                {"name": "当前价格", "value": f"{price:.4f}", "inline": True},
                {"name": "信号强度", "value": f"{signal:.4f}", "inline": True},
                {"name": "触发条件", "value": condition, "inline": False},
                {"name": "详细信息", "value": details or "无额外信息", "inline": False},
                {"name": "建议操作", "value": recommendation or "请根据策略执行相应操作", "inline": False}
            ],
            "timestamp": datetime.now().isoformat(),
            "footer": {"text": "ETF套利系统"}
        }
        
        return await self.send_discord_message("", embeds=[embed])


# 测试函数
async def test_webhook_notifier():
    """测试Webhook通知器"""
    logger.info("开始测试Webhook通知器...")
    
    # 创建Webhook通知器实例
    notifier = WebhookNotifier()
    
    try:
        # 测试连接
        connection_ok = await notifier.test_connection()
        if not connection_ok:
            logger.warning("Webhook连接测试失败，跳过发送测试")
            return False
        
        # 测试预警消息
        success1 = await notifier.send_alert(
            symbol="159740",
            alert_type="买入信号",
            condition="signal <= -0.006",
            price=0.7523,
            signal=-0.0078,
            details="信号强度较强，建议关注",
            recommendation="考虑买入操作"
        )
        
        # 测试自定义消息
        success2 = await notifier.send_custom(
            message_type="test",
            data={"message": "这是一条测试消息"}
        )
        
        logger.info(f"Webhook测试结果: 预警消息={success1}, 自定义消息={success2}")
        return success1 or success2
        
    except Exception as e:
        logger.error(f"Webhook测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_webhook_notifier())
