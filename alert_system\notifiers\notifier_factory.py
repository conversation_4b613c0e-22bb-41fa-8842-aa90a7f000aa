#!/usr/bin/env python3
"""
通知器工厂
统一管理和创建各种通知器实例
"""

import logging
from typing import Dict, List, Optional, Type, Any
import os

from .email_notifier import EmailNotifier
from .wechat_notifier import WeChatNotifier, WeChatWorkNotifier
from .qq_notifier import QQNotifier
from .webhook_notifier import WebhookNotifier, SlackNotifier, DiscordNotifier

logger = logging.getLogger(__name__)

class NotifierFactory:
    """通知器工厂类"""
    
    # 注册的通知器类型
    NOTIFIER_TYPES = {
        'email': EmailNotifier,
        'wechat': WeChatNotifier,
        'wechat_work': WeChatWorkNotifier,
        'qq': QQNotifier,
        'webhook': WebhookNotifier,
        'slack': SlackNotifier,
        'discord': DiscordNotifier
    }
    
    @classmethod
    def create_notifier(cls, notifier_type: str, **kwargs) -> Optional[Any]:
        """
        创建通知器实例
        
        Args:
            notifier_type: 通知器类型
            **kwargs: 通知器配置参数
            
        Returns:
            通知器实例或None
        """
        if notifier_type not in cls.NOTIFIER_TYPES:
            logger.error(f"不支持的通知器类型: {notifier_type}")
            return None
        
        try:
            notifier_class = cls.NOTIFIER_TYPES[notifier_type]
            notifier = notifier_class(**kwargs)
            logger.info(f"创建{notifier_type}通知器成功")
            return notifier
            
        except Exception as e:
            logger.error(f"创建{notifier_type}通知器失败: {e}")
            return None
    
    @classmethod
    def create_from_config(cls, config: Dict[str, Any]) -> List[Any]:
        """
        从配置创建多个通知器
        
        Args:
            config: 通知器配置字典
            
        Returns:
            通知器实例列表
        """
        notifiers = []
        
        for notifier_type, notifier_config in config.items():
            if not notifier_config.get('enabled', False):
                logger.info(f"跳过未启用的通知器: {notifier_type}")
                continue
            
            # 移除enabled字段
            config_copy = notifier_config.copy()
            config_copy.pop('enabled', None)
            
            notifier = cls.create_notifier(notifier_type, **config_copy)
            if notifier:
                notifiers.append(notifier)
        
        logger.info(f"从配置创建了 {len(notifiers)} 个通知器")
        return notifiers
    
    @classmethod
    def create_from_env(cls) -> List[Any]:
        """
        从环境变量创建通知器
        
        Returns:
            通知器实例列表
        """
        notifiers = []
        
        # 邮件通知器
        if os.getenv('EMAIL_USERNAME') and os.getenv('EMAIL_PASSWORD'):
            email_notifier = cls.create_notifier('email')
            if email_notifier:
                notifiers.append(email_notifier)
        
        # 微信通知器
        if os.getenv('WECHAT_WEBHOOK_URL'):
            wechat_notifier = cls.create_notifier('wechat')
            if wechat_notifier:
                notifiers.append(wechat_notifier)
        
        # QQ通知器
        if os.getenv('QQ_WEBHOOK_URL') and os.getenv('QQ_GROUP_ID'):
            qq_notifier = cls.create_notifier('qq')
            if qq_notifier:
                notifiers.append(qq_notifier)
        
        # Webhook通知器
        if os.getenv('WEBHOOK_URL'):
            webhook_notifier = cls.create_notifier('webhook')
            if webhook_notifier:
                notifiers.append(webhook_notifier)
        
        logger.info(f"从环境变量创建了 {len(notifiers)} 个通知器")
        return notifiers
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """获取可用的通知器类型"""
        return list(cls.NOTIFIER_TYPES.keys())
    
    @classmethod
    def register_notifier(cls, notifier_type: str, notifier_class: Type) -> bool:
        """
        注册新的通知器类型
        
        Args:
            notifier_type: 通知器类型名称
            notifier_class: 通知器类
            
        Returns:
            注册是否成功
        """
        try:
            cls.NOTIFIER_TYPES[notifier_type] = notifier_class
            logger.info(f"注册通知器类型成功: {notifier_type}")
            return True
        except Exception as e:
            logger.error(f"注册通知器类型失败: {e}")
            return False


class NotifierManager:
    """通知器管理器"""
    
    def __init__(self, notifiers: List[Any] = None):
        """
        初始化通知器管理器
        
        Args:
            notifiers: 通知器实例列表
        """
        self.notifiers = notifiers or []
        logger.info(f"通知器管理器初始化，包含 {len(self.notifiers)} 个通知器")
    
    def add_notifier(self, notifier: Any):
        """添加通知器"""
        self.notifiers.append(notifier)
        logger.info(f"添加通知器，当前总数: {len(self.notifiers)}")
    
    def remove_notifier(self, notifier: Any):
        """移除通知器"""
        if notifier in self.notifiers:
            self.notifiers.remove(notifier)
            logger.info(f"移除通知器，当前总数: {len(self.notifiers)}")
    
    async def send_to_all(self, method_name: str, *args, **kwargs) -> Dict[str, bool]:
        """
        向所有通知器发送消息
        
        Args:
            method_name: 方法名称
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            发送结果字典
        """
        results = {}
        
        for i, notifier in enumerate(self.notifiers):
            notifier_name = f"{notifier.__class__.__name__}_{i}"
            
            try:
                if hasattr(notifier, method_name):
                    method = getattr(notifier, method_name)
                    result = await method(*args, **kwargs)
                    results[notifier_name] = result
                else:
                    logger.warning(f"通知器 {notifier_name} 不支持方法 {method_name}")
                    results[notifier_name] = False
                    
            except Exception as e:
                logger.error(f"通知器 {notifier_name} 发送失败: {e}")
                results[notifier_name] = False
        
        success_count = sum(1 for success in results.values() if success)
        logger.info(f"批量发送完成: {success_count}/{len(self.notifiers)} 成功")
        
        return results
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> Dict[str, bool]:
        """向所有通知器发送预警"""
        return await self.send_to_all(
            'send_alert', symbol, alert_type, condition, 
            price, signal, details, recommendation
        )
    
    async def send_summary(self, total_return: float, trade_count: int,
                          win_rate: float, max_drawdown: float,
                          alert_count: int, successful_trades: int) -> Dict[str, bool]:
        """向所有通知器发送日报"""
        return await self.send_to_all(
            'send_summary', total_return, trade_count, win_rate, 
            max_drawdown, alert_count, successful_trades
        )
    
    async def send_error(self, error_type: str, error_message: str,
                        module: str, suggestion: str = "") -> Dict[str, bool]:
        """向所有通知器发送错误通知"""
        return await self.send_to_all(
            'send_error', error_type, error_message, module, suggestion
        )
    
    async def test_all_connections(self) -> Dict[str, bool]:
        """测试所有通知器连接"""
        return await self.send_to_all('test_connection')
    
    def get_notifier_count(self) -> int:
        """获取通知器数量"""
        return len(self.notifiers)
    
    def get_notifier_types(self) -> List[str]:
        """获取通知器类型列表"""
        return [notifier.__class__.__name__ for notifier in self.notifiers]


# 默认配置示例
DEFAULT_CONFIG = {
    'email': {
        'enabled': False,
        'smtp_server': 'smtp.qq.com',
        'smtp_port': 587,
        'use_tls': True
    },
    'wechat': {
        'enabled': False
    },
    'qq': {
        'enabled': False
    },
    'webhook': {
        'enabled': False,
        'timeout': 30
    },
    'slack': {
        'enabled': False,
        'username': 'ETF套利系统',
        'channel': '#trading'
    },
    'discord': {
        'enabled': False,
        'username': 'ETF套利系统'
    }
}


# 测试函数
async def test_notifier_factory():
    """测试通知器工厂"""
    logger.info("开始测试通知器工厂...")
    
    try:
        # 测试创建单个通知器
        email_notifier = NotifierFactory.create_notifier('email')
        logger.info(f"创建邮件通知器: {'成功' if email_notifier else '失败'}")
        
        # 测试从环境变量创建
        env_notifiers = NotifierFactory.create_from_env()
        logger.info(f"从环境变量创建 {len(env_notifiers)} 个通知器")
        
        # 测试通知器管理器
        manager = NotifierManager(env_notifiers)
        
        # 测试连接
        connection_results = await manager.test_all_connections()
        logger.info(f"连接测试结果: {connection_results}")
        
        # 测试发送消息
        if manager.get_notifier_count() > 0:
            alert_results = await manager.send_alert(
                symbol="TEST",
                alert_type="测试信号",
                condition="test condition",
                price=1.0,
                signal=0.5,
                details="这是一条测试消息"
            )
            logger.info(f"预警发送结果: {alert_results}")
        
        logger.info("✅ 通知器工厂测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 通知器工厂测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_notifier_factory())
