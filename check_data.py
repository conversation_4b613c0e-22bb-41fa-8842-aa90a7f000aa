#!/usr/bin/env python3
"""检查数据库中的数据"""

import sqlite3
import pandas as pd
from datetime import datetime, <PERSON><PERSON><PERSON>

def check_ticks_data():
    """检查ticks数据"""
    try:
        conn = sqlite3.connect('ticks.db')
        
        # 检查总记录数
        cursor = conn.execute('SELECT COUNT(*) FROM ticks')
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count}")
        
        # 检查表结构
        cursor = conn.execute("PRAGMA table_info(ticks)")
        columns = cursor.fetchall()
        print(f"表结构: {[col[1] for col in columns]}")
        
        # 检查最近的记录
        cursor = conn.execute('SELECT * FROM ticks ORDER BY tick_time DESC LIMIT 5')
        records = cursor.fetchall()
        print(f"最近5条记录:")
        for i, record in enumerate(records):
            print(f"  {i+1}: {record}")
        
        # 检查不同symbol的数据
        cursor = conn.execute('SELECT symbol, COUNT(*) FROM ticks GROUP BY symbol')
        symbol_counts = cursor.fetchall()
        print(f"各symbol记录数:")
        for symbol, count in symbol_counts:
            print(f"  {symbol}: {count}")
        
        # 检查时间范围
        cursor = conn.execute('SELECT MIN(tick_time), MAX(tick_time) FROM ticks')
        time_range = cursor.fetchone()
        print(f"时间范围: {time_range[0]} 到 {time_range[1]}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据失败: {e}")

def check_data_collector_status():
    """检查数据收集器状态"""
    try:
        # 检查是否有数据收集器在运行
        import psutil
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'python' in proc.info['name'].lower():
                    cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                    if 'fetch_ticks' in cmdline or 'data_collector' in cmdline:
                        print(f"发现数据收集器进程: PID={proc.info['pid']}, CMD={cmdline}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
    except ImportError:
        print("psutil未安装，无法检查进程状态")
    except Exception as e:
        print(f"检查进程状态失败: {e}")

if __name__ == "__main__":
    print("=== 检查数据库状态 ===")
    check_ticks_data()
    
    print("\n=== 检查数据收集器状态 ===")
    check_data_collector_status()
