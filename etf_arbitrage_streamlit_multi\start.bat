@echo off
chcp 65001 > nul
title ETF Arbitrage System - Streamlit Multi-page App

echo ========================================
echo   ETF套利交易系统 - 多页面Streamlit应用
echo ========================================
echo.

:: 检查当前目录
cd /d "%~dp0"
echo 当前工作目录: %CD%
echo.

:: 检查conda环境
echo [1/4] 检查conda环境...
where conda >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到conda，尝试使用Python...
    goto :use_python
) else (
    echo ✅ 找到conda环境
    goto :use_conda
)

:use_conda
echo [2/4] 激活castock环境...
call conda activate castock
if errorlevel 1 (
    echo ⚠️ castock环境不存在，使用base环境...
    call conda activate base
)
goto :check_streamlit

:use_python
echo [2/4] 使用系统Python...
where python >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请安装Python或conda
    pause
    exit /b 1
)

:check_streamlit
echo [3/4] 检查Streamlit安装...
python -c "import streamlit" 2>nul
if errorlevel 1 (
    echo ⚠️ 未找到Streamlit，正在安装...
    pip install streamlit plotly pandas numpy
    if errorlevel 1 (
        echo ❌ Streamlit安装失败
        pause
        exit /b 1
    )
    echo ✅ Streamlit安装完成
) else (
    echo ✅ Streamlit已安装
)

:: 检查其他依赖
echo 检查其他依赖包...
python -c "import plotly, pandas, numpy, psutil" 2>nul
if errorlevel 1 (
    echo 正在安装缺失的依赖包...
    pip install plotly pandas numpy psutil
)

echo [4/4] 启动应用...
echo.
echo 🚀 正在启动ETF套利交易系统...
echo 📱 应用将在浏览器中自动打开
echo 🌐 默认地址: http://localhost:8501
echo.
echo ⚠️ 请保持此窗口打开，关闭将停止应用
echo 💡 按Ctrl+C可停止应用
echo ========================================
echo.

:: 启动Streamlit应用
streamlit run main.py --server.port 8501 --server.address localhost --browser.gatherUsageStats false

echo.
echo 应用已停止
pause