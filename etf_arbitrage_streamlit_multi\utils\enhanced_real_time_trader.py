#!/usr/bin/env python3
"""
增强版实时交易引擎
连接真实策略引擎，实现完整的交易生命周期管理
"""

import threading
import time
import logging
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from pathlib import Path
import json

# 导入核心基础设施
try:
    from ..core.database_manager import DatabaseManager
    from ..core.config_manager import ConfigManager
    from ..core.logger_manager import LoggerManager
    from ..core.exception_handler import ExceptionHandler, ErrorCategory, ErrorSeverity
    CORE_INFRASTRUCTURE_AVAILABLE = True
except ImportError:
    CORE_INFRASTRUCTURE_AVAILABLE = False
    logging.warning("核心基础设施不可用，使用传统方式")

# 初始化核心服务
if CORE_INFRASTRUCTURE_AVAILABLE:
    db_manager = DatabaseManager.get_instance()
    config_manager = ConfigManager()
    logger_manager = LoggerManager()
    exception_handler = ExceptionHandler()
    logger = logger_manager.create_module_logger('enhanced_real_time_trader')
else:
    logger = logging.getLogger(__name__)

# 导入策略引擎和数据连接器
import sys
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from strategy_engine_enhanced import Position as StrategyPosition, RiskManager
    from strategy_config import StrategyConfig
    STRATEGY_AVAILABLE = True
except ImportError:
    STRATEGY_AVAILABLE = False
    logger.warning("策略引擎不可用，将使用模拟模式")

# 导入真实数据连接器
try:
    from .real_data_connector import RealDataConnector
    DATA_CONNECTOR_AVAILABLE = True
except ImportError:
    DATA_CONNECTOR_AVAILABLE = False
    logger.warning("数据连接器不可用，将使用内置数据源")

# 导入策略管理器
try:
    from .trading_strategies import get_strategy_manager, SignalResult
    STRATEGY_MANAGER_AVAILABLE = True
except ImportError:
    STRATEGY_MANAGER_AVAILABLE = False
    logger.warning("策略管理器不可用")

# 🆕 导入A股交易规则管理器
try:
    from .trading_rules_manager import get_trading_rules_manager
    TRADING_RULES_AVAILABLE = True
except ImportError:
    TRADING_RULES_AVAILABLE = False
    logger.warning("A股交易规则管理器不可用")

@dataclass
class TradeSignal:
    """交易信号"""
    timestamp: datetime
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    price: float
    confidence: float
    reason: str
    quantity: int = 0
    signal_strength: float = 0.0

@dataclass
class Position:
    """持仓信息"""
    id: str
    symbol: str
    quantity: int
    avg_price: float
    open_time: datetime
    cost: float
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    return_pct: float = 0.0
    hold_time: int = 0  # 持仓时间（秒）
    
    def update_price(self, price: float):
        """更新当前价格和未实现盈亏"""
        self.current_price = price
        self.unrealized_pnl = (price - self.avg_price) * self.quantity
        self.return_pct = (price - self.avg_price) / self.avg_price if self.avg_price > 0 else 0.0
        self.hold_time = int((datetime.now() - self.open_time).total_seconds())

@dataclass
class TradeRecord:
    """交易记录"""
    id: str
    timestamp: datetime
    symbol: str
    action: str  # 'BUY', 'SELL'
    quantity: int
    price: float
    amount: float
    commission: float
    stamp_tax: float = 0.0
    transfer_fee: float = 0.0
    pnl: float = 0.0
    position_id: str = ""
    reason: str = ""

class RealTimeDataFeed:
    """实时数据源"""
    
    def __init__(self, db_path: str = None):
        # 使用核心基础设施获取数据库路径
        if CORE_INFRASTRUCTURE_AVAILABLE and db_path is None:
            db_config = config_manager.get_database_config()
            self.db_path = db_config.ticks_db
        else:
            self.db_path = db_path or "ticks.db"

        self.subscribers = []
        self.is_active = False
        self.current_symbol = None
        self.feed_thread = None
        self.stop_event = threading.Event()

        # 初始化真实数据连接器
        if DATA_CONNECTOR_AVAILABLE:
            self.data_connector = RealDataConnector(self.db_path)
            logger.info("使用真实数据连接器")
        else:
            self.data_connector = None
            logger.warning("使用内置数据源")
        
    def subscribe(self, callback: Callable):
        """订阅数据更新"""
        self.subscribers.append(callback)
        
    def start_feed(self, symbol: str):
        """启动实时数据推送"""
        if self.is_active:
            logger.warning("数据源已在运行中")
            return False
            
        self.current_symbol = symbol
        self.is_active = True
        self.stop_event.clear()
        
        self.feed_thread = threading.Thread(target=self._feed_loop, daemon=True)
        self.feed_thread.start()
        
        logger.info(f"启动实时数据源: {symbol}")
        return True
        
    def stop_feed(self):
        """停止数据推送"""
        if not self.is_active:
            return False
            
        self.stop_event.set()
        self.is_active = False
        
        if self.feed_thread and self.feed_thread.is_alive():
            self.feed_thread.join(timeout=5)
            
        logger.info("实时数据源已停止")
        return True
        
    def _feed_loop(self):
        """数据推送循环"""
        while not self.stop_event.is_set():
            try:
                # 获取最新数据
                tick_data = self._get_latest_tick()
                
                if tick_data:
                    # 通知所有订阅者
                    for callback in self.subscribers:
                        try:
                            callback(tick_data)
                        except Exception as e:
                            logger.error(f"数据推送回调失败: {e}")
                
                # 等待下一次更新
                time.sleep(1)  # 1秒更新一次
                
            except Exception as e:
                logger.error(f"数据推送循环出错: {e}")
                time.sleep(5)  # 出错后等待5秒再重试
                
    def _get_latest_tick(self) -> Optional[dict]:
        """获取最新tick数据"""
        try:
            # 使用真实数据连接器
            if self.data_connector:
                market_data = self.data_connector.get_current_market_data(self.current_symbol)
                if market_data:
                    return market_data
            
            # 备用方案：从数据库获取
            return self._get_tick_from_database()
                
        except Exception as e:
            logger.error(f"获取tick数据失败: {e}")
            return None
            
    def _get_tick_from_database(self) -> Optional[dict]:
        """从数据库获取tick数据（备用方案）"""
        try:
            # 使用核心DatabaseManager或传统方式
            if CORE_INFRASTRUCTURE_AVAILABLE:
                # 首先检查是否有真实的最新数据
                query_latest = """
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol = ? AND tick_time >= datetime('now', '-5 minutes')
                ORDER BY tick_time DESC
                LIMIT 1
                """

                df = db_manager.read_dataframe(query_latest, (self.current_symbol,), 'ticks')

                if not df.empty:
                    # 有最新数据，直接返回
                    return {
                        'symbol': self.current_symbol,
                        'timestamp': pd.to_datetime(df.iloc[0]['tick_time']),
                        'price': float(df.iloc[0]['price']),
                        'volume': int(df.iloc[0]['volume'])
                    }

                # 没有最新数据，获取最近的历史数据并基于它生成当前tick
                query_recent = """
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol = ?
                ORDER BY tick_time DESC
                LIMIT 10
                """

                df_recent = db_manager.read_dataframe(query_recent, (self.current_symbol,), 'ticks')
            else:
                # 传统数据库访问方式
                conn = sqlite3.connect(self.db_path)

                query_latest = """
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol = ? AND tick_time >= datetime('now', '-5 minutes')
                ORDER BY tick_time DESC
                LIMIT 1
                """

                df = pd.read_sql_query(query_latest, conn, params=[self.current_symbol])

                if not df.empty:
                    conn.close()
                    return {
                        'symbol': self.current_symbol,
                        'timestamp': pd.to_datetime(df.iloc[0]['tick_time']),
                        'price': float(df.iloc[0]['price']),
                        'volume': int(df.iloc[0]['volume'])
                    }

                query_recent = """
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol = ?
                ORDER BY tick_time DESC
                LIMIT 10
                """

                df_recent = pd.read_sql_query(query_recent, conn, params=[self.current_symbol])
                conn.close()
            
            if not df_recent.empty:
                # 基于最近的历史数据生成当前tick
                latest_price = float(df_recent.iloc[0]['price'])
                
                # 添加小幅随机波动（基于历史波动率）
                price_changes = df_recent['price'].pct_change().dropna()
                if len(price_changes) > 0:
                    volatility = price_changes.std()
                else:
                    volatility = 0.001
                
                price_change = np.random.normal(0, latest_price * volatility)
                current_price = max(0.01, latest_price + price_change)
                
                # 生成成交量
                avg_volume = int(df_recent['volume'].mean())
                current_volume = max(100, int(avg_volume * np.random.uniform(0.8, 1.2)))
                
                return {
                    'symbol': self.current_symbol,
                    'timestamp': datetime.now(),
                    'price': current_price,
                    'volume': current_volume
                }
                
        except Exception as e:
            logger.error(f"从数据库获取tick数据失败: {e}")
            
        return None

class EnhancedRiskManager:
    """增强版风险管理器"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.daily_loss_limit = -20000  # 日亏损限制
        self.position_limit_pct = 0.8   # 仓位限制比例
        self.max_drawdown_limit = -0.1  # 最大回撤限制
        self.max_positions = 5          # 最大持仓数
        self.emergency_stop = False     # 紧急停止标志
        
        # 统计数据
        self.daily_pnl = 0.0
        self.max_drawdown = 0.0
        self.peak_capital = initial_capital
        
    def check_risk_before_trade(self, signal: TradeSignal, positions: Dict[str, Position], 
                               available_cash: float) -> Tuple[bool, str]:
        """交易前风险检查"""
        
        # 检查紧急停止状态
        if self.emergency_stop:
            return False, "系统处于紧急停止状态"
            
        # 检查持仓数量限制
        if signal.signal_type == 'BUY' and len(positions) >= self.max_positions:
            return False, f"持仓数量已达上限 ({self.max_positions})"
            
        # 检查资金充足性
        if signal.signal_type == 'BUY':
            required_amount = signal.quantity * signal.price * 1.001  # 包含手续费
            if available_cash < required_amount:
                return False, f"资金不足，需要 {required_amount:,.2f}，可用 {available_cash:,.2f}"
                
        # 检查日亏损限制
        if self.daily_pnl <= self.daily_loss_limit:
            return False, f"已触发日亏损限制 {self.daily_loss_limit:,.2f}"
            
        # 检查最大回撤限制
        current_drawdown = (self.current_capital - self.peak_capital) / self.peak_capital
        if current_drawdown <= self.max_drawdown_limit:
            return False, f"已触发最大回撤限制 {self.max_drawdown_limit:.2%}"
            
        return True, "风险检查通过"
        
    def update_capital(self, new_capital: float):
        """更新资金状态"""
        self.current_capital = new_capital
        
        # 更新峰值资金
        if new_capital > self.peak_capital:
            self.peak_capital = new_capital
            
        # 更新最大回撤
        current_drawdown = (new_capital - self.peak_capital) / self.peak_capital
        if current_drawdown < self.max_drawdown:
            self.max_drawdown = current_drawdown
            
        # 更新日盈亏
        self.daily_pnl = new_capital - self.initial_capital
        
    def trigger_emergency_stop(self, reason: str):
        """触发紧急停止"""
        self.emergency_stop = True
        logger.critical(f"触发紧急停止: {reason}")
        
    def reset_emergency_stop(self):
        """重置紧急停止状态"""
        self.emergency_stop = False
        logger.info("紧急停止状态已重置")

class EnhancedRealTimeTrader:
    """增强版实时交易引擎"""
    
    def __init__(self, initial_capital: float = 1000000):
        self.initial_capital = initial_capital
        self.is_running = False
        self.current_symbol = None
        
        # 核心组件
        self.data_feed = RealTimeDataFeed()
        self.risk_manager = EnhancedRiskManager(initial_capital)

        # 🆕 A股交易规则管理器
        self.trading_rules = get_trading_rules_manager() if TRADING_RULES_AVAILABLE else None

        # 🆕 策略管理器
        self.strategy_manager = None
        if STRATEGY_MANAGER_AVAILABLE:
            try:
                self.strategy_manager = get_strategy_manager()
                logger.info("策略管理器初始化成功")
            except Exception as e:
                logger.error(f"策略管理器初始化失败: {e}")
                self.strategy_manager = None

        # 数据存储
        self.positions: Dict[str, Position] = {}
        self.signals: List[TradeSignal] = []
        self.trades: List[TradeRecord] = []
        
        # 线程控制
        self.trader_thread = None
        self.stop_event = threading.Event()
        self.status_callbacks = []
        
        # 策略参数
        self.strategy_params = StrategyConfig.get_default_values() if STRATEGY_AVAILABLE else {
            'buy_trigger_drop': -0.002,
            'profit_target': 0.0025,
            'stop_loss': -0.02,
            'max_hold_time': 3600,
            'position_size': 100000,
            'commission_rate': 0.0003
        }
        
        # 统计信息
        self.stats = {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'start_time': None,
            'last_signal_time': None,
            'signals_today': 0,
            'valid_signals_today': 0
        }

        # 🆕 A股规则统计
        self.a_stock_stats = {
            'data_quality_checks': 0,
            'data_quality_failures': 0,
            'trading_rule_violations': 0,
            'quantity_normalizations': 0,
            'price_limit_hits': 0,
            'time_restrictions': 0,
            'system_health_checks': 0,
            'system_health_failures': 0
        }
        
        # 订阅数据更新
        self.data_feed.subscribe(self._on_tick_data)
        
    def add_status_callback(self, callback: Callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
        
    def _notify_status_change(self, status: Dict):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")

    def validate_data_freshness(self, latest_data: dict, max_delay_seconds: int = 5) -> Tuple[bool, str]:
        """
        🆕 验证数据新鲜度

        Args:
            latest_data: 最新数据
            max_delay_seconds: 最大允许延迟（秒）

        Returns:
            (是否新鲜, 说明信息)
        """
        try:
            self.a_stock_stats['data_quality_checks'] += 1

            if not latest_data or 'timestamp' not in latest_data:
                self.a_stock_stats['data_quality_failures'] += 1
                return False, "数据缺少时间戳"

            # 解析数据时间戳
            data_time = latest_data['timestamp']
            if isinstance(data_time, str):
                data_time = datetime.fromisoformat(data_time.replace('Z', '+00:00'))
            elif not isinstance(data_time, datetime):
                self.a_stock_stats['data_quality_failures'] += 1
                return False, "时间戳格式无效"

            # 计算延迟
            current_time = datetime.now()
            delay = (current_time - data_time).total_seconds()

            if delay > max_delay_seconds:
                self.a_stock_stats['data_quality_failures'] += 1
                return False, f"数据延迟过大: {delay:.1f}秒 > {max_delay_seconds}秒"

            return True, f"数据新鲜，延迟: {delay:.1f}秒"

        except Exception as e:
            self.a_stock_stats['data_quality_failures'] += 1
            logger.error(f"验证数据新鲜度时出错: {e}")
            return False, f"验证失败: {str(e)}"

    def enhanced_risk_check(self, signal: dict, positions: dict, market_data: dict) -> Tuple[bool, str]:
        """
        🆕 增强风险检查

        Args:
            signal: 交易信号
            positions: 当前持仓
            market_data: 市场数据

        Returns:
            (是否通过风险检查, 说明信息)
        """
        try:
            risk_issues = []

            # 1. 单股票持仓比例检查
            symbol = signal.get('symbol', self.current_symbol)
            if symbol in positions:
                position_value = positions[symbol].get('market_value', 0)
                total_value = sum(pos.get('market_value', 0) for pos in positions.values())
                if total_value > 0:
                    position_ratio = position_value / total_value
                    if position_ratio > 0.30:  # 最大30%
                        risk_issues.append(f"单股票持仓比例过高: {position_ratio:.2%} > 30%")

            # 2. 市场风险检查
            if 'market_trend' in market_data:
                market_decline = market_data.get('market_decline', 0)
                if market_decline < -0.03:  # 大盘下跌超过3%
                    risk_issues.append(f"市场风险过高: 大盘下跌 {market_decline:.2%}")

            # 3. 流动性检查
            if 'volume' in market_data:
                volume = market_data.get('volume', 0)
                if volume < 1000000:  # 最小成交量100万
                    risk_issues.append(f"流动性不足: 成交量 {volume:,} < 1,000,000")

            # 4. 系统健康检查
            system_health = self.check_system_health()
            if not system_health[0]:
                risk_issues.append(f"系统健康检查失败: {system_health[1]}")

            if risk_issues:
                return False, "; ".join(risk_issues)

            return True, "风险检查通过"

        except Exception as e:
            logger.error(f"增强风险检查时出错: {e}")
            return False, f"风险检查失败: {str(e)}"

    def check_system_health(self) -> Tuple[bool, str]:
        """
        🆕 系统健康检查

        Returns:
            (是否健康, 状态说明)
        """
        try:
            self.a_stock_stats['system_health_checks'] += 1

            import psutil

            # CPU使用率检查
            cpu_percent = psutil.cpu_percent(interval=0.1)
            if cpu_percent > 95:
                self.a_stock_stats['system_health_failures'] += 1
                return False, f"CPU使用率过高: {cpu_percent:.1f}% > 95%"

            # 内存使用率检查
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                self.a_stock_stats['system_health_failures'] += 1
                return False, f"内存使用率过高: {memory.percent:.1f}% > 90%"

            # 检查关键线程状态
            if self.trader_thread and not self.trader_thread.is_alive():
                self.a_stock_stats['system_health_failures'] += 1
                return False, "交易线程已停止"

            return True, f"系统健康 (CPU: {cpu_percent:.1f}%, 内存: {memory.percent:.1f}%)"

        except ImportError:
            # psutil不可用，跳过系统监控
            return True, "系统监控不可用，跳过检查"
        except Exception as e:
            self.a_stock_stats['system_health_failures'] += 1
            logger.error(f"系统健康检查时出错: {e}")
            return False, f"健康检查失败: {str(e)}"

    def calculate_execution_price(self, signal_price: float, quantity: int, trade_type: str = 'buy') -> Tuple[float, float]:
        """
        🆕 计算考虑滑点的执行价格

        Args:
            signal_price: 信号价格
            quantity: 交易数量
            trade_type: 交易类型 ('buy' 或 'sell')

        Returns:
            (执行价格, 滑点成本)
        """
        try:
            # 基础滑点配置
            base_slippage = 0.0005  # 0.05%

            # 根据数量调整滑点（成交量冲击）
            volume_impact = min(quantity / 100000, 0.002)  # 最大0.2%

            # 根据交易类型调整
            direction_multiplier = 1 if trade_type == 'buy' else -1

            # 计算总滑点
            total_slippage = (base_slippage + volume_impact) * direction_multiplier

            # 计算执行价格
            execution_price = signal_price * (1 + total_slippage)
            slippage_cost = abs(execution_price - signal_price) * quantity

            return execution_price, slippage_cost

        except Exception as e:
            logger.error(f"计算执行价格时出错: {e}")
            return signal_price, 0.0

    def split_large_order(self, quantity: int, max_single_order: int = 10000) -> List[int]:
        """
        🆕 大订单拆分逻辑

        Args:
            quantity: 总数量
            max_single_order: 单笔最大订单

        Returns:
            拆分后的订单数量列表
        """
        try:
            if quantity <= max_single_order:
                return [quantity]

            # 计算拆分数量
            num_orders = (quantity + max_single_order - 1) // max_single_order
            base_size = quantity // num_orders
            remainder = quantity % num_orders

            # 生成订单列表
            orders = [base_size] * num_orders

            # 分配余数
            for i in range(remainder):
                orders[i] += 1

            # 使用A股规则标准化每个订单
            if self.trading_rules:
                orders = [self.trading_rules.normalize_quantity(order) for order in orders]
                orders = [order for order in orders if order > 0]  # 移除0数量订单

            return orders

        except Exception as e:
            logger.error(f"拆分大订单时出错: {e}")
            return [quantity]

    def execute_trade_with_validation(self, signal: TradeSignal, market_data: dict) -> Optional[TradeRecord]:
        """
        🆕 带完整验证的交易执行

        Args:
            signal: 交易信号
            market_data: 市场数据

        Returns:
            交易记录或None
        """
        try:
            # 1. 数据新鲜度验证
            is_fresh, freshness_msg = self.validate_data_freshness(market_data)
            if not is_fresh:
                logger.warning(f"数据新鲜度验证失败: {freshness_msg}")
                return None

            # 2. A股交易规则验证
            if self.trading_rules:
                compliance_result = self.trading_rules.validate_trade_compliance(
                    symbol=signal.symbol,
                    trade_type=signal.signal_type,
                    quantity=signal.quantity,
                    current_price=signal.price,
                    prev_close=market_data.get('prev_close'),
                    current_time=signal.timestamp
                )

                if not compliance_result['is_compliant']:
                    self.a_stock_stats['trading_rule_violations'] += 1
                    violations = '; '.join(compliance_result.get('violations', []))
                    logger.warning(f"A股规则验证失败: {violations}")
                    return None

                normalized_qty = compliance_result['normalized_quantity']
                if normalized_qty != signal.quantity:
                    self.a_stock_stats['quantity_normalizations'] += 1
                    signal.quantity = normalized_qty

            # 3. 增强风险检查
            risk_ok, risk_msg = self.enhanced_risk_check(
                signal=asdict(signal),
                positions={pos.symbol: asdict(pos) for pos in self.positions.values()},
                market_data=market_data
            )

            if not risk_ok:
                logger.warning(f"风险检查失败: {risk_msg}")
                return None

            # 4. 计算执行价格
            execution_price, slippage_cost = self.calculate_execution_price(
                signal.price, signal.quantity, signal.signal_type
            )

            # 5. 拆分大订单
            order_sizes = self.split_large_order(signal.quantity)

            # 6. 执行交易
            total_executed = 0
            total_cost = 0.0

            for order_size in order_sizes:
                if order_size <= 0:
                    continue

                # 模拟交易执行
                trade_record = TradeRecord(
                    id=f"{signal.symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.trades)}",
                    timestamp=datetime.now(),
                    symbol=signal.symbol,
                    action=signal.signal_type,
                    quantity=order_size,
                    price=execution_price,
                    amount=order_size * execution_price,
                    commission=order_size * execution_price * 0.0003,  # 0.03%佣金
                    reason=signal.reason
                )

                self.trades.append(trade_record)
                total_executed += order_size
                total_cost += order_size * execution_price

                logger.info(f"交易执行: {signal.signal_type} {order_size}@{execution_price:.4f}")

            # 7. 更新统计
            self.stats['total_trades'] += 1

            return trade_record if order_sizes else None

        except Exception as e:
            logger.error(f"执行交易时出错: {e}")
            return None

    def start_trading(self, symbol: str) -> bool:
        """启动实时交易"""
        if self.is_running:
            logger.warning("实时交易已在运行中")
            return False
            
        try:
            self.current_symbol = symbol
            self.is_running = True
            self.stop_event.clear()
            
            # 启动数据源
            if not self.data_feed.start_feed(symbol):
                self.is_running = False
                return False
                
            # 启动交易线程
            self.trader_thread = threading.Thread(target=self._trading_loop, daemon=True)
            self.trader_thread.start()
            
            # 更新统计信息
            self.stats['start_time'] = datetime.now()
            
            logger.info(f"开始实时交易: {symbol}")
            self._notify_status_change({
                'status': 'started',
                'symbol': symbol,
                'timestamp': datetime.now()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"启动实时交易失败: {e}")
            self.is_running = False
            return False
            
    def stop_trading(self) -> bool:
        """停止实时交易"""
        if not self.is_running:
            logger.warning("实时交易未在运行")
            return False
            
        try:
            self.stop_event.set()
            self.is_running = False
            
            # 停止数据源
            self.data_feed.stop_feed()
            
            # 等待交易线程结束
            if self.trader_thread and self.trader_thread.is_alive():
                self.trader_thread.join(timeout=10)
                
            logger.info("实时交易已停止")
            self._notify_status_change({
                'status': 'stopped',
                'timestamp': datetime.now()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"停止实时交易失败: {e}")
            return False
            
    def _trading_loop(self):
        """交易主循环"""
        logger.info("交易循环已启动")
        
        while not self.stop_event.is_set():
            try:
                # 检查持仓状态
                self._check_positions()
                
                # 更新统计信息
                self._update_stats()
                
                # 等待下一次检查
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"交易循环出错: {e}")
                time.sleep(5)
                
        logger.info("交易循环已结束")
        
    def _on_tick_data(self, tick_data: dict):
        """处理tick数据"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']
            
            # 更新持仓价格
            for position in self.positions.values():
                if position.symbol == symbol:
                    position.update_price(price)
                    
            # 生成交易信号
            signal = self._generate_signal(tick_data)
            
            if signal and signal.signal_type != 'HOLD':
                self.signals.append(signal)
                self.stats['signals_today'] += 1
                self.stats['last_signal_time'] = timestamp
                
                # 执行信号
                if self._execute_signal(signal):
                    self.stats['valid_signals_today'] += 1
                    
        except Exception as e:
            logger.error(f"处理tick数据失败: {e}")
            
    def _generate_signal(self, tick_data: dict) -> Optional[TradeSignal]:
        """生成交易信号 - 连接真实策略引擎"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']
            
            # 使用真实的策略引擎
            if STRATEGY_AVAILABLE:
                signal = self._generate_real_strategy_signal(tick_data)
                if signal:
                    return signal
            
            # 备用：基于历史数据的信号生成
            return self._generate_historical_signal(tick_data)
            
        except Exception as e:
            logger.error(f"生成交易信号失败: {e}")
            return None
            
    def _generate_real_strategy_signal(self, tick_data: dict) -> Optional[TradeSignal]:
        """使用真实策略引擎生成信号"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']

            # 🆕 优先使用策略管理器
            if self.strategy_manager:
                try:
                    # 获取历史数据用于策略计算
                    historical_data = self._get_historical_data(symbol, lookback_minutes=60)

                    if historical_data is None or len(historical_data) < 20:
                        logger.warning(f"历史数据不足，无法生成信号: {symbol}")
                        return None

                    # 添加当前tick到历史数据
                    current_tick = pd.DataFrame({
                        'timestamp': [timestamp],
                        'price': [price],
                        'volume': [tick_data.get('volume', 1000)]
                    })

                    # 合并数据
                    full_data = pd.concat([historical_data, current_tick], ignore_index=True)
                    full_data = full_data.sort_values('timestamp').reset_index(drop=True)

                    # 🆕 使用策略管理器生成信号
                    current_strategy = self.strategy_manager.get_current_strategy()
                    if current_strategy:
                        signal_result = current_strategy.generate_signal(full_data)

                        if signal_result and abs(signal_result.signal) > 0.1:  # 信号强度阈值
                            # 将信号强度转换为信号类型
                            if signal_result.signal > 0.1:
                                signal_type = 'BUY'
                            elif signal_result.signal < -0.1:
                                signal_type = 'SELL'
                            else:
                                signal_type = 'HOLD'

                            if signal_type != 'HOLD':
                                return TradeSignal(
                                    timestamp=timestamp,
                                    symbol=symbol,
                                    signal_type=signal_type,
                                    price=price,
                                    confidence=signal_result.confidence,
                                    reason=signal_result.reason,
                                    quantity=int(self.strategy_params['position_size'] / price),
                                    signal_strength=abs(signal_result.signal)
                                )

                except Exception as e:
                    logger.error(f"策略管理器信号生成失败: {e}")
                    # 继续使用备用方案

            # 备用方案：使用内置技术指标逻辑
            return self._generate_fallback_signal(tick_data)

        except Exception as e:
            logger.error(f"真实策略信号生成失败: {e}")
            return None

    def _generate_fallback_signal(self, tick_data: dict) -> Optional[TradeSignal]:
        """备用信号生成方案（使用内置技术指标）"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']

            # 获取历史数据用于策略计算
            historical_data = self._get_historical_data(symbol, lookback_minutes=60)

            if historical_data is None or len(historical_data) < 20:
                logger.warning(f"历史数据不足，无法生成信号: {symbol}")
                return None

            # 添加当前tick到历史数据
            current_tick = pd.DataFrame({
                'timestamp': [timestamp],
                'price': [price],
                'volume': [tick_data.get('volume', 1000)]
            })

            # 合并数据
            full_data = pd.concat([historical_data, current_tick], ignore_index=True)
            full_data = full_data.sort_values('timestamp').reset_index(drop=True)

            # 计算技术指标
            full_data = self._calculate_technical_indicators(full_data)

            # 使用内置策略逻辑计算信号
            latest_data = full_data.iloc[-1]

            # 买入信号检查
            buy_signal = self._check_buy_conditions(full_data, latest_data)
            if buy_signal:
                return TradeSignal(
                    timestamp=timestamp,
                    symbol=symbol,
                    signal_type='BUY',
                    price=price,
                    confidence=buy_signal['confidence'],
                    reason=f"[内置策略] {buy_signal['reason']}",
                    quantity=int(self.strategy_params['position_size'] / price),
                    signal_strength=buy_signal['strength']
                )

            # 卖出信号检查（基于持仓）
            for position in self.positions.values():
                if position.symbol == symbol:
                    sell_signal = self._check_sell_conditions(position, full_data, latest_data)
                    if sell_signal:
                        return TradeSignal(
                            timestamp=timestamp,
                            symbol=symbol,
                            signal_type='SELL',
                            price=price,
                            confidence=sell_signal['confidence'],
                            reason=f"[内置策略] {sell_signal['reason']}",
                            quantity=position.quantity,
                            signal_strength=sell_signal['strength']
                        )

            return None

        except Exception as e:
            logger.error(f"备用策略信号生成失败: {e}")
            return None
            
    def _generate_historical_signal(self, tick_data: dict) -> Optional[TradeSignal]:
        """基于历史数据生成信号（备用方案）"""
        try:
            symbol = tick_data['symbol']
            price = tick_data['price']
            timestamp = tick_data['timestamp']
            
            # 获取历史价格数据
            historical_data = self._get_historical_data(symbol, lookback_minutes=30)
            
            if historical_data is None or len(historical_data) < 10:
                return None
            
            # 计算价格变化
            recent_prices = historical_data['price'].tail(10).values
            current_price = price
            
            # 计算移动平均
            ma_short = np.mean(recent_prices[-5:])  # 5分钟均线
            ma_long = np.mean(recent_prices)        # 10分钟均线
            
            # 计算价格相对于均线的偏离
            deviation_from_ma = (current_price - ma_short) / ma_short
            
            # 买入条件：价格显著低于短期均线
            if deviation_from_ma <= self.strategy_params['buy_trigger_drop']:
                return TradeSignal(
                    timestamp=timestamp,
                    symbol=symbol,
                    signal_type='BUY',
                    price=price,
                    confidence=min(abs(deviation_from_ma) * 10, 1.0),
                    reason=f"价格低于均线 {deviation_from_ma:.2%}",
                    quantity=int(self.strategy_params['position_size'] / price),
                    signal_strength=deviation_from_ma
                )
            
            # 检查持仓的卖出条件
            for position in self.positions.values():
                if position.symbol == symbol:
                    # 止盈检查
                    if position.return_pct >= self.strategy_params.get('profit_target', 0.0025):
                        return TradeSignal(
                            timestamp=timestamp,
                            symbol=symbol,
                            signal_type='SELL',
                            price=price,
                            confidence=0.8,
                            reason=f"止盈 {position.return_pct:.2%}",
                            quantity=position.quantity,
                            signal_strength=position.return_pct
                        )
                        
                    # 止损检查
                    if position.return_pct <= self.strategy_params.get('stop_loss', -0.02):
                        return TradeSignal(
                            timestamp=timestamp,
                            symbol=symbol,
                            signal_type='SELL',
                            price=price,
                            confidence=0.9,
                            reason=f"止损 {position.return_pct:.2%}",
                            quantity=position.quantity,
                            signal_strength=position.return_pct
                        )
                        
                    # 时间止损检查
                    max_hold_time = self.strategy_params.get('max_hold_time', 3600)
                    if position.hold_time >= max_hold_time:
                        return TradeSignal(
                            timestamp=timestamp,
                            symbol=symbol,
                            signal_type='SELL',
                            price=price,
                            confidence=0.7,
                            reason=f"时间止损 {position.hold_time}s",
                            quantity=position.quantity,
                            signal_strength=0.0
                        )
            
            return None
            
        except Exception as e:
            logger.error(f"历史数据信号生成失败: {e}")
            return None
            
    def _get_historical_data(self, symbol: str, lookback_minutes: int = 60) -> Optional[pd.DataFrame]:
        """获取历史数据"""
        try:
            conn = sqlite3.connect(self.data_feed.db_path)
            
            # 计算查询时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(minutes=lookback_minutes)
            
            query = """
            SELECT tick_time as timestamp, price, volume
            FROM ticks 
            WHERE symbol = ? AND tick_time >= ? AND tick_time <= ?
            ORDER BY tick_time ASC
            """
            
            df = pd.read_sql_query(
                query, 
                conn, 
                params=[symbol, start_time.isoformat(), end_time.isoformat()]
            )
            conn.close()
            
            if df.empty:
                logger.warning(f"未找到历史数据: {symbol}")
                return None
                
            # 转换时间戳
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return None
            
    def _calculate_technical_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        try:
            df = data.copy()
            
            # 移动平均线
            df['ma5'] = df['price'].rolling(window=5, min_periods=1).mean()
            df['ma10'] = df['price'].rolling(window=10, min_periods=1).mean()
            df['ma20'] = df['price'].rolling(window=20, min_periods=1).mean()
            
            # 价格变化率
            df['price_change'] = df['price'].pct_change()
            df['price_change_5min'] = df['price'].pct_change(periods=5)
            
            # 波动率（滚动标准差）
            df['volatility'] = df['price_change'].rolling(window=10, min_periods=1).std()
            
            # RSI指标
            df['rsi'] = self._calculate_rsi(df['price'])
            
            # 布林带
            df['bb_upper'], df['bb_lower'] = self._calculate_bollinger_bands(df['price'])
            
            # 成交量相关
            df['volume_ma'] = df['volume'].rolling(window=10, min_periods=1).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma']
            
            return df
            
        except Exception as e:
            logger.error(f"计算技术指标失败: {e}")
            return data
            
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period, min_periods=1).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period, min_periods=1).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.fillna(50)  # 默认值50
            
        except Exception as e:
            logger.error(f"计算RSI失败: {e}")
            return pd.Series([50] * len(prices), index=prices.index)
            
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int = 20, std_dev: float = 2) -> Tuple[pd.Series, pd.Series]:
        """计算布林带"""
        try:
            ma = prices.rolling(window=period, min_periods=1).mean()
            std = prices.rolling(window=period, min_periods=1).std()
            
            upper_band = ma + (std * std_dev)
            lower_band = ma - (std * std_dev)
            
            return upper_band, lower_band
            
        except Exception as e:
            logger.error(f"计算布林带失败: {e}")
            return prices, prices
            
    def _check_buy_conditions(self, data: pd.DataFrame, latest: pd.Series) -> Optional[Dict]:
        """检查买入条件"""
        try:
            # 条件1：价格低于短期均线一定幅度
            price_vs_ma5 = (latest['price'] - latest['ma5']) / latest['ma5']
            
            # 条件2：RSI超卖
            rsi_oversold = latest['rsi'] < 30
            
            # 条件3：价格接近布林带下轨
            near_lower_bb = latest['price'] <= latest['bb_lower'] * 1.01
            
            # 条件4：成交量放大
            volume_surge = latest['volume_ratio'] > 1.5
            
            # 综合判断
            buy_score = 0
            reasons = []
            
            if price_vs_ma5 <= self.strategy_params['buy_trigger_drop']:
                buy_score += 0.4
                reasons.append(f"价格低于MA5 {price_vs_ma5:.2%}")
                
            if rsi_oversold:
                buy_score += 0.3
                reasons.append(f"RSI超卖 {latest['rsi']:.1f}")
                
            if near_lower_bb:
                buy_score += 0.2
                reasons.append("接近布林带下轨")
                
            if volume_surge:
                buy_score += 0.1
                reasons.append(f"成交量放大 {latest['volume_ratio']:.1f}x")
            
            # 买入阈值
            if buy_score >= 0.5:
                return {
                    'confidence': min(buy_score, 1.0),
                    'reason': '; '.join(reasons),
                    'strength': price_vs_ma5
                }
                
            return None
            
        except Exception as e:
            logger.error(f"检查买入条件失败: {e}")
            return None
            
    def _check_sell_conditions(self, position: Position, data: pd.DataFrame, latest: pd.Series) -> Optional[Dict]:
        """检查卖出条件"""
        try:
            # 更新持仓价格
            position.update_price(latest['price'])
            
            # 条件1：止盈
            profit_target = self.strategy_params.get('profit_target', 0.0025)
            if position.return_pct >= profit_target:
                return {
                    'confidence': 0.9,
                    'reason': f"止盈 {position.return_pct:.2%}",
                    'strength': position.return_pct
                }
            
            # 条件2：止损
            stop_loss = self.strategy_params.get('stop_loss', -0.02)
            if position.return_pct <= stop_loss:
                return {
                    'confidence': 1.0,
                    'reason': f"止损 {position.return_pct:.2%}",
                    'strength': position.return_pct
                }
            
            # 条件3：时间止损
            max_hold_time = self.strategy_params.get('max_hold_time', 3600)
            if position.hold_time >= max_hold_time:
                return {
                    'confidence': 0.8,
                    'reason': f"时间止损 {position.hold_time}s",
                    'strength': 0.0
                }
            
            # 条件4：技术指标卖出
            # RSI超买
            if latest['rsi'] > 70 and position.return_pct > 0:
                return {
                    'confidence': 0.6,
                    'reason': f"RSI超买 {latest['rsi']:.1f}, 盈利 {position.return_pct:.2%}",
                    'strength': position.return_pct
                }
            
            # 价格突破布林带上轨且有盈利
            if latest['price'] >= latest['bb_upper'] and position.return_pct > 0.001:
                return {
                    'confidence': 0.7,
                    'reason': f"突破布林带上轨, 盈利 {position.return_pct:.2%}",
                    'strength': position.return_pct
                }
            
            return None
            
        except Exception as e:
            logger.error(f"检查卖出条件失败: {e}")
            return None
            
    def _execute_signal(self, signal: TradeSignal) -> bool:
        """执行交易信号（带A股规则验证）"""
        try:
            # 🆕 使用增强的交易执行逻辑
            market_data = {
                'timestamp': signal.timestamp,
                'symbol': signal.symbol,
                'price': signal.price,
                'volume': getattr(signal, 'volume', 1000000),  # 默认成交量
                'prev_close': getattr(signal, 'prev_close', signal.price * 0.99)  # 估算前收盘价
            }

            # 执行带完整验证的交易
            trade_record = self.execute_trade_with_validation(signal, market_data)

            if trade_record:
                # 更新持仓（如果是买入）
                if signal.signal_type == 'BUY':
                    self._update_position_after_buy(signal, trade_record)
                elif signal.signal_type == 'SELL':
                    self._update_position_after_sell(signal, trade_record)

                return True

            return False

        except Exception as e:
            logger.error(f"执行交易信号失败: {e}")
            return False
            
    def _execute_buy(self, signal: TradeSignal) -> bool:
        """执行买入"""
        try:
            # 计算交易费用
            amount = signal.quantity * signal.price
            commission = max(amount * self.strategy_params['commission_rate'], 5.0)
            transfer_fee = amount * 0.00001  # 过户费
            total_cost = amount + commission + transfer_fee
            
            # 创建持仓
            position_id = f"POS_{len(self.positions)+1:03d}_{int(time.time())}"
            position = Position(
                id=position_id,
                symbol=signal.symbol,
                quantity=signal.quantity,
                avg_price=signal.price,
                open_time=signal.timestamp,
                cost=total_cost,
                current_price=signal.price
            )
            
            self.positions[position_id] = position
            
            # 记录交易
            trade_id = f"TRD_{len(self.trades)+1:03d}_{int(time.time())}"
            trade = TradeRecord(
                id=trade_id,
                timestamp=signal.timestamp,
                symbol=signal.symbol,
                action='BUY',
                quantity=signal.quantity,
                price=signal.price,
                amount=amount,
                commission=commission,
                transfer_fee=transfer_fee,
                position_id=position_id,
                reason=signal.reason
            )
            
            self.trades.append(trade)
            self.stats['total_trades'] += 1
            
            logger.info(f"执行买入: {signal.symbol} {signal.quantity}@{signal.price:.4f}")
            return True
            
        except Exception as e:
            logger.error(f"执行买入失败: {e}")
            return False
            
    def _execute_sell(self, signal: TradeSignal) -> bool:
        """执行卖出"""
        try:
            # 找到对应的持仓
            position = None
            for pos in self.positions.values():
                if pos.symbol == signal.symbol and pos.quantity >= signal.quantity:
                    position = pos
                    break
                    
            if not position:
                logger.warning(f"未找到可卖出的持仓: {signal.symbol}")
                return False
                
            # 计算交易费用
            amount = signal.quantity * signal.price
            commission = max(amount * self.strategy_params['commission_rate'], 5.0)
            stamp_tax = amount * 0.001  # 印花税
            transfer_fee = amount * 0.00001  # 过户费
            total_fees = commission + stamp_tax + transfer_fee
            
            # 计算盈亏
            cost_basis = (position.cost / position.quantity) * signal.quantity
            pnl = amount - cost_basis - total_fees
            
            # 更新持仓
            if position.quantity == signal.quantity:
                # 全部卖出，删除持仓
                del self.positions[position.id]
            else:
                # 部分卖出，更新持仓
                position.quantity -= signal.quantity
                position.cost -= cost_basis
                
            # 记录交易
            trade_id = f"TRD_{len(self.trades)+1:03d}_{int(time.time())}"
            trade = TradeRecord(
                id=trade_id,
                timestamp=signal.timestamp,
                symbol=signal.symbol,
                action='SELL',
                quantity=signal.quantity,
                price=signal.price,
                amount=amount,
                commission=commission,
                stamp_tax=stamp_tax,
                transfer_fee=transfer_fee,
                pnl=pnl,
                position_id=position.id,
                reason=signal.reason
            )
            
            self.trades.append(trade)
            self.stats['total_trades'] += 1
            
            if pnl > 0:
                self.stats['winning_trades'] += 1
                
            self.stats['total_pnl'] += pnl
            
            logger.info(f"执行卖出: {signal.symbol} {signal.quantity}@{signal.price:.4f}, 盈亏: {pnl:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"执行卖出失败: {e}")
            return False
            
    def _check_positions(self):
        """检查持仓状态"""
        try:
            # 这里可以添加持仓监控逻辑
            # 例如：检查是否需要强制平仓等
            pass
        except Exception as e:
            logger.error(f"检查持仓状态失败: {e}")

    def _update_position_after_buy(self, signal: TradeSignal, trade_record: TradeRecord):
        """🆕 买入后更新持仓"""
        try:
            # 计算交易费用
            amount = signal.quantity * signal.price
            commission = max(amount * self.strategy_params['commission_rate'], 5.0)
            transfer_fee = amount * 0.00001  # 过户费
            total_cost = amount + commission + transfer_fee

            # 创建或更新持仓
            position_id = f"POS_{len(self.positions)+1:03d}_{int(time.time())}"

            # 检查是否已有该股票的持仓
            existing_position = None
            for pos in self.positions.values():
                if pos.symbol == signal.symbol:
                    existing_position = pos
                    break

            if existing_position:
                # 更新现有持仓
                total_quantity = existing_position.quantity + signal.quantity
                total_cost_new = existing_position.cost + total_cost
                existing_position.avg_price = total_cost_new / total_quantity
                existing_position.quantity = total_quantity
                existing_position.cost = total_cost_new
                existing_position.current_price = signal.price
            else:
                # 创建新持仓
                position = Position(
                    id=position_id,
                    symbol=signal.symbol,
                    quantity=signal.quantity,
                    avg_price=signal.price,
                    open_time=signal.timestamp,
                    cost=total_cost,
                    current_price=signal.price
                )
                self.positions[position_id] = position

            logger.info(f"持仓更新: {signal.symbol} +{signal.quantity}@{signal.price:.4f}")

        except Exception as e:
            logger.error(f"更新买入持仓失败: {e}")

    def _update_position_after_sell(self, signal: TradeSignal, trade_record: TradeRecord):
        """🆕 卖出后更新持仓"""
        try:
            # 找到对应的持仓
            position = None
            for pos in self.positions.values():
                if pos.symbol == signal.symbol and pos.quantity >= signal.quantity:
                    position = pos
                    break

            if not position:
                logger.warning(f"未找到足够的持仓进行卖出: {signal.symbol} {signal.quantity}")
                return

            # 计算盈亏
            amount = signal.quantity * signal.price
            commission = max(amount * self.strategy_params['commission_rate'], 5.0)
            stamp_tax = amount * 0.001  # 印花税0.1%（仅卖出）
            transfer_fee = amount * 0.00001  # 过户费
            total_fees = commission + stamp_tax + transfer_fee

            cost_basis = (position.cost / position.quantity) * signal.quantity
            pnl = amount - cost_basis - total_fees

            # 更新持仓
            if position.quantity == signal.quantity:
                # 全部卖出，删除持仓
                del self.positions[position.id]
            else:
                # 部分卖出，更新持仓
                position.quantity -= signal.quantity
                position.cost -= cost_basis

            # 更新统计
            if pnl > 0:
                self.stats['winning_trades'] += 1
            self.stats['total_pnl'] += pnl

            logger.info(f"持仓更新: {signal.symbol} -{signal.quantity}@{signal.price:.4f}, 盈亏: {pnl:.2f}")

        except Exception as e:
            logger.error(f"更新卖出持仓失败: {e}")
            
        except Exception as e:
            logger.error(f"检查持仓状态失败: {e}")
            
    def _calculate_available_cash(self) -> float:
        """计算可用资金"""
        try:
            # 计算已用资金
            used_cash = sum(pos.cost for pos in self.positions.values())
            
            # 计算已实现盈亏
            realized_pnl = sum(trade.pnl for trade in self.trades if trade.action == 'SELL')
            
            # 可用资金 = 初始资金 - 已用资金 + 已实现盈亏
            available_cash = self.initial_capital - used_cash + realized_pnl
            
            return max(0, available_cash)
            
        except Exception as e:
            logger.error(f"计算可用资金失败: {e}")
            return 0.0
            
    def _update_stats(self):
        """更新统计信息"""
        try:
            # 计算胜率
            sell_trades = [t for t in self.trades if t.action == 'SELL']
            if sell_trades:
                winning_trades = len([t for t in sell_trades if t.pnl > 0])
                self.stats['win_rate'] = winning_trades / len(sell_trades)
            else:
                self.stats['win_rate'] = 0.0
                
            # 更新风险管理器
            current_capital = self._calculate_current_capital()
            self.risk_manager.update_capital(current_capital)
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
            
    def _calculate_current_capital(self) -> float:
        """计算当前总资金"""
        try:
            # 可用资金
            available_cash = self._calculate_available_cash()
            
            # 持仓市值
            market_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            
            return available_cash + market_value
            
        except Exception as e:
            logger.error(f"计算当前资金失败: {e}")
            return self.initial_capital
            
    def get_status(self) -> dict:
        """获取交易状态"""
        try:
            current_capital = self._calculate_current_capital()
            available_cash = self._calculate_available_cash()
            market_value = sum(pos.quantity * pos.current_price for pos in self.positions.values())
            
            # 计算今日盈亏
            today_pnl = current_capital - self.initial_capital
            
            # 🆕 获取当前策略信息
            current_strategy_info = None
            if self.strategy_manager:
                try:
                    current_strategy = self.strategy_manager.get_current_strategy()
                    if current_strategy:
                        current_strategy_info = {
                            'name': current_strategy.name,
                            'description': current_strategy.description,
                            'parameters': current_strategy.parameters
                        }
                except Exception as e:
                    logger.error(f"获取当前策略信息失败: {e}")

            return {
                'is_running': self.is_running,
                'symbol': self.current_symbol,
                'start_time': self.stats.get('start_time'),
                'current_capital': current_capital,
                'available_cash': available_cash,
                'market_value': market_value,
                'today_pnl': today_pnl,
                'total_pnl': self.stats['total_pnl'],
                'positions': [asdict(pos) for pos in self.positions.values()],
                'today_trades': [asdict(trade) for trade in self.trades[-10:]],  # 最近10笔交易
                'stats': self.stats.copy(),
                'risk_status': {
                    'daily_pnl': self.risk_manager.daily_pnl,
                    'max_drawdown': self.risk_manager.max_drawdown,
                    'emergency_stop': self.risk_manager.emergency_stop
                },
                'last_signal': asdict(self.signals[-1]) if self.signals else None,
                'current_strategy': current_strategy_info,  # 🆕 当前策略信息
                'a_stock_stats': self.a_stock_stats.copy()  # 🆕 A股规则统计
            }
            
        except Exception as e:
            logger.error(f"获取交易状态失败: {e}")
            return {'error': str(e)}
            
    def close_all_positions(self, reason: str = "手动平仓") -> bool:
        """平仓所有持仓"""
        try:
            if not self.positions:
                return True
                
            # 获取当前价格（简化处理）
            current_price = 0.75  # 这里应该获取实际的当前价格
            
            for position in list(self.positions.values()):
                signal = TradeSignal(
                    timestamp=datetime.now(),
                    symbol=position.symbol,
                    signal_type='SELL',
                    price=current_price,
                    confidence=1.0,
                    reason=reason,
                    quantity=position.quantity
                )
                
                self._execute_sell(signal)
                
            logger.info(f"已平仓所有持仓，原因: {reason}")
            return True
            
        except Exception as e:
            logger.error(f"平仓所有持仓失败: {e}")
            return False
            
    def update_strategy_params(self, params: dict):
        """更新策略参数"""
        try:
            self.strategy_params.update(params)
            logger.info(f"策略参数已更新: {params}")

        except Exception as e:
            logger.error(f"更新策略参数失败: {e}")

    def refresh_strategy_manager(self):
        """刷新策略管理器状态（与页面选择同步）"""
        try:
            if STRATEGY_MANAGER_AVAILABLE and self.strategy_manager:
                # 重新获取策略管理器实例以同步状态
                self.strategy_manager = get_strategy_manager()
                current_strategy = self.strategy_manager.get_current_strategy()
                if current_strategy:
                    logger.info(f"策略管理器已刷新，当前策略: {current_strategy.name}")
                    return True
            return False
        except Exception as e:
            logger.error(f"刷新策略管理器失败: {e}")
            return False

# 全局实例
enhanced_trader = EnhancedRealTimeTrader()

# 便捷函数
def start_enhanced_trading(symbol: str, params: Dict = None) -> bool:
    """启动增强版实时交易"""
    if params:
        enhanced_trader.update_strategy_params(params)
    return enhanced_trader.start_trading(symbol)

def stop_enhanced_trading() -> bool:
    """停止增强版实时交易"""
    return enhanced_trader.stop_trading()

def get_enhanced_trading_status() -> dict:
    """获取增强版交易状态"""
    return enhanced_trader.get_status()

def close_all_enhanced_positions() -> bool:
    """平仓所有持仓"""
    return enhanced_trader.close_all_positions()