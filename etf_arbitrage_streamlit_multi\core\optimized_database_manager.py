#!/usr/bin/env python3
"""
优化的数据库管理器
实现高性能数据库操作，包括查询优化、连接池、缓存等
"""

import sqlite3
import threading
import time
import logging
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime, timedelta
from contextlib import contextmanager
from dataclasses import dataclass
from pathlib import Path
import pandas as pd
import json
import hashlib
from functools import lru_cache
import queue
import weakref

# 尝试导入Redis用于分布式缓存
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class QueryPerformanceMetrics:
    """查询性能指标"""
    query_hash: str
    execution_time: float
    rows_returned: int
    cache_hit: bool
    timestamp: datetime

@dataclass
class ConnectionPoolStats:
    """连接池统计"""
    total_connections: int
    active_connections: int
    idle_connections: int
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_wait_time: float
    cache_hit_rate: float

class QueryCache:
    """查询结果缓存"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache = {}
        self._access_times = {}
        self._lock = threading.RLock()
        
        # Redis缓存（如果可用）
        self.redis_client = None
        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
                self.redis_client.ping()
                logger.info("Redis缓存已启用")
            except:
                self.redis_client = None
                logger.info("Redis不可用，使用内存缓存")
    
    def _generate_cache_key(self, query: str, params: tuple) -> str:
        """生成缓存键"""
        content = f"{query}:{params}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def get(self, query: str, params: tuple) -> Optional[Any]:
        """获取缓存结果"""
        cache_key = self._generate_cache_key(query, params)
        
        # 先尝试Redis缓存
        if self.redis_client:
            try:
                cached_data = self.redis_client.get(cache_key)
                if cached_data:
                    return json.loads(cached_data)
            except Exception as e:
                logger.warning(f"Redis缓存读取失败: {e}")
        
        # 内存缓存
        with self._lock:
            if cache_key in self._cache:
                cached_time, data = self._cache[cache_key]
                if time.time() - cached_time < self.ttl_seconds:
                    self._access_times[cache_key] = time.time()
                    return data
                else:
                    # 过期删除
                    del self._cache[cache_key]
                    del self._access_times[cache_key]
        
        return None
    
    def set(self, query: str, params: tuple, data: Any):
        """设置缓存"""
        cache_key = self._generate_cache_key(query, params)
        current_time = time.time()
        
        # Redis缓存
        if self.redis_client:
            try:
                self.redis_client.setex(
                    cache_key, 
                    self.ttl_seconds, 
                    json.dumps(data, default=str)
                )
            except Exception as e:
                logger.warning(f"Redis缓存写入失败: {e}")
        
        # 内存缓存
        with self._lock:
            # 清理过期缓存
            self._cleanup_expired()
            
            # LRU清理
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            self._cache[cache_key] = (current_time, data)
            self._access_times[cache_key] = current_time
    
    def _cleanup_expired(self):
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (cached_time, _) in self._cache.items()
            if current_time - cached_time >= self.ttl_seconds
        ]
        
        for key in expired_keys:
            del self._cache[key]
            del self._access_times[key]
    
    def _evict_lru(self):
        """LRU淘汰"""
        if not self._access_times:
            return
        
        # 找到最久未访问的键
        lru_key = min(self._access_times.items(), key=lambda x: x[1])[0]
        del self._cache[lru_key]
        del self._access_times[lru_key]
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
        
        if self.redis_client:
            try:
                self.redis_client.flushdb()
            except Exception as e:
                logger.warning(f"Redis缓存清空失败: {e}")

class OptimizedConnectionPool:
    """优化的连接池"""
    
    def __init__(self, db_path: str, pool_size: int = 10, max_overflow: int = 5):
        self.db_path = db_path
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        
        self._pool = queue.Queue(maxsize=pool_size)
        self._overflow_connections = set()
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'pool_hits': 0,
            'pool_misses': 0,
            'overflow_created': 0,
            'wait_times': []
        }
        
        # 初始化连接池
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.pool_size):
            conn = self._create_connection()
            if conn:
                self._pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建优化的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=30.0,
                isolation_level=None  # 自动提交
            )
            
            # SQLite性能优化设置
            optimizations = [
                "PRAGMA journal_mode=WAL",           # WAL模式提升并发性能
                "PRAGMA synchronous=NORMAL",         # 平衡安全性和性能
                "PRAGMA cache_size=20000",           # 增大缓存
                "PRAGMA temp_store=MEMORY",          # 临时表存储在内存
                "PRAGMA mmap_size=536870912",        # 512MB内存映射
                "PRAGMA foreign_keys=ON",            # 启用外键约束
                "PRAGMA optimize",                   # 自动优化
                "PRAGMA analysis_limit=1000",        # 分析限制
                "PRAGMA threads=4"                   # 多线程支持
            ]
            
            for pragma in optimizations:
                try:
                    conn.execute(pragma)
                except sqlite3.Error as e:
                    logger.warning(f"SQLite优化设置失败 '{pragma}': {e}")
            
            # 设置行工厂
            conn.row_factory = sqlite3.Row
            
            return conn
            
        except sqlite3.Error as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接"""
        start_time = time.time()
        conn = None
        is_overflow = False
        
        try:
            self.stats['total_requests'] += 1
            
            # 尝试从池中获取连接
            try:
                conn = self._pool.get_nowait()
                self.stats['pool_hits'] += 1
            except queue.Empty:
                # 池中无可用连接，尝试创建溢出连接
                with self._lock:
                    if len(self._overflow_connections) < self.max_overflow:
                        conn = self._create_connection()
                        if conn:
                            self._overflow_connections.add(conn)
                            is_overflow = True
                            self.stats['overflow_created'] += 1
                        else:
                            raise Exception("无法创建数据库连接")
                    else:
                        # 等待池中连接可用
                        conn = self._pool.get(timeout=10)
                        self.stats['pool_hits'] += 1
                
                self.stats['pool_misses'] += 1
            
            # 验证连接
            if not self._validate_connection(conn):
                if is_overflow:
                    self._overflow_connections.discard(conn)
                conn.close()
                conn = self._create_connection()
                if not conn:
                    raise Exception("无法创建有效连接")
                if is_overflow:
                    self._overflow_connections.add(conn)
            
            wait_time = time.time() - start_time
            self.stats['wait_times'].append(wait_time)
            
            yield conn
            
            self.stats['successful_requests'] += 1
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"获取数据库连接失败: {e}")
            if conn:
                conn.close()
            raise
        
        finally:
            # 归还连接
            if conn:
                if is_overflow:
                    with self._lock:
                        self._overflow_connections.discard(conn)
                    conn.close()
                else:
                    try:
                        self._pool.put_nowait(conn)
                    except queue.Full:
                        conn.close()
    
    def _validate_connection(self, conn: sqlite3.Connection) -> bool:
        """验证连接有效性"""
        try:
            conn.execute("SELECT 1").fetchone()
            return True
        except sqlite3.Error:
            return False
    
    def get_stats(self) -> ConnectionPoolStats:
        """获取连接池统计"""
        with self._lock:
            total_connections = self.pool_size + len(self._overflow_connections)
            active_connections = len(self._overflow_connections)
            idle_connections = self._pool.qsize()
            
            avg_wait_time = (
                sum(self.stats['wait_times']) / len(self.stats['wait_times'])
                if self.stats['wait_times'] else 0
            )
            
            cache_hit_rate = (
                self.stats['pool_hits'] / max(1, self.stats['total_requests']) * 100
            )
            
            return ConnectionPoolStats(
                total_connections=total_connections,
                active_connections=active_connections,
                idle_connections=idle_connections,
                total_requests=self.stats['total_requests'],
                successful_requests=self.stats['successful_requests'],
                failed_requests=self.stats['failed_requests'],
                average_wait_time=avg_wait_time,
                cache_hit_rate=cache_hit_rate
            )
    
    def close_all(self):
        """关闭所有连接"""
        # 关闭池中连接
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except queue.Empty:
                break
        
        # 关闭溢出连接
        with self._lock:
            for conn in self._overflow_connections:
                conn.close()
            self._overflow_connections.clear()

class OptimizedDatabaseManager:
    """优化的数据库管理器"""
    
    def __init__(self, db_configs: Dict[str, str] = None):
        self.db_configs = db_configs or {
            'ticks': 'ticks.db',
            'monitoring': 'monitoring.db',
            'backtest': 'backtest.db'
        }
        
        # 连接池
        self.connection_pools = {}
        for db_name, db_path in self.db_configs.items():
            self.connection_pools[db_name] = OptimizedConnectionPool(db_path)
        
        # 查询缓存
        self.query_cache = QueryCache()
        
        # 性能监控
        self.performance_metrics = []
        self._metrics_lock = threading.Lock()
        
        logger.info("优化数据库管理器初始化完成")
    
    @contextmanager
    def get_connection(self, db_name: str = 'ticks'):
        """获取数据库连接"""
        if db_name not in self.connection_pools:
            raise ValueError(f"未知数据库: {db_name}")

        with self.connection_pools[db_name].get_connection() as conn:
            yield conn

    def execute_query_cached(self, query: str, params: tuple = (),
                           db_name: str = 'ticks', use_cache: bool = True) -> List[Dict]:
        """执行查询（带缓存）"""
        start_time = time.time()
        cache_hit = False

        # 尝试从缓存获取
        if use_cache:
            cached_result = self.query_cache.get(query, params)
            if cached_result is not None:
                cache_hit = True
                execution_time = time.time() - start_time
                self._record_performance_metric(query, execution_time, len(cached_result), cache_hit)
                return cached_result

        # 执行查询
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            rows = cursor.fetchall()
            result = [dict(row) for row in rows]

        # 缓存结果
        if use_cache:
            self.query_cache.set(query, params, result)

        execution_time = time.time() - start_time
        self._record_performance_metric(query, execution_time, len(result), cache_hit)

        return result

    def execute_query_optimized(self, query: str, params: tuple = (),
                              db_name: str = 'ticks') -> List[Dict]:
        """执行优化查询"""
        # 查询优化提示
        optimized_query = self._optimize_query(query)
        return self.execute_query_cached(optimized_query, params, db_name)

    def _optimize_query(self, query: str) -> str:
        """查询优化"""
        # 基本查询优化
        optimizations = []

        # 添加LIMIT提示（如果没有）
        if 'LIMIT' not in query.upper() and 'SELECT' in query.upper():
            if 'ORDER BY' in query.upper():
                # 在ORDER BY后添加LIMIT
                parts = query.split('ORDER BY')
                if len(parts) == 2:
                    optimizations.append(f"{parts[0]}ORDER BY{parts[1]}")
            else:
                # 简单添加LIMIT
                if not query.strip().endswith(';'):
                    optimizations.append(query)

        return optimizations[0] if optimizations else query

    def read_dataframe_cached(self, query: str, params: tuple = (),
                            db_name: str = 'ticks', use_cache: bool = True) -> pd.DataFrame:
        """读取DataFrame（带缓存）"""
        try:
            # 对于DataFrame，我们需要特殊处理缓存
            cache_key = f"df_{hashlib.md5(f'{query}:{params}'.encode()).hexdigest()}"

            if use_cache:
                # 尝试从缓存获取
                cached_data = self.query_cache.get(query, params)
                if cached_data is not None:
                    return pd.DataFrame(cached_data)

            # 执行查询
            with self.get_connection(db_name) as conn:
                df = pd.read_sql_query(query, conn, params=params)

            # 缓存结果（转换为字典列表）
            if use_cache and not df.empty:
                self.query_cache.set(query, params, df.to_dict('records'))

            return df

        except Exception as e:
            logger.error(f"DataFrame查询失败: {e}")
            return pd.DataFrame()

    def batch_insert_optimized(self, table: str, data: List[Dict],
                             db_name: str = 'ticks', batch_size: int = 1000) -> int:
        """批量插入优化"""
        if not data:
            return 0

        total_inserted = 0

        # 分批处理
        for i in range(0, len(data), batch_size):
            batch = data[i:i + batch_size]

            # 构建批量插入语句
            if batch:
                columns = list(batch[0].keys())
                placeholders = ', '.join(['?' * len(columns)])
                query = f"INSERT OR REPLACE INTO {table} ({', '.join(columns)}) VALUES ({placeholders})"

                # 准备参数
                params_list = [
                    tuple(row.get(col) for col in columns)
                    for row in batch
                ]

                # 执行批量插入
                with self.get_connection(db_name) as conn:
                    cursor = conn.cursor()
                    cursor.executemany(query, params_list)
                    conn.commit()
                    total_inserted += cursor.rowcount

        # 清除相关缓存
        self.query_cache.clear()

        return total_inserted

    def get_latest_ticks_optimized(self, symbol: str, limit: int = 100,
                                 use_cache: bool = True) -> pd.DataFrame:
        """获取最新tick数据（优化版）"""
        # 使用优化的查询
        query = '''
            SELECT symbol, tick_time, price, volume, side
            FROM ticks
            WHERE symbol = ?
            ORDER BY tick_time DESC
            LIMIT ?
        '''

        return self.read_dataframe_cached(query, (symbol, limit), 'ticks', use_cache)

    def get_tick_statistics_optimized(self, symbol: str,
                                    start_time: str = None,
                                    end_time: str = None) -> Dict:
        """获取tick统计（优化版）"""
        # 构建优化查询
        base_query = "SELECT COUNT(*) as count, MIN(price) as min_price, MAX(price) as max_price, AVG(price) as avg_price FROM ticks WHERE symbol = ?"
        params = [symbol]

        if start_time:
            base_query += " AND tick_time >= ?"
            params.append(start_time)

        if end_time:
            base_query += " AND tick_time <= ?"
            params.append(end_time)

        result = self.execute_query_cached(base_query, tuple(params))
        return result[0] if result else {}

    def _record_performance_metric(self, query: str, execution_time: float,
                                 rows_returned: int, cache_hit: bool):
        """记录性能指标"""
        query_hash = hashlib.md5(query.encode()).hexdigest()[:8]

        metric = QueryPerformanceMetrics(
            query_hash=query_hash,
            execution_time=execution_time,
            rows_returned=rows_returned,
            cache_hit=cache_hit,
            timestamp=datetime.now()
        )

        with self._metrics_lock:
            self.performance_metrics.append(metric)

            # 保持最近1000条记录
            if len(self.performance_metrics) > 1000:
                self.performance_metrics = self.performance_metrics[-1000:]

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        with self._metrics_lock:
            if not self.performance_metrics:
                return {}

            # 计算统计信息
            total_queries = len(self.performance_metrics)
            cache_hits = sum(1 for m in self.performance_metrics if m.cache_hit)
            cache_hit_rate = cache_hits / total_queries * 100

            execution_times = [m.execution_time for m in self.performance_metrics]
            avg_execution_time = sum(execution_times) / len(execution_times)
            max_execution_time = max(execution_times)
            min_execution_time = min(execution_times)

            # 连接池统计
            pool_stats = {}
            for db_name, pool in self.connection_pools.items():
                pool_stats[db_name] = pool.get_stats()

            return {
                'query_performance': {
                    'total_queries': total_queries,
                    'cache_hit_rate': cache_hit_rate,
                    'avg_execution_time': avg_execution_time,
                    'max_execution_time': max_execution_time,
                    'min_execution_time': min_execution_time
                },
                'connection_pools': pool_stats,
                'cache_info': {
                    'cache_size': len(self.query_cache._cache),
                    'redis_available': self.query_cache.redis_client is not None
                }
            }

    def optimize_database(self, db_name: str = 'ticks'):
        """数据库优化操作"""
        with self.get_connection(db_name) as conn:
            cursor = conn.cursor()

            # 执行优化操作
            optimizations = [
                "ANALYZE",                    # 更新统计信息
                "PRAGMA optimize",            # 自动优化
                "VACUUM",                     # 整理数据库文件
                "REINDEX"                     # 重建索引
            ]

            for optimization in optimizations:
                try:
                    logger.info(f"执行数据库优化: {optimization}")
                    cursor.execute(optimization)
                    conn.commit()
                except sqlite3.Error as e:
                    logger.warning(f"优化操作失败 '{optimization}': {e}")

        logger.info(f"数据库 {db_name} 优化完成")

    def close_all(self):
        """关闭所有连接"""
        for pool in self.connection_pools.values():
            pool.close_all()

        self.query_cache.clear()
        logger.info("优化数据库管理器已关闭")

# 全局实例
_optimized_db_manager = None
_manager_lock = threading.Lock()

def get_optimized_db_manager() -> OptimizedDatabaseManager:
    """获取优化数据库管理器单例"""
    global _optimized_db_manager

    if _optimized_db_manager is None:
        with _manager_lock:
            if _optimized_db_manager is None:
                _optimized_db_manager = OptimizedDatabaseManager()

    return _optimized_db_manager

# 便捷函数
def execute_optimized_query(query: str, params: tuple = (), db_name: str = 'ticks') -> List[Dict]:
    """执行优化查询的便捷函数"""
    manager = get_optimized_db_manager()
    return manager.execute_query_optimized(query, params, db_name)

def read_optimized_dataframe(query: str, params: tuple = (), db_name: str = 'ticks') -> pd.DataFrame:
    """读取优化DataFrame的便捷函数"""
    manager = get_optimized_db_manager()
    return manager.read_dataframe_cached(query, params, db_name)
