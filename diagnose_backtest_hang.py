#!/usr/bin/env python3
"""
诊断回测仪表板卡住的问题
"""

import sqlite3
import pandas as pd
import time
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_database_connection():
    """检查数据库连接和数据"""
    logger.info("🔍 检查数据库连接和数据...")
    
    try:
        conn = sqlite3.connect("ticks.db")
        
        # 检查表是否存在
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        logger.info(f"   数据库表: {[table[0] for table in tables]}")
        
        # 检查ticks表数据量
        cursor = conn.execute("SELECT COUNT(*) FROM ticks")
        total_count = cursor.fetchone()[0]
        logger.info(f"   总tick数量: {total_count:,}")
        
        # 检查最近数据
        cursor = conn.execute("SELECT tick_time FROM ticks ORDER BY tick_time DESC LIMIT 1")
        latest_time = cursor.fetchone()
        if latest_time:
            logger.info(f"   最新数据时间: {latest_time[0]}")
        
        # 检查159740数据
        cursor = conn.execute("SELECT COUNT(*) FROM ticks WHERE symbol='159740'")
        symbol_count = cursor.fetchone()[0]
        logger.info(f"   159740数据量: {symbol_count:,}")
        
        conn.close()
        
        if total_count == 0:
            logger.error("❌ 数据库为空！")
            return False
        elif symbol_count == 0:
            logger.error("❌ 没有159740的数据！")
            return False
        else:
            logger.info("✅ 数据库连接正常")
            return True
            
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def test_simple_backtest():
    """测试简单回测是否能正常运行"""
    logger.info("\n🔧 测试简单回测...")
    
    try:
        from backtest_enhanced import BacktestConfig, run_enhanced_backtest
        
        # 创建最简单的配置
        config = BacktestConfig(
            symbol="159740",
            start_date="2025-09-03",  # 今天
            end_date="2025-09-03",    # 今天
            initial_capital=100000,   # 较小的初始资金
            buy_trigger_drop=-0.01,   # 简单参数
            profit_target=0.01,
            stop_loss=-0.05
        )
        
        logger.info("   配置创建成功")
        
        # 测试数据加载
        from backtest_enhanced import EnhancedBacktest
        backtest = EnhancedBacktest(config)
        
        start_time = time.time()
        df = backtest.load_data()
        load_time = time.time() - start_time
        
        logger.info(f"   数据加载耗时: {load_time:.2f}秒")
        logger.info(f"   加载数据量: {len(df)} 条")
        
        if df.empty:
            logger.error("❌ 没有加载到数据！")
            return False
        
        # 测试信号计算
        if len(df) > 0:
            signal = backtest.calculate_signal(df, 0)
            logger.info(f"   首个信号: {signal:.6f}")
        
        logger.info("✅ 简单回测测试通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 简单回测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_data_date_range():
    """检查数据日期范围"""
    logger.info("\n📅 检查数据日期范围...")
    
    try:
        conn = sqlite3.connect("ticks.db")
        
        # 检查数据日期范围
        query = """
        SELECT 
            MIN(tick_time) as earliest,
            MAX(tick_time) as latest,
            COUNT(*) as total_count
        FROM ticks 
        WHERE symbol='159740'
        """
        
        cursor = conn.execute(query)
        result = cursor.fetchone()
        
        if result:
            earliest, latest, count = result
            logger.info(f"   最早数据: {earliest}")
            logger.info(f"   最新数据: {latest}")
            logger.info(f"   数据总量: {count:,}")
            
            # 检查今天是否有数据
            today = datetime.now().strftime('%Y-%m-%d')
            cursor = conn.execute(
                "SELECT COUNT(*) FROM ticks WHERE symbol='159740' AND tick_time LIKE ?",
                [f"{today}%"]
            )
            today_count = cursor.fetchone()[0]
            logger.info(f"   今天数据量: {today_count:,}")
            
            if today_count == 0:
                logger.warning("⚠️ 今天没有数据，回测可能会失败")
                
                # 建议使用有数据的日期
                cursor = conn.execute("""
                    SELECT DISTINCT DATE(tick_time) as date, COUNT(*) as count
                    FROM ticks 
                    WHERE symbol='159740'
                    ORDER BY date DESC
                    LIMIT 5
                """)
                recent_dates = cursor.fetchall()
                
                logger.info("   最近有数据的日期:")
                for date, count in recent_dates:
                    logger.info(f"     {date}: {count:,} 条数据")
        
        conn.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ 检查数据日期范围失败: {e}")
        return False

def check_system_resources():
    """检查系统资源"""
    logger.info("\n💻 检查系统资源...")
    
    try:
        import psutil
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        logger.info(f"   CPU使用率: {cpu_percent:.1f}%")
        
        # 内存使用率
        memory = psutil.virtual_memory()
        logger.info(f"   内存使用率: {memory.percent:.1f}%")
        logger.info(f"   可用内存: {memory.available / 1024**3:.1f} GB")
        
        # 磁盘使用率
        disk = psutil.disk_usage('.')
        logger.info(f"   磁盘使用率: {disk.percent:.1f}%")
        
        if cpu_percent > 90:
            logger.warning("⚠️ CPU使用率过高")
        if memory.percent > 90:
            logger.warning("⚠️ 内存使用率过高")
        if disk.percent > 90:
            logger.warning("⚠️ 磁盘空间不足")
            
        return True
        
    except ImportError:
        logger.info("   psutil未安装，跳过系统资源检查")
        return True
    except Exception as e:
        logger.error(f"❌ 系统资源检查失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    logger.info("\n💡 建议解决方案:")
    
    logger.info("1. 🔄 重启回测面板:")
    logger.info("   • 停止当前streamlit进程 (Ctrl+C)")
    logger.info("   • 重新运行: streamlit run app_enhanced_backtest_dashboard.py")
    
    logger.info("\n2. 📅 检查日期设置:")
    logger.info("   • 确保选择的日期范围有数据")
    logger.info("   • 建议使用最近几天的日期")
    
    logger.info("\n3. ⚙️ 简化参数:")
    logger.info("   • 使用默认参数配置")
    logger.info("   • 避免使用智能优化功能")
    
    logger.info("\n4. 🗄️ 检查数据:")
    logger.info("   • 确保ticks.db文件存在且有数据")
    logger.info("   • 检查159740符号的数据是否完整")
    
    logger.info("\n5. 🔧 调试模式:")
    logger.info("   • 在终端中直接运行简单回测")
    logger.info("   • 查看详细错误信息")

def main():
    """主诊断函数"""
    logger.info("🔍 开始诊断回测仪表板卡住问题")
    logger.info("=" * 60)
    
    # 诊断结果
    results = {}
    
    # 检查1: 数据库连接
    results['database'] = check_database_connection()
    
    # 检查2: 数据日期范围
    results['date_range'] = check_data_date_range()
    
    # 检查3: 简单回测
    results['simple_backtest'] = test_simple_backtest()
    
    # 检查4: 系统资源
    results['system_resources'] = check_system_resources()
    
    logger.info("\n" + "=" * 60)
    logger.info("📊 诊断结果总结:")
    
    for check_name, success in results.items():
        status = "✅ 正常" if success else "❌ 异常"
        logger.info(f"   {check_name}: {status}")
    
    success_count = sum(1 for success in results.values() if success)
    total_count = len(results)
    
    logger.info(f"\n🎯 总体状态: {success_count}/{total_count} 检查通过")
    
    if success_count < total_count:
        logger.info("⚠️ 发现问题，请查看上述检查结果")
    else:
        logger.info("✅ 系统状态正常，可能是临时性问题")
    
    # 提供解决方案
    suggest_solutions()
    
    logger.info("=" * 60)

if __name__ == "__main__":
    main()
