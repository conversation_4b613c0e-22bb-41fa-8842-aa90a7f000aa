#!/usr/bin/env python3
"""
数据库连接池管理
提供高性能的数据库连接复用机制
"""

import sqlite3
import queue
import threading
import time
import logging
from contextlib import contextmanager
from typing import Generator, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DatabasePool:
    """数据库连接池"""
    
    def __init__(self, db_path: str, pool_size: int = 10, timeout: float = 30.0):
        """
        初始化数据库连接池
        
        Args:
            db_path: 数据库文件路径
            pool_size: 连接池大小
            timeout: 连接超时时间（秒）
        """
        self.db_path = db_path
        self.pool_size = pool_size
        self.timeout = timeout
        self.pool = queue.Queue(maxsize=pool_size)
        self.lock = threading.Lock()
        self.created_connections = 0
        self.active_connections = 0
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_wait_time': 0.0,
            'max_wait_time': 0.0,
            'pool_hits': 0,
            'pool_misses': 0
        }
        
        # 初始化连接池
        self._initialize_pool()
        
        logger.info(f"数据库连接池初始化完成: {pool_size} 个连接")
    
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self.pool_size):
            conn = self._create_connection()
            if conn:
                self.pool.put(conn)
                self.created_connections += 1
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建新的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                check_same_thread=False,
                timeout=self.timeout,
                isolation_level=None  # 自动提交模式
            )
            
            # 优化设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            conn.execute("PRAGMA mmap_size=268435456")  # 256MB
            conn.execute("PRAGMA foreign_keys=ON")
            
            # 设置行工厂，返回字典格式
            conn.row_factory = sqlite3.Row
            
            return conn
            
        except sqlite3.Error as e:
            logger.error(f"创建数据库连接失败: {e}")
            return None
    
    @contextmanager
    def get_connection(self) -> Generator[sqlite3.Connection, None, None]:
        """
        获取数据库连接的上下文管理器
        
        使用方式:
        with db_pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM table")
        """
        start_time = time.time()
        conn = None
        
        try:
            self.stats['total_requests'] += 1
            
            # 尝试从池中获取连接
            try:
                conn = self.pool.get(timeout=10)
                self.stats['pool_hits'] += 1
                self.active_connections += 1
                
            except queue.Empty:
                # 池中没有可用连接，创建新连接
                logger.warning("连接池已满，创建临时连接")
                conn = self._create_connection()
                self.stats['pool_misses'] += 1
                
                if not conn:
                    raise Exception("无法创建数据库连接")
            
            # 验证连接是否有效
            if not self._validate_connection(conn):
                logger.warning("连接无效，重新创建")
                conn.close()
                conn = self._create_connection()
                
                if not conn:
                    raise Exception("无法创建有效的数据库连接")
            
            wait_time = time.time() - start_time
            self._update_wait_time_stats(wait_time)
            
            yield conn
            
            self.stats['successful_requests'] += 1
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"获取数据库连接失败: {e}")
            raise
            
        finally:
            if conn:
                try:
                    # 回滚任何未提交的事务
                    conn.rollback()
                    
                    # 将连接返回池中
                    if self.pool.qsize() < self.pool_size:
                        self.pool.put(conn)
                    else:
                        # 池已满，关闭连接
                        conn.close()
                    
                    self.active_connections = max(0, self.active_connections - 1)
                    
                except Exception as e:
                    logger.error(f"返回连接到池时出错: {e}")
                    try:
                        conn.close()
                    except:
                        pass
    
    def _validate_connection(self, conn: sqlite3.Connection) -> bool:
        """验证连接是否有效"""
        try:
            conn.execute("SELECT 1")
            return True
        except sqlite3.Error:
            return False
    
    def _update_wait_time_stats(self, wait_time: float):
        """更新等待时间统计"""
        self.stats['max_wait_time'] = max(self.stats['max_wait_time'], wait_time)
        
        # 计算平均等待时间
        total_requests = self.stats['total_requests']
        if total_requests > 1:
            current_avg = self.stats['avg_wait_time']
            self.stats['avg_wait_time'] = (current_avg * (total_requests - 1) + wait_time) / total_requests
        else:
            self.stats['avg_wait_time'] = wait_time
    
    def get_stats(self) -> dict:
        """获取连接池统计信息"""
        return {
            **self.stats,
            'pool_size': self.pool_size,
            'created_connections': self.created_connections,
            'active_connections': self.active_connections,
            'available_connections': self.pool.qsize(),
            'hit_rate': self.stats['pool_hits'] / max(1, self.stats['total_requests']),
            'success_rate': self.stats['successful_requests'] / max(1, self.stats['total_requests'])
        }
    
    def close_all(self):
        """关闭所有连接"""
        logger.info("关闭数据库连接池...")
        
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except queue.Empty:
                break
            except Exception as e:
                logger.error(f"关闭连接时出错: {e}")
        
        logger.info("数据库连接池已关闭")
    
    def __del__(self):
        """析构函数，确保连接被正确关闭"""
        try:
            self.close_all()
        except:
            pass

# 全局数据库连接池实例
_db_pool = None
_pool_lock = threading.Lock()

def get_db_pool() -> DatabasePool:
    """获取全局数据库连接池实例"""
    global _db_pool
    
    if _db_pool is None:
        with _pool_lock:
            if _db_pool is None:
                db_path = Path(__file__).parent.parent / "ticks.db"
                _db_pool = DatabasePool(str(db_path), pool_size=15)
    
    return _db_pool

def close_db_pool():
    """关闭全局数据库连接池"""
    global _db_pool
    
    if _db_pool:
        _db_pool.close_all()
        _db_pool = None

# 便捷函数
@contextmanager
def get_db_connection():
    """获取数据库连接的便捷函数"""
    pool = get_db_pool()
    with pool.get_connection() as conn:
        yield conn

def execute_query(query: str, params: tuple = None) -> list:
    """执行查询并返回结果"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        return cursor.fetchall()

def execute_update(query: str, params: tuple = None) -> int:
    """执行更新操作并返回影响的行数"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        conn.commit()
        return cursor.rowcount

def execute_many(query: str, params_list: list) -> int:
    """批量执行操作"""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.executemany(query, params_list)
        conn.commit()
        return cursor.rowcount

# 测试函数
def test_connection_pool():
    """测试连接池功能"""
    logger.info("开始测试数据库连接池...")
    
    pool = get_db_pool()
    
    # 测试基本连接
    try:
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM ticks")
            result = cursor.fetchone()
            logger.info(f"✅ 基本连接测试成功，ticks表记录数: {result[0]}")
    except Exception as e:
        logger.error(f"❌ 基本连接测试失败: {e}")
        return False
    
    # 测试并发连接
    import concurrent.futures
    
    def test_concurrent_query(i):
        try:
            with pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT COUNT(*) FROM ticks WHERE id % 10 = ?", (i,))
                result = cursor.fetchone()
                return f"线程{i}: {result[0]}条记录"
        except Exception as e:
            return f"线程{i}: 错误 - {e}"
    
    # 并发测试
    with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(test_concurrent_query, i) for i in range(20)]
        results = [future.result() for future in concurrent.futures.as_completed(futures)]
    
    logger.info("✅ 并发连接测试完成")
    for result in results[:5]:  # 只显示前5个结果
        logger.info(f"  {result}")
    
    # 显示统计信息
    stats = pool.get_stats()
    logger.info("📊 连接池统计信息:")
    logger.info(f"  总请求数: {stats['total_requests']}")
    logger.info(f"  成功率: {stats['success_rate']:.2%}")
    logger.info(f"  命中率: {stats['hit_rate']:.2%}")
    logger.info(f"  平均等待时间: {stats['avg_wait_time']:.4f}秒")
    logger.info(f"  活跃连接数: {stats['active_connections']}")
    logger.info(f"  可用连接数: {stats['available_connections']}")
    
    return True

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 测试数据库连接池...")
    print("=" * 50)
    
    success = test_connection_pool()
    
    if success:
        print("\n🎉 数据库连接池测试成功！")
        print("✅ 基本连接功能正常")
        print("✅ 并发连接功能正常")
        print("✅ 统计信息功能正常")
    else:
        print("\n❌ 数据库连接池测试失败")
    
    print("=" * 50)
