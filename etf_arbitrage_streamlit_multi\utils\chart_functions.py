#!/usr/bin/env python3
"""
图表创建功能模块
从 app_enhanced_backtest_dashboard.py 提取的图表功能
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px

def create_price_and_signals_chart(df_signals: pd.DataFrame, df_trades: pd.DataFrame, config) -> go.Figure:
    """创建价格和信号图表"""
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('价格走势与交易信号', '持仓变化', '交易信号强度'),
        row_heights=[0.5, 0.25, 0.25]
    )
    
    # 价格走势
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['price'],
            mode='lines',
            name='价格',
            line=dict(color='blue', width=1),
            hovertemplate='时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 交易点位
    if not df_trades.empty:
        buy_trades = df_trades[df_trades['type'] == 'BUY']
        sell_trades = df_trades[df_trades['type'] == 'SELL']
        
        if not buy_trades.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_trades['time'],
                    y=buy_trades['price'],
                    mode='markers',
                    name='买入',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,
                        color='green',
                        line=dict(width=2, color='darkgreen')
                    ),
                    hovertemplate='买入<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=buy_trades['quantity']
                ),
                row=1, col=1
            )
        
        if not sell_trades.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_trades['time'],
                    y=sell_trades['price'],
                    mode='markers',
                    name='卖出',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,
                        color='red',
                        line=dict(width=2, color='darkred')
                    ),
                    hovertemplate='卖出<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=sell_trades['quantity']
                ),
                row=1, col=1
            )
    
    # 持仓变化
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['position'],
            mode='lines',
            name='持仓',
            line=dict(color='orange', width=2),
            fill='tonexty',
            hovertemplate='时间: %{x}<br>持仓: %{y}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 交易信号强度
    buy_threshold = config.buy_trigger_drop
    profit_threshold = config.profit_target
    
    signal_colors = [
        'green' if s <= buy_threshold 
        else 'red' if s >= profit_threshold 
        else 'gray' 
        for s in df_signals['signal']
    ]
    
    fig.add_trace(
        go.Scatter(
            x=df_signals['time'],
            y=df_signals['signal'],
            mode='markers',
            name='信号',
            marker=dict(
                color=signal_colors,
                size=4,
                opacity=0.6
            ),
            hovertemplate='时间: %{x}<br>信号: %{y:.6f}<extra></extra>'
        ),
        row=3, col=1
    )
    
    # 添加参数化的信号阈值线
    fig.add_hline(y=buy_threshold, line_dash="dash", line_color="red", 
                  annotation_text=f"买入阈值: {buy_threshold:.4f}", row=3, col=1)
    fig.add_hline(y=profit_threshold, line_dash="dash", line_color="green", 
                  annotation_text=f"止盈阈值: {profit_threshold:.4f}", row=3, col=1)
    
    # 更新布局
    fig.update_layout(
        height=800,
        showlegend=True,
        hovermode='x unified',
        xaxis3=dict(
            title="时间",
            rangeslider=dict(visible=True, thickness=0.05),
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),
                dict(bounds=[16, 9], pattern="hour"),
            ]
        )
    )
    
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="持仓数量", row=2, col=1)
    fig.update_yaxes(title_text="信号强度", row=3, col=1)
    
    return fig

def create_equity_curve_chart(df_equity: pd.DataFrame, config) -> go.Figure:
    """创建净值曲线图表"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('净值曲线', '回撤曲线'),
        row_heights=[0.7, 0.3]
    )
    
    # 净值曲线
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=df_equity['equity'],
            mode='lines',
            name='净值',
            line=dict(color='blue', width=2),
            hovertemplate='时间: %{x}<br>净值: %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 初始资金线
    fig.add_hline(
        y=config.initial_capital,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"初始资金: {config.initial_capital:,.0f}",
        row=1, col=1
    )
    
    # 回撤曲线
    peak = df_equity['equity'].cummax()
    drawdown = (df_equity['equity'] - peak) / peak
    
    fig.add_trace(
        go.Scatter(
            x=df_equity['time'],
            y=drawdown,
            mode='lines',
            name='回撤',
            line=dict(color='red', width=1),
            fill='tonexty',
            fillcolor='rgba(255,0,0,0.3)',
            hovertemplate='时间: %{x}<br>回撤: %{y:.2%}<extra></extra>'
        ),
        row=2, col=1
    )
    
    # 更新布局
    fig.update_layout(
        height=600,
        showlegend=True,
        hovermode='x unified',
        xaxis2=dict(
            title="时间",
            type='date',
            rangebreaks=[
                dict(bounds=["sat", "mon"]),
                dict(bounds=[16, 9], pattern="hour"),
            ]
        )
    )
    
    fig.update_yaxes(title_text="净值", row=1, col=1)
    fig.update_yaxes(title_text="回撤", tickformat='.2%', row=2, col=1)
    
    return fig

def create_trade_analysis_chart(df_trades: pd.DataFrame) -> go.Figure:
    """创建交易分析图表"""
    if df_trades.empty:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无交易数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=20)
        )
        return fig
    
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('交易时间分布', '买卖比例', '交易原因分析', '盈亏分布'),
        specs=[[{"type": "histogram"}, {"type": "pie"}],
               [{"type": "bar"}, {"type": "histogram"}]]
    )
    
    # 交易时间分布
    df_trades['hour'] = pd.to_datetime(df_trades['time']).dt.hour
    fig.add_trace(
        go.Histogram(
            x=df_trades['hour'],
            nbinsx=24,
            name='交易时间',
            marker_color='lightblue'
        ),
        row=1, col=1
    )
    
    # 买卖比例
    trade_counts = df_trades['type'].value_counts()
    fig.add_trace(
        go.Pie(
            labels=trade_counts.index,
            values=trade_counts.values,
            name='买卖比例',
            marker_colors=['green', 'red']
        ),
        row=1, col=2
    )
    
    # 交易原因分析
    if 'reason' in df_trades.columns:
        reason_counts = df_trades['reason'].value_counts()
        fig.add_trace(
            go.Bar(
                x=reason_counts.index,
                y=reason_counts.values,
                name='交易原因',
                marker_color='orange'
            ),
            row=2, col=1
        )
    
    # 盈亏分布
    sell_trades = df_trades[df_trades['type'] == 'SELL']
    if not sell_trades.empty and 'pnl' in sell_trades.columns:
        fig.add_trace(
            go.Histogram(
                x=sell_trades['pnl'],
                name='盈亏分布',
                marker_color='purple'
            ),
            row=2, col=2
        )
    
    fig.update_layout(
        title="交易行为分析",
        height=600,
        showlegend=False
    )
    
    return fig