#!/usr/bin/env python3
"""
增强版实时交易面板
集成真实策略引擎，实现完整的交易生命周期管理
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import json
from pathlib import Path
import sys
import logging
from io import StringIO
from typing import Dict, List, Optional, Any

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

# 导入增强版交易引擎
from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import (
    enhanced_trader, start_enhanced_trading, stop_enhanced_trading,
    get_enhanced_trading_status, close_all_enhanced_positions
)

# 导入优化的交易动作处理器
try:
    from etf_arbitrage_streamlit_multi.utils.trade_action_optimizer import (
        OptimizedTradeActionProcessor, TradeActionType
    )
    TRADE_ACTION_OPTIMIZER_AVAILABLE = True
    logging.info("交易动作优化器加载成功")
except ImportError as e:
    TRADE_ACTION_OPTIMIZER_AVAILABLE = False
    logging.warning(f"交易动作优化器不可用，使用传统处理方式: {e}")

# 自定义日志处理器，用于在Streamlit界面显示日志
class StreamlitLogHandler(logging.Handler):
    """自定义日志处理器，将日志输出到Streamlit界面"""
    def __init__(self):
        super().__init__()
        self.log_records = []
        self.max_records = 500  # 最多保存500条日志
    
    def emit(self, record):
        try:
            log_entry = {
                'timestamp': datetime.fromtimestamp(record.created),
                'level': record.levelname,
                'module': record.name.split('.')[-1],  # 只显示模块名
                'function': record.funcName,
                'line': record.lineno,
                'message': record.getMessage()
            }
            self.log_records.append(log_entry)
            
            # 保持日志数量在限制内
            if len(self.log_records) > self.max_records:
                self.log_records = self.log_records[-self.max_records:]
        except Exception as e:
            # 避免日志处理器本身出错
            pass
    
    def get_recent_logs(self, count: int = 100, level_filter: str = None) -> List[Dict]:
        """获取最近的日志记录"""
        logs = self.log_records[-count:] if self.log_records else []
        
        if level_filter and level_filter != "ALL":
            logs = [log for log in logs if log['level'] == level_filter]
        
        return logs
    
    def clear_logs(self):
        """清空日志记录"""
        self.log_records.clear()

# 初始化全局日志处理器
if 'log_handler' not in st.session_state:
    st.session_state.log_handler = StreamlitLogHandler()

# 配置详细日志系统
def setup_logging():
    """设置日志系统"""
    # 设置根日志级别
    logging.getLogger().setLevel(logging.DEBUG)
    
    # 需要监控的模块
    modules_to_monitor = [
        'enhanced_real_time_trader',
        'real_data_connector',
        'risk_alert_system',
        'strategy_engine_enhanced',
        '__main__'
    ]
    
    # 为每个模块添加自定义处理器
    for module_name in modules_to_monitor:
        logger = logging.getLogger(module_name)
        logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if st.session_state.log_handler not in logger.handlers:
            logger.addHandler(st.session_state.log_handler)

# 设置日志系统
setup_logging()

# 获取当前模块的logger
logger = logging.getLogger(__name__)

# 页面配置
st.set_page_config(
    page_title="增强版实时交易",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 10px;
        color: white;
        margin: 0.5rem 0;
    }
    .profit-positive {
        color: #00ff00;
        font-weight: bold;
    }
    .profit-negative {
        color: #ff4444;
        font-weight: bold;
    }
    .status-running {
        color: #00ff00;
        font-weight: bold;
    }
    .status-stopped {
        color: #ff4444;
        font-weight: bold;
    }
    .alert-box {
        padding: 1rem;
        border-radius: 5px;
        margin: 1rem 0;
    }
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .alert-warning {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
    }
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .position-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 1rem;
        margin: 0.5rem 0;
        background: #f8f9fa;
    }
</style>
""", unsafe_allow_html=True)

def init_session_state():
    """初始化会话状态"""
    if 'trading_status' not in st.session_state:
        st.session_state.trading_status = get_enhanced_trading_status()
    if 'auto_refresh' not in st.session_state:
        st.session_state.auto_refresh = True
    if 'refresh_interval' not in st.session_state:
        st.session_state.refresh_interval = 2
    if 'last_refresh' not in st.session_state:
        st.session_state.last_refresh = datetime.now()

# 交易动作相关常量和枚举
class TradeAction:
    """交易动作常量"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

    # 扩展动作类型
    PARTIAL_SELL = "PARTIAL_SELL"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    FORCE_CLOSE = "FORCE_CLOSE"

    # 动作分组
    BUY_ACTIONS = [BUY]
    SELL_ACTIONS = [SELL, PARTIAL_SELL, STOP_LOSS, TAKE_PROFIT, FORCE_CLOSE]
    ALL_ACTIONS = [BUY, SELL, HOLD, PARTIAL_SELL, STOP_LOSS, TAKE_PROFIT, FORCE_CLOSE]

    @classmethod
    def is_buy_action(cls, action: str) -> bool:
        """判断是否为买入类动作"""
        return action in cls.BUY_ACTIONS

    @classmethod
    def is_sell_action(cls, action: str) -> bool:
        """判断是否为卖出类动作"""
        return action in cls.SELL_ACTIONS

    @classmethod
    def is_valid_action(cls, action: str) -> bool:
        """判断是否为有效的交易动作"""
        return action in cls.ALL_ACTIONS

    @classmethod
    def normalize_action(cls, action: str) -> str:
        """标准化交易动作（处理大小写和别名）"""
        if not action:
            return cls.HOLD

        action = action.upper().strip()

        # 处理常见别名
        alias_map = {
            'B': cls.BUY,
            'S': cls.SELL,
            'H': cls.HOLD,
            'PARTIAL': cls.PARTIAL_SELL,
            'STOP': cls.STOP_LOSS,
            'PROFIT': cls.TAKE_PROFIT,
            'CLOSE': cls.FORCE_CLOSE
        }

        return alias_map.get(action, action if cls.is_valid_action(action) else cls.HOLD)

class TradeActionDisplay:
    """交易动作显示映射"""
    DISPLAY_MAP = {
        TradeAction.BUY: {"text": "买入", "icon": "🟢", "color": "#28a745", "priority": 1},
        TradeAction.SELL: {"text": "卖出", "icon": "🔴", "color": "#dc3545", "priority": 2},
        TradeAction.PARTIAL_SELL: {"text": "部分卖出", "icon": "🟡", "color": "#ffc107", "priority": 3},
        TradeAction.STOP_LOSS: {"text": "止损", "icon": "🛑", "color": "#dc3545", "priority": 4},
        TradeAction.TAKE_PROFIT: {"text": "止盈", "icon": "💰", "color": "#28a745", "priority": 5},
        TradeAction.FORCE_CLOSE: {"text": "强制平仓", "icon": "⚠️", "color": "#fd7e14", "priority": 6},
        TradeAction.HOLD: {"text": "持有", "icon": "⏸️", "color": "#6c757d", "priority": 7}
    }

    @classmethod
    def get_display_info(cls, action: str) -> dict:
        """获取交易动作的显示信息"""
        normalized_action = TradeAction.normalize_action(action)
        return cls.DISPLAY_MAP.get(normalized_action, {
            "text": action,
            "icon": "❓",
            "color": "#6c757d",
            "priority": 99
        })

    @classmethod
    def format_action_display(cls, action: str, include_icon: bool = True,
                            include_color: bool = False) -> str:
        """格式化交易动作显示"""
        info = cls.get_display_info(action)

        if include_color:
            color_style = f'style="color: {info["color"]}"'
            if include_icon:
                return f'<span {color_style}>{info["icon"]} {info["text"]}</span>'
            return f'<span {color_style}>{info["text"]}</span>'
        else:
            if include_icon:
                return f"{info['icon']} {info['text']}"
            return info['text']

class TradeProcessor:
    """交易数据处理器"""

    @staticmethod
    def validate_trade_record(trade: dict) -> tuple[bool, str]:
        """验证交易记录的完整性"""
        required_fields = ['timestamp', 'symbol', 'action', 'quantity', 'price', 'amount']

        for field in required_fields:
            if field not in trade:
                return False, f"缺少必需字段: {field}"

        # 验证交易动作
        action = trade.get('action')
        if not TradeAction.is_valid_action(action):
            return False, f"无效的交易动作: {action}"

        # 验证数值字段
        numeric_fields = ['quantity', 'price', 'amount']
        for field in numeric_fields:
            try:
                value = float(trade[field])
                if value < 0:
                    return False, f"{field} 不能为负数"
            except (ValueError, TypeError):
                return False, f"{field} 必须是有效数字"

        return True, "验证通过"

    @staticmethod
    def calculate_trade_metrics(trade: dict) -> dict:
        """计算交易相关指标"""
        action = TradeAction.normalize_action(trade.get('action', ''))
        quantity = trade.get('quantity', 0)
        price = trade.get('price', 0)
        amount = trade.get('amount', 0)

        # 基础费用
        commission = trade.get('commission', 0)
        stamp_tax = trade.get('stamp_tax', 0)
        transfer_fee = trade.get('transfer_fee', 0)
        total_fees = commission + stamp_tax + transfer_fee

        # 计算净金额
        if TradeAction.is_buy_action(action):
            net_amount = -(amount + total_fees)  # 买入为负现金流
            cash_impact = net_amount
        else:
            net_amount = amount - total_fees  # 卖出为正现金流
            cash_impact = net_amount

        # 费用率
        fee_rate = total_fees / amount if amount > 0 else 0

        return {
            'action_normalized': action,
            'total_fees': total_fees,
            'net_amount': net_amount,
            'cash_impact': cash_impact,
            'fee_rate': fee_rate,
            'is_buy': TradeAction.is_buy_action(action),
            'is_sell': TradeAction.is_sell_action(action)
        }

# 交易动作处理系统
class TradeAction:
    """统一的交易动作处理类"""

    # 基础动作类型
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"

    # 扩展动作类型
    PARTIAL_SELL = "PARTIAL_SELL"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"
    FORCE_CLOSE = "FORCE_CLOSE"

    # 动作分组
    BUY_ACTIONS = {BUY}
    SELL_ACTIONS = {SELL, PARTIAL_SELL, STOP_LOSS, TAKE_PROFIT, FORCE_CLOSE}
    NEUTRAL_ACTIONS = {HOLD}

    # 显示配置
    DISPLAY_CONFIG = {
        BUY: {"text": "买入", "icon": "🟢", "color": "#28a745", "bg_color": "#d4edda"},
        SELL: {"text": "卖出", "icon": "🔴", "color": "#dc3545", "bg_color": "#f8d7da"},
        PARTIAL_SELL: {"text": "部分卖出", "icon": "🟡", "color": "#ffc107", "bg_color": "#fff3cd"},
        STOP_LOSS: {"text": "止损", "icon": "🛑", "color": "#dc3545", "bg_color": "#f8d7da"},
        TAKE_PROFIT: {"text": "止盈", "icon": "💰", "color": "#28a745", "bg_color": "#d4edda"},
        FORCE_CLOSE: {"text": "强制平仓", "icon": "⚠️", "color": "#fd7e14", "bg_color": "#ffeaa7"},
        HOLD: {"text": "持有", "icon": "⏸️", "color": "#6c757d", "bg_color": "#e2e3e5"}
    }

    @classmethod
    def normalize_action(cls, action: str) -> str:
        """标准化交易动作"""
        if not action:
            return cls.HOLD

        action = action.upper().strip()

        # 处理常见的动作别名
        action_aliases = {
            "买": cls.BUY,
            "买入": cls.BUY,
            "购买": cls.BUY,
            "卖": cls.SELL,
            "卖出": cls.SELL,
            "出售": cls.SELL,
            "持有": cls.HOLD,
            "等待": cls.HOLD,
            "观望": cls.HOLD
        }

        return action_aliases.get(action, action)

    @classmethod
    def is_buy_action(cls, action: str) -> bool:
        """判断是否为买入动作"""
        return cls.normalize_action(action) in cls.BUY_ACTIONS

    @classmethod
    def is_sell_action(cls, action: str) -> bool:
        """判断是否为卖出动作"""
        return cls.normalize_action(action) in cls.SELL_ACTIONS

    @classmethod
    def is_neutral_action(cls, action: str) -> bool:
        """判断是否为中性动作"""
        return cls.normalize_action(action) in cls.NEUTRAL_ACTIONS

    @classmethod
    def get_display_info(cls, action: str) -> dict:
        """获取动作显示信息"""
        normalized_action = cls.normalize_action(action)
        return cls.DISPLAY_CONFIG.get(normalized_action, {
            "text": action,
            "icon": "❓",
            "color": "#6c757d",
            "bg_color": "#e2e3e5"
        })

    @classmethod
    def format_display(cls, action: str, style: str = "full") -> str:
        """格式化动作显示

        Args:
            action: 交易动作
            style: 显示样式 ('full', 'icon_only', 'text_only', 'colored')
        """
        info = cls.get_display_info(action)

        if style == "icon_only":
            return info["icon"]
        elif style == "text_only":
            return info["text"]
        elif style == "colored":
            return f"<span style='color: {info['color']}'>{info['icon']} {info['text']}</span>"
        else:  # full
            return f"{info['icon']} {info['text']}"

def format_currency(value: float) -> str:
    """格式化货币显示"""
    if value >= 0:
        return f"¥{value:,.2f}"
    else:
        return f"-¥{abs(value):,.2f}"

def format_percentage(value: float) -> str:
    """格式化百分比显示"""
    return f"{value:.2%}"

def get_profit_class(value: float) -> str:
    """获取盈亏样式类"""
    return "profit-positive" if value >= 0 else "profit-negative"

def validate_and_normalize_trade_action(action: str) -> str:
    """验证和标准化交易动作"""
    if not action:
        return TradeAction.HOLD

    # 转换为大写进行匹配
    action_upper = action.upper()

    # 标准化映射
    action_mapping = {
        'BUY': TradeAction.BUY,
        'SELL': TradeAction.SELL,
        'HOLD': TradeAction.HOLD,
        'PARTIAL_SELL': TradeAction.PARTIAL_SELL,
        'STOP_LOSS': TradeAction.STOP_LOSS,
        'TAKE_PROFIT': TradeAction.TAKE_PROFIT,
        'FORCE_CLOSE': TradeAction.FORCE_CLOSE,
        # 兼容性映射
        'PURCHASE': TradeAction.BUY,
        'SALE': TradeAction.SELL,
        'CLOSE': TradeAction.SELL,
        'EXIT': TradeAction.SELL,
        'STOP': TradeAction.STOP_LOSS,
        'PROFIT': TradeAction.TAKE_PROFIT
    }

    return action_mapping.get(action_upper, TradeAction.HOLD)

def calculate_trade_metrics(trade: dict) -> dict:
    """计算单笔交易的详细指标"""
    action = validate_and_normalize_trade_action(trade.get('action', ''))

    # 基础信息
    quantity = trade.get('quantity', 0)
    price = trade.get('price', 0)
    amount = trade.get('amount', quantity * price)

    # 费用计算
    commission = trade.get('commission', 0)
    stamp_tax = trade.get('stamp_tax', 0)
    transfer_fee = trade.get('transfer_fee', 0)
    total_fees = commission + stamp_tax + transfer_fee

    # 净金额计算
    if action in [TradeAction.BUY]:
        net_amount = -(amount + total_fees)  # 买入为负现金流
        cash_impact = net_amount
    else:
        net_amount = amount - total_fees  # 卖出为正现金流
        cash_impact = net_amount

    # 盈亏计算
    pnl = trade.get('pnl', 0)

    return {
        'action': action,
        'action_display': TradeActionDisplay.format_action_display(action),
        'quantity': quantity,
        'price': price,
        'amount': amount,
        'commission': commission,
        'stamp_tax': stamp_tax,
        'transfer_fee': transfer_fee,
        'total_fees': total_fees,
        'net_amount': net_amount,
        'cash_impact': cash_impact,
        'pnl': pnl,
        'is_profitable': pnl > 0 if action != TradeAction.BUY else None,
        'fee_ratio': total_fees / amount if amount > 0 else 0
    }

def create_status_indicator(is_running: bool) -> str:
    """创建状态指示器"""
    if is_running:
        return '<span class="status-running">🟢 运行中</span>'
    else:
        return '<span class="status-stopped">🔴 已停止</span>'

def create_metric_card(title: str, value: str, delta: str = None) -> str:
    """创建指标卡片"""
    delta_html = f"<div style='font-size: 0.8rem; opacity: 0.8;'>{delta}</div>" if delta else ""
    return f"""
    <div class="metric-card">
        <div style="font-size: 0.9rem; opacity: 0.8;">{title}</div>
        <div style="font-size: 1.5rem; font-weight: bold;">{value}</div>
        {delta_html}
    </div>
    """

def create_position_card(position: dict) -> str:
    """创建持仓卡片"""
    pnl_class = get_profit_class(position['unrealized_pnl'])
    return_class = get_profit_class(position['return_pct'])
    
    return f"""
    <div class="position-card">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h4 style="margin: 0; color: #333;">{position['symbol']}</h4>
                <p style="margin: 0.2rem 0; color: #666;">
                    数量: {position['quantity']} | 均价: ¥{position['avg_price']:.4f}
                </p>
                <p style="margin: 0.2rem 0; color: #666;">
                    持仓时间: {position['hold_time']}秒
                </p>
            </div>
            <div style="text-align: right;">
                <div class="{pnl_class}" style="font-size: 1.2rem;">
                    {format_currency(position['unrealized_pnl'])}
                </div>
                <div class="{return_class}">
                    {format_percentage(position['return_pct'])}
                </div>
            </div>
        </div>
    </div>
    """

def create_trade_record_table(trades: list) -> pd.DataFrame:
    """创建增强的交易记录表格 - 使用优化的交易动作处理器"""
    if not trades:
        return pd.DataFrame()

    # 使用优化的批量处理器（如果可用）
    if TRADE_ACTION_OPTIMIZER_AVAILABLE:
        try:
            # 使用优化的批量处理
            trades_df = OptimizedTradeActionProcessor.batch_process_trades(trades)

            # 转换为显示格式
            df_data = []
            for _, trade_row in trades_df.iterrows():
                # 格式化时间显示
                timestamp = trade_row['timestamp']
                if isinstance(timestamp, str):
                    time_str = timestamp[:19]
                else:
                    time_str = timestamp.strftime('%H:%M:%S')

                # 使用优化的动作显示
                action_display = trade_row['action_display']

                # 计算费用和盈亏
                amount = trade_row.get('amount', 0)
                commission = trade_row.get('commission', 0)
                pnl = trade_row.get('pnl', 0)

                # 费用率计算
                fee_rate = commission / amount if amount > 0 else 0
                fee_warning = '⚠️' if fee_rate > 0.01 else ''

                # 盈亏显示
                pnl_display = '-'
                if TradeActionType(trade_row['action_normalized']).value in ['SELL', 'PARTIAL_SELL', 'STOP_LOSS', 'TAKE_PROFIT']:
                    pnl_display = format_currency(pnl)

                # 数量格式化
                quantity = trade_row.get('quantity', 0)
                quantity_display = f"{quantity:,}"
                if quantity >= 10000:
                    quantity_display = f"{quantity/10000:.1f}万"

                df_data.append({
                    '时间': time_str,
                    '代码': trade_row.get('symbol', ''),
                    '操作': action_display,
                    '数量': quantity_display,
                    '价格': f"¥{trade_row.get('price', 0):.4f}",
                    '金额': format_currency(amount),
                    '手续费': f"{fee_warning}{format_currency(commission)}",
                    '费用率': f"{fee_rate:.3%}",
                    '盈亏': pnl_display,
                    '原因': trade_row.get('reason', '')
                })

            return pd.DataFrame(df_data)

        except Exception as e:
            logger.warning(f"使用优化处理器失败，回退到传统方式: {e}")

    # 传统处理方式（回退）
    df_data = []
    validation_errors = []

    for i, trade in enumerate(trades):
        # 验证交易记录
        try:
            is_valid, error_msg = TradeProcessor.validate_trade_record(trade)
            if not is_valid:
                validation_errors.append(f"交易{i+1}: {error_msg}")
                continue

            # 计算交易指标
            metrics = TradeProcessor.calculate_trade_metrics(trade)
            action = metrics['action_normalized']

            # 使用传统的交易动作显示逻辑
            action_display = TradeActionDisplay.format_action_display(action, include_icon=True)
        except Exception as e:
            # 如果TradeProcessor不可用，使用简化处理
            action = trade.get('action', 'HOLD')
            action_display = f"{'🟢 买入' if action == 'BUY' else '🔴 卖出' if action == 'SELL' else '⏸️ 持有'}"
            metrics = {
                'is_sell': action in ['SELL', 'PARTIAL_SELL'],
                'fee_rate': trade.get('commission', 0) / trade.get('amount', 1) if trade.get('amount', 0) > 0 else 0
            }

        # 格式化时间显示
        if isinstance(trade['timestamp'], str):
            time_str = trade['timestamp'][:19]
        else:
            time_str = trade['timestamp'].strftime('%H:%M:%S')

        # 盈亏显示逻辑优化
        pnl_display = '-'
        pnl_color_class = ''

        if metrics['is_sell']:
            pnl_value = trade.get('pnl', 0)
            pnl_display = format_currency(pnl_value)
            pnl_color_class = get_profit_class(pnl_value)

        # 费用率警告
        fee_warning = ''
        if metrics['fee_rate'] > 0.01:  # 费用率超过1%
            fee_warning = '⚠️'

        df_data.append({
            '时间': time_str,
            '代码': trade['symbol'],
            '操作': action_display,
            '数量': f"{trade['quantity']:,}",
            '价格': f"¥{trade['price']:.4f}",
            '金额': format_currency(trade['amount']),
            '净金额': format_currency(metrics['net_amount']),
            '手续费': format_currency(trade.get('commission', 0)),
            '印花税': format_currency(trade.get('stamp_tax', 0)),
            '过户费': format_currency(trade.get('transfer_fee', 0)),
            '总费用': f"{fee_warning}{format_currency(metrics['total_fees'])}",
            '费用率': f"{metrics['fee_rate']:.3%}",
            '盈亏': pnl_display,
            '现金影响': format_currency(metrics['cash_impact']),
            '原因': trade.get('reason', '')
        })

    # 如果有验证错误，记录日志
    if validation_errors:
        logger.warning(f"交易记录验证发现 {len(validation_errors)} 个错误:")
        for error in validation_errors:
            logger.warning(f"  - {error}")

    return pd.DataFrame(df_data)

def calculate_capital_curve_traditional(trades: list, initial_capital: float, current_capital: float) -> tuple:
    """传统的资金曲线计算方法"""
    # 按时间排序交易记录
    sorted_trades = sorted(trades, key=lambda x: x['timestamp'] if isinstance(x['timestamp'], str) else x['timestamp'].isoformat())

    # 构建资金变化时间序列
    times = []
    capital_values = []
    running_capital = initial_capital

    # 添加起始点
    if sorted_trades:
        first_trade_time = sorted_trades[0]['timestamp']
        if isinstance(first_trade_time, str):
            first_trade_time = datetime.fromisoformat(first_trade_time.replace('Z', '+00:00'))

        start_time = first_trade_time - timedelta(minutes=5)
        times.append(start_time)
        capital_values.append(initial_capital)

    # 处理每笔交易
    for trade in sorted_trades:
        trade_time = trade['timestamp']
        if isinstance(trade_time, str):
            trade_time = datetime.fromisoformat(trade_time.replace('Z', '+00:00'))

        # 计算资金变化
        if trade['action'] == 'BUY':
            running_capital -= trade['amount']
            running_capital -= trade.get('commission', 0)
        elif trade['action'] == 'SELL':
            running_capital += trade['amount']
            running_capital -= trade.get('commission', 0)

        times.append(trade_time)
        capital_values.append(running_capital)

    # 添加当前时间点
    times.append(datetime.now())
    capital_values.append(current_capital)

    return times, capital_values

def create_performance_chart(status: dict) -> go.Figure:
    """创建性能图表 - 使用真实资金数据和优化处理器"""
    try:
        # 获取真实的交易历史和资金变化数据
        trades = status.get('today_trades', [])
        current_capital = status.get('current_capital', 1000000)
        initial_capital = 1000000
        
        logger.info(f"创建资金曲线图 - 当前资金: {current_capital}, 交易记录数: {len(trades)}")
        
        if trades and len(trades) > 0:
            # 基于真实交易记录构建资金曲线 - 使用优化处理器
            logger.info("使用真实交易数据构建资金曲线")

            # 使用优化的资金曲线计算（如果可用）
            if TRADE_ACTION_OPTIMIZER_AVAILABLE:
                try:
                    # 使用优化的批量处理和资金曲线计算
                    trades_df = OptimizedTradeActionProcessor.batch_process_trades(trades)
                    times, capital_values = OptimizedTradeActionProcessor.calculate_capital_curve_optimized(
                        trades_df, initial_capital
                    )

                    # 添加起始点和结束点
                    if times:
                        start_time = times[0] - timedelta(minutes=30)
                        times.insert(0, start_time)
                        capital_values.insert(0, initial_capital)

                        # 添加当前时间点
                        times.append(datetime.now())
                        capital_values.append(current_capital)

                    logger.info(f"使用优化器计算资金曲线 - 数据点数: {len(times)}")

                except Exception as e:
                    logger.warning(f"优化资金曲线计算失败，使用传统方式: {e}")
                    # 回退到传统计算方式
                    times, capital_values = calculate_capital_curve_traditional(trades, initial_capital, current_capital)
            else:
                # 传统计算方式
                times, capital_values = calculate_capital_curve_traditional(trades, initial_capital, current_capital)
            
            # 如果数据点太少，在中间插值
            if len(times) < 10:
                logger.info("交易数据点较少，进行插值处理")
                # 创建更密集的时间序列
                full_times = pd.date_range(start=times[0], end=times[-1], freq='5min')
                full_capital = np.interp(
                    [t.timestamp() for t in full_times],
                    [t.timestamp() for t in times],
                    capital_values
                )
                times = full_times.to_pydatetime().tolist()
                capital_values = full_capital.tolist()
            
        else:
            # 没有交易记录时，显示平稳的资金线
            logger.info("无交易记录，显示初始资金线")
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            times = pd.date_range(start=start_time, end=end_time, freq='5min').to_pydatetime().tolist()
            capital_values = [current_capital] * len(times)
        
        # 创建图表
        fig = go.Figure()
        
        # 资金曲线
        fig.add_trace(go.Scatter(
            x=times,
            y=capital_values,
            mode='lines+markers',
            name='资金曲线',
            line=dict(color='#1f77b4', width=2),
            marker=dict(size=4, color='#1f77b4'),
            hovertemplate='时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
        ))
        
        # 基准线
        fig.add_hline(y=initial_capital, line_dash="dash", line_color="gray", 
                      annotation_text="初始资金")
        
        # 如果有盈亏，添加盈亏区域
        if len(capital_values) > 0:
            max_capital = max(capital_values)
            min_capital = min(capital_values)
            
            if max_capital > initial_capital:
                # 盈利区域
                fig.add_hrect(
                    y0=initial_capital, y1=max_capital,
                    fillcolor="green", opacity=0.1,
                    annotation_text="盈利区域", annotation_position="top left"
                )
            
            if min_capital < initial_capital:
                # 亏损区域
                fig.add_hrect(
                    y0=min_capital, y1=initial_capital,
                    fillcolor="red", opacity=0.1,
                    annotation_text="亏损区域", annotation_position="bottom left"
                )
        
        # 添加交易点标记（支持多种交易类型）
        if trades:
            # 分类存储不同类型的交易点
            trade_points = {
                'buy': {'times': [], 'capitals': [], 'trades': []},
                'sell': {'times': [], 'capitals': [], 'trades': []},
                'stop_loss': {'times': [], 'capitals': [], 'trades': []},
                'take_profit': {'times': [], 'capitals': [], 'trades': []},
                'partial_sell': {'times': [], 'capitals': [], 'trades': []}
            }

            for trade in trades:
                trade_time = trade['timestamp']
                if isinstance(trade_time, str):
                    trade_time = datetime.fromisoformat(trade_time.replace('Z', '+00:00'))

                # 找到对应的资金值
                capital_at_trade = current_capital  # 默认值
                for j, t in enumerate(times):
                    if abs((t - trade_time).total_seconds()) < 300:  # 5分钟内
                        capital_at_trade = capital_values[j]
                        break

                # 使用交易处理器标准化动作并分类
                try:
                    metrics = TradeProcessor.calculate_trade_metrics(trade)
                    action = metrics['action_normalized']

                    # 根据标准化的交易动作分类
                    if action == TradeAction.BUY:
                        trade_points['buy']['times'].append(trade_time)
                        trade_points['buy']['capitals'].append(capital_at_trade)
                        trade_points['buy']['trades'].append(trade)
                    elif action == TradeAction.SELL:
                        trade_points['sell']['times'].append(trade_time)
                        trade_points['sell']['capitals'].append(capital_at_trade)
                        trade_points['sell']['trades'].append(trade)
                    elif action == TradeAction.STOP_LOSS:
                        trade_points['stop_loss']['times'].append(trade_time)
                        trade_points['stop_loss']['capitals'].append(capital_at_trade)
                        trade_points['stop_loss']['trades'].append(trade)
                    elif action == TradeAction.TAKE_PROFIT:
                        trade_points['take_profit']['times'].append(trade_time)
                        trade_points['take_profit']['capitals'].append(capital_at_trade)
                        trade_points['take_profit']['trades'].append(trade)
                    elif action == TradeAction.PARTIAL_SELL:
                        trade_points['partial_sell']['times'].append(trade_time)
                        trade_points['partial_sell']['capitals'].append(capital_at_trade)
                        trade_points['partial_sell']['trades'].append(trade)

                except Exception as e:
                    logger.warning(f"交易分类失败，使用原始动作: {e}")
                    # 回退到原始逻辑
                    action = TradeAction.normalize_action(trade.get('action', ''))

                    if TradeAction.is_buy_action(action):
                        trade_points['buy']['times'].append(trade_time)
                        trade_points['buy']['capitals'].append(capital_at_trade)
                        trade_points['buy']['trades'].append(trade)
                    elif TradeAction.is_sell_action(action):
                        trade_points['sell']['times'].append(trade_time)
                        trade_points['sell']['capitals'].append(capital_at_trade)
                        trade_points['sell']['trades'].append(trade)
            
            # 绘制不同类型的交易点
            trade_markers = {
                'buy': {
                    'name': '买入', 'color': '#dc3545', 'symbol': 'triangle-up', 'size': 10,
                    'hovertemplate': '买入<br>时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
                },
                'sell': {
                    'name': '卖出', 'color': '#28a745', 'symbol': 'triangle-down', 'size': 10,
                    'hovertemplate': '卖出<br>时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
                },
                'stop_loss': {
                    'name': '止损', 'color': '#dc3545', 'symbol': 'x', 'size': 12,
                    'hovertemplate': '止损<br>时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
                },
                'take_profit': {
                    'name': '止盈', 'color': '#28a745', 'symbol': 'star', 'size': 12,
                    'hovertemplate': '止盈<br>时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
                },
                'partial_sell': {
                    'name': '部分卖出', 'color': '#ffc107', 'symbol': 'diamond', 'size': 10,
                    'hovertemplate': '部分卖出<br>时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
                }
            }

            # 为每种交易类型添加图表轨迹
            for trade_type, points in trade_points.items():
                if points['times']:  # 如果有该类型的交易
                    marker_config = trade_markers[trade_type]
                    fig.add_trace(go.Scatter(
                        x=points['times'],
                        y=points['capitals'],
                        mode='markers',
                        name=marker_config['name'],
                        marker=dict(
                            color=marker_config['color'],
                            size=marker_config['size'],
                            symbol=marker_config['symbol']
                        ),
                        hovertemplate=marker_config['hovertemplate']
                    ))
        
        # 计算收益率
        if len(capital_values) > 0:
            total_return = (capital_values[-1] - initial_capital) / initial_capital
            title_text = f"资金曲线 (总收益率: {total_return:.2%})"
        else:
            title_text = "资金曲线"
        
        fig.update_layout(
            title=title_text,
            xaxis_title="时间",
            yaxis_title="资金 (¥)",
            height=400,
            showlegend=True,
            hovermode='x unified'
        )
        
        logger.info(f"资金曲线图创建完成 - 数据点数: {len(times)}")
        return fig
        
    except Exception as e:
        logger.error(f"创建资金曲线图时出错: {e}")
        # 出错时返回简单的图表
        return create_fallback_performance_chart(status)


def create_fallback_performance_chart(status: dict) -> go.Figure:
    """备用资金曲线图（当无法获取真实数据时使用）"""
    logger.info("使用备用资金曲线图")
    
    current_capital = status.get('current_capital', 1000000)
    initial_capital = 1000000
    
    # 创建简单的资金线
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    times = pd.date_range(start=start_time, end=end_time, freq='5min')
    
    # 如果有资金变化，创建简单的线性变化
    if current_capital != initial_capital:
        capital_change = current_capital - initial_capital
        capital_values = [initial_capital + (capital_change * i / (len(times) - 1)) for i in range(len(times))]
    else:
        capital_values = [current_capital] * len(times)
    
    fig = go.Figure()
    
    # 资金曲线
    fig.add_trace(go.Scatter(
        x=times,
        y=capital_values,
        mode='lines',
        name='资金曲线',
        line=dict(color='#1f77b4', width=2),
        hovertemplate='时间: %{x}<br>资金: ¥%{y:,.2f}<extra></extra>'
    ))
    
    # 基准线
    fig.add_hline(y=initial_capital, line_dash="dash", line_color="gray", 
                  annotation_text="初始资金")
    
    total_return = (current_capital - initial_capital) / initial_capital
    title_text = f"资金曲线 (总收益率: {total_return:.2%})"
    
    fig.update_layout(
        title=title_text,
        xaxis_title="时间",
        yaxis_title="资金 (¥)",
        height=400,
        showlegend=True,
        hovermode='x unified'
    )
    
    return fig

def analyze_trade_patterns(trades: list) -> dict:
    """分析交易模式和统计信息"""
    if not trades:
        return {}

    analysis = {
        'total_trades': len(trades),
        'action_distribution': {},
        'pnl_by_action': {},
        'success_rates': {},
        'avg_hold_times': {},
        'trade_frequency': {}
    }

    # 按交易动作分类统计
    for trade in trades:
        action = trade.get('action', TradeAction.HOLD)

        # 动作分布统计
        if action not in analysis['action_distribution']:
            analysis['action_distribution'][action] = 0
        analysis['action_distribution'][action] += 1

        # 盈亏统计（仅卖出类交易）
        if action in [TradeAction.SELL, TradeAction.PARTIAL_SELL,
                     TradeAction.STOP_LOSS, TradeAction.TAKE_PROFIT,
                     TradeAction.FORCE_CLOSE]:
            pnl = trade.get('pnl', 0)
            if action not in analysis['pnl_by_action']:
                analysis['pnl_by_action'][action] = []
            analysis['pnl_by_action'][action].append(pnl)

    # 计算成功率（盈利交易比例）
    for action, pnl_list in analysis['pnl_by_action'].items():
        if pnl_list:
            profitable_trades = sum(1 for pnl in pnl_list if pnl > 0)
            analysis['success_rates'][action] = profitable_trades / len(pnl_list)

    # 计算交易频率（按小时）
    if trades:
        trade_times = []
        for trade in trades:
            timestamp = trade['timestamp']
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            trade_times.append(timestamp)

        if trade_times:
            time_span = (max(trade_times) - min(trade_times)).total_seconds() / 3600  # 小时
            analysis['trade_frequency']['trades_per_hour'] = len(trades) / max(time_span, 1)

    return analysis

def create_pnl_distribution_chart(trades: list) -> go.Figure:
    """创建盈亏分布图"""
    if not trades:
        # 空数据时显示占位图
        fig = go.Figure()
        fig.add_annotation(
            text="暂无交易数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False, font_size=16
        )
        fig.update_layout(title="盈亏分布", height=300)
        return fig
    
    # 提取卖出交易的盈亏数据
    pnl_data = [trade.get('pnl', 0) for trade in trades if trade['action'] == 'SELL']
    
    if not pnl_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无卖出交易",
            xref="paper", yref="paper",
            x=0.5, y=0.5, xanchor='center', yanchor='middle',
            showarrow=False, font_size=16
        )
        fig.update_layout(title="盈亏分布", height=300)
        return fig
    
    fig = go.Figure(data=[go.Histogram(
        x=pnl_data,
        nbinsx=20,
        name='盈亏分布',
        marker_color='lightblue',
        opacity=0.7
    )])
    
    fig.update_layout(
        title="盈亏分布",
        xaxis_title="盈亏 (¥)",
        yaxis_title="频次",
        height=300,
        showlegend=False
    )
    
    return fig

def generate_signal_data(symbol: str) -> pd.DataFrame:
    """生成实时信号数据 - 使用真实数据"""
    try:
        # 导入数据库模块
        from etf_arbitrage_streamlit_multi.utils.database import get_tick_data
        
        # 获取最近1小时的真实数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=2)  # 获取更多数据以确保有足够的点
        
        logger.info(f"正在获取 {symbol} 从 {start_time} 到 {end_time} 的真实数据")
        
        # 从数据库获取真实tick数据
        real_data = get_tick_data(
            symbol=symbol,
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            limit=100
        )
        
        if not real_data.empty and len(real_data) > 10:
            logger.info(f"成功获取 {symbol} 的真实数据，共 {len(real_data)} 个数据点")
            
            # 处理真实数据
            df = real_data.copy()
            
            # 确保时间列格式正确
            if 'timestamp' in df.columns:
                df['time'] = pd.to_datetime(df['timestamp'])
            elif 'time' in df.columns:
                df['time'] = pd.to_datetime(df['time'])
            else:
                # 如果没有时间列，创建一个
                df['time'] = pd.date_range(end=end_time, periods=len(df), freq='1min')
            
            # 确保价格列存在
            if 'price' not in df.columns:
                if 'close' in df.columns:
                    df['price'] = df['close']
                elif 'last_price' in df.columns:
                    df['price'] = df['last_price']
                else:
                    logger.warning(f"未找到价格列，使用模拟数据")
                    return generate_fallback_signal_data(symbol)
            
            # 按时间排序并取最近60个点
            df = df.sort_values('time').tail(60).reset_index(drop=True)
            
            # 基于真实价格数据生成交易信号
            signals = []
            signal_strengths = []
            
            for i in range(len(df)):
                if i < 5:  # 前5个点没有足够历史数据
                    signals.append(0)
                    signal_strengths.append(0.5)
                    continue
                
                # 计算技术指标
                recent_prices = df['price'].iloc[max(0, i-9):i+1].values
                
                if len(recent_prices) < 5:
                    signals.append(0)
                    signal_strengths.append(0.5)
                    continue
                
                # 计算移动平均
                short_ma = np.mean(recent_prices[-3:])  # 3期移动平均
                long_ma = np.mean(recent_prices[-6:])   # 6期移动平均
                current_price = recent_prices[-1]
                
                # 价格变化率
                if len(recent_prices) >= 5:
                    price_change = (current_price - recent_prices[-5]) / recent_prices[-5]
                else:
                    price_change = 0
                
                # 价格动量
                momentum = (current_price - recent_prices[0]) / recent_prices[0]
                
                # 生成信号逻辑
                signal = 0
                strength = 0.5
                
                # 买入信号条件
                if (short_ma > long_ma * 1.001 and  # 短期均线上穿
                    price_change > 0.005 and        # 价格上涨超过0.5%
                    momentum > 0.002):              # 正动量
                    signal = 1
                    strength = min(0.9, 0.5 + abs(price_change) * 20)
                
                # 卖出信号条件
                elif (short_ma < long_ma * 0.999 and  # 短期均线下穿
                      price_change < -0.005 and       # 价格下跌超过0.5%
                      momentum < -0.002):             # 负动量
                    signal = -1
                    strength = min(0.9, 0.5 + abs(price_change) * 20)
                
                # 检查价格波动率
                if len(recent_prices) >= 5:
                    volatility = np.std(recent_prices[-5:]) / np.mean(recent_prices[-5:])
                    if volatility > 0.01:  # 高波动时降低信号强度
                        strength *= 0.7
                
                signals.append(signal)
                signal_strengths.append(strength)
            
            df['signal'] = signals
            df['strength'] = signal_strengths
            
            logger.info(f"成功为 {symbol} 生成基于真实数据的信号，买入信号: {sum(1 for s in signals if s == 1)}, 卖出信号: {sum(1 for s in signals if s == -1)}")
            return df[['time', 'price', 'signal', 'strength']]
            
        else:
            logger.warning(f"未能获取足够的 {symbol} 真实数据（获得 {len(real_data) if not real_data.empty else 0} 条），使用备用模拟数据")
            return generate_fallback_signal_data(symbol)
            
    except Exception as e:
        logger.error(f"获取真实信号数据时出错: {e}")
        logger.info("使用备用模拟数据")
        return generate_fallback_signal_data(symbol)


def generate_fallback_signal_data(symbol: str) -> pd.DataFrame:
    """备用信号数据生成（当无法获取真实数据时使用）"""
    logger.info(f"为 {symbol} 生成备用模拟信号数据")
    
    # 生成最近1小时的数据点
    points = 60
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=1)
    times = pd.date_range(start=start_time, end=end_time, periods=points)
    
    # 生成更真实的模拟价格数据（基于随机游走）
    base_price = 0.75  # ETF基础价格
    prices = []
    current_price = base_price
    
    # 添加一些趋势性
    trend_factor = np.random.choice([-1, 0, 1], p=[0.3, 0.4, 0.3])  # 趋势方向
    
    for i in range(points):
        # 添加趋势和周期性
        trend = trend_factor * 0.0001 * np.sin(i * 0.1)  # 轻微趋势
        cycle = 0.0002 * np.sin(i * 0.3)  # 周期性波动
        noise = np.random.normal(0, 0.002)  # 随机噪声
        
        change = trend + cycle + noise
        current_price *= (1 + change)
        prices.append(current_price)
    
    # 基于价格变化生成更合理的信号
    signals = []
    signal_strengths = []
    
    for i in range(points):
        if i < 5:
            signals.append(0)
            signal_strengths.append(0.5)
            continue
            
        # 基于价格动量和技术指标生成信号
        recent_prices = prices[max(0, i-9):i+1]
        
        if len(recent_prices) >= 5:
            short_ma = np.mean(recent_prices[-3:])
            long_ma = np.mean(recent_prices[-6:])
            price_momentum = (recent_prices[-1] - recent_prices[-5]) / recent_prices[-5]
            
            # 信号生成逻辑
            if short_ma > long_ma and price_momentum > 0.003:
                signal = 1 if np.random.random() > 0.7 else 0  # 买入信号
            elif short_ma < long_ma and price_momentum < -0.003:
                signal = -1 if np.random.random() > 0.7 else 0  # 卖出信号
            else:
                signal = 0
                
            strength = min(0.9, 0.3 + abs(price_momentum) * 30)
        else:
            signal = 0
            strength = 0.5
            
        signals.append(signal)
        signal_strengths.append(strength)
    
    logger.info(f"备用数据生成完成 - 买入信号: {sum(1 for s in signals if s == 1)}, 卖出信号: {sum(1 for s in signals if s == -1)}")
    
    return pd.DataFrame({
        'time': times,
        'price': prices,
        'signal': signals,
        'strength': signal_strengths
    })

def create_signal_chart(data: pd.DataFrame) -> go.Figure:
    """创建双图表信号监控 - 恢复信号强度图表"""
    try:
        # 使用更稳定的子图创建方式
        from plotly.subplots import make_subplots

        # 创建2行1列的子图，但使用更简单的配置
        fig = make_subplots(
            rows=2, cols=1,
            shared_xaxes=True,
            vertical_spacing=0.08,
            row_heights=[0.65, 0.35],
            subplot_titles=('价格走势与交易信号', '信号强度'),
            specs=[[{"secondary_y": False}], [{"secondary_y": False}]]
        )

        # 第一个子图：价格线和交易信号
        fig.add_trace(
            go.Scatter(
                x=data['time'],
                y=data['price'],
                mode='lines',
                name='价格',
                line=dict(color='#1f77b4', width=2),
                hovertemplate='时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
            ),
            row=1, col=1
        )

        # 买入信号点
        buy_signals = data[data['signal'] == 1]
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals['time'],
                    y=buy_signals['price'],
                    mode='markers',
                    name='买入信号',
                    marker=dict(color='#d62728', size=10, symbol='triangle-up'),
                    hovertemplate='买入信号<br>时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
                ),
                row=1, col=1
            )

        # 卖出信号点
        sell_signals = data[data['signal'] == -1]
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals['time'],
                    y=sell_signals['price'],
                    mode='markers',
                    name='卖出信号',
                    marker=dict(color='#2ca02c', size=10, symbol='triangle-down'),
                    hovertemplate='卖出信号<br>时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
                ),
                row=1, col=1
            )

        # 第二个子图：信号强度柱状图
        # 为信号强度设置颜色
        signal_colors = []
        for signal in data['signal']:
            if signal == 1:
                signal_colors.append('#d62728')  # 红色 - 买入
            elif signal == -1:
                signal_colors.append('#2ca02c')  # 绿色 - 卖出
            else:
                signal_colors.append('#808080')  # 灰色 - 持有

        # 计算显示的信号强度值（带符号）
        display_strength = data['strength'] * data['signal']

        fig.add_trace(
            go.Bar(
                x=data['time'],
                y=display_strength,
                name='信号强度',
                marker_color=signal_colors,
                opacity=0.7,
                hovertemplate='时间: %{x}<br>信号强度: %{y:.3f}<extra></extra>'
            ),
            row=2, col=1
        )

        # 更新布局
        fig.update_layout(
            title={
                'text': "📡 实时信号监控",
                'x': 0.5,
                'xanchor': 'center',
                'font': {'size': 16}
            },
            height=500,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white',
            margin=dict(l=60, r=60, t=80, b=60)
        )

        # 更新坐标轴标题和格式
        fig.update_xaxes(title_text="时间", row=2, col=1)
        fig.update_yaxes(title_text="价格 (¥)", row=1, col=1)
        fig.update_yaxes(title_text="信号强度", row=2, col=1)

        # 设置网格
        fig.update_xaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')
        fig.update_yaxes(showgrid=True, gridwidth=1, gridcolor='lightgray')

        return fig

    except Exception as e:
        logger.error(f"创建双图表信号监控失败: {e}")
        # 如果子图创建失败，回退到单图表
        return create_simple_signal_chart(data)

def create_simple_signal_chart(data: pd.DataFrame) -> go.Figure:
    """创建简化的单图表信号监控（备用方案）"""
    try:
        fig = go.Figure()

        # 价格线
        fig.add_trace(
            go.Scatter(
                x=data['time'],
                y=data['price'],
                mode='lines',
                name='价格',
                line=dict(color='#1f77b4', width=2),
                hovertemplate='时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
            )
        )

        # 买入信号
        buy_signals = data[data['signal'] == 1]
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals['time'],
                    y=buy_signals['price'],
                    mode='markers',
                    name='买入信号',
                    marker=dict(color='#d62728', size=10, symbol='triangle-up'),
                    hovertemplate='买入信号<br>时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
                )
            )

        # 卖出信号
        sell_signals = data[data['signal'] == -1]
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals['time'],
                    y=sell_signals['price'],
                    mode='markers',
                    name='卖出信号',
                    marker=dict(color='#2ca02c', size=10, symbol='triangle-down'),
                    hovertemplate='卖出信号<br>时间: %{x}<br>价格: ¥%{y:.4f}<extra></extra>'
                )
            )

        # 配置布局
        fig.update_layout(
            title={
                'text': "📡 实时信号监控 (简化版)",
                'x': 0.5,
                'xanchor': 'center'
            },
            xaxis_title="时间",
            yaxis_title="价格 (¥)",
            height=400,
            showlegend=True,
            hovermode='x unified',
            template='plotly_white',
            margin=dict(l=50, r=50, t=80, b=50)
        )

        # 添加说明
        fig.add_annotation(
            text="注：信号强度图表已简化显示",
            xref="paper", yref="paper",
            x=0.02, y=0.98,
            showarrow=False,
            font=dict(size=10, color="gray"),
            align="left"
        )

        return fig

    except Exception as e:
        logger.error(f"创建简化信号图表失败: {e}")
        # 最后的备用方案
        return create_fallback_chart("信号监控图表", f"图表加载失败: {str(e)}")

def create_fallback_chart(title: str, message: str) -> go.Figure:
    """创建备用图表，当主图表失败时使用"""
    fig = go.Figure()

    fig.add_annotation(
        text=message,
        xref="paper", yref="paper",
        x=0.5, y=0.5,
        showarrow=False,
        font=dict(size=14, color="gray"),
        align="center"
    )

    fig.update_layout(
        title=title,
        height=300,
        showlegend=False,
        xaxis=dict(visible=False),
        yaxis=dict(visible=False),
        template='plotly_white'
    )

    return fig

def safe_plotly_chart(fig, container_width=True, key=None):
    """安全的Plotly图表显示函数"""
    try:
        st.plotly_chart(fig, use_container_width=container_width, key=key)
        return True
    except Exception as e:
        logger.error(f"Plotly图表显示失败: {e}")

        # 显示错误信息
        st.error("📊 图表显示失败")

        # 显示备用图表
        fallback_fig = create_fallback_chart(
            "图表加载失败",
            f"无法显示图表\n错误: {str(e)}\n\n请刷新页面重试"
        )

        try:
            st.plotly_chart(fallback_fig, use_container_width=container_width)
        except:
            st.warning("⚠️ 图表组件暂时不可用，请刷新页面")

        return False

def main():
    """主函数"""
    logger.info("🚀 增强版实时交易面板启动")
    logger.debug("开始初始化会话状态...")
    
    init_session_state()
    
    logger.debug("会话状态初始化完成")
    logger.info("开始渲染交易面板界面...")
    
    # 页面标题
    st.title("🚀 增强版实时交易")
    st.markdown("---")
    
    # 侧边栏控制面板
    with st.sidebar:
        st.header("🎛️ 控制面板")
        
        # 交易控制
        st.subheader("交易控制")
        
        # 获取当前状态
        current_status = get_enhanced_trading_status()
        is_running = current_status.get('is_running', False)
        
        # 交易标的选择
        symbol = st.selectbox(
            "选择交易标的",
            options=["159740", "159741", "159742"],
            index=0,
            disabled=is_running
        )
        
        # 策略选择
        st.subheader("策略选择")
        
        # 导入策略管理器
        try:
            from utils.trading_strategies import get_strategy_manager
            strategy_manager = get_strategy_manager()
            
            # 获取可用策略
            available_strategies = strategy_manager.list_strategies()
            strategy_options = list(available_strategies.keys())
            
            # 当前策略
            current_strategy_key = None
            if strategy_manager.current_strategy:
                for key, strategy in strategy_manager.strategies.items():
                    if strategy == strategy_manager.current_strategy:
                        current_strategy_key = key
                        break
            
            # 策略选择下拉框
            selected_strategy_key = st.selectbox(
                "选择交易策略",
                options=strategy_options,
                format_func=lambda x: available_strategies[x],
                index=strategy_options.index(current_strategy_key) if current_strategy_key in strategy_options else 0,
                disabled=is_running,
                help="选择用于生成交易信号的策略算法"
            )
            
            # 切换策略
            if selected_strategy_key != current_strategy_key and not is_running:
                if strategy_manager.set_current_strategy(selected_strategy_key):
                    # 🆕 同步交易器的策略管理器状态
                    try:
                        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import enhanced_trader
                        enhanced_trader.refresh_strategy_manager()
                        logger.info(f"交易器策略管理器已同步: {selected_strategy_key}")
                    except Exception as e:
                        logger.error(f"同步交易器策略管理器失败: {e}")

                    st.success(f"策略已切换为: {available_strategies[selected_strategy_key]}")
                    st.rerun()
            
            # 显示当前策略信息
            current_strategy = strategy_manager.get_current_strategy()
            if current_strategy:
                with st.expander("策略信息", expanded=False):
                    st.write(f"**策略名称**: {current_strategy.name}")
                    st.write(f"**策略描述**: {current_strategy.description}")
                    
                    # 显示策略参数
                    st.write("**策略参数**:")
                    for param_name, param_value in current_strategy.parameters.items():
                        if isinstance(param_value, float):
                            if param_value < 1:
                                st.write(f"- {param_name}: {param_value:.4f} ({param_value:.2%})")
                            else:
                                st.write(f"- {param_name}: {param_value:.2f}")
                        else:
                            st.write(f"- {param_name}: {param_value}")
            
        except ImportError as e:
            st.error(f"策略管理器加载失败: {e}")
            st.info("将使用默认策略")
        
        # 策略参数设置
        st.subheader("策略参数")
        
        with st.expander("参数设置", expanded=False):
            buy_trigger = st.slider(
                "买入触发阈值 (%)",
                min_value=-1.0, max_value=0.0, value=-0.2, step=0.01,
                disabled=is_running
            )
            
            profit_target = st.slider(
                "止盈目标 (%)",
                min_value=0.1, max_value=2.0, value=0.25, step=0.01,
                disabled=is_running
            )
            
            stop_loss = st.slider(
                "止损阈值 (%)",
                min_value=-5.0, max_value=-0.5, value=-2.0, step=0.1,
                disabled=is_running
            )
            
            max_hold_time = st.number_input(
                "最大持仓时间 (秒)",
                min_value=60, max_value=7200, value=3600, step=60,
                disabled=is_running
            )
            
            position_size = st.number_input(
                "单笔仓位大小 (¥)",
                min_value=10000, max_value=500000, value=100000, step=10000,
                disabled=is_running
            )
        
        # 交易按钮
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("🚀 启动交易", disabled=is_running, width='stretch'):
                logger.info(f"用户点击启动交易按钮 - 标的: {symbol}")
                
                # 准备策略参数
                params = {
                    'buy_trigger_drop': buy_trigger / 100,
                    'profit_target': profit_target / 100,
                    'stop_loss': stop_loss / 100,
                    'max_hold_time': max_hold_time,
                    'position_size': position_size,
                    'commission_rate': 0.0003
                }
                
                logger.info(f"策略参数配置: {params}")
                
                # 启动交易
                logger.info("开始启动增强版交易引擎...")
                if start_enhanced_trading(symbol, params):
                    logger.info(f"✅ 成功启动 {symbol} 实时交易")
                    st.success(f"✅ 已启动 {symbol} 实时交易")
                    st.rerun()
                else:
                    logger.error(f"❌ 启动 {symbol} 交易失败")
                    st.error("❌ 启动交易失败")
        
        with col2:
            if st.button("⏹️ 停止交易", disabled=not is_running, width='stretch'):
                logger.info("用户点击停止交易按钮")
                logger.info("开始停止增强版交易引擎...")
                
                if stop_enhanced_trading():
                    logger.info("✅ 交易引擎已成功停止")
                    st.success("✅ 交易已停止")
                    st.rerun()
                else:
                    logger.error("❌ 停止交易引擎失败")
                    st.error("❌ 停止交易失败")
        
        # 紧急操作
        st.subheader("紧急操作")
        
        if st.button("🚨 紧急平仓", disabled=not is_running, width='stretch'):
            logger.warning("🚨 用户触发紧急平仓操作")
            logger.warning("开始执行紧急平仓...")
            
            if close_all_enhanced_positions():
                logger.warning("✅ 紧急平仓操作执行成功")
                st.success("✅ 已执行紧急平仓")
                st.rerun()
            else:
                logger.error("❌ 紧急平仓操作执行失败")
                st.error("❌ 紧急平仓失败")
        
        # 自动刷新设置
        st.subheader("显示设置")
        
        auto_refresh = st.checkbox("自动刷新", value=st.session_state.auto_refresh)
        st.session_state.auto_refresh = auto_refresh
        
        if auto_refresh:
            refresh_interval = st.slider(
                "刷新间隔 (秒)",
                min_value=1, max_value=10, value=st.session_state.refresh_interval
            )
            st.session_state.refresh_interval = refresh_interval
    
    # 实时信号监控
    st.subheader("📡 实时信号监控")

    if is_running:
        try:
            # 生成实时信号数据
            with st.spinner("正在加载信号数据..."):
                signal_data = generate_signal_data(symbol)

            if signal_data is not None and not signal_data.empty:
                # 创建信号图表
                signal_fig = create_signal_chart(signal_data)

                # 使用安全的图表显示函数
                chart_success = safe_plotly_chart(
                    signal_fig,
                    container_width=True,
                    key=f"signal_chart_{symbol}"
                )

                # 如果图表显示失败，显示数据表格作为备选
                if not chart_success:
                    with st.expander("查看信号数据"):
                        st.dataframe(signal_data.tail(10))
            else:
                st.warning("📊 暂无信号数据，请稍后再试")

        except Exception as e:
            logger.error(f"实时信号监控失败: {e}")
            st.error(f"📊 信号监控加载失败: {str(e)}")

            # 显示简化的状态信息
            st.info("💡 提示：如果持续出现问题，请检查网络连接或联系技术支持")
    else:
        st.info("📊 启动交易后将显示实时信号监控图表")
    
    # 主面板内容
    # 获取最新状态
    logger.debug("开始获取增强版交易状态...")
    status = get_enhanced_trading_status()
    logger.debug(f"交易状态获取完成 - 运行状态: {status.get('is_running', False)}, 当前资金: {status.get('current_capital', 0)}")
    
    # 状态概览
    st.subheader("📊 交易状态概览")
    
    col1, col2, col3, col4, col5 = st.columns(5)
    
    with col1:
        st.markdown(
            create_metric_card(
                "系统状态",
                create_status_indicator(status.get('is_running', False))
            ),
            unsafe_allow_html=True
        )
    
    with col2:
        current_capital = status.get('current_capital', 1000000)
        initial_capital = 1000000
        capital_change = current_capital - initial_capital
        st.markdown(
            create_metric_card(
                "当前资金",
                format_currency(current_capital),
                f"变化: {format_currency(capital_change)}"
            ),
            unsafe_allow_html=True
        )
    
    with col3:
        today_pnl = status.get('today_pnl', 0)
        st.markdown(
            create_metric_card(
                "今日盈亏",
                f'<span class="{get_profit_class(today_pnl)}">{format_currency(today_pnl)}</span>',
                f"收益率: {format_percentage(today_pnl / 1000000)}"
            ),
            unsafe_allow_html=True
        )
    
    with col4:
        available_cash = status.get('available_cash', 1000000)
        market_value = status.get('market_value', 0)
        st.markdown(
            create_metric_card(
                "可用资金",
                format_currency(available_cash),
                f"持仓市值: {format_currency(market_value)}"
            ),
            unsafe_allow_html=True
        )
    
    with col5:
        stats = status.get('stats', {})
        win_rate = stats.get('win_rate', 0)
        total_trades = stats.get('total_trades', 0)
        st.markdown(
            create_metric_card(
                "胜率",
                format_percentage(win_rate),
                f"总交易: {total_trades}笔"
            ),
            unsafe_allow_html=True
        )
    
    # 风险状态提醒
    risk_status = status.get('risk_status', {})
    if risk_status.get('emergency_stop', False):
        st.markdown(
            '<div class="alert-box alert-danger">🚨 系统处于紧急停止状态</div>',
            unsafe_allow_html=True
        )
    
    daily_pnl = risk_status.get('daily_pnl', 0)
    if daily_pnl < -15000:
        st.markdown(
            f'<div class="alert-box alert-warning">⚠️ 日亏损较大: {format_currency(daily_pnl)}</div>',
            unsafe_allow_html=True
        )
    
    # 主要内容区域
    tab1, tab2, tab3, tab4, tab5 = st.tabs(["📈 实时监控", "💼 持仓管理", "📋 交易记录", "📊 性能分析", "🔍 调试日志"])
    
    with tab1:
        # 实时监控
        st.subheader("📈 实时市场监控")

        # 资金曲线图
        col1, col2 = st.columns([2, 1])

        with col1:
            try:
                with st.spinner("正在加载资金曲线图..."):
                    performance_chart = create_performance_chart(status)

                # 使用安全的图表显示函数
                chart_success = safe_plotly_chart(
                    performance_chart,
                    container_width=True,
                    key=f"performance_chart_{symbol}_{datetime.now().strftime('%H%M%S')}"
                )

                # 如果图表显示失败，显示简化的资金信息
                if not chart_success:
                    current_capital = status.get('current_capital', 1000000)
                    initial_capital = 1000000
                    capital_change = current_capital - initial_capital

                    st.metric(
                        label="当前资金",
                        value=format_currency(current_capital),
                        delta=format_currency(capital_change)
                    )

            except Exception as e:
                logger.error(f"实时市场监控失败: {e}")
                st.error(f"📈 市场监控加载失败: {str(e)}")
                st.info("💡 提示：如果持续出现问题，请检查数据连接或联系技术支持")

        with col2:
            # 最新信号
            st.subheader("最新信号")

            # 🆕 显示当前策略信息
            current_strategy = status.get('current_strategy')
            if current_strategy:
                st.markdown(f"""
                <div style="background-color: #f0f2f6; padding: 8px; border-radius: 4px; margin-bottom: 10px;">
                <small><strong>当前策略:</strong> {current_strategy['name']}</small>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown(f"""
                <div style="background-color: #fff3cd; padding: 8px; border-radius: 4px; margin-bottom: 10px;">
                <small><strong>当前策略:</strong> 内置策略（策略管理器不可用）</small>
                </div>
                """, unsafe_allow_html=True)

            last_signal = status.get('last_signal')

            if last_signal:
                signal_type = last_signal['signal_type']
                signal_color = "🟢" if signal_type == "BUY" else "🔴" if signal_type == "SELL" else "🟡"

                st.markdown(f"""
                **{signal_color} {signal_type}信号**

                - 标的: {last_signal['symbol']}
                - 价格: ¥{last_signal['price']:.4f}
                - 置信度: {last_signal['confidence']:.2%}
                - 原因: {last_signal['reason']}
                - 时间: {last_signal['timestamp'][:19] if isinstance(last_signal['timestamp'], str) else last_signal['timestamp'].strftime('%H:%M:%S')}
                """)
            else:
                st.info("暂无信号")
            
            # 今日统计
            st.subheader("今日统计")
            st.metric("信号数量", stats.get('signals_today', 0))
            st.metric("有效信号", stats.get('valid_signals_today', 0))
            
            last_signal_time = stats.get('last_signal_time')
            if last_signal_time:
                st.metric("最后信号", 
                         last_signal_time.strftime('%H:%M:%S') if hasattr(last_signal_time, 'strftime') else str(last_signal_time)[:19])
    
    with tab2:
        # 持仓管理
        st.subheader("当前持仓")
        
        positions = status.get('positions', [])
        
        if positions:
            # 持仓概览
            total_market_value = sum(pos['quantity'] * pos['current_price'] for pos in positions)
            total_unrealized_pnl = sum(pos['unrealized_pnl'] for pos in positions)
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("持仓数量", len(positions))
            with col2:
                st.metric("持仓市值", format_currency(total_market_value))
            with col3:
                st.metric("浮动盈亏", format_currency(total_unrealized_pnl))
            
            # 持仓详情
            for position in positions:
                st.markdown(create_position_card(position), unsafe_allow_html=True)
        else:
            st.info("当前无持仓")
    
    with tab3:
        # 交易记录
        st.subheader("交易记录")
        
        trades = status.get('today_trades', [])
        
        if trades:
            # 增强的交易统计（使用标准化处理）
            trade_stats = {
                'buy': [],
                'sell': [],
                'partial_sell': [],
                'stop_loss': [],
                'take_profit': [],
                'force_close': [],
                'invalid': []  # 无效交易
            }

            # 分类统计交易
            for trade in trades:
                try:
                    metrics = TradeProcessor.calculate_trade_metrics(trade)
                    action = metrics['action_normalized']

                    if action == TradeAction.BUY:
                        trade_stats['buy'].append(trade)
                    elif action == TradeAction.SELL:
                        trade_stats['sell'].append(trade)
                    elif action == TradeAction.PARTIAL_SELL:
                        trade_stats['partial_sell'].append(trade)
                    elif action == TradeAction.STOP_LOSS:
                        trade_stats['stop_loss'].append(trade)
                    elif action == TradeAction.TAKE_PROFIT:
                        trade_stats['take_profit'].append(trade)
                    elif action == TradeAction.FORCE_CLOSE:
                        trade_stats['force_close'].append(trade)
                    else:
                        trade_stats['invalid'].append(trade)

                except Exception as e:
                    logger.warning(f"交易统计处理失败: {e}")
                    trade_stats['invalid'].append(trade)

            # 计算盈亏统计
            sell_trades = (trade_stats['sell'] + trade_stats['partial_sell'] +
                          trade_stats['stop_loss'] + trade_stats['take_profit'] +
                          trade_stats['force_close'])
            total_pnl = sum(t.get('pnl', 0) for t in sell_trades)

            # 第一行统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("总交易", len(trades))
            with col2:
                st.metric("买入交易", len(trade_stats['buy']))
            with col3:
                st.metric("卖出交易", len(sell_trades))
            with col4:
                st.metric("已实现盈亏", format_currency(total_pnl))

            # 第二行详细统计
            if any(len(trades) > 0 for trades in [trade_stats['stop_loss'],
                                                 trade_stats['take_profit'],
                                                 trade_stats['partial_sell']]):
                st.markdown("**详细交易分类**")
                col1, col2, col3, col4 = st.columns(4)
                with col1:
                    if trade_stats['stop_loss']:
                        st.metric("🛑 止损", len(trade_stats['stop_loss']))
                with col2:
                    if trade_stats['take_profit']:
                        st.metric("💰 止盈", len(trade_stats['take_profit']))
                with col3:
                    if trade_stats['partial_sell']:
                        st.metric("🟡 部分卖出", len(trade_stats['partial_sell']))
                with col4:
                    if trade_stats['force_close']:
                        st.metric("⚠️ 强制平仓", len(trade_stats['force_close']))
            
            # 数据质量检查
            if trade_stats['invalid']:
                st.warning(f"⚠️ 发现 {len(trade_stats['invalid'])} 条无效交易记录，请检查数据质量")

                with st.expander("查看无效交易详情"):
                    for i, invalid_trade in enumerate(trade_stats['invalid']):
                        _, error_msg = TradeProcessor.validate_trade_record(invalid_trade)
                        st.error(f"交易 {i+1}: {error_msg}")
                        st.json(invalid_trade)

            # 交易记录表格
            trade_df = create_trade_record_table(trades)
            if not trade_df.empty:
                # 添加表格说明
                st.markdown("""
                **表格说明**：
                - 🟢 买入 | 🔴 卖出 | 🟡 部分卖出 | 🛑 止损 | 💰 止盈 | ⚠️ 强制平仓
                - **净金额**: 扣除所有费用后的实际现金流
                - **费用率**: 总费用占交易金额的比例
                - **现金影响**: 对账户现金的实际影响（买入为负，卖出为正）
                """)

                st.dataframe(
                    trade_df,
                    width='stretch',
                    hide_index=True,
                    column_config={
                        "操作": st.column_config.TextColumn("操作", width="small"),
                        "净金额": st.column_config.NumberColumn("净金额", format="¥%.2f"),
                        "费用率": st.column_config.NumberColumn("费用率", format="%.3f%%"),
                        "现金影响": st.column_config.NumberColumn("现金影响", format="¥%.2f")
                    }
                )
        else:
            st.info("今日暂无交易记录")
    
    with tab4:
        # 性能分析
        st.subheader("性能分析")

        trades_data = status.get('today_trades', [])
        trade_analysis = analyze_trade_patterns(trades_data)

        # 第一行：图表和基础指标
        col1, col2 = st.columns(2)

        with col1:
            # 盈亏分布
            pnl_chart = create_pnl_distribution_chart(trades_data)
            st.plotly_chart(pnl_chart, width='stretch')

        with col2:
            # 关键指标
            st.subheader("关键指标")

            stats = status.get('stats', {})

            st.metric("总交易次数", stats.get('total_trades', 0))
            st.metric("胜率", format_percentage(stats.get('win_rate', 0)))
            st.metric("总盈亏", format_currency(stats.get('total_pnl', 0)))

            # 风险指标
            risk_status = status.get('risk_status', {})
            st.metric("日盈亏", format_currency(risk_status.get('daily_pnl', 0)))
            st.metric("最大回撤", format_percentage(risk_status.get('max_drawdown', 0)))

        # 第二行：交易模式分析
        if trade_analysis and trade_analysis.get('total_trades', 0) > 0:
            st.subheader("📊 交易模式分析")

            col1, col2, col3 = st.columns(3)

            with col1:
                st.markdown("**交易动作分布**")
                action_dist = trade_analysis.get('action_distribution', {})
                for action, count in action_dist.items():
                    display_info = TradeActionDisplay.get_display_info(action)
                    percentage = (count / trade_analysis['total_trades']) * 100
                    st.write(f"{display_info['icon']} {display_info['text']}: {count} ({percentage:.1f}%)")

            with col2:
                st.markdown("**各动作成功率**")
                success_rates = trade_analysis.get('success_rates', {})
                for action, rate in success_rates.items():
                    display_info = TradeActionDisplay.get_display_info(action)
                    color = "🟢" if rate > 0.6 else "🟡" if rate > 0.4 else "🔴"
                    st.write(f"{color} {display_info['text']}: {rate:.1%}")

            with col3:
                st.markdown("**盈亏统计**")
                pnl_by_action = trade_analysis.get('pnl_by_action', {})
                for action, pnl_list in pnl_by_action.items():
                    if pnl_list:
                        display_info = TradeActionDisplay.get_display_info(action)
                        avg_pnl = sum(pnl_list) / len(pnl_list)
                        st.write(f"{display_info['icon']} {display_info['text']}: {format_currency(avg_pnl)}")

            # 交易频率分析
            trade_freq = trade_analysis.get('trade_frequency', {})
            if trade_freq.get('trades_per_hour'):
                st.markdown("**交易频率**")
                st.write(f"⏱️ 平均每小时交易: {trade_freq['trades_per_hour']:.1f} 次")
    
    with tab5:
        # 调试日志
        st.subheader("🔍 系统调试日志")
        
        # 日志控制面板
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            log_level_filter = st.selectbox(
                "日志级别过滤",
                options=["ALL", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                index=1  # 默认显示INFO及以上
            )
        
        with col2:
            log_count = st.number_input(
                "显示条数",
                min_value=10, max_value=500, value=100, step=10
            )
        
        with col3:
            if st.button("🔄 刷新日志"):
                logger.info("用户手动刷新日志")
                st.rerun()
        
        with col4:
            if st.button("🗑️ 清空日志"):
                st.session_state.log_handler.clear_logs()
                logger.info("日志已清空")
                st.rerun()
        
        # 获取日志记录
        logs = st.session_state.log_handler.get_recent_logs(
            count=log_count, 
            level_filter=log_level_filter
        )
        
        if logs:
            # 日志统计
            log_stats = {}
            for log in logs:
                level = log['level']
                log_stats[level] = log_stats.get(level, 0) + 1
            
            # 显示日志统计
            st.subheader("📊 日志统计")
            stat_cols = st.columns(len(log_stats))
            for i, (level, count) in enumerate(log_stats.items()):
                with stat_cols[i]:
                    color = {
                        'DEBUG': '🔍',
                        'INFO': 'ℹ️',
                        'WARNING': '⚠️',
                        'ERROR': '❌',
                        'CRITICAL': '🚨'
                    }.get(level, '📝')
                    st.metric(f"{color} {level}", count)
            
            # 日志详情表格
            st.subheader("📋 日志详情")
            
            # 创建日志DataFrame
            log_data = []
            for log in reversed(logs):  # 最新的在前面
                log_data.append({
                    '时间': log['timestamp'].strftime('%H:%M:%S.%f')[:-3],
                    '级别': log['level'],
                    '模块': log['module'],
                    '函数': log['function'],
                    '行号': log['line'],
                    '消息': log['message']
                })
            
            log_df = pd.DataFrame(log_data)
            
            # 根据日志级别设置颜色
            def highlight_log_level(row):
                level = row['级别']
                if level == 'ERROR' or level == 'CRITICAL':
                    return ['background-color: #ffebee'] * len(row)
                elif level == 'WARNING':
                    return ['background-color: #fff3e0'] * len(row)
                elif level == 'INFO':
                    return ['background-color: #e8f5e8'] * len(row)
                else:
                    return [''] * len(row)
            
            # 显示日志表格
            st.dataframe(
                log_df.style.apply(highlight_log_level, axis=1),
                width='stretch',
                hide_index=True,
                height=400
            )
            
            # 实时日志流
            if st.checkbox("📡 实时日志流", value=False):
                st.subheader("📡 实时日志流")
                
                # 创建一个容器用于显示最新日志
                log_container = st.empty()
                
                # 显示最近5条日志
                recent_logs = logs[-5:] if len(logs) >= 5 else logs
                log_text = ""
                
                for log in reversed(recent_logs):
                    timestamp = log['timestamp'].strftime('%H:%M:%S')
                    level_icon = {
                        'DEBUG': '🔍',
                        'INFO': 'ℹ️',
                        'WARNING': '⚠️',
                        'ERROR': '❌',
                        'CRITICAL': '🚨'
                    }.get(log['level'], '📝')
                    
                    log_text += f"{timestamp} {level_icon} [{log['module']}.{log['function']}:{log['line']}] {log['message']}\n"
                
                log_container.code(log_text, language="text")
        else:
            st.info("暂无日志记录")
            
        # 系统状态监控
        st.subheader("🖥️ 系统状态监控")
        
        # 添加系统状态日志
        logger.debug(f"当前系统状态检查 - 运行状态: {status.get('is_running', False)}")
        logger.debug(f"当前资金: {status.get('current_capital', 0)}")
        logger.debug(f"持仓数量: {len(status.get('positions', []))}")
        logger.debug(f"今日交易数: {len(status.get('today_trades', []))}")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("日志处理器状态", "✅ 正常")
            st.metric("日志记录数", len(st.session_state.log_handler.log_records))
        
        with col2:
            st.metric("交易引擎状态", "✅ 正常" if status.get('is_running') else "⏹️ 停止")
            st.metric("数据连接状态", "✅ 正常")  # 这里可以添加实际的连接检查
        
        with col3:
            st.metric("风险系统状态", "✅ 正常")
            st.metric("最后日志时间",
                     logs[-1]['timestamp'].strftime('%H:%M:%S') if logs else "无")

        # 🆕 A股规则合规性监控
        st.subheader("🏛️ A股规则合规性监控")

        # 获取A股规则统计（如果可用）
        a_stock_stats = status.get('a_stock_stats', {})

        if a_stock_stats:
            col_a1, col_a2, col_a3, col_a4 = st.columns(4)

            with col_a1:
                data_checks = a_stock_stats.get('data_quality_checks', 0)
                data_failures = a_stock_stats.get('data_quality_failures', 0)
                data_success_rate = (data_checks - data_failures) / max(data_checks, 1) * 100

                st.metric(
                    "数据质量检查",
                    f"{data_success_rate:.1f}%",
                    delta=f"{data_checks}次检查"
                )

            with col_a2:
                rule_violations = a_stock_stats.get('trading_rule_violations', 0)
                normalizations = a_stock_stats.get('quantity_normalizations', 0)

                st.metric(
                    "交易规则违规",
                    f"{rule_violations}次",
                    delta=f"{normalizations}次标准化"
                )

            with col_a3:
                price_limit_hits = a_stock_stats.get('price_limit_hits', 0)
                time_restrictions = a_stock_stats.get('time_restrictions', 0)

                st.metric(
                    "价格限制触发",
                    f"{price_limit_hits}次",
                    delta=f"{time_restrictions}次时间限制"
                )

            with col_a4:
                system_checks = a_stock_stats.get('system_health_checks', 0)
                system_failures = a_stock_stats.get('system_health_failures', 0)
                system_health_rate = (system_checks - system_failures) / max(system_checks, 1) * 100

                st.metric(
                    "系统健康度",
                    f"{system_health_rate:.1f}%",
                    delta=f"{system_checks}次检查"
                )

            # 合规性状态指示器
            total_violations = rule_violations + data_failures + system_failures
            if total_violations == 0:
                compliance_status = "🟢 优秀"
                compliance_color = "#2ca02c"
            elif total_violations <= 5:
                compliance_status = "🟡 良好"
                compliance_color = "#ff7f0e"
            else:
                compliance_status = "🔴 需关注"
                compliance_color = "#d62728"

            st.markdown(f"""
            **实时合规性状态:** <span style="color: {compliance_color}; font-weight: bold;">{compliance_status}</span>
            """, unsafe_allow_html=True)

            # 详细统计信息（可折叠）
            with st.expander("📊 详细合规性统计", expanded=False):
                st.json(a_stock_stats)
        else:
            st.info("A股规则统计数据暂不可用，请确保交易引擎已启动并集成了A股规则管理器")

    # 自动刷新逻辑
    if st.session_state.auto_refresh and status.get('is_running', False):
        logger.debug(f"自动刷新触发 - 间隔: {st.session_state.refresh_interval}秒")
        time.sleep(st.session_state.refresh_interval)
        logger.debug("执行页面自动刷新")
        st.rerun()
    
    # 页面底部信息
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.caption(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
    
    with col2:
        if status.get('start_time'):
            start_time = status['start_time']
            if isinstance(start_time, str):
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            running_time = datetime.now() - start_time
            st.caption(f"运行时间: {str(running_time).split('.')[0]}")
    
    with col3:
        st.caption("增强版实时交易系统 v2.0")

if __name__ == "__main__":
    main()