#!/usr/bin/env python3
"""
交易策略模块
包含多种交易策略的实现
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
from abc import ABC, abstractmethod
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class SignalResult:
    """信号结果"""
    signal: float  # 信号强度 (-1到1)
    confidence: float  # 置信度 (0到1)
    reason: str  # 信号原因
    timestamp: datetime
    
@dataclass
class StrategyConfig:
    """策略配置"""
    name: str
    description: str
    parameters: Dict[str, Any]

class TradingStrategy(ABC):
    """交易策略基类"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        self.description = config.description
        self.parameters = config.parameters
        
    @abstractmethod
    def generate_signal(self, data: pd.DataFrame) -> SignalResult:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        pass
    
    def validate_data(self, data: pd.DataFrame) -> bool:
        """验证数据有效性"""
        if data is None or data.empty:
            return False
        required_columns = ['price', 'timestamp']
        return all(col in data.columns for col in required_columns)

class EnhancedRealTimeStrategy(TradingStrategy):
    """增强版实时交易策略（当前使用的策略）"""
    
    def __init__(self, config: Optional[StrategyConfig] = None):
        if config is None:
            config = StrategyConfig(
                name="增强版实时策略",
                description="基于移动平均线交叉、价格变化率和动量指标的多层次策略",
                parameters=self.get_default_parameters()
            )
        super().__init__(config)
    
    def get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'short_window': 3,      # 短期均线窗口
            'long_window': 10,      # 长期均线窗口
            'price_change_threshold': 0.005,  # 价格变化阈值 0.5%
            'momentum_threshold': 0.002,      # 动量阈值 0.2%
            'ma_cross_threshold': 0.001,      # 均线交叉阈值 0.1%
            'buy_trigger_drop': -0.002,       # 买入触发跌幅 -0.2%
            'profit_target': 0.0025,          # 止盈目标 0.25%
            'stop_loss': -0.02,               # 止损阈值 -2%
            'max_hold_time': 3600,            # 最大持仓时间（秒）
        }
    
    def generate_signal(self, data: pd.DataFrame) -> SignalResult:
        """生成交易信号"""
        try:
            if not self.validate_data(data):
                return SignalResult(0, 0, "数据无效", datetime.now())
            
            if len(data) < self.parameters['long_window']:
                return SignalResult(0, 0, "数据不足", datetime.now())
            
            # 获取最新价格数据
            prices = data['price'].values
            latest_price = prices[-1]
            
            # 计算移动平均线
            short_window = self.parameters['short_window']
            long_window = self.parameters['long_window']
            
            short_ma = np.mean(prices[-short_window:])
            long_ma = np.mean(prices[-long_window:])
            
            # 计算价格变化率
            if len(prices) >= 2:
                price_change = (latest_price - prices[-2]) / prices[-2]
            else:
                price_change = 0
            
            # 计算动量（5期价格变化）
            if len(prices) >= 6:
                momentum = (latest_price - prices[-6]) / prices[-6]
            else:
                momentum = 0
            
            # 信号判断逻辑
            signal = 0
            confidence = 0
            reason = "无信号"
            
            ma_cross_threshold = self.parameters['ma_cross_threshold']
            price_threshold = self.parameters['price_change_threshold']
            momentum_threshold = self.parameters['momentum_threshold']
            
            # 买入信号条件
            if (short_ma > long_ma * (1 + ma_cross_threshold) and 
                price_change > price_threshold and 
                momentum > momentum_threshold):
                signal = 1
                confidence = min(0.9, abs(price_change) * 100 + abs(momentum) * 50)
                reason = f"买入信号: 短期均线上穿({short_ma:.4f}>{long_ma:.4f}), 价格上涨{price_change:.2%}, 正动量{momentum:.2%}"
            
            # 卖出信号条件
            elif (short_ma < long_ma * (1 - ma_cross_threshold) and 
                  price_change < -price_threshold and 
                  momentum < -momentum_threshold):
                signal = -1
                confidence = min(0.9, abs(price_change) * 100 + abs(momentum) * 50)
                reason = f"卖出信号: 短期均线下穿({short_ma:.4f}<{long_ma:.4f}), 价格下跌{price_change:.2%}, 负动量{momentum:.2%}"
            
            # 弱信号检测
            elif abs(price_change) > price_threshold * 0.5:
                signal = np.sign(price_change) * 0.3
                confidence = abs(price_change) * 50
                reason = f"弱信号: 价格变化{price_change:.2%}"
            
            return SignalResult(signal, confidence, reason, datetime.now())
            
        except Exception as e:
            logger.error(f"增强版实时策略信号生成错误: {e}")
            return SignalResult(0, 0, f"计算错误: {e}", datetime.now())

class BacktestStrategy(TradingStrategy):
    """回测策略（基于最高价回撤）"""
    
    def __init__(self, config: Optional[StrategyConfig] = None):
        if config is None:
            config = StrategyConfig(
                name="回测策略",
                description="基于最高价回撤的经典套利策略，适用于历史数据回测",
                parameters=self.get_default_parameters()
            )
        super().__init__(config)
    
    def get_default_parameters(self) -> Dict[str, Any]:
        """获取默认参数"""
        return {
            'signal_window': 20,        # 信号计算窗口
            'buy_trigger': -0.002,      # 买入触发阈值 -0.2%
            'profit_target': 0.0025,    # 止盈目标 0.25%
            'stop_loss': -0.02,         # 止损阈值 -2%
            'lookback_period': 100,     # 回看周期
        }
    
    def generate_signal(self, data: pd.DataFrame) -> SignalResult:
        """生成交易信号（基于最高价回撤）"""
        try:
            if not self.validate_data(data):
                return SignalResult(0, 0, "数据无效", datetime.now())
            
            signal_window = self.parameters['signal_window']
            if len(data) < signal_window:
                return SignalResult(0, 0, "数据不足", datetime.now())
            
            # 获取最新的signal_window个数据点
            window_data = data.tail(signal_window)
            prices = window_data['price'].values
            
            # 计算最高价和最新价
            max_price = np.max(prices)
            latest_price = prices[-1]
            
            # 计算回撤信号
            if max_price > 0:
                signal = (latest_price - max_price) / max_price
            else:
                signal = 0
            
            # 计算置信度（基于回撤幅度）
            confidence = min(0.9, abs(signal) * 100)
            
            # 判断信号类型
            buy_trigger = self.parameters['buy_trigger']
            
            if signal <= buy_trigger:
                reason = f"买入信号: 价格回撤{signal:.2%}，触发阈值{buy_trigger:.2%}"
                signal_strength = -1  # 买入信号
            elif signal > 0:
                reason = f"价格上涨{signal:.2%}，高于最高价"
                signal_strength = 0.5  # 弱卖出信号
            else:
                reason = f"价格回撤{signal:.2%}，未达触发阈值"
                signal_strength = 0  # 无信号
            
            return SignalResult(signal_strength, confidence, reason, datetime.now())
            
        except Exception as e:
            logger.error(f"回测策略信号生成错误: {e}")
            return SignalResult(0, 0, f"计算错误: {e}", datetime.now())

class StrategyManager:
    """策略管理器"""
    
    def __init__(self):
        self.strategies = {}
        self.current_strategy = None
        self._register_default_strategies()
    
    def _register_default_strategies(self):
        """注册默认策略"""
        # 注册增强版实时策略
        enhanced_strategy = EnhancedRealTimeStrategy()
        self.register_strategy("enhanced_realtime", enhanced_strategy)
        
        # 注册回测策略
        backtest_strategy = BacktestStrategy()
        self.register_strategy("backtest", backtest_strategy)
        
        # 设置默认策略
        self.current_strategy = enhanced_strategy
    
    def register_strategy(self, key: str, strategy: TradingStrategy):
        """注册策略"""
        self.strategies[key] = strategy
        logger.info(f"策略已注册: {key} - {strategy.name}")
    
    def get_strategy(self, key: str) -> Optional[TradingStrategy]:
        """获取策略"""
        return self.strategies.get(key)
    
    def set_current_strategy(self, key: str) -> bool:
        """设置当前策略"""
        strategy = self.get_strategy(key)
        if strategy:
            self.current_strategy = strategy
            logger.info(f"当前策略已切换为: {strategy.name}")
            return True
        return False
    
    def get_current_strategy(self) -> Optional[TradingStrategy]:
        """获取当前策略"""
        return self.current_strategy
    
    def list_strategies(self) -> Dict[str, str]:
        """列出所有策略"""
        return {key: strategy.name for key, strategy in self.strategies.items()}
    
    def generate_signal(self, data: pd.DataFrame) -> SignalResult:
        """使用当前策略生成信号"""
        if self.current_strategy:
            return self.current_strategy.generate_signal(data)
        else:
            return SignalResult(0, 0, "无可用策略", datetime.now())

# 全局策略管理器实例
strategy_manager = StrategyManager()

def get_strategy_manager() -> StrategyManager:
    """获取策略管理器实例"""
    return strategy_manager