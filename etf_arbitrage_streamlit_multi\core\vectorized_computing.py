#!/usr/bin/env python3
"""
向量化计算优化模块
使用NumPy、Pandas和Numba实现高性能计算
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
import time
from dataclasses import dataclass
from functools import wraps
import threading
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp

# 尝试导入Numba进行JIT编译
try:
    from numba import jit, njit, prange, types
    from numba.typed import Dict as NumbaDict, List as NumbaList
    NUMBA_AVAILABLE = True
except ImportError:
    # 如果没有numba，创建空装饰器
    def njit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator if args else decorator
    
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator if args else decorator
    
    def prange(x):
        return range(x)
    
    NUMBA_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class ComputePerformanceMetrics:
    """计算性能指标"""
    operation_name: str
    execution_time: float
    data_size: int
    speedup_ratio: float
    memory_usage_mb: float
    cpu_cores_used: int
    timestamp: datetime

class PerformanceProfiler:
    """性能分析器"""
    
    def __init__(self):
        self.metrics = []
        self._lock = threading.Lock()
    
    def profile(self, operation_name: str):
        """性能分析装饰器"""
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                start_memory = self._get_memory_usage()
                
                result = func(*args, **kwargs)
                
                end_time = time.time()
                end_memory = self._get_memory_usage()
                
                execution_time = end_time - start_time
                memory_usage = max(0, end_memory - start_memory)
                
                # 估算数据大小
                data_size = self._estimate_data_size(args, kwargs)
                
                metric = ComputePerformanceMetrics(
                    operation_name=operation_name,
                    execution_time=execution_time,
                    data_size=data_size,
                    speedup_ratio=1.0,  # 基准值
                    memory_usage_mb=memory_usage,
                    cpu_cores_used=1,
                    timestamp=datetime.now()
                )
                
                with self._lock:
                    self.metrics.append(metric)
                    if len(self.metrics) > 1000:  # 保持最近1000条记录
                        self.metrics = self.metrics[-1000:]
                
                return result
            return wrapper
        return decorator
    
    def _get_memory_usage(self) -> float:
        """获取内存使用量（MB）"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except ImportError:
            return 0.0
    
    def _estimate_data_size(self, args, kwargs) -> int:
        """估算数据大小"""
        total_size = 0
        
        for arg in args:
            if isinstance(arg, (pd.DataFrame, pd.Series)):
                total_size += len(arg)
            elif isinstance(arg, np.ndarray):
                total_size += arg.size
            elif isinstance(arg, (list, tuple)):
                total_size += len(arg)
        
        return total_size
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            if not self.metrics:
                return {}
            
            # 按操作分组统计
            operations = {}
            for metric in self.metrics:
                op_name = metric.operation_name
                if op_name not in operations:
                    operations[op_name] = []
                operations[op_name].append(metric)
            
            summary = {}
            for op_name, op_metrics in operations.items():
                execution_times = [m.execution_time for m in op_metrics]
                data_sizes = [m.data_size for m in op_metrics]
                
                summary[op_name] = {
                    'count': len(op_metrics),
                    'avg_execution_time': np.mean(execution_times),
                    'min_execution_time': np.min(execution_times),
                    'max_execution_time': np.max(execution_times),
                    'avg_data_size': np.mean(data_sizes),
                    'total_data_processed': np.sum(data_sizes),
                    'throughput_ops_per_sec': len(op_metrics) / np.sum(execution_times) if np.sum(execution_times) > 0 else 0
                }
            
            return summary

# 全局性能分析器
profiler = PerformanceProfiler()

# Numba优化的技术指标计算函数
if NUMBA_AVAILABLE:
    @njit
    def _numba_sma(prices: np.ndarray, window: int) -> np.ndarray:
        """Numba优化的简单移动平均"""
        n = len(prices)
        sma = np.empty(n)
        sma[:window-1] = np.nan
        
        for i in prange(window-1, n):
            sma[i] = np.mean(prices[i-window+1:i+1])
        
        return sma
    
    @njit
    def _numba_ema(prices: np.ndarray, alpha: float) -> np.ndarray:
        """Numba优化的指数移动平均"""
        n = len(prices)
        ema = np.empty(n)
        ema[0] = prices[0]
        
        for i in prange(1, n):
            ema[i] = alpha * prices[i] + (1 - alpha) * ema[i-1]
        
        return ema
    
    @njit
    def _numba_rsi(prices: np.ndarray, window: int = 14) -> np.ndarray:
        """Numba优化的RSI计算"""
        n = len(prices)
        rsi = np.empty(n)
        rsi[:window] = np.nan
        
        # 计算价格变化
        deltas = np.diff(prices)
        
        for i in prange(window, n):
            gains = deltas[i-window:i]
            gains = gains[gains > 0]
            losses = -deltas[i-window:i]
            losses = losses[losses > 0]
            
            avg_gain = np.mean(gains) if len(gains) > 0 else 0
            avg_loss = np.mean(losses) if len(losses) > 0 else 0
            
            if avg_loss == 0:
                rsi[i] = 100
            else:
                rs = avg_gain / avg_loss
                rsi[i] = 100 - (100 / (1 + rs))
        
        return rsi
    
    @njit
    def _numba_bollinger_bands(prices: np.ndarray, window: int = 20, 
                              num_std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Numba优化的布林带计算"""
        n = len(prices)
        middle = np.empty(n)
        upper = np.empty(n)
        lower = np.empty(n)
        
        middle[:window-1] = np.nan
        upper[:window-1] = np.nan
        lower[:window-1] = np.nan
        
        for i in prange(window-1, n):
            window_data = prices[i-window+1:i+1]
            mean_val = np.mean(window_data)
            std_val = np.std(window_data)
            
            middle[i] = mean_val
            upper[i] = mean_val + num_std * std_val
            lower[i] = mean_val - num_std * std_val
        
        return middle, upper, lower

class VectorizedComputing:
    """向量化计算引擎"""
    
    def __init__(self, use_numba: bool = NUMBA_AVAILABLE, max_workers: int = None):
        self.use_numba = use_numba and NUMBA_AVAILABLE
        self.max_workers = max_workers or min(8, mp.cpu_count())
        
        logger.info(f"向量化计算引擎初始化: Numba={'启用' if self.use_numba else '禁用'}, "
                   f"最大工作线程: {self.max_workers}")
    
    @profiler.profile("technical_indicators")
    def calculate_technical_indicators(self, df: pd.DataFrame, 
                                     indicators: List[str] = None) -> pd.DataFrame:
        """批量计算技术指标"""
        if df.empty or 'price' not in df.columns:
            return df
        
        result_df = df.copy()
        prices = df['price'].values
        
        # 默认指标
        if indicators is None:
            indicators = ['sma_20', 'ema_12', 'rsi_14', 'bollinger']
        
        # 并行计算指标
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = {}
            
            for indicator in indicators:
                if indicator.startswith('sma_'):
                    window = int(indicator.split('_')[1])
                    futures[indicator] = executor.submit(self._calculate_sma, prices, window)
                
                elif indicator.startswith('ema_'):
                    window = int(indicator.split('_')[1])
                    futures[indicator] = executor.submit(self._calculate_ema, prices, window)
                
                elif indicator.startswith('rsi_'):
                    window = int(indicator.split('_')[1])
                    futures[indicator] = executor.submit(self._calculate_rsi, prices, window)
                
                elif indicator == 'bollinger':
                    futures[indicator] = executor.submit(self._calculate_bollinger, prices)
            
            # 收集结果
            for indicator, future in futures.items():
                try:
                    result = future.result(timeout=30)
                    if isinstance(result, tuple):
                        # 布林带返回三个值
                        result_df[f'{indicator}_middle'] = result[0]
                        result_df[f'{indicator}_upper'] = result[1]
                        result_df[f'{indicator}_lower'] = result[2]
                    else:
                        result_df[indicator] = result
                except Exception as e:
                    logger.error(f"计算指标 {indicator} 失败: {e}")
        
        return result_df
    
    def _calculate_sma(self, prices: np.ndarray, window: int) -> np.ndarray:
        """计算简单移动平均"""
        if self.use_numba:
            return _numba_sma(prices, window)
        else:
            return pd.Series(prices).rolling(window=window).mean().values
    
    def _calculate_ema(self, prices: np.ndarray, window: int) -> np.ndarray:
        """计算指数移动平均"""
        if self.use_numba:
            alpha = 2.0 / (window + 1)
            return _numba_ema(prices, alpha)
        else:
            return pd.Series(prices).ewm(span=window).mean().values
    
    def _calculate_rsi(self, prices: np.ndarray, window: int) -> np.ndarray:
        """计算RSI"""
        if self.use_numba:
            return _numba_rsi(prices, window)
        else:
            delta = pd.Series(prices).diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
            rs = gain / loss
            return (100 - (100 / (1 + rs))).values
    
    def _calculate_bollinger(self, prices: np.ndarray, window: int = 20, 
                           num_std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """计算布林带"""
        if self.use_numba:
            return _numba_bollinger_bands(prices, window, num_std)
        else:
            series = pd.Series(prices)
            middle = series.rolling(window=window).mean()
            std = series.rolling(window=window).std()
            upper = middle + (std * num_std)
            lower = middle - (std * num_std)
            return middle.values, upper.values, lower.values
    
    @profiler.profile("signal_generation")
    def generate_signals_vectorized(self, df: pd.DataFrame, 
                                  strategy_params: Dict) -> pd.DataFrame:
        """向量化信号生成"""
        if df.empty:
            return df
        
        result_df = df.copy()
        
        # 确保有必要的技术指标
        if 'sma_20' not in df.columns:
            result_df = self.calculate_technical_indicators(result_df)
        
        # 向量化信号计算
        prices = result_df['price'].values
        sma_20 = result_df.get('sma_20', np.zeros_like(prices)).values
        
        # 买入信号：价格跌破SMA20一定比例
        buy_threshold = strategy_params.get('buy_trigger_drop', -0.002)
        price_vs_sma = (prices - sma_20) / sma_20
        buy_signals = price_vs_sma < buy_threshold
        
        # 卖出信号：价格涨幅达到目标
        sell_threshold = strategy_params.get('profit_target', 0.0025)
        # 这里需要基于持仓成本计算，简化处理
        sell_signals = price_vs_sma > sell_threshold
        
        result_df['buy_signal'] = buy_signals
        result_df['sell_signal'] = sell_signals
        result_df['signal_strength'] = np.abs(price_vs_sma)
        
        return result_df
    
    @profiler.profile("batch_backtest")
    def batch_backtest_vectorized(self, price_data: pd.DataFrame, 
                                parameter_sets: List[Dict]) -> pd.DataFrame:
        """批量向量化回测"""
        results = []
        
        # 并行处理多个参数集
        with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            for i, params in enumerate(parameter_sets):
                future = executor.submit(
                    self._single_backtest_vectorized, 
                    price_data, 
                    params, 
                    f"config_{i}"
                )
                futures.append(future)
            
            # 收集结果
            for future in futures:
                try:
                    result = future.result(timeout=60)
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"批量回测失败: {e}")
        
        return pd.DataFrame(results) if results else pd.DataFrame()
    
    def _single_backtest_vectorized(self, price_data: pd.DataFrame, 
                                  params: Dict, config_name: str) -> Dict:
        """单个参数集的向量化回测"""
        try:
            # 生成信号
            signals_df = self.generate_signals_vectorized(price_data, params)
            
            # 向量化回测计算
            prices = signals_df['price'].values
            buy_signals = signals_df['buy_signal'].values
            sell_signals = signals_df['sell_signal'].values
            
            # 简化的向量化回测逻辑
            positions = np.zeros_like(prices)
            cash = params.get('initial_capital', 1000000)
            portfolio_value = np.zeros_like(prices)
            
            current_position = 0
            current_cash = cash
            
            for i in range(len(prices)):
                if buy_signals[i] and current_position == 0:
                    # 买入
                    position_size = params.get('position_size', 100000)
                    shares = int(position_size / prices[i])
                    cost = shares * prices[i]
                    
                    if cost <= current_cash:
                        current_position = shares
                        current_cash -= cost
                
                elif sell_signals[i] and current_position > 0:
                    # 卖出
                    current_cash += current_position * prices[i]
                    current_position = 0
                
                positions[i] = current_position
                portfolio_value[i] = current_cash + current_position * prices[i]
            
            # 计算性能指标
            total_return = (portfolio_value[-1] - cash) / cash
            max_drawdown = np.min((portfolio_value - np.maximum.accumulate(portfolio_value)) / np.maximum.accumulate(portfolio_value))
            
            return {
                'config_name': config_name,
                'total_return': total_return,
                'max_drawdown': max_drawdown,
                'final_value': portfolio_value[-1],
                'parameters': params
            }
            
        except Exception as e:
            logger.error(f"向量化回测失败 {config_name}: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'use_numba': self.use_numba,
            'max_workers': self.max_workers,
            'performance_summary': profiler.get_performance_summary()
        }

# 全局向量化计算引擎
_vectorized_computer = None
_computer_lock = threading.Lock()

def get_vectorized_computer() -> VectorizedComputing:
    """获取向量化计算引擎单例"""
    global _vectorized_computer
    
    if _vectorized_computer is None:
        with _computer_lock:
            if _vectorized_computer is None:
                _vectorized_computer = VectorizedComputing()
    
    return _vectorized_computer
