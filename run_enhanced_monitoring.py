#!/usr/bin/env python3
"""
启动增强版监控面板
提供便捷的启动脚本和配置选项
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'streamlit',
        'psutil', 
        'pandas',
        'plotly',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_files():
    """检查必要文件"""
    required_files = [
        'monitoring/enhanced_monitoring_dashboard.py',
        'monitoring/real_data_integration.py',
        'monitoring/alert_rules_engine.py'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def init_databases():
    """初始化数据库"""
    try:
        from monitoring.enhanced_monitoring_dashboard import EnhancedMonitoringDashboard
        
        print("🔧 初始化监控数据库...")
        dashboard = EnhancedMonitoringDashboard()
        print("✅ 数据库初始化完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库初始化失败: {e}")
        return False

def start_monitoring_dashboard(port=8504, host="localhost"):
    """启动监控面板"""
    try:
        dashboard_file = "monitoring/enhanced_monitoring_dashboard.py"
        
        print(f"🚀 启动增强版监控面板...")
        print(f"📊 访问地址: http://{host}:{port}")
        print("💡 按 Ctrl+C 停止服务")
        
        # 启动streamlit应用
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            dashboard_file,
            "--server.address", host,
            "--server.port", str(port),
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ]
        
        process = subprocess.run(cmd)
        return process.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏹️ 监控面板已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def run_health_check():
    """运行系统健康检查"""
    try:
        from monitoring.enhanced_monitoring_dashboard import EnhancedMonitoringDashboard
        
        print("🔍 运行系统健康检查...")
        
        dashboard = EnhancedMonitoringDashboard()
        
        # 收集性能数据
        perf_data = dashboard.collect_real_performance_data()
        print(f"📊 CPU使用率: {perf_data.get('cpu_percent', 0):.1f}%")
        print(f"💾 内存使用率: {perf_data.get('memory_percent', 0):.1f}%")
        print(f"💿 磁盘使用率: {perf_data.get('disk_percent', 0):.1f}%")
        
        # 获取健康评分
        health_score = dashboard.get_system_health_score()
        print(f"🎯 系统健康评分: {health_score['overall_score']:.0f}/100 ({health_score['health_status']})")
        
        # 检查告警
        alerts = dashboard.get_active_alerts()
        if not alerts.empty:
            print(f"🚨 活跃告警: {len(alerts)} 个")
        else:
            print("✅ 无活跃告警")
        
        print("✅ 健康检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

def generate_report():
    """生成监控报告"""
    try:
        from monitoring.enhanced_monitoring_dashboard import EnhancedMonitoringDashboard
        from datetime import datetime
        
        print("📊 生成监控报告...")
        
        dashboard = EnhancedMonitoringDashboard()
        report = dashboard.generate_monitoring_report(24)
        
        # 保存报告
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"monitoring_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"✅ 报告已生成: {report_file}")
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def optimize_thresholds():
    """优化告警阈值"""
    try:
        from monitoring.alert_rules_engine import optimize_alert_thresholds
        
        print("🔧 优化告警阈值...")
        optimize_alert_thresholds(30)  # 基于30天数据
        print("✅ 阈值优化完成")
        return True
        
    except Exception as e:
        print(f"❌ 阈值优化失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强版监控面板启动器")
    
    parser.add_argument("--port", type=int, default=8504, help="服务端口 (默认: 8504)")
    parser.add_argument("--host", default="localhost", help="服务地址 (默认: localhost)")
    parser.add_argument("--check", action="store_true", help="运行系统检查")
    parser.add_argument("--health", action="store_true", help="运行健康检查")
    parser.add_argument("--report", action="store_true", help="生成监控报告")
    parser.add_argument("--optimize", action="store_true", help="优化告警阈值")
    parser.add_argument("--init", action="store_true", help="初始化数据库")
    
    args = parser.parse_args()
    
    print("🎯 ETF套利系统 - 增强版监控面板")
    print("=" * 50)
    
    # 系统检查
    if args.check or not any([args.health, args.report, args.optimize, args.init]):
        print("🔍 运行系统检查...")
        
        if not check_dependencies():
            sys.exit(1)
        
        if not check_files():
            sys.exit(1)
        
        print("✅ 系统检查通过")
        print("-" * 30)
    
    # 初始化数据库
    if args.init:
        if not init_databases():
            sys.exit(1)
        return
    
    # 健康检查
    if args.health:
        if not run_health_check():
            sys.exit(1)
        return
    
    # 生成报告
    if args.report:
        if not generate_report():
            sys.exit(1)
        return
    
    # 优化阈值
    if args.optimize:
        if not optimize_thresholds():
            sys.exit(1)
        return
    
    # 启动监控面板
    if not any([args.health, args.report, args.optimize, args.init]):
        # 初始化数据库
        if not init_databases():
            sys.exit(1)
        
        # 启动面板
        if not start_monitoring_dashboard(args.port, args.host):
            sys.exit(1)

if __name__ == "__main__":
    main()