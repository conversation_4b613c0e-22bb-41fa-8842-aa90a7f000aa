@echo off
echo ========================================
echo    ETF套利交易系统 - 启动脚本
echo ========================================
echo.

echo 正在启动主控制面板...
echo 请稍候，系统将在浏览器中打开...
echo.

echo 💡 启动后可用功能：
echo    - 数据采集模块
echo    - 回测分析模块  
echo    - 实时交易模块
echo    - 系统管理模块
echo.

echo 🌐 访问地址: http://localhost:8501
echo.

REM 激活conda环境并启动主控制面板
call conda activate castock
streamlit run main_control_panel.py

pause
