#!/usr/bin/env python3
"""
数据采集集成模块
将原项目的数据采集功能集成到多页面系统中
"""

import asyncio
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Tuple
from pathlib import Path
import requests
import json
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加父目录到路径
current_dir = Path(__file__).parent.parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

try:
    # 导入原项目的数据采集功能
    from fetch_ticks_eastmoney import api_poll_loop, market_of, secid_prefix_for_market, fetch_details_api, parse_detail_to_row, insert_ticks
    REAL_DATA_AVAILABLE = True
except ImportError:
    REAL_DATA_AVAILABLE = False

from utils.database import db_manager

logger = logging.getLogger(__name__)

class DataCollector:
    """数据采集管理器"""
    
    def __init__(self):
        self.is_running = False
        self.collector_threads = {}  # 存储每个symbol的线程
        self.stop_events = {}  # 存储每个symbol的停止事件
        self.status_callbacks = []
        self.last_update = None
        self.error_count = 0
        self.total_collected = 0
        self.symbol_stats = {}  # 存储每个股票的统计信息
        self.executor = ThreadPoolExecutor(max_workers=10)  # 线程池
    
    def update_status(self, status: str):
        """更新数据采集状态"""
        self.status = status
        self._notify_status_change({'status': status})
        
    def add_status_callback(self, callback: Callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
    
    def _notify_status_change(self, status: Dict):
        """通知状态变化"""
        # 首先更新到数据库（线程安全）
        try:
            db_manager.update_system_status('data_collection', status)
        except Exception as e:
            logger.error(f"更新数据库状态失败: {e}")
        
        # 然后通知回调函数
        for callback in self.status_callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")
    
    def start_collection(self, symbols: List[str], interval: int = 5, batch_size: int = 100, use_real_data: bool = True) -> bool:
        """启动数据采集（支持多股票）"""
        logger.info(f"=== DataCollector.start_collection 被调用 ===")
        logger.info(f"参数 - symbols: {symbols}, interval: {interval}, batch_size: {batch_size}, use_real_data: {use_real_data}")
        
        if self.is_running:
            logger.warning("数据采集已在运行中")
            return False
        
        try:
            # 如果传入单个symbol，转换为列表
            if isinstance(symbols, str):
                symbols = [symbols]
                logger.info(f"转换单个symbol为列表: {symbols}")
            
            logger.info(f"开始启动数据采集，股票列表: {symbols}")
            
            self.is_running = True
            self.error_count = 0
            self.symbol_stats = {}
            
            # 为每个股票创建停止事件和初始化统计
            for symbol in symbols:
                logger.info(f"为股票 {symbol} 创建停止事件和初始化统计")
                self.stop_events[symbol] = threading.Event()
                self.symbol_stats[symbol] = {
                    'total_collected': 0,
                    'total_fetched': 0,
                    'total_duplicates': 0,
                    'error_count': 0,
                    'last_update': None,
                    'consecutive_failures': 0
                }
            
            # 启动每个股票的采集线程
            for symbol in symbols:
                logger.info(f"为股票 {symbol} 启动采集线程")
                if use_real_data and REAL_DATA_AVAILABLE:
                    # 使用真实数据采集
                    logger.info(f"使用真实数据采集模式: {symbol}")
                    thread = threading.Thread(
                        target=self._real_data_collection_loop,
                        args=(symbol, interval, batch_size),
                        name=f"DataCollector-{symbol}",
                        daemon=True
                    )
                else:
                    # 使用模拟数据采集
                    logger.info(f"使用模拟数据采集模式: {symbol}")
                    thread = threading.Thread(
                        target=self._simulated_data_collection_loop,
                        args=(symbol, interval, batch_size),
                        name=f"SimDataCollector-{symbol}",
                        daemon=True
                    )
                
                self.collector_threads[symbol] = thread
                logger.info(f"启动线程: {thread.name}")
                thread.start()
                logger.info(f"线程 {thread.name} 已启动，状态: {thread.is_alive()}")
            
            logger.info(f"开始采集数据: {symbols}, 间隔: {interval}秒")
            
            status_update = {
                'is_running': True,
                'symbols': symbols,
                'interval': interval,
                'start_time': datetime.now().isoformat(),
                'mode': 'real' if use_real_data and REAL_DATA_AVAILABLE else 'simulated'
            }
            logger.info(f"准备通知状态变化: {status_update}")
            
            self._notify_status_change(status_update)
            logger.info("状态变化通知完成")
            
            logger.info("数据采集启动成功，返回True")
            return True
            
        except Exception as e:
            logger.error(f"启动数据采集失败: {e}", exc_info=True)
            self.is_running = False
            return False
    
    def stop_collection(self) -> bool:
        """停止数据采集"""
        if not self.is_running:
            return True
        
        try:
            # 设置所有停止事件
            for stop_event in self.stop_events.values():
                stop_event.set()
            
            # 等待所有线程结束
            for symbol, thread in self.collector_threads.items():
                if thread and thread.is_alive():
                    thread.join(timeout=10)
                    if thread.is_alive():
                        logger.warning(f"线程 {symbol} 未能正常结束")
            
            self.is_running = False
            
            # 计算总体统计
            total_collected = sum(stats['total_collected'] for stats in self.symbol_stats.values())
            total_fetched = sum(stats['total_fetched'] for stats in self.symbol_stats.values())
            total_duplicates = sum(stats['total_duplicates'] for stats in self.symbol_stats.values())
            total_errors = sum(stats['error_count'] for stats in self.symbol_stats.values())
            
            logger.info(f"数据采集已停止 - 总计: 获取{total_fetched}条, 新增{total_collected}条, 重复{total_duplicates}条, 错误{total_errors}次")
            
            self._notify_status_change({
                'is_running': False,
                'stop_time': datetime.now().isoformat(),
                'total_collected': total_collected,
                'total_fetched': total_fetched,
                'total_duplicates': total_duplicates,
                'error_count': total_errors,
                'symbol_stats': self.symbol_stats.copy()
            })
            
            # 清理资源
            self.collector_threads.clear()
            self.stop_events.clear()
            
            return True
            
        except Exception as e:
            logger.error(f"停止数据采集失败: {e}")
            return False
    
    def _real_data_collection_loop(self, symbol: str, interval: int, batch_size: int):
        """真实数据采集循环（单个股票）"""
        try:
            # 使用原项目的API轮询功能
            logger.info(f"启动真实数据采集: {symbol}")
            
            stop_event = self.stop_events[symbol]
            stats = self.symbol_stats[symbol]
            
            while not stop_event.is_set():
                try:
                    # 调用原项目的API轮询函数
                    if REAL_DATA_AVAILABLE:
                        # 直接使用改进后的数据获取函数
                        success, inserted, duplicates = self._fetch_real_data(symbol, batch_size)
                        
                        if success:
                            stats['total_fetched'] += (inserted + duplicates)
                            stats['total_collected'] += inserted
                            stats['total_duplicates'] += duplicates
                            stats['last_update'] = datetime.now().isoformat()
                            stats['consecutive_failures'] = 0  # 重置失败计数
                            
                            # 更新全局统计
                            self.total_collected = sum(s['total_collected'] for s in self.symbol_stats.values())
                            self.last_update = stats['last_update']
                            
                            # 通知状态更新
                            self._notify_status_change({
                                'last_update': self.last_update,
                                'total_collected': self.total_collected,
                                'symbol_stats': self.symbol_stats.copy(),
                                'current_symbol': symbol,
                                'symbol_update': {
                                    'symbol': symbol,
                                    'inserted': inserted,
                                    'duplicates': duplicates,
                                    'last_update': stats['last_update']
                                }
                            })
                        else:
                            stats['consecutive_failures'] += 1
                            stats['error_count'] += 1
                            self.error_count = sum(s['error_count'] for s in self.symbol_stats.values())
                            
                            # 如果连续失败太多，增加等待时间，但不要停止整个采集
                            if stats['consecutive_failures'] > 10:
                                logger.warning(f"{symbol} 连续失败 {stats['consecutive_failures']} 次，延长等待时间")
                                stop_event.wait(interval * 3)
                                continue
                            elif stats['consecutive_failures'] > 20:
                                logger.error(f"{symbol} 连续失败过多({stats['consecutive_failures']}次)，暂停该股票采集5分钟")
                                stop_event.wait(300)  # 暂停5分钟
                                continue
                    else:
                        stats['consecutive_failures'] += 1
                        stats['error_count'] += 1
                        
                except Exception as e:
                    logger.error(f"{symbol} 数据采集错误: {e}")
                    stats['consecutive_failures'] += 1
                    stats['error_count'] += 1
                
                # 等待下次采集
                stop_event.wait(interval)
                
        except Exception as e:
            logger.error(f"{symbol} 数据采集循环异常: {e}")
        finally:
            logger.info(f"{symbol} 数据采集结束: 获取 {stats['total_fetched']} 条, 新增 {stats['total_collected']} 条, 重复 {stats['total_duplicates']} 条")
    
    def _simulated_data_collection_loop(self, symbol: str, interval: int, batch_size: int):
        """模拟数据采集循环（单个股票）"""
        try:
            logger.info(f"启动模拟数据采集: {symbol}")
            
            # 为不同股票设置不同的基础价格
            base_prices = {
                '159740': 2.5,   # 纳指ETF
                '159915': 2.8,   # 创业板ETF
                '159919': 4.2,   # 沪深300ETF
                '510300': 4.5,   # 沪深300ETF
                '510500': 7.1,   # 中证500ETF
            }
            base_price = base_prices.get(symbol, 2.5)
            
            stop_event = self.stop_events[symbol]
            stats = self.symbol_stats[symbol]
            
            while not stop_event.is_set():
                try:
                    # 生成模拟tick数据
                    current_time = datetime.now()
                    
                    for i in range(min(batch_size, 10)):  # 每次生成少量数据
                        if stop_event.is_set():
                            break
                        
                        # 模拟价格波动（不同股票有不同的波动率）
                        import random
                        volatility = 0.01 if symbol.startswith('510') else 0.015  # 沪市ETF波动率稍小
                        price_change = random.uniform(-volatility, volatility)
                        price = base_price * (1 + price_change)
                        volume = random.randint(100, 10000)
                        
                        tick_time = current_time + timedelta(seconds=i)
                        
                        # 插入模拟数据
                        success = db_manager.insert_tick_data(
                            symbol=symbol,
                            tick_time=tick_time.isoformat(),
                            price=price,
                            volume=volume,
                            side=random.choice(['买', '卖']),
                            raw=json.dumps({'simulated': True, 'symbol': symbol, 'price': price, 'volume': volume})
                        )
                        
                        if success:
                            stats['total_collected'] += 1
                            stats['total_fetched'] += 1
                    
                    stats['last_update'] = datetime.now().isoformat()
                    
                    # 更新全局统计
                    self.total_collected = sum(s['total_collected'] for s in self.symbol_stats.values())
                    self.last_update = stats['last_update']
                    
                    # 通知状态更新
                    self._notify_status_change({
                        'last_update': self.last_update,
                        'total_collected': self.total_collected,
                        'symbol_stats': self.symbol_stats.copy(),
                        'current_symbol': symbol,
                        'mode': 'simulated'
                    })
                    
                except Exception as e:
                    logger.error(f"{symbol} 模拟数据生成错误: {e}")
                    stats['error_count'] += 1
                
                # 等待下次采集
                stop_event.wait(interval)
                
        except Exception as e:
            logger.error(f"{symbol} 模拟数据采集循环异常: {e}")
        finally:
            logger.info(f"{symbol} 模拟数据采集结束: 生成 {stats['total_collected']} 条数据")
    
    def _fetch_real_data(self, symbol: str, count: int = 100) -> Tuple[bool, int, int]:
        """获取真实数据
        
        Returns:
            Tuple[bool, int, int]: (success, inserted_count, duplicate_count)
        """
        try:
            # 使用 fetch_ticks_eastmoney.py 中的函数获取真实的tick数据
            if REAL_DATA_AVAILABLE:
                # 添加重试机制和更好的错误处理
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        details, server_ts = fetch_details_api(symbol, count=count)
                        if not details:
                            if attempt < max_retries - 1:
                                logger.warning(f"{symbol} 第{attempt+1}次获取数据为空，重试...")
                                time.sleep(1)  # 短暂等待后重试
                                continue
                            else:
                                logger.warning(f"{symbol} 获取数据为空，跳过本次采集")
                                return False, 0, 0
                        
                        rows_to_insert = []
                        for detail in details:
                            try:
                                row = parse_detail_to_row(symbol, detail, server_date=server_ts)
                                if row:
                                    rows_to_insert.append(row)
                            except Exception as parse_e:
                                logger.warning(f"{symbol} 解析数据行失败: {parse_e}")
                                continue
                        
                        if rows_to_insert:
                            # 使用数据库管理器的批量插入功能来获取准确的插入统计
                            inserted_count = db_manager.batch_insert_ticks(rows_to_insert)
                            duplicate_count = len(rows_to_insert) - inserted_count
                            
                            # 记录统计信息
                            if duplicate_count > 0:
                                logger.debug(f"{symbol} 数据采集: 新增 {inserted_count} 条, 重复忽略 {duplicate_count} 条")
                            else:
                                logger.debug(f"{symbol} 数据采集: 新增 {inserted_count} 条")
                            
                            return True, inserted_count, duplicate_count
                        else:
                            logger.warning(f"{symbol} 解析后无有效数据")
                            return False, 0, 0
                            
                    except requests.exceptions.RequestException as req_e:
                        logger.warning(f"{symbol} 网络请求失败 (尝试 {attempt+1}/{max_retries}): {req_e}")
                        if attempt < max_retries - 1:
                            time.sleep(2 ** attempt)  # 指数退避
                            continue
                        else:
                            return False, 0, 0
                    except Exception as api_e:
                        logger.error(f"{symbol} API调用失败 (尝试 {attempt+1}/{max_retries}): {api_e}")
                        if attempt < max_retries - 1:
                            time.sleep(1)
                            continue
                        else:
                            return False, 0, 0
            
            return False, 0, 0
            
        except Exception as e:
            logger.error(f"{symbol} 获取真实数据失败: {e}")
            return False, 0, 0
    
    def get_status(self) -> Dict:
        """获取采集状态"""
        # 从数据库获取持久化状态
        db_status = {}
        try:
            db_status = db_manager.get_system_status('data_collection')
        except Exception as e:
            logger.error(f"从数据库获取状态失败: {e}")
        
        # 获取实时统计数据
        from utils.database import get_data_stats
        stats = {}
        try:
            stats = get_data_stats()  # 获取全局统计
        except Exception:
            pass
        
        # 计算汇总统计
        if self.symbol_stats:
            total_fetched = sum(s['total_fetched'] for s in self.symbol_stats.values())
            total_duplicates = sum(s['total_duplicates'] for s in self.symbol_stats.values())
            total_errors = sum(s['error_count'] for s in self.symbol_stats.values())
            
            # 获取当前运行的股票列表
            running_symbols = list(self.symbol_stats.keys()) if self.is_running else []
        else:
            total_fetched = db_status.get('total_fetched', 0)
            total_duplicates = db_status.get('total_duplicates', 0)
            total_errors = db_status.get('error_count', 0)
            running_symbols = db_status.get('symbols', [])
        
        # 合并状态信息
        status = {
            'is_running': self.is_running,
            'last_update': self.last_update or db_status.get('last_update'),
            'total_collected': self.total_collected or db_status.get('total_collected', 0),
            'total_fetched': total_fetched,
            'total_duplicates': total_duplicates,
            'error_count': total_errors,
            'real_data_available': REAL_DATA_AVAILABLE,
            'actual_records': stats.get('total_records', 0) if 'total_records' in stats else None,
            'db_updated_at': db_status.get('db_updated_at'),
            'symbols': running_symbols,
            'symbol_stats': self.symbol_stats.copy() if self.symbol_stats else {},
            'start_time': db_status.get('start_time'),
            'mode': db_status.get('mode', 'unknown')
        }
        
        return status
    
    def get_recent_data(self, symbol: str, limit: int = 100) -> Dict:
        """获取最近的数据"""
        try:
            df = db_manager.get_tick_data(symbol, limit=limit)
            
            if df.empty:
                return {
                    'success': False,
                    'message': '暂无数据',
                    'data': [],
                    'count': 0
                }
            
            # 转换为前端可用格式
            data = []
            for _, row in df.iterrows():
                data.append({
                    'time': row['tick_time'],
                    'price': row['price'],
                    'volume': row['volume'],
                    'side': row.get('side', ''),
                })
            
            return {
                'success': True,
                'data': data,
                'count': len(data),
                'latest_price': data[0]['price'] if data else None,
                'latest_time': data[0]['time'] if data else None
            }
            
        except Exception as e:
            logger.error(f"获取最近数据失败: {e}")
            return {
                'success': False,
                'message': f'获取数据失败: {e}',
                'data': [],
                'count': 0
            }

# 全局数据采集器实例
data_collector = DataCollector()

# 便捷函数
def start_data_collection(symbols, **kwargs) -> bool:
    """启动数据采集的便捷函数（支持多股票）"""
    # 确保symbols是列表
    if isinstance(symbols, str):
        symbols = [symbols]
    return data_collector.start_collection(symbols, **kwargs)

def stop_data_collection() -> bool:
    """停止数据采集的便捷函数"""
    return data_collector.stop_collection()

def get_collection_status() -> Dict:
    """获取采集状态的便捷函数"""
    return data_collector.get_status()

def get_recent_data(symbol: str, limit: int = 100) -> Dict:
    """获取最近数据的便捷函数"""
    return data_collector.get_recent_data(symbol, limit)