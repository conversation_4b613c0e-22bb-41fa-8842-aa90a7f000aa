"""
统一异常处理器
提供统一的异常处理、错误记录和恢复机制
"""

import logging
import traceback
import functools
from typing import Dict, Any, Optional, Callable, Type, Union
from datetime import datetime
from enum import Enum
import threading

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """错误分类"""
    DATA_ERROR = "data_error"
    TRADING_ERROR = "trading_error"
    NETWORK_ERROR = "network_error"
    DATABASE_ERROR = "database_error"
    VALIDATION_ERROR = "validation_error"
    SYSTEM_ERROR = "system_error"
    USER_ERROR = "user_error"

class ErrorRecord:
    """错误记录"""
    
    def __init__(self, exception: Exception, category: ErrorCategory, 
                 severity: ErrorSeverity, context: Dict[str, Any] = None):
        self.timestamp = datetime.now()
        self.exception = exception
        self.exception_type = type(exception).__name__
        self.message = str(exception)
        self.category = category
        self.severity = severity
        self.context = context or {}
        self.traceback = traceback.format_exc()
        self.thread_id = threading.get_ident()
        self.resolved = False
        self.resolution_notes = ""

class ExceptionHandler:
    """统一异常处理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._error_records: Dict[str, ErrorRecord] = {}
        self._error_handlers: Dict[Type[Exception], Callable] = {}
        self._recovery_strategies: Dict[ErrorCategory, Callable] = {}
        self._notification_callbacks: list = []
        
        # 设置日志器
        self.logger = logging.getLogger('exception_handler')
        
        # 注册默认处理器
        self._register_default_handlers()
    
    def _register_default_handlers(self):
        """注册默认异常处理器"""
        
        # 数据库错误处理
        self.register_handler(
            Exception,  # 通用异常
            self._handle_database_error,
            ErrorCategory.DATABASE_ERROR
        )
        
        # 网络错误处理
        self.register_handler(
            ConnectionError,
            self._handle_network_error,
            ErrorCategory.NETWORK_ERROR
        )
        
        # 值错误处理
        self.register_handler(
            ValueError,
            self._handle_validation_error,
            ErrorCategory.VALIDATION_ERROR
        )
    
    def register_handler(self, exception_type: Type[Exception], 
                        handler: Callable, category: ErrorCategory):
        """注册异常处理器"""
        self._error_handlers[exception_type] = {
            'handler': handler,
            'category': category
        }
    
    def register_recovery_strategy(self, category: ErrorCategory, strategy: Callable):
        """注册恢复策略"""
        self._recovery_strategies[category] = strategy
    
    def add_notification_callback(self, callback: Callable):
        """添加通知回调"""
        self._notification_callbacks.append(callback)
    
    def handle_exception(self, exception: Exception, context: Dict[str, Any] = None,
                        severity: ErrorSeverity = ErrorSeverity.MEDIUM) -> Optional[Any]:
        """处理异常"""
        
        # 确定错误分类
        category = self._categorize_exception(exception)
        
        # 创建错误记录
        error_record = ErrorRecord(exception, category, severity, context)
        error_id = f"{error_record.timestamp.strftime('%Y%m%d_%H%M%S')}_{error_record.thread_id}"
        self._error_records[error_id] = error_record
        
        # 记录日志
        self._log_error(error_record, error_id)
        
        # 查找并执行处理器
        handler_info = self._find_handler(type(exception))
        if handler_info:
            try:
                result = handler_info['handler'](exception, context)
                self.logger.info(f"异常处理完成: {error_id}")
                return result
            except Exception as handler_error:
                self.logger.error(f"异常处理器失败: {handler_error}")
        
        # 尝试恢复策略
        if category in self._recovery_strategies:
            try:
                recovery_result = self._recovery_strategies[category](exception, context)
                self.logger.info(f"恢复策略执行完成: {error_id}")
                return recovery_result
            except Exception as recovery_error:
                self.logger.error(f"恢复策略失败: {recovery_error}")
        
        # 发送通知
        self._notify_error(error_record, error_id)
        
        # 对于严重错误，重新抛出
        if severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            raise exception
        
        return None
    
    def _categorize_exception(self, exception: Exception) -> ErrorCategory:
        """对异常进行分类"""
        exception_type = type(exception)
        exception_message = str(exception).lower()
        
        # 根据异常类型分类
        if issubclass(exception_type, (ConnectionError, TimeoutError)):
            return ErrorCategory.NETWORK_ERROR
        elif issubclass(exception_type, (ValueError, TypeError)):
            return ErrorCategory.VALIDATION_ERROR
        elif 'database' in exception_message or 'sql' in exception_message:
            return ErrorCategory.DATABASE_ERROR
        elif 'trading' in exception_message or 'order' in exception_message:
            return ErrorCategory.TRADING_ERROR
        elif 'data' in exception_message:
            return ErrorCategory.DATA_ERROR
        else:
            return ErrorCategory.SYSTEM_ERROR
    
    def _find_handler(self, exception_type: Type[Exception]) -> Optional[Dict]:
        """查找异常处理器"""
        # 精确匹配
        if exception_type in self._error_handlers:
            return self._error_handlers[exception_type]
        
        # 继承匹配
        for registered_type, handler_info in self._error_handlers.items():
            if issubclass(exception_type, registered_type):
                return handler_info
        
        return None
    
    def _log_error(self, error_record: ErrorRecord, error_id: str):
        """记录错误日志"""
        log_message = (
            f"异常处理 [{error_id}] - "
            f"类型: {error_record.exception_type}, "
            f"分类: {error_record.category.value}, "
            f"严重程度: {error_record.severity.value}, "
            f"消息: {error_record.message}"
        )
        
        if error_record.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_message)
        elif error_record.severity == ErrorSeverity.HIGH:
            self.logger.error(log_message)
        elif error_record.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # 记录详细信息到DEBUG级别
        self.logger.debug(f"异常详情 [{error_id}]:\n{error_record.traceback}")
        if error_record.context:
            self.logger.debug(f"异常上下文 [{error_id}]: {error_record.context}")
    
    def _notify_error(self, error_record: ErrorRecord, error_id: str):
        """发送错误通知"""
        for callback in self._notification_callbacks:
            try:
                callback(error_record, error_id)
            except Exception as e:
                self.logger.error(f"错误通知回调失败: {e}")
    
    def _handle_database_error(self, exception: Exception, context: Dict[str, Any] = None):
        """处理数据库错误"""
        self.logger.warning(f"数据库错误处理: {exception}")
        
        # 可以在这里实现数据库重连逻辑
        # 例如：重新初始化数据库连接
        
        return {"status": "handled", "action": "database_reconnect"}
    
    def _handle_network_error(self, exception: Exception, context: Dict[str, Any] = None):
        """处理网络错误"""
        self.logger.warning(f"网络错误处理: {exception}")
        
        # 可以在这里实现重试逻辑
        # 例如：等待一段时间后重试
        
        return {"status": "handled", "action": "retry_later"}
    
    def _handle_validation_error(self, exception: Exception, context: Dict[str, Any] = None):
        """处理验证错误"""
        self.logger.warning(f"验证错误处理: {exception}")
        
        # 可以在这里实现数据清理或默认值设置
        
        return {"status": "handled", "action": "use_default_value"}
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计"""
        total_errors = len(self._error_records)
        
        if total_errors == 0:
            return {"total_errors": 0}
        
        # 按分类统计
        category_stats = {}
        for record in self._error_records.values():
            category = record.category.value
            category_stats[category] = category_stats.get(category, 0) + 1
        
        # 按严重程度统计
        severity_stats = {}
        for record in self._error_records.values():
            severity = record.severity.value
            severity_stats[severity] = severity_stats.get(severity, 0) + 1
        
        # 最近错误
        recent_errors = sorted(
            self._error_records.values(),
            key=lambda x: x.timestamp,
            reverse=True
        )[:5]
        
        return {
            "total_errors": total_errors,
            "category_distribution": category_stats,
            "severity_distribution": severity_stats,
            "recent_errors": [
                {
                    "timestamp": error.timestamp.isoformat(),
                    "type": error.exception_type,
                    "category": error.category.value,
                    "severity": error.severity.value,
                    "message": error.message[:100]  # 截断长消息
                }
                for error in recent_errors
            ]
        }
    
    def clear_old_errors(self, days_to_keep: int = 7):
        """清理旧错误记录"""
        cutoff_time = datetime.now().timestamp() - (days_to_keep * 24 * 3600)
        
        old_error_ids = [
            error_id for error_id, record in self._error_records.items()
            if record.timestamp.timestamp() < cutoff_time
        ]
        
        for error_id in old_error_ids:
            del self._error_records[error_id]
        
        self.logger.info(f"清理了 {len(old_error_ids)} 条旧错误记录")
        return len(old_error_ids)

# 全局异常处理器实例
exception_handler = ExceptionHandler()

# 装饰器函数
def handle_exceptions(category: ErrorCategory = ErrorCategory.SYSTEM_ERROR,
                     severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                     reraise: bool = False):
    """异常处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                context = {
                    'function': func.__name__,
                    'args': str(args)[:200],  # 限制长度
                    'kwargs': str(kwargs)[:200]
                }
                
                result = exception_handler.handle_exception(e, context, severity)
                
                if reraise:
                    raise e
                
                return result
        return wrapper
    return decorator
