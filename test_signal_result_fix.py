#!/usr/bin/env python3
"""
测试SignalResult属性修复
验证SignalResult到TradeSignal的转换是否正确
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def test_signal_result_fix():
    """测试SignalResult属性修复"""
    print("🔧 SignalResult属性修复测试")
    print("=" * 50)
    
    try:
        # 1. 测试SignalResult结构
        print("📋 测试1: SignalResult结构")
        try:
            from etf_arbitrage_streamlit_multi.utils.trading_strategies import SignalResult
            from datetime import datetime
            
            # 创建测试SignalResult
            test_signal_result = SignalResult(
                signal=0.5,  # 买入信号
                confidence=0.8,
                reason="测试买入信号",
                timestamp=datetime.now()
            )
            
            print(f"✅ SignalResult创建成功")
            print(f"   signal: {test_signal_result.signal}")
            print(f"   confidence: {test_signal_result.confidence}")
            print(f"   reason: {test_signal_result.reason}")
            
            # 验证属性
            if hasattr(test_signal_result, 'signal'):
                print("✅ SignalResult有signal属性（信号强度）")
            else:
                print("❌ SignalResult缺少signal属性")
                return False
                
            if hasattr(test_signal_result, 'signal_type'):
                print("⚠️ SignalResult有signal_type属性（可能冗余）")
            else:
                print("✅ SignalResult正确使用signal属性而非signal_type")
                
        except Exception as e:
            print(f"❌ SignalResult结构测试失败: {e}")
            return False
        
        # 2. 测试信号强度到信号类型的转换逻辑
        print("\n📋 测试2: 信号强度到信号类型转换")
        try:
            # 测试不同信号强度的转换
            test_cases = [
                (0.5, 'BUY', "强买入信号"),
                (0.2, 'BUY', "弱买入信号"),
                (0.05, 'HOLD', "无信号（低于阈值）"),
                (-0.2, 'SELL', "弱卖出信号"),
                (-0.5, 'SELL', "强卖出信号"),
                (0.0, 'HOLD', "中性信号")
            ]
            
            for signal_strength, expected_type, description in test_cases:
                # 模拟转换逻辑
                if signal_strength > 0.1:
                    signal_type = 'BUY'
                elif signal_strength < -0.1:
                    signal_type = 'SELL'
                else:
                    signal_type = 'HOLD'
                
                if signal_type == expected_type:
                    print(f"✅ {description}: {signal_strength} -> {signal_type}")
                else:
                    print(f"❌ {description}: {signal_strength} -> {signal_type} (期望: {expected_type})")
                    return False
                    
        except Exception as e:
            print(f"❌ 信号转换逻辑测试失败: {e}")
            return False
        
        # 3. 测试策略生成SignalResult
        print("\n📋 测试3: 策略生成SignalResult")
        try:
            from etf_arbitrage_streamlit_multi.utils.trading_strategies import get_strategy_manager
            import pandas as pd
            import numpy as np
            from datetime import datetime
            
            strategy_manager = get_strategy_manager()
            current_strategy = strategy_manager.get_current_strategy()
            
            # 创建测试数据
            test_data = pd.DataFrame({
                'timestamp': pd.date_range('2025-09-24 09:30:00', periods=20, freq='1min'),
                'price': np.random.normal(0.75, 0.01, 20),
                'volume': np.random.randint(1000, 5000, 20)
            })
            
            # 生成信号
            signal_result = current_strategy.generate_signal(test_data)
            
            print(f"✅ 策略生成SignalResult成功")
            print(f"   signal: {signal_result.signal}")
            print(f"   confidence: {signal_result.confidence}")
            print(f"   reason: {signal_result.reason}")
            
            # 验证SignalResult属性
            if hasattr(signal_result, 'signal') and not hasattr(signal_result, 'signal_type'):
                print("✅ SignalResult使用正确的属性结构")
            else:
                print("❌ SignalResult属性结构不正确")
                return False
                
        except Exception as e:
            print(f"❌ 策略生成SignalResult测试失败: {e}")
            return False
        
        # 4. 测试实时交易器信号转换
        print("\n📋 测试4: 实时交易器信号转换")
        try:
            from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import enhanced_trader
            from datetime import datetime
            
            # 模拟tick数据
            test_tick_data = {
                'symbol': '159740',
                'price': 0.75,
                'volume': 1000,
                'timestamp': datetime.now()
            }
            
            # 测试信号生成（这里主要测试不会出现属性错误）
            try:
                signal = enhanced_trader._generate_signal(test_tick_data)
                print(f"✅ 实时交易器信号生成无错误")
                
                if signal:
                    print(f"   生成信号类型: {signal.signal_type}")
                    print(f"   信号原因: {signal.reason}")
                    print(f"   信号强度: {signal.signal_strength}")
                else:
                    print("   无信号生成（正常情况）")
                    
            except AttributeError as e:
                if 'signal_type' in str(e):
                    print(f"❌ 仍存在signal_type属性错误: {e}")
                    return False
                else:
                    print(f"⚠️ 其他属性错误: {e}")
            except Exception as e:
                print(f"⚠️ 其他错误（可能正常）: {e}")
                
        except Exception as e:
            print(f"❌ 实时交易器信号转换测试失败: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 SignalResult属性修复测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_signal_result_fix()
    if success:
        print("\n✅ 修复验证成功：SignalResult属性问题已解决")
        print("现在策略管理器生成的信号可以正确转换为TradeSignal")
    else:
        print("\n❌ 修复验证失败：仍存在SignalResult属性问题")
