#!/usr/bin/env python3
"""
预警监控系统
统一管理和分发各种预警信息
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
from enum import Enum
import json

logger = logging.getLogger(__name__)

class AlertSeverity(Enum):
    """告警严重程度"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

class AlertStatus(Enum):
    """告警状态"""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"

@dataclass
class Alert:
    """统一告警对象"""
    id: str
    timestamp: datetime
    source: str  # performance, business, system
    alert_type: str
    severity: AlertSeverity
    status: AlertStatus
    title: str
    message: str
    symbol: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None

@dataclass
class AlertRule:
    """告警规则"""
    name: str
    source: str
    condition: str
    severity: AlertSeverity
    enabled: bool = True
    cooldown_minutes: int = 5  # 冷却时间
    notification_channels: List[str] = None

class AlertMonitor:
    """预警监控器"""
    
    def __init__(self, 
                 max_alerts: int = 10000,
                 auto_resolve_hours: int = 24):
        """
        初始化预警监控器
        
        Args:
            max_alerts: 最大告警数量
            auto_resolve_hours: 自动解决告警的小时数
        """
        self.max_alerts = max_alerts
        self.auto_resolve_hours = auto_resolve_hours
        
        # 告警存储
        self.alerts: deque = deque(maxlen=max_alerts)
        self.active_alerts: Dict[str, Alert] = {}
        
        # 告警规则
        self.alert_rules: Dict[str, AlertRule] = {}
        
        # 告警统计
        self.alert_stats = defaultdict(int)
        
        # 通知渠道
        self.notification_channels: Dict[str, Callable] = {}
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task = None
        
        # 告警抑制（防止重复告警）
        self.suppression_cache: Dict[str, datetime] = {}
        
        logger.info("预警监控器初始化完成")
    
    async def start_monitoring(self):
        """开始预警监控"""
        if self.is_monitoring:
            logger.warning("预警监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("预警监控已启动")
    
    async def stop_monitoring(self):
        """停止预警监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("预警监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        logger.info("开始预警监控循环")
        
        while self.is_monitoring:
            try:
                # 自动解决过期告警
                await self._auto_resolve_alerts()
                
                # 清理抑制缓存
                await self._cleanup_suppression_cache()
                
                # 等待下次检查
                await asyncio.sleep(300)  # 5分钟检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"预警监控循环异常: {e}")
                await asyncio.sleep(300)
        
        logger.info("预警监控循环结束")
    
    async def create_alert(self, 
                          source: str,
                          alert_type: str,
                          severity: Union[AlertSeverity, str],
                          title: str,
                          message: str,
                          symbol: Optional[str] = None,
                          metrics: Optional[Dict[str, Any]] = None) -> str:
        """
        创建告警
        
        Args:
            source: 告警源
            alert_type: 告警类型
            severity: 严重程度
            title: 告警标题
            message: 告警消息
            symbol: 相关标的
            metrics: 相关指标
            
        Returns:
            告警ID
        """
        # 转换严重程度
        if isinstance(severity, str):
            severity = AlertSeverity(severity)
        
        # 生成告警ID
        alert_id = f"{source}_{alert_type}_{int(datetime.now().timestamp())}"
        
        # 检查告警抑制
        suppression_key = f"{source}_{alert_type}_{symbol or 'global'}"
        if await self._is_suppressed(suppression_key):
            logger.debug(f"告警被抑制: {suppression_key}")
            return alert_id
        
        # 创建告警对象
        alert = Alert(
            id=alert_id,
            timestamp=datetime.now(),
            source=source,
            alert_type=alert_type,
            severity=severity,
            status=AlertStatus.ACTIVE,
            title=title,
            message=message,
            symbol=symbol,
            metrics=metrics
        )
        
        # 存储告警
        self.alerts.append(alert)
        self.active_alerts[alert_id] = alert
        
        # 更新统计
        self.alert_stats[f"{source}_{severity.value}"] += 1
        self.alert_stats['total'] += 1
        
        # 设置抑制
        await self._set_suppression(suppression_key)
        
        # 发送通知
        await self._send_notifications(alert)
        
        logger.info(f"创建告警 [{severity.value.upper()}] {source}: {title}")
        
        return alert_id
    
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """确认告警"""
        if alert_id not in self.active_alerts:
            return False
        
        alert = self.active_alerts[alert_id]
        alert.status = AlertStatus.ACKNOWLEDGED
        alert.acknowledged_by = acknowledged_by
        alert.acknowledged_at = datetime.now()
        
        logger.info(f"告警已确认: {alert_id} by {acknowledged_by}")
        return True
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """解决告警"""
        if alert_id not in self.active_alerts:
            return False
        
        alert = self.active_alerts[alert_id]
        alert.status = AlertStatus.RESOLVED
        alert.resolved_at = datetime.now()
        
        # 从活跃告警中移除
        del self.active_alerts[alert_id]
        
        logger.info(f"告警已解决: {alert_id}")
        return True
    
    async def _auto_resolve_alerts(self):
        """自动解决过期告警"""
        cutoff_time = datetime.now() - timedelta(hours=self.auto_resolve_hours)
        
        expired_alerts = [
            alert_id for alert_id, alert in self.active_alerts.items()
            if alert.timestamp < cutoff_time
        ]
        
        for alert_id in expired_alerts:
            await self.resolve_alert(alert_id)
            logger.info(f"自动解决过期告警: {alert_id}")
    
    async def _is_suppressed(self, suppression_key: str) -> bool:
        """检查告警是否被抑制"""
        if suppression_key not in self.suppression_cache:
            return False
        
        # 检查冷却时间
        last_time = self.suppression_cache[suppression_key]
        cooldown_time = timedelta(minutes=5)  # 默认5分钟冷却
        
        return datetime.now() - last_time < cooldown_time
    
    async def _set_suppression(self, suppression_key: str):
        """设置告警抑制"""
        self.suppression_cache[suppression_key] = datetime.now()
    
    async def _cleanup_suppression_cache(self):
        """清理抑制缓存"""
        cutoff_time = datetime.now() - timedelta(hours=1)
        
        expired_keys = [
            key for key, timestamp in self.suppression_cache.items()
            if timestamp < cutoff_time
        ]
        
        for key in expired_keys:
            del self.suppression_cache[key]
    
    async def _send_notifications(self, alert: Alert):
        """发送通知"""
        # 根据严重程度选择通知渠道
        channels = []
        
        if alert.severity == AlertSeverity.CRITICAL:
            channels = ['email', 'sms', 'webhook']
        elif alert.severity == AlertSeverity.WARNING:
            channels = ['email', 'webhook']
        else:
            channels = ['webhook']
        
        # 发送到各个渠道
        for channel in channels:
            if channel in self.notification_channels:
                try:
                    notifier = self.notification_channels[channel]
                    if asyncio.iscoroutinefunction(notifier):
                        await notifier(alert)
                    else:
                        notifier(alert)
                except Exception as e:
                    logger.error(f"通知发送失败 [{channel}]: {e}")
    
    def register_notification_channel(self, name: str, notifier: Callable):
        """注册通知渠道"""
        self.notification_channels[name] = notifier
        logger.info(f"注册通知渠道: {name}")
    
    def add_alert_rule(self, rule: AlertRule):
        """添加告警规则"""
        self.alert_rules[rule.name] = rule
        logger.info(f"添加告警规则: {rule.name}")
    
    def get_active_alerts(self, 
                         severity: Optional[AlertSeverity] = None,
                         source: Optional[str] = None,
                         symbol: Optional[str] = None) -> List[Alert]:
        """获取活跃告警"""
        alerts = list(self.active_alerts.values())
        
        if severity:
            alerts = [a for a in alerts if a.severity == severity]
        
        if source:
            alerts = [a for a in alerts if a.source == source]
        
        if symbol:
            alerts = [a for a in alerts if a.symbol == symbol]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_history(self, 
                         hours: int = 24,
                         severity: Optional[AlertSeverity] = None) -> List[Alert]:
        """获取告警历史"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        alerts = [a for a in self.alerts if a.timestamp >= cutoff_time]
        
        if severity:
            alerts = [a for a in alerts if a.severity == severity]
        
        return sorted(alerts, key=lambda x: x.timestamp, reverse=True)
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """获取告警统计"""
        active_by_severity = defaultdict(int)
        active_by_source = defaultdict(int)
        
        for alert in self.active_alerts.values():
            active_by_severity[alert.severity.value] += 1
            active_by_source[alert.source] += 1
        
        return {
            'total_alerts': self.alert_stats['total'],
            'active_alerts': len(self.active_alerts),
            'active_by_severity': dict(active_by_severity),
            'active_by_source': dict(active_by_source),
            'historical_stats': dict(self.alert_stats),
            'suppression_cache_size': len(self.suppression_cache)
        }
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        stats = self.get_alert_statistics()
        recent_alerts = self.get_alert_history(hours=1)
        
        critical_alerts = [a for a in self.active_alerts.values() 
                          if a.severity == AlertSeverity.CRITICAL]
        
        return {
            'monitoring_status': 'active' if self.is_monitoring else 'stopped',
            'active_alerts': stats['active_alerts'],
            'critical_alerts': len(critical_alerts),
            'recent_alerts_1h': len(recent_alerts),
            'notification_channels': list(self.notification_channels.keys()),
            'alert_rules': len(self.alert_rules),
            'statistics': stats
        }


# 通知渠道实现示例
async def email_notifier(alert: Alert):
    """邮件通知器"""
    logger.info(f"📧 邮件通知: [{alert.severity.value.upper()}] {alert.title}")

async def webhook_notifier(alert: Alert):
    """Webhook通知器"""
    logger.info(f"🔗 Webhook通知: [{alert.severity.value.upper()}] {alert.title}")

def sms_notifier(alert: Alert):
    """短信通知器"""
    logger.info(f"📱 短信通知: [{alert.severity.value.upper()}] {alert.title}")


# 测试函数
async def test_alert_monitor():
    """测试预警监控器"""
    logger.info("开始测试预警监控器...")
    
    try:
        # 创建监控器
        monitor = AlertMonitor(max_alerts=100)
        
        # 注册通知渠道
        monitor.register_notification_channel('email', email_notifier)
        monitor.register_notification_channel('webhook', webhook_notifier)
        monitor.register_notification_channel('sms', sms_notifier)
        
        # 启动监控
        await monitor.start_monitoring()
        
        # 创建测试告警
        alert_id1 = await monitor.create_alert(
            source='performance',
            alert_type='cpu_high',
            severity=AlertSeverity.WARNING,
            title='CPU使用率过高',
            message='CPU使用率达到85%，超过警告阈值',
            metrics={'cpu_percent': 85.0}
        )
        
        alert_id2 = await monitor.create_alert(
            source='business',
            alert_type='signal_accuracy_low',
            severity=AlertSeverity.CRITICAL,
            title='信号准确率过低',
            message='ETF信号准确率降至45%',
            symbol='159740',
            metrics={'accuracy': 0.45}
        )
        
        # 等待一下
        await asyncio.sleep(2)
        
        # 确认告警
        await monitor.acknowledge_alert(alert_id1, 'admin')
        
        # 解决告警
        await monitor.resolve_alert(alert_id2)
        
        # 获取统计信息
        summary = monitor.get_alert_summary()
        stats = monitor.get_alert_statistics()
        active_alerts = monitor.get_active_alerts()
        
        logger.info(f"告警摘要: {summary}")
        logger.info(f"活跃告警数: {len(active_alerts)}")
        
        # 停止监控
        await monitor.stop_monitoring()
        
        logger.info("✅ 预警监控器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 预警监控器测试失败: {e}")
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_alert_monitor())
