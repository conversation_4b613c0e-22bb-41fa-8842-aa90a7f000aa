#!/usr/bin/env python3
"""
贝叶斯优化参数优化器
使用高斯过程和采集函数进行智能参数搜索
"""

import asyncio
import numpy as np
import logging
from typing import Dict, List, Tuple, Callable, Optional
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

try:
    from sklearn.gaussian_process import GaussianProcessRegressor
    from sklearn.gaussian_process.kernels import RBF, ConstantKernel, Matern
    from scipy.optimize import minimize
    from scipy.stats import norm
    SKLEARN_AVAILABLE = True
except ImportError:
    logger.warning("scikit-learn未安装，贝叶斯优化将使用简化实现")
    SKLEARN_AVAILABLE = False
    # 简化的norm实现
    class norm:
        @staticmethod
        def cdf(x):
            """简化的累积分布函数"""
            return 0.5 * (1 + np.tanh(x * 0.7978845608))  # 近似标准正态分布CDF

        @staticmethod
        def pdf(x):
            """简化的概率密度函数"""
            return np.exp(-0.5 * x * x) / np.sqrt(2 * np.pi)

@dataclass
class BayesianPoint:
    """贝叶斯优化点"""
    params: Dict[str, float]
    fitness: float
    acquisition_value: float = 0.0

class SimpleBayesianOptimizer:
    """简化的贝叶斯优化器（不依赖sklearn）"""
    
    def __init__(self, param_space: Dict[str, Tuple[float, float, float]],
                 n_initial_points: int = 10,
                 n_iterations: int = 50,
                 acquisition_function: str = 'ei'):
        self.param_space = param_space
        self.n_initial_points = n_initial_points
        self.n_iterations = n_iterations
        self.acquisition_function = acquisition_function
        
        self.evaluated_points: List[BayesianPoint] = []
        self.param_names = list(param_space.keys())
        self.param_bounds = [(bounds[0], bounds[1]) for bounds in param_space.values()]
        
    def _normalize_params(self, params: Dict[str, float]) -> np.ndarray:
        """标准化参数到[0,1]范围"""
        normalized = []
        for i, param_name in enumerate(self.param_names):
            min_val, max_val, _ = self.param_space[param_name]
            normalized_val = (params[param_name] - min_val) / (max_val - min_val)
            normalized.append(np.clip(normalized_val, 0, 1))
        return np.array(normalized)
    
    def _denormalize_params(self, normalized: np.ndarray) -> Dict[str, float]:
        """反标准化参数"""
        params = {}
        for i, param_name in enumerate(self.param_names):
            min_val, max_val, step = self.param_space[param_name]
            value = min_val + normalized[i] * (max_val - min_val)
            
            # 处理离散参数
            if step > 0:
                steps_from_min = round((value - min_val) / step)
                value = min_val + steps_from_min * step
                value = np.clip(value, min_val, max_val)
            
            params[param_name] = value
        return params
    
    def _rbf_kernel(self, x1: np.ndarray, x2: np.ndarray, length_scale: float = 1.0) -> float:
        """RBF核函数"""
        distance = np.linalg.norm(x1 - x2)
        return np.exp(-0.5 * (distance / length_scale) ** 2)
    
    def _predict_mean_std(self, x: np.ndarray) -> Tuple[float, float]:
        """预测均值和标准差（简化高斯过程）"""
        if len(self.evaluated_points) == 0:
            return 0.0, 1.0
        
        # 计算与已评估点的相似度
        similarities = []
        fitness_values = []
        
        for point in self.evaluated_points:
            x_eval = self._normalize_params(point.params)
            similarity = self._rbf_kernel(x, x_eval)
            similarities.append(similarity)
            fitness_values.append(point.fitness)
        
        similarities = np.array(similarities)
        fitness_values = np.array(fitness_values)
        
        # 加权平均预测
        if np.sum(similarities) > 0:
            weights = similarities / np.sum(similarities)
            mean = np.sum(weights * fitness_values)
            
            # 简化的不确定性估计
            variance = np.sum(weights * (fitness_values - mean) ** 2)
            std = np.sqrt(variance + 0.01)  # 添加小的基础不确定性
        else:
            mean = np.mean(fitness_values)
            std = np.std(fitness_values) + 0.1
        
        return mean, std
    
    def _expected_improvement(self, x: np.ndarray) -> float:
        """期望改进采集函数"""
        if len(self.evaluated_points) == 0:
            return 1.0
        
        mean, std = self._predict_mean_std(x)
        best_fitness = max(point.fitness for point in self.evaluated_points)
        
        if std == 0:
            return 0.0
        
        z = (mean - best_fitness) / std
        ei = (mean - best_fitness) * norm.cdf(z) + std * norm.pdf(z)
        return ei
    
    def _upper_confidence_bound(self, x: np.ndarray, kappa: float = 2.0) -> float:
        """置信上界采集函数"""
        mean, std = self._predict_mean_std(x)
        return mean + kappa * std
    
    def _acquisition_function(self, x: np.ndarray) -> float:
        """采集函数"""
        if self.acquisition_function == 'ei':
            return self._expected_improvement(x)
        elif self.acquisition_function == 'ucb':
            return self._upper_confidence_bound(x)
        else:
            return self._expected_improvement(x)
    
    def _optimize_acquisition(self) -> Dict[str, float]:
        """优化采集函数找到下一个评估点"""
        best_acquisition = -np.inf
        best_params = None
        
        # 随机搜索最优采集点
        n_candidates = 1000
        for _ in range(n_candidates):
            # 生成随机候选点
            x_candidate = np.random.uniform(0, 1, len(self.param_names))
            acquisition_value = self._acquisition_function(x_candidate)
            
            if acquisition_value > best_acquisition:
                best_acquisition = acquisition_value
                best_params = self._denormalize_params(x_candidate)
        
        return best_params
    
    def _generate_initial_points(self) -> List[Dict[str, float]]:
        """生成初始采样点"""
        points = []
        
        # 拉丁超立方采样的简化版本
        for i in range(self.n_initial_points):
            params = {}
            for param_name, (min_val, max_val, step) in self.param_space.items():
                if step > 0:
                    # 离散参数
                    num_steps = int((max_val - min_val) / step) + 1
                    random_step = np.random.randint(0, num_steps)
                    value = min_val + random_step * step
                else:
                    # 连续参数
                    value = np.random.uniform(min_val, max_val)
                
                params[param_name] = value
            
            points.append(params)
        
        return points

class BayesianOptimization:
    """贝叶斯优化器"""
    
    def __init__(self, 
                 param_space: Dict[str, Tuple[float, float, float]],
                 n_initial_points: int = 10,
                 n_iterations: int = 50,
                 acquisition_function: str = 'ei',
                 kernel: str = 'rbf'):
        """
        初始化贝叶斯优化器
        
        Args:
            param_space: 参数空间
            n_initial_points: 初始采样点数
            n_iterations: 优化迭代次数
            acquisition_function: 采集函数 ('ei', 'ucb', 'poi')
            kernel: 核函数类型
        """
        self.param_space = param_space
        self.n_initial_points = n_initial_points
        self.n_iterations = n_iterations
        self.acquisition_function = acquisition_function
        self.kernel = kernel
        
        # 使用简化实现或sklearn实现
        if SKLEARN_AVAILABLE:
            self._init_sklearn_optimizer()
        else:
            self.optimizer = SimpleBayesianOptimizer(
                param_space, n_initial_points, n_iterations, acquisition_function
            )
        
        self.results: List[Dict] = []
        
        logger.info(f"贝叶斯优化初始化: 初始点={n_initial_points}, 迭代={n_iterations}")
    
    def _init_sklearn_optimizer(self):
        """初始化sklearn版本的优化器"""
        # 设置核函数
        if self.kernel == 'rbf':
            kernel = ConstantKernel(1.0) * RBF(length_scale=1.0)
        elif self.kernel == 'matern':
            kernel = ConstantKernel(1.0) * Matern(length_scale=1.0, nu=2.5)
        else:
            kernel = ConstantKernel(1.0) * RBF(length_scale=1.0)
        
        self.gp = GaussianProcessRegressor(
            kernel=kernel,
            alpha=1e-6,
            normalize_y=True,
            n_restarts_optimizer=5,
            random_state=42
        )
        
        self.param_names = list(self.param_space.keys())
        self.param_bounds = [(bounds[0], bounds[1]) for bounds in self.param_space.values()]
        self.X_evaluated = []
        self.y_evaluated = []
        
        # 🔧 修复：创建简化优化器作为备用，确保self.optimizer存在
        self.optimizer = SimpleBayesianOptimizer(
            self.param_space, self.n_initial_points, self.n_iterations, self.acquisition_function
        )
    
    async def optimize(self, symbol: str, days: int, evaluate_func: Callable) -> List[Dict]:
        """
        执行贝叶斯优化
        
        Args:
            symbol: 交易标的
            days: 回测天数
            evaluate_func: 评估函数
            
        Returns:
            优化结果列表
        """
        logger.info(f"开始贝叶斯优化: {symbol}, 回测天数: {days}")
        
        if SKLEARN_AVAILABLE:
            return await self._optimize_sklearn(symbol, days, evaluate_func)
        else:
            return await self._optimize_simple(symbol, days, evaluate_func)
    
    async def _optimize_simple(self, symbol: str, days: int, evaluate_func: Callable) -> List[Dict]:
        """使用简化实现的优化"""
        # 初始采样
        initial_points = self.optimizer._generate_initial_points()
        
        logger.info(f"初始采样 {len(initial_points)} 个点...")
        
        # 评估初始点
        for i, params in enumerate(initial_points):
            try:
                metrics = await evaluate_func(params, symbol, days)
                fitness = metrics.get('fitness', -999.0)
                
                point = BayesianPoint(params=params, fitness=fitness)
                self.optimizer.evaluated_points.append(point)
                
                self.results.append({
                    'config': params,
                    'metrics': metrics
                })
                
                logger.info(f"初始点 {i+1}/{len(initial_points)}: 适应度 {fitness:.4f}")
                
            except Exception as e:
                logger.error(f"评估初始点失败: {e}")
        
        # 贝叶斯优化迭代
        for iteration in range(self.n_iterations):
            logger.info(f"贝叶斯优化迭代 {iteration + 1}/{self.n_iterations}")
            
            # 找到下一个评估点
            next_params = self.optimizer._optimize_acquisition()
            
            if next_params is None:
                logger.warning("无法找到下一个评估点，提前结束")
                break
            
            try:
                # 评估新点
                metrics = await evaluate_func(next_params, symbol, days)
                fitness = metrics.get('fitness', -999.0)
                
                point = BayesianPoint(params=next_params, fitness=fitness)
                self.optimizer.evaluated_points.append(point)
                
                self.results.append({
                    'config': next_params,
                    'metrics': metrics
                })
                
                # 显示当前最优
                best_fitness = max(p.fitness for p in self.optimizer.evaluated_points)
                logger.info(f"迭代 {iteration + 1}: 当前适应度 {fitness:.4f}, 最优 {best_fitness:.4f}")
                
            except Exception as e:
                logger.error(f"评估点失败: {e}")
        
        # 按适应度排序返回结果
        valid_results = [r for r in self.results if r['metrics'].get('fitness', -999) > -999]
        valid_results.sort(key=lambda x: x['metrics']['fitness'], reverse=True)
        
        logger.info(f"贝叶斯优化完成，有效结果: {len(valid_results)}")
        return valid_results[:10]  # 返回前10个最优结果
    
    async def _optimize_sklearn(self, symbol: str, days: int, evaluate_func: Callable) -> List[Dict]:
        """使用sklearn实现的优化（如果可用）"""
        logger.info("使用sklearn版本的贝叶斯优化")
        
        try:
            # 初始采样
            initial_points = self.optimizer._generate_initial_points()
            
            logger.info(f"初始采样 {len(initial_points)} 个点...")
            
            # 评估初始点
            for i, params in enumerate(initial_points):
                try:
                    metrics = await evaluate_func(params, symbol, days)
                    fitness = metrics.get('fitness', -999.0)
                    
                    # 转换参数为数值数组
                    x = np.array([params[name] for name in self.param_names]).reshape(1, -1)
                    self.X_evaluated.append(x[0])
                    self.y_evaluated.append(fitness)
                    
                    self.results.append({
                        'config': params,
                        'metrics': metrics
                    })
                    
                    logger.info(f"初始点 {i+1}/{len(initial_points)}: 适应度 {fitness:.4f}")
                    
                except Exception as e:
                    logger.error(f"评估初始点失败: {e}")
            
            # 如果有足够的数据点，进行贝叶斯优化迭代
            if len(self.X_evaluated) >= 2:
                X = np.array(self.X_evaluated)
                y = np.array(self.y_evaluated)
                
                for iteration in range(self.n_iterations):
                    logger.info(f"sklearn贝叶斯优化迭代 {iteration + 1}/{self.n_iterations}")
                    
                    try:
                        # 拟合高斯过程
                        self.gp.fit(X, y)
                        
                        # 找到下一个评估点（使用简化的采集函数优化）
                        next_params = self.optimizer._optimize_acquisition()
                        
                        if next_params is None:
                            logger.warning("无法找到下一个评估点，提前结束")
                            break
                        
                        # 评估新点
                        metrics = await evaluate_func(next_params, symbol, days)
                        fitness = metrics.get('fitness', -999.0)
                        
                        # 更新数据
                        x_new = np.array([next_params[name] for name in self.param_names])
                        self.X_evaluated.append(x_new)
                        self.y_evaluated.append(fitness)
                        
                        X = np.array(self.X_evaluated)
                        y = np.array(self.y_evaluated)
                        
                        self.results.append({
                            'config': next_params,
                            'metrics': metrics
                        })
                        
                        # 显示当前最优
                        best_fitness = max(self.y_evaluated)
                        logger.info(f"迭代 {iteration + 1}: 当前适应度 {fitness:.4f}, 最优 {best_fitness:.4f}")
                        
                    except Exception as e:
                        logger.error(f"sklearn优化迭代失败: {e}")
                        break
            
            # 按适应度排序返回结果
            valid_results = [r for r in self.results if r['metrics'].get('fitness', -999) > -999]
            valid_results.sort(key=lambda x: x['metrics']['fitness'], reverse=True)
            
            logger.info(f"sklearn贝叶斯优化完成，有效结果: {len(valid_results)}")
            return valid_results[:10]  # 返回前10个最优结果
            
        except Exception as e:
            logger.error(f"sklearn版本优化失败，回退到简化版本: {e}")
            # 回退到简化版本
            return await self._optimize_simple(symbol, days, evaluate_func)
    
    def get_optimization_summary(self) -> Dict:
        """获取优化摘要"""
        if not self.results:
            return {'status': 'not_started'}
        
        valid_results = [r for r in self.results if r['metrics'].get('fitness', -999) > -999]
        fitness_values = [r['metrics']['fitness'] for r in valid_results]
        
        return {
            'status': 'completed',
            'total_evaluations': len(self.results),
            'valid_count': len(valid_results),
            'success_rate': len(valid_results) / len(self.results) if self.results else 0,
            'best_fitness': max(fitness_values) if fitness_values else -999,
            'fitness_stats': {
                'mean': np.mean(fitness_values) if fitness_values else 0,
                'std': np.std(fitness_values) if fitness_values else 0,
                'min': min(fitness_values) if fitness_values else 0,
                'max': max(fitness_values) if fitness_values else 0
            },
            'convergence_info': self._analyze_convergence(fitness_values)
        }
    
    def _analyze_convergence(self, fitness_values: List[float]) -> Dict:
        """分析收敛情况"""
        if len(fitness_values) < 5:
            return {'converged': False, 'reason': 'insufficient_data'}
        
        # 计算最后几次的改进
        recent_improvements = []
        for i in range(len(fitness_values) - 5, len(fitness_values)):
            if i > 0:
                improvement = fitness_values[i] - max(fitness_values[:i])
                recent_improvements.append(improvement)
        
        avg_recent_improvement = np.mean(recent_improvements) if recent_improvements else 0
        
        return {
            'converged': avg_recent_improvement < 0.001,
            'avg_recent_improvement': avg_recent_improvement,
            'total_improvement': fitness_values[-1] - fitness_values[0] if len(fitness_values) > 1 else 0
        }


# 测试函数
async def test_bayesian_optimization():
    """测试贝叶斯优化"""
    logger.info("开始测试贝叶斯优化...")
    
    # 定义测试参数空间
    param_space = {
        'buy_trigger_drop': (-0.010, -0.005, 0.001),
        'profit_target': (0.003, 0.008, 0.001),
        'stop_loss': (-0.025, -0.015, 0.002)
    }
    
    # 创建贝叶斯优化实例
    bo = BayesianOptimization(
        param_space=param_space,
        n_initial_points=8,
        n_iterations=15,
        acquisition_function='ei'
    )
    
    # 模拟评估函数
    async def mock_evaluate_func(config: Dict, symbol: str, days: int) -> Dict:
        """模拟评估函数"""
        await asyncio.sleep(0.01)  # 模拟计算时间
        
        # 复杂的适应度函数（有全局最优解）
        x1 = abs(config['buy_trigger_drop'] + 0.007)  # 最优在-0.007附近
        x2 = abs(config['profit_target'] - 0.006)     # 最优在0.006附近
        x3 = abs(config['stop_loss'] + 0.020)         # 最优在-0.020附近
        
        fitness = 1.0 - (x1 * 100 + x2 * 150 + x3 * 50) + np.random.normal(0, 0.02)
        
        return {
            'fitness': fitness,
            'total_return': fitness * 0.05,
            'max_drawdown': -fitness * 0.01,
            'sharpe_ratio': fitness * 2.0
        }
    
    try:
        # 执行优化
        results = await bo.optimize("TEST", 30, mock_evaluate_func)
        
        logger.info(f"✅ 贝叶斯优化完成，获得 {len(results)} 个结果")
        
        if results:
            best_result = results[0]
            logger.info(f"最优适应度: {best_result['metrics']['fitness']:.4f}")
            logger.info(f"最优配置: {best_result['config']}")
        
        # 获取优化摘要
        summary = bo.get_optimization_summary()
        logger.info(f"优化摘要: 成功率 {summary['success_rate']:.2%}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 贝叶斯优化测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_bayesian_optimization())
