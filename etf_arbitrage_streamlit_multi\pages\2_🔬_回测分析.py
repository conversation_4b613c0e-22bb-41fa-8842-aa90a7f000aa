#!/usr/bin/env python3
"""
ETF套利回测分析 - 原版逻辑（完整重构版）
将侧边栏配置移至主内容区顶部，保持所有业务逻辑一致
完全基于 app_enhanced_backtest_dashboard.py 重构，包含所有功能
采用模块化设计，避免单文件过长
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import sqlite3
from typing import Dict, List, Optional
import sys
import os
import importlib.util
from pathlib import Path
import json
import asyncio

# 导入增强的交易日志显示模块
try:
    from utils.enhanced_trade_log_display import display_enhanced_trade_log, add_fund_change_tracking
except ImportError:
    # 如果导入失败，定义简化版本
    def display_enhanced_trade_log(trades_df, config):
        st.info("增强交易日志模块未加载，使用简化显示")
        if trades_df is not None and not trades_df.empty:
            st.dataframe(trades_df, width='stretch')
    
    def add_fund_change_tracking(equity_df, trades_df, config):
        st.info("资金变化追踪模块未加载")

# 添加项目路径
current_dir = Path(__file__).parent.parent.parent.absolute()
root_dir = str(current_dir)
sys.path.insert(0, root_dir)

# 调试信息 - 仅在调试模式下显示
DEBUG_MODE = False  # 设置为False以关闭调试输出
if DEBUG_MODE:
    print(f"当前文件路径: {__file__}")
    print(f"计算的根目录: {root_dir}")
    print(f"backtest_enhanced.py 路径: {os.path.join(root_dir, 'backtest_enhanced.py')}")
    print(f"文件是否存在: {os.path.exists(os.path.join(root_dir, 'backtest_enhanced.py'))}")

# 导入自定义模块
try:
    from utils.complete_functions import (
        load_available_symbols,
        get_data_date_range,
        run_enhanced_backtest,
        create_price_and_signals_chart,
        create_equity_curve_chart,
        create_trade_analysis_chart,
        analyze_tick_volatility
    )
    from utils.advanced_optimization import (
        load_saved_optimal_configs,
        load_optimal_config,
        run_parameter_optimization,
        delete_optimal_config,
        edit_config_interface
    )
    from utils.database_setup import ensure_optimal_configs_table
    from utils.safe_charts import (
        safe_plotly_chart,
        create_safe_equity_curve,
        create_safe_trade_analysis,
    )
    from utils.safe_plotly_display import (
        safe_plotly_chart as ultra_safe_plotly_chart,
        display_chart_with_fallback,
        create_empty_chart
    )
    from utils.performance_optimizer import (
        optimize_chart_data,
        optimize_trade_data,
        create_optimized_price_chart,
        create_optimized_equity_curve,
        create_optimized_trade_chart
    )
    from utils.performance_config import (
        performance_config
    )
    from utils.safe_charts import (
        safe_plotly_chart,
        create_safe_equity_curve,
        create_safe_trade_analysis,
        display_safe_dataframe
    )
    from utils.ultra_safe_charts import (
        ultra_safe_plotly_chart,
        create_ultra_safe_line_chart,
        display_ultra_safe_metrics,
        emergency_data_display
    )
    from utils.performance_optimizer import (
        ChartOptimizer,
        DataSampler,
        create_optimized_charts,
        display_performance_info
    )
except ImportError as e:
    st.error(f"无法导入自定义模块: {e}")
    st.stop()

# 初始化数据库
ensure_optimal_configs_table()

# 强制使用根目录的模块
try:
    # 简化导入方式，直接导入
    import backtest_enhanced
    import strategy_config
    
    BacktestConfig = backtest_enhanced.BacktestConfig
    EnhancedBacktest = backtest_enhanced.EnhancedBacktest
    StrategyConfig = strategy_config.StrategyConfig
    
except Exception as e:
    st.error(f"❌ 模块导入失败: {e}")
    st.error(f"当前工作目录: {os.getcwd()}")
    st.error(f"根目录路径: {root_dir}")
    
    # 检查文件是否存在
    backtest_path = os.path.join(root_dir, "backtest_enhanced.py")
    strategy_path = os.path.join(root_dir, "strategy_config.py")
    
    st.error(f"backtest_enhanced.py 存在: {os.path.exists(backtest_path)}")
    st.error(f"strategy_config.py 存在: {os.path.exists(strategy_path)}")
    
    # 显示可用文件
    if os.path.exists(root_dir):
        files = [f for f in os.listdir(root_dir) if f.endswith('.py')]
        st.info(f"根目录中的Python文件: {files}")
    
    st.stop()

# 页面配置
st.set_page_config(
    page_title="ETF套利回测分析",
    page_icon="🚀",
    layout="wide"
)

# 自定义CSS（与原版一致）
st.markdown("""
<style>
    .main-header {
        font-size: 2.0rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: rgba(240, 242, 246, 0.7);
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }
    .metric-card.blue {
        background-color: rgba(31, 119, 180, 0.15);
        border-left: 4px solid #1f77b4;
    }
    .metric-card.green {
        background-color: rgba(44, 160, 44, 0.15);
        border-left: 4px solid #2ca02c;
    }
    .metric-card.red {
        background-color: rgba(214, 40, 40, 0.15);
        border-left: 4px solid #d62728;
    }
    .metric-card.orange {
        background-color: rgba(255, 127, 14, 0.15);
        border-left: 4px solid #ff7f0e;
    }
    .metric-card.purple {
        background-color: rgba(148, 103, 189, 0.15);
        border-left: 4px solid #9467bd;
    }
    .positive { color: #00cc00; }
    .negative { color: #ff0000; }
    .neutral { color: #666666; }
</style>
""", unsafe_allow_html=True)

def main():
    """主函数"""
    st.markdown('<h1 class="main-header">🚀 ETF套利回测分析</h1>', unsafe_allow_html=True)
    
    # 性能优化配置界面
    with st.expander("⚡ 性能优化设置", expanded=False):
        perf_config = performance_config()
        st.session_state['performance_config'] = perf_config
    
    # ==================== 配置面板区域（原侧边栏内容移至主内容区） ====================
    st.header("📊 回测配置")
    
    # 基本参数配置
    with st.expander("🎯 基本参数配置", expanded=True):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            # 基本参数
            symbols = load_available_symbols()
            symbol = st.selectbox("交易标的", symbols, index=0)
            
            # 获取数据日期范围
            min_date, max_date = get_data_date_range(symbol)
            if min_date and max_date:
                min_date = datetime.strptime(min_date, '%Y-%m-%d').date()
                max_date = datetime.strptime(max_date, '%Y-%m-%d').date()
                
                start_date = st.date_input(
                    "开始日期", 
                    value=min_date,
                    min_value=min_date,
                    max_value=max_date
                )
            else:
                st.error("无法获取数据日期范围")
                return
        
        with col2:
            if min_date and max_date:
                end_date = st.date_input(
                    "结束日期", 
                    value=max_date,
                    min_value=min_date,
                    max_value=max_date
                )
            
            # 使用统一配置的初始资金参数
            initial_capital_config = StrategyConfig.get_streamlit_config('initial_capital')
            initial_capital = st.number_input(
                "初始资金", 
                **initial_capital_config
            )
        
        with col3:
            # 预设配置选择
            preset_options = ["自定义", "智能优化"] + list(StrategyConfig.STRATEGY_PRESETS.keys())
            config_preset = st.selectbox(
                "预设配置",
                preset_options
            )
    
    # 智能优化配置（已移至独立的参数优化区域）
    if config_preset == "智能优化":
        st.info("💡 智能优化功能已移至下方的 '🤖 智能参数优化' 区域")
        
        # 使用session state中的参数（智能优化模式）
        buy_trigger = getattr(st.session_state, 'buy_trigger', -0.006)
        profit_target = getattr(st.session_state, 'profit_target', 0.0025)
        stop_loss = getattr(st.session_state, 'stop_loss', -0.015)
        max_hold_time = getattr(st.session_state, 'max_hold_time', 86400)
        
        # 显示当前参数
        st.info(f"""
        **当前优化参数:**
        - 买入触发: {buy_trigger:.4f}
        - 止盈目标: {profit_target:.4f}
        - 止损线: {stop_loss:.4f}
        - 最大持仓时间: {max_hold_time}秒
        """)
        
    elif config_preset in StrategyConfig.STRATEGY_PRESETS:
        # 预设配置模式
        preset_config = StrategyConfig.get_preset_config(config_preset)
        buy_trigger = preset_config['buy_trigger_drop']
        profit_target = preset_config['profit_target']
        stop_loss = preset_config['stop_loss']
        max_hold_time = preset_config['max_hold_time']
        
        st.info(f"""
        **{config_preset}预设参数:**
        - 买入触发跌幅: {buy_trigger:.2%}
        - 止盈目标: {profit_target:.2%}
        - 止损线: {abs(stop_loss):.2%}
        - 最大持仓时间: {max_hold_time}秒
        """)
        
    else:  # 自定义模式
        with st.expander("🎯 策略参数配置", expanded=True):
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                trigger_config = StrategyConfig.get_streamlit_config('buy_trigger_drop')
                buy_trigger = st.slider(
                    "买入触发跌幅", 
                    min_value=trigger_config['min_value'] * 100,
                    max_value=trigger_config['max_value'] * 100,
                    value=trigger_config['value'] * 100, 
                    step=trigger_config['step'] * 100,
                    format="%.2f%%",
                    help=trigger_config['help']
                ) / 100
            
            with col2:
                profit_config = StrategyConfig.get_streamlit_config('profit_target')
                profit_target = st.slider(
                    "止盈目标", 
                    min_value=profit_config['min_value'] * 100,
                    max_value=profit_config['max_value'] * 100,
                    value=profit_config['value'] * 100, 
                    step=profit_config['step'] * 100,
                    format="%.2f%%",
                    help=profit_config['help']
                ) / 100
            
            with col3:
                loss_config = StrategyConfig.get_streamlit_config('stop_loss')
                stop_loss = st.slider(
                    "止损线", 
                    min_value=loss_config['min_value'] * 100,
                    max_value=loss_config['max_value'] * 100,
                    value=loss_config['value'] * 100, 
                    step=loss_config['step'] * 100,
                    format="%.2f%%",
                    help=loss_config['help']
                ) / 100
            
            with col4:
                # 时间控制参数说明
                st.markdown("**⏰ 时间控制**")
                
                # 时间控制总开关
                enable_time_control_config = StrategyConfig.get_streamlit_config('enable_time_control')
                enable_time_control = st.checkbox(
                    "启用时间控制",
                    value=bool(enable_time_control_config['value']),
                    help="是否启用时间止损功能"
                )
                
                if enable_time_control:
                    st.caption("⚡ 双重时间保护已启用")
                    
                    # 秒级时间控制
                    holding_config = StrategyConfig.get_streamlit_config('max_hold_time')
                    max_hold_time = st.slider(
                        "最大持仓时间（秒）", 
                        min_value=0,  # 0表示禁用
                        max_value=int(holding_config['max_value']),
                        value=int(holding_config['value']), 
                        step=int(holding_config['step']),
                        format="%d秒",
                        help="短期时间控制，0表示禁用此项"
                    )
                    
                    # 天级时间控制
                    days_config = StrategyConfig.get_streamlit_config('max_holding_days')
                    max_holding_days = st.slider(
                        "最大持仓天数",
                        min_value=0,  # 0表示禁用
                        max_value=int(days_config['max_value']),
                        value=int(days_config['value']),
                        step=int(days_config['step']),
                        format="%d天",
                        help="长期保护，0表示禁用此项"
                    )
                    
                    # 显示当前设置状态
                    status_parts = []
                    if max_hold_time > 0:
                        status_parts.append(f"秒级: {max_hold_time/3600:.1f}小时")
                    if max_holding_days > 0:
                        status_parts.append(f"天级: {max_holding_days}天")
                    
                    if status_parts:
                        st.caption(f"🔒 启用: {' + '.join(status_parts)}")
                    else:
                        st.caption("⚠️ 所有时间控制均已禁用")
                else:
                    st.caption("🔓 时间控制已关闭")
                    # 禁用时设置默认值
                    max_hold_time = 0
                    max_holding_days = 0
    
    # 高级参数配置
    with st.expander("🔧 高级参数配置"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("交易参数")
            
            # 手续费和滑点
            commission_config = StrategyConfig.get_streamlit_config('commission_rate')
            commission_rate = st.slider(
                "手续费率", 
                min_value=commission_config['min_value'],
                max_value=commission_config['max_value'],
                value=commission_config['value'],
                step=commission_config['step'],
                format="%.4f",
                help=commission_config['help']
            )
            
            slippage_config = StrategyConfig.get_streamlit_config('slippage')
            slippage = st.slider(
                "滑点", 
                min_value=slippage_config['min_value'],
                max_value=slippage_config['max_value'],
                value=slippage_config['value'],
                step=slippage_config['step'],
                format="%.4f",
                help=slippage_config['help']
            )
            
            # 持仓参数
            if config_preset != "智能优化":
                max_position_config = StrategyConfig.get_param_config('max_position')
                max_position = st.number_input(
                    "最大持仓数量",
                    min_value=int(max_position_config.min_value),
                    max_value=int(max_position_config.max_value),
                    value=int(max_position_config.default_value),
                    step=int(max_position_config.step),
                    format="%d"
                )
                
                position_size_config = StrategyConfig.get_param_config('position_size')
                position_size = st.number_input(
                    "单次买入仓位大小",
                    min_value=int(position_size_config.min_value),
                    max_value=int(position_size_config.max_value),
                    value=int(position_size_config.default_value),
                    step=int(position_size_config.step),
                    format="%d"
                )
                
                # 资金缓冲比例配置
                fund_buffer_config = StrategyConfig.get_param_config('fund_buffer_ratio')
                fund_buffer_ratio = st.number_input(
                    "资金缓冲比例",
                    min_value=fund_buffer_config.min_value,
                    max_value=fund_buffer_config.max_value,
                    value=fund_buffer_config.default_value,
                    step=fund_buffer_config.step,
                    format="%.2f",
                    help="保留的资金比例，用于应对市场波动和手续费"
                )
            else:
                max_position_config = StrategyConfig.get_param_config('max_position')
                max_position = int(max_position_config.default_value)
                position_size_config = StrategyConfig.get_param_config('position_size')
                position_size = int(position_size_config.default_value)
                fund_buffer_config = StrategyConfig.get_param_config('fund_buffer_ratio')
                fund_buffer_ratio = fund_buffer_config.default_value
        
        with col2:
            st.subheader("技术参数")
            
            if config_preset != "智能优化":
                signal_window_config = StrategyConfig.get_streamlit_config('signal_window')
                signal_window = st.slider(
                    "信号计算窗口(tick数)",
                    min_value=int(signal_window_config['min_value']),
                    max_value=int(signal_window_config['max_value']),
                    value=int(signal_window_config['value']),
                    step=int(signal_window_config['step']),
                    format="%d个tick",
                    help=signal_window_config['help']
                )
                
                min_hold_time = st.slider(
                    "最小持仓时间",
                    min_value=10,
                    max_value=300,
                    value=30,
                    step=10,
                    format="%d秒",
                    help="防止频繁交易的最小持仓时间保护"
                )
            else:
                signal_window_config = StrategyConfig.get_streamlit_config('signal_window')
                signal_window = int(signal_window_config['value'])
                min_hold_time = 30
    
    # 分层买入参数
    with st.expander("📊 分层买入设置"):
        col1, col2, col3 = st.columns(3)
        
        with col1:
            layer1_config = StrategyConfig.get_streamlit_config('layer1_ratio')
            layer1_ratio = st.slider(
                "第一层买入比例",
                min_value=int(layer1_config['min_value'] * 100),
                max_value=int(layer1_config['max_value'] * 100),
                value=int(layer1_config['value'] * 100),
                step=int(layer1_config['step'] * 100),
                format="%.0f%%",
                help=layer1_config['help']
            ) / 100
        
        with col2:
            layer2_config = StrategyConfig.get_streamlit_config('layer2_ratio')
            layer2_ratio = st.slider(
                "第二层买入比例", 
                min_value=int(layer2_config['min_value'] * 100),
                max_value=int(layer2_config['max_value'] * 100),
                value=int(layer2_config['value'] * 100),
                step=int(layer2_config['step'] * 100),
                format="%.0f%%",
                help=layer2_config['help']
            ) / 100
        
        with col3:
            layer3_config = StrategyConfig.get_streamlit_config('layer3_ratio')
            layer3_ratio = st.slider(
                "第三层买入比例",
                min_value=int(layer3_config['min_value'] * 100),
                max_value=int(layer3_config['max_value'] * 100),
                value=int(layer3_config['value'] * 100),
                step=int(layer3_config['step'] * 100),
                format="%.0f%%",
                help=layer3_config['help']
            ) / 100
    
    # 分批止盈参数
    with st.expander("💰 分批止盈设置"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("止盈倍数")
            # 获取分批止盈参数配置
            partial_profit_config1 = StrategyConfig.get_param_config('partial_profit_multiplier1')
            partial_profit_multiplier1 = st.slider(
                "第一次止盈倍数",
                min_value=partial_profit_config1.min_value,
                max_value=partial_profit_config1.max_value,
                value=float(partial_profit_config1.default_value),
                step=partial_profit_config1.step,
                format="%.1fx",
                help="相对于止盈目标的倍数"
            )
            
            partial_profit_config2 = StrategyConfig.get_param_config('partial_profit_multiplier2')
            partial_profit_multiplier2 = st.slider(
                "第二次止盈倍数",
                min_value=partial_profit_config2.min_value,
                max_value=partial_profit_config2.max_value,
                value=float(partial_profit_config2.default_value),
                step=partial_profit_config2.step,
                format="%.1fx"
            )
            
            partial_profit_config3 = StrategyConfig.get_param_config('partial_profit_multiplier3')
            partial_profit_multiplier3 = st.slider(
                "第三次止盈倍数",
                min_value=partial_profit_config3.min_value,
                max_value=partial_profit_config3.max_value,
                value=float(partial_profit_config3.default_value),
                step=partial_profit_config3.step,
                format="%.1fx"
            )
        
        with col2:
            st.subheader("卖出比例")
            # 获取分批卖出比例参数配置
            partial_sell_config1 = StrategyConfig.get_param_config('partial_sell_ratio1')
            partial_sell_ratio1 = st.slider(
                "第一次卖出比例",
                min_value=partial_sell_config1.min_value * 100,
                max_value=partial_sell_config1.max_value * 100,
                value=partial_sell_config1.default_value * 100,
                step=partial_sell_config1.step * 100,
                format="%.0f%%"
            ) / 100
            
            partial_sell_config2 = StrategyConfig.get_param_config('partial_sell_ratio2')
            partial_sell_ratio2 = st.slider(
                "第二次卖出比例",
                min_value=partial_sell_config2.min_value * 100,
                max_value=partial_sell_config2.max_value * 100,
                value=partial_sell_config2.default_value * 100,
                step=partial_sell_config2.step * 100,
                format="%.0f%%"
            ) / 100
            
            partial_sell_config3 = StrategyConfig.get_param_config('partial_sell_ratio3')
            partial_sell_ratio3 = st.slider(
                "第三次卖出比例",
                min_value=partial_sell_config3.min_value * 100,
                max_value=partial_sell_config3.max_value * 100,
                value=partial_sell_config3.default_value * 100,
                step=partial_sell_config3.step * 100,
                format="%.0f%%"
            ) / 100
    
    # 风险控制参数
    with st.expander("⚠️ 风险控制"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("交易时段控制")
            trading_session = st.selectbox(
                "允许交易时段",
                options=["全天", "上午", "下午"],
                index=0,
                help="选择允许进行交易的时段"
            )
            
            st.subheader("平仓时间控制")
            close_before_morning = st.checkbox(
                "上午收盘前平仓",
                value=False,
                help="在上午收盘前（11:25）自动平仓"
            )
            close_before_afternoon = st.checkbox(
                "下午收盘前平仓",
                value=True,
                help="在下午收盘前（14:55）自动平仓"
            )
            
            exit_at_close_config = StrategyConfig.get_param_config('exit_at_close')
            exit_at_close = st.checkbox(
                "收盘前平仓（兼容模式）",
                value=bool(exit_at_close_config.default_value),
                help="兼容原有的收盘前平仓设置"
            )
        
        with col2:
            st.subheader("风险限制")
            # 获取风险控制参数配置
            daily_loss_config = StrategyConfig.get_param_config('daily_loss_limit')
            daily_loss_limit = st.slider(
                "日损失限制",
                min_value=daily_loss_config.min_value * 100,
                max_value=daily_loss_config.max_value * 100,
                value=daily_loss_config.default_value * 100,
                step=daily_loss_config.step * 100,
                format="%.0f%%",
                help="单日最大允许损失"
            ) / 100
            
            max_drawdown_config = StrategyConfig.get_param_config('max_drawdown_limit')
            max_drawdown_limit = st.slider(
                "最大回撤限制",
                min_value=max_drawdown_config.min_value * 100,
                max_value=max_drawdown_config.max_value * 100,
                value=max_drawdown_config.default_value * 100,
                step=max_drawdown_config.step * 100,
                format="%.0f%%",
                help="最大允许回撤"
            ) / 100
    
    # ==================== 参数优化功能区域 ====================
    st.markdown("---")
    with st.expander("🤖 智能参数优化", expanded=False):
        st.markdown("### 🎯 自动寻找最优策略参数")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            optimization_method = st.selectbox(
                "优化方法",
                ["网格搜索", "遗传算法", "贝叶斯优化"],
                help="选择参数优化算法",
                key="opt_method"
            )
        
        with col2:
            optimization_days = st.slider(
                "优化数据天数",
                min_value=7,
                max_value=90,
                value=30,
                step=7,
                help="用于参数优化的历史数据天数",
                key="opt_days"
            )
        
        with col3:
            strategy_type = st.selectbox(
                "策略类型",
                ["conservative", "balanced", "aggressive"],
                index=1,
                help="保守型、平衡型或激进型策略",
                key="opt_strategy_type"
            )
        
        # 优化按钮和状态管理
        if st.button("🔍 开始智能优化", type="primary", width='stretch', key="start_optimization"):
            # 启动后台优化
            run_parameter_optimization(symbol, optimization_method, optimization_days, strategy_type)
            st.session_state.optimization_started = True
            st.rerun()
        
        # 显示优化状态
        optimization_status = st.session_state.get('optimization_status', 'idle')
        
        if optimization_status == 'running':
            st.info("🚀 参数优化正在后台运行，您可以自由切换到其他页面...")
            
            # 显示进度
            progress = st.session_state.get('optimization_progress', 0)
            message = st.session_state.get('optimization_message', '优化中...')
            
            st.progress(progress / 100)
            st.write(f"📊 {message}")
            
            # 停止按钮
            if st.button("⏹️ 停止优化", key="stop_optimization"):
                st.session_state.optimization_status = 'stopped'
                st.success("优化已停止")
                st.rerun()
            
            # 自动刷新
            import time
            time.sleep(1)
            st.rerun()
            
        elif optimization_status == 'completed':
            result = st.session_state.get('optimization_result', {})
            if result:
                st.success(f"""
                🎉 参数优化完成！

                **最优参数配置:**
                - 买入触发: {result['params']['buy_trigger_drop']:.4f}
                - 止盈目标: {result['params']['profit_target']:.4f}
                - 止损线: {result['params']['stop_loss']:.4f}
                - 最大持仓时间: {result['params']['max_hold_time']}秒

                **预期性能指标:**
                - 总收益率: {result['performance']['total_return']:.2%}
                - 最大回撤: {result['performance']['max_drawdown']:.2%}
                - 夏普比率: {result['performance']['sharpe_ratio']:.2f}
                - 胜率: {result['performance']['win_rate']:.1%}
                - 适应度分数: {result['fitness']:.3f}

                配置已保存，可在下方的优化结果中选择使用。
                """)
            
            # 重置状态
            if st.button("🔄 开始新的优化", key="reset_optimization"):
                st.session_state.optimization_status = 'idle'
                st.session_state.pop('optimization_result', None)
                st.rerun()
                
        elif optimization_status == 'error':
            error_msg = st.session_state.get('optimization_error', '未知错误')
            st.error(f"❌ 参数优化失败: {error_msg}")
            
            # 重置状态
            if st.button("🔄 重新开始", key="retry_optimization"):
                st.session_state.optimization_status = 'idle'
                st.session_state.pop('optimization_error', None)
                st.rerun()
        
        # 显示已保存的优化配置
        saved_configs = load_saved_optimal_configs(symbol)
        if saved_configs:
            st.markdown("#### 📋 已保存的优化配置")
            for i, config_data in enumerate(saved_configs):
                col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                
                with col1:
                    st.write(f"**{config_data['config_name']}** (适应度: {config_data['fitness_score']:.3f})")
                    params = config_data['parameters']
                    st.write(f"参数: 买入{params.get('buy_trigger_drop', 0):.3f}, 止盈{params.get('profit_target', 0):.3f}")
                
                with col2:
                    if st.button(f"使用", key=f"load_opt_{i}"):
                        load_optimal_config(config_data)
                        st.success("配置已加载")
                        st.rerun()
                
                with col3:
                    if st.button(f"编辑", key=f"edit_opt_{i}"):
                        st.session_state.editing_config = True
                        st.session_state.edit_config_idx = i
                        st.rerun()
                
                with col4:
                    if st.button(f"删除", key=f"delete_opt_{i}", type="secondary"):
                        if delete_optimal_config(config_data['config_name'], symbol):
                            st.success("配置已删除")
                            st.rerun()
            
            # 编辑配置界面
            if getattr(st.session_state, 'editing_config', False):
                edit_idx = getattr(st.session_state, 'edit_config_idx', 0)
                if edit_idx < len(saved_configs):
                    st.markdown("---")
                    edit_config_interface(saved_configs[edit_idx], symbol)
        else:
            st.info("暂无保存的优化配置")
    
    # ==================== 操作按钮区域 ====================
    st.markdown("---")
    col1, col2 = st.columns([1, 1])
    
    with col1:
        if st.button("📈 分析Tick波动", width='stretch'):
            with st.spinner("正在分析Tick波动..."):
                volatility_analysis = analyze_tick_volatility(
                    symbol=symbol,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d'),
                    window=signal_window if config_preset != "智能优化" else 20
                )
            
            if 'error' in volatility_analysis:
                st.error(f"波动分析失败: {volatility_analysis['error']}")
            else:
                st.session_state.volatility_analysis = volatility_analysis
                st.success("波动分析完成！")
    
    with col2:
        if st.button("🚀 运行回测", type="primary", width='stretch'):
            # 创建完整配置
            config = BacktestConfig(
                symbol=symbol,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                initial_capital=initial_capital,
                buy_trigger_drop=buy_trigger,
                profit_target=profit_target,
                stop_loss=stop_loss,
                enable_time_control=enable_time_control,
                max_hold_time=max_hold_time,
                max_holding_days=max_holding_days,
                commission_rate=commission_rate,
                slippage=slippage,
                signal_window=signal_window,
                min_hold_time=min_hold_time,
                max_position=max_position,
                position_size=position_size,
                fund_buffer_ratio=fund_buffer_ratio,
                layer1_ratio=layer1_ratio,
                layer2_ratio=layer2_ratio,
                layer3_ratio=layer3_ratio,
                partial_profit_multiplier1=partial_profit_multiplier1,
                partial_profit_multiplier2=partial_profit_multiplier2,
                partial_profit_multiplier3=partial_profit_multiplier3,
                partial_sell_ratio1=partial_sell_ratio1,
                partial_sell_ratio2=partial_sell_ratio2,
                partial_sell_ratio3=partial_sell_ratio3,
                daily_loss_limit=daily_loss_limit,
                max_drawdown_limit=max_drawdown_limit,
                exit_at_close=1 if exit_at_close else 0,
                trading_session=trading_session,
                close_before_morning=close_before_morning,
                close_before_afternoon=close_before_afternoon
            )
            
            # 运行回测
            with st.spinner("正在运行回测..."):
                results = run_enhanced_backtest(config)
            
            if 'error' in results:
                st.error(f"回测失败: {results['error']}")
                return
            
            # 存储结果到session state
            st.session_state.backtest_results = results
            st.session_state.backtest_config = config
            st.success("回测完成！")
    
    # ==================== 结果展示区域 ====================
    if 'backtest_results' in st.session_state:
        st.markdown("---")
        results = st.session_state.backtest_results
        config = st.session_state.backtest_config
        
        # 性能指标展示
        st.header("📊 回测结果")
        
        perf = results['performance']
        
        # 计算实际利润（扣除所有费用后的到手利润）
        initial_capital = float(perf.get('初始资金', '0').replace('元', '').replace(',', ''))
        final_capital = float(perf.get('期末净值', '0').replace('元', '').replace(',', ''))
        actual_profit = final_capital - initial_capital
        
        # 突出显示实际利润
        st.markdown("### 💰 实际到手利润")
        profit_col1, profit_col2, profit_col3 = st.columns(3)
        
        with profit_col1:
            profit_color = "green" if actual_profit >= 0 else "red"
            st.markdown(f"""
            <div class="metric-card {profit_color}">
                <h3 style="margin: 0; color: {'#2ca02c' if actual_profit >= 0 else '#d62728'};">
                    💰 实际利润
                </h3>
                <h2 style="margin: 10px 0; color: {'#2ca02c' if actual_profit >= 0 else '#d62728'};">
                    {actual_profit:,.2f}元
                </h2>
                <p style="margin: 0; color: #666;">扣除所有费用后</p>
            </div>
            """, unsafe_allow_html=True)
        
        with profit_col2:
            profit_rate = (actual_profit / initial_capital * 100) if initial_capital > 0 else 0
            st.markdown(f"""
            <div class="metric-card blue">
                <h3 style="margin: 0; color: #1f77b4;">📈 实际收益率</h3>
                <h2 style="margin: 10px 0; color: {'#2ca02c' if profit_rate >= 0 else '#d62728'};">
                    {profit_rate:+.2f}%
                </h2>
                <p style="margin: 0; color: #666;">净收益率</p>
            </div>
            """, unsafe_allow_html=True)
        
        with profit_col3:
            total_cost = float(perf.get('总费用', '0').replace('元', '').replace(',', ''))
            st.markdown(f"""
            <div class="metric-card orange">
                <h3 style="margin: 0; color: #ff7f0e;">💸 总费用</h3>
                <h2 style="margin: 10px 0; color: #ff7f0e;">
                    {total_cost:.2f}元
                </h2>
                <p style="margin: 0; color: #666;">佣金+印花税+过户费</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown("---")
        st.markdown("### 📊 其他指标")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_return = float(perf['总收益率'].rstrip('%')) / 100
            color_class = "positive" if total_return > 0 else "negative" if total_return < 0 else "neutral"
            st.markdown(f"""
            <div class="metric-card blue">
                <h4>总收益率</h4>
                <h2 class="{color_class}">{perf['总收益率']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col2:
            max_dd = float(perf['最大回撤'].rstrip('%')) / 100
            color_class = "negative" if max_dd < -0.05 else "neutral"
            st.markdown(f"""
            <div class="metric-card green">
                <h4>最大回撤</h4>
                <h2 class="{color_class}">{perf['最大回撤']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col3:
            win_rate = float(perf['胜率'].rstrip('%')) / 100
            color_class = "positive" if win_rate > 0.6 else "neutral"
            st.markdown(f"""
            <div class="metric-card orange">
                <h4>胜率</h4>
                <h2 class="{color_class}">{perf['胜率']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        with col4:
            st.markdown(f"""
            <div class="metric-card purple">
                <h4>交易次数</h4>
                <h2 class="neutral">{perf['总交易次数']}</h2>
            </div>
            """, unsafe_allow_html=True)
        
        # 详细指标
        st.subheader("📈 详细指标")
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**财务指标**")
            st.write(f"初始资金: {perf['初始资金']}")
            st.write(f"期末净值: {perf['期末净值']}")
            st.write(f"夏普比率: {perf['夏普比率']}")
            st.write(f"总佣金: {perf['总佣金']}")
            
            # 安全处理印花税显示
            stamp_tax_value = perf.get('总印花税', '0.00元')
            if isinstance(stamp_tax_value, str):
                st.write(f"总印花税: {stamp_tax_value}")
            else:
                st.write(f"总印花税: {float(stamp_tax_value):.2f}元")
            
            # 安全处理过户费显示
            transfer_fee_value = perf.get('总过户费', '0.00元')
            if isinstance(transfer_fee_value, str):
                st.write(f"总过户费: {transfer_fee_value}")
            else:
                st.write(f"总过户费: {float(transfer_fee_value):.2f}元")
            
            # 安全处理总费用显示
            total_fee_value = perf.get('总费用', '0.00元')
            if isinstance(total_fee_value, str):
                st.write(f"总费用: {total_fee_value}")
            else:
                st.write(f"总费用: {float(total_fee_value):.2f}元")
            
            st.caption("💡 总费用 = 总佣金 + 总印花税 + 总过户费")
            
            # 添加手续费一致性验证提示 - 安全格式化
            try:
                # 安全转换和格式化
                commission_value = perf.get('总佣金', 0)
                if isinstance(commission_value, str):
                    # 移除可能的单位和格式化字符
                    commission_str = commission_value.replace('元', '').replace(',', '').strip()
                    total_commission_value = float(commission_str)
                else:
                    total_commission_value = float(commission_value)
                
                # 安全转换印花税
                stamp_tax_raw = perf.get('总印花税', 0.0)
                if isinstance(stamp_tax_raw, str):
                    stamp_tax_str = stamp_tax_raw.replace('元', '').replace(',', '').strip()
                    stamp_tax_value = float(stamp_tax_str)
                else:
                    stamp_tax_value = float(stamp_tax_raw)

                # 安全转换过户费
                transfer_fee_raw = perf.get('总过户费', 0.0)
                if isinstance(transfer_fee_raw, str):
                    transfer_fee_str = transfer_fee_raw.replace('元', '').replace(',', '').strip()
                    transfer_fee_value = float(transfer_fee_str)
                else:
                    transfer_fee_value = float(transfer_fee_raw)

                total_fee_value = total_commission_value + stamp_tax_value + transfer_fee_value
                
                print(f"💰 DEBUG: 详细指标总佣金 = {total_commission_value:.2f}元")
                print(f"💰 DEBUG: 详细指标总印花税 = {stamp_tax_value:.2f}元")
                print(f"💰 DEBUG: 详细指标总过户费 = {transfer_fee_value:.2f}元")
                print(f"💰 DEBUG: 详细指标总费用 = {total_fee_value:.2f}元")
            except (ValueError, TypeError) as e:
                print(f"💰 DEBUG: 详细指标费用格式化失败, 错误: {e}")
            
            # 添加净值分解信息
            equity_df = results['raw_data']['equity_curve']
            trades_df = results['raw_data']['trades']
            if not equity_df.empty and not trades_df.empty:
                final_row = equity_df.iloc[-1]
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                total_realized_pnl = sell_trades['pnl'].sum() if not sell_trades.empty else 0

                # 计算完整的盈亏分解
                total_commission = float(perf.get('总佣金', '0元').replace('元', '').replace(',', ''))
                total_stamp_tax = float(perf.get('总印花税', '0元').replace('元', '').replace(',', ''))
                total_transfer_fee = float(perf.get('总过户费', '0元').replace('元', '').replace(',', ''))
                total_fees = total_commission + total_stamp_tax + total_transfer_fee
                net_pnl = total_realized_pnl - total_fees
                
                st.write("**💰 盈亏分解**")
                # 使用st.metric或st.info替代st.write的help参数
                col1, col2 = st.columns(2)
                with col1:
                    st.metric("📈 税前盈亏", f"{total_realized_pnl:.2f}元", help="仅计算买卖价差，未扣除交易费用")
                with col2:
                    st.metric("💰 税后盈亏", f"{net_pnl:.2f}元", help="扣除所有交易费用后的实际盈亏")

                st.write(f"💸 交易费用: -{total_fees:.2f}元")
                st.write(f"   ├─ 佣金: -{total_commission:.2f}元")
                st.write(f"   ├─ 印花税: -{total_stamp_tax:.2f}元")
                st.write(f"   └─ 过户费: -{total_transfer_fee:.2f}元")
                
                st.write("**💵 资金状况**")
                if 'cash' in final_row:
                    st.write(f"现金余额: {final_row['cash']:.2f}元")
                if 'market_value' in final_row:
                    st.write(f"持仓市值: {final_row['market_value']:.2f}元")
        
        with col2:
            st.write("**交易统计**")
            st.write(f"买入次数: {perf['买入次数']}")
            st.write(f"卖出次数: {perf['卖出次数']}")
            st.write(f"胜率: {perf['胜率']}")
            
            # 添加详细的胜率调试信息
            trades_df = results['raw_data']['trades']
            if not trades_df.empty:
                sell_trades = trades_df[trades_df['type'] == 'SELL']
                if not sell_trades.empty and 'pnl' in sell_trades.columns:
                    profitable = len(sell_trades[sell_trades['pnl'] > 0])
                    losing = len(sell_trades[sell_trades['pnl'] < 0])
                    st.write(f"📊 胜率详情: {profitable}胜/{losing}负 (共{len(sell_trades)}笔)")

                    # 显示PnL范围
                    pnl_min = sell_trades['pnl'].min()
                    pnl_max = sell_trades['pnl'].max()
                    st.write(f"💰 盈亏范围: {pnl_min:.2f} ~ {pnl_max:.2f}")
                else:
                    st.write("⚠️ 无卖出交易或缺少PnL数据")
        
        # 添加资金管理指标列
        with st.container():
            st.write("**💰 资金管理指标**")
            col_fund1, col_fund2 = st.columns(2)
            
            with col_fund1:
                # 从回测结果中获取资金利用率指标
                if 'fund_metrics' in results:
                    fund_metrics = results['fund_metrics']
                    st.write(f"资金利用率: {fund_metrics.get('fund_utilization', 0):.2%}")
                    st.write(f"杠杆率: {fund_metrics.get('leverage_ratio', 0):.2f}x")
                else:
                    st.write("资金利用率: 计算中...")
                    st.write("杠杆率: 计算中...")
            
            with col_fund2:
                if 'fund_metrics' in results:
                    fund_metrics = results['fund_metrics']
                    st.write(f"现金比例: {fund_metrics.get('cash_ratio', 0):.2%}")
                    st.write(f"持仓比例: {fund_metrics.get('position_ratio', 0):.2%}")
                else:
                    st.write("现金比例: 计算中...")
                    st.write("持仓比例: 计算中...")

        # 🆕 A股规则合规性显示
        if 'a_stock_compliance' in results:
            st.subheader("🏛️ A股规则合规性")
            compliance = results['a_stock_compliance']

            # 合规性概览
            col_comp1, col_comp2, col_comp3, col_comp4 = st.columns(4)

            with col_comp1:
                compliance_rate = float(compliance['合规率'].rstrip('%')) / 100
                color_class = "positive" if compliance_rate >= 0.95 else "warning" if compliance_rate >= 0.90 else "negative"
                st.markdown(f"""
                <div class="metric-card green">
                    <h4>合规率</h4>
                    <h2 class="{color_class}">{compliance['合规率']}</h2>
                </div>
                """, unsafe_allow_html=True)

            with col_comp2:
                total_violations = compliance['总违规次数']
                color_class = "positive" if total_violations == 0 else "warning" if total_violations <= 5 else "negative"
                st.markdown(f"""
                <div class="metric-card orange">
                    <h4>总违规次数</h4>
                    <h2 class="{color_class}">{total_violations}</h2>
                </div>
                """, unsafe_allow_html=True)

            with col_comp3:
                normalizations = compliance['数量标准化次数']
                st.markdown(f"""
                <div class="metric-card blue">
                    <h4>数量标准化</h4>
                    <h2 class="neutral">{normalizations}</h2>
                </div>
                """, unsafe_allow_html=True)

            with col_comp4:
                rules_version = compliance['规则版本']
                st.markdown(f"""
                <div class="metric-card purple">
                    <h4>规则版本</h4>
                    <h2 class="neutral">{rules_version}</h2>
                </div>
                """, unsafe_allow_html=True)

            # 详细合规性统计
            col_detail1, col_detail2 = st.columns(2)

            with col_detail1:
                st.write("**🕐 时间限制统计**")
                st.write(f"交易时间限制次数: {compliance['交易时间限制次数']}")
                st.write(f"T+1限制次数: {compliance['T+1限制次数']}")

                st.write("**💹 价格限制统计**")
                st.write(f"涨跌停限制次数: {compliance['涨跌停限制次数']}")

            with col_detail2:
                st.write("**📊 数量规范化统计**")
                st.write(f"数量标准化次数: {compliance['数量标准化次数']}")
                st.caption("💡 A股要求交易数量为100股的整数倍")

                # 合规性评级
                if compliance_rate >= 0.98:
                    grade = "🏆 优秀"
                    grade_color = "#2ca02c"
                elif compliance_rate >= 0.95:
                    grade = "✅ 良好"
                    grade_color = "#1f77b4"
                elif compliance_rate >= 0.90:
                    grade = "⚠️ 一般"
                    grade_color = "#ff7f0e"
                else:
                    grade = "❌ 需改进"
                    grade_color = "#d62728"

                st.markdown(f"""
                **🎯 合规性评级**
                <span style="color: {grade_color}; font-weight: bold; font-size: 1.2em;">{grade}</span>
                """, unsafe_allow_html=True)
                
                st.write(f"资金缓冲: {getattr(config, 'fund_buffer_ratio', 0.05):.2%}")
        
        # 图表展示
        raw_data = results['raw_data']
        
        if not raw_data['signals'].empty:
            st.subheader("📊 策略执行详情")
            fig1 = ChartOptimizer().optimize_price_chart(raw_data['signals'], raw_data['trades'], config, max_points=2000)
            ultra_safe_plotly_chart(fig1)
            # 显示性能优化信息
            if "raw_data" in locals() and "signals" in raw_data:
                display_performance_info(len(raw_data["signals"]), len(raw_data["signals"]))
        
        if not raw_data['equity_curve'].empty:
            st.subheader("💰 净值表现")
            fig2 = ChartOptimizer().optimize_equity_curve(raw_data['equity_curve'], "净值表现")
            ultra_safe_plotly_chart(fig2)
            # 显示性能优化信息
            if "raw_data" in locals() and "signals" in raw_data:
                display_performance_info(len(raw_data["signals"]), len(raw_data["signals"]))
        
        if not raw_data['trades'].empty:
            st.subheader("🔍 交易分析")
            fig3 = create_trade_analysis_chart(raw_data['trades'])
            ultra_safe_plotly_chart(fig3)
            # 显示性能优化信息
            if "raw_data" in locals() and "signals" in raw_data:
                display_performance_info(len(raw_data["signals"]), len(raw_data["signals"]))
            
            # 使用增强的交易日志显示
            display_enhanced_trade_log(raw_data['trades'], config)
            
            # 添加资金变化追踪
            add_fund_change_tracking(raw_data['equity_curve'], raw_data['trades'], config)

            # 添加导出按钮
            st.subheader("📥 数据导出")
            col1, col2 = st.columns([3, 1])
            with col2:
                if st.button("📥 导出CSV"):
                    # 生成带时间戳的文件名
                    timestamp = datetime.now().strftime("%Y-%m-%dT%H-%M")
                    csv_filename = f"{timestamp}_export.csv"

                    # 导出完整的交易数据
                    raw_data['trades'].to_csv(csv_filename, index=True)
                    st.success(f"交易数据已导出到: {csv_filename}")

                    # 显示导出统计
                    trades_df = raw_data['trades']
                    sell_trades = trades_df[trades_df['type'] == 'SELL']
                    if not sell_trades.empty and 'pnl' in sell_trades.columns:
                        profitable = len(sell_trades[sell_trades['pnl'] > 0])
                        total_sell = len(sell_trades)
                        export_win_rate = profitable / total_sell if total_sell > 0 else 0
                        st.info(f"导出数据统计: {total_sell}笔卖出交易，胜率: {export_win_rate:.2%}")

            with col1:
                st.info("💡 上方的详细交易记录已包含完整的计算过程，可直接查看每笔交易的准确性")
    
    # 显示波动分析结果
    if 'volatility_analysis' in st.session_state:
        st.markdown("---")
        st.markdown("## 📈 Tick数据波动分析结果")
        st.info("💡 以下分析结果可以帮助您优化策略参数设置")
        analysis = st.session_state.volatility_analysis
        
        # 创建四列显示分析结果
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.subheader("📊 数据概况")
            for key, value in analysis['数据概况'].items():
                st.metric(key, value)
        
        with col2:
            st.subheader("📈 波动分析")
            # 找到波动分析的键名
            volatility_key = None
            for key in analysis.keys():
                if 'tick窗口波动分析' in key:
                    volatility_key = key
                    break
            if volatility_key:
                for key, value in analysis[volatility_key].items():
                    st.metric(key, value)
        
        with col3:
            st.subheader("📉 回撤分析")
            # 找到回撤分析的键名
            drawdown_key = None
            for key in analysis.keys():
                if 'tick窗口回撤分析' in key:
                    drawdown_key = key
                    break
            if drawdown_key:
                for key, value in analysis[drawdown_key].items():
                    st.metric(key, value)
        
        with col4:
            st.subheader("💡 参数建议")
            if '策略参数建议' in analysis:
                for key, value in analysis['策略参数建议'].items():
                    st.metric(key, value)
        
        # 动态窗口波动分布图
        if 'raw_data' in analysis:
            raw_data = analysis['raw_data']
            window_returns = raw_data['window_returns']
            window_drawdowns = raw_data['window_drawdowns']
            
            # 从分析结果中获取实际的窗口大小
            window_size = 20  # 默认值
            for key in analysis.keys():
                if 'tick窗口波动分析' in key:
                    # 从键名中提取窗口大小，如 "20tick窗口波动分析"
                    import re
                    match = re.search(r'(\d+)tick窗口', key)
                    if match:
                        window_size = int(match.group(1))
                    break
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader(f"{window_size}tick窗口收益率分布")
                try:
                    # 数据验证和清理
                    if len(window_returns) > 0:
                        # 使用简化的plotly配置，避免JavaScript模块问题
                        try:
                            fig_returns = px.histogram(
                                x=window_returns,
                                nbins=min(50, len(window_returns)//10 + 1),
                                title=f"{window_size}tick窗口收益率分布",
                                labels={'x': f'{window_size}tick收益率', 'y': '频次'}
                            )
                            
                            # 添加参考线 - 使用与文字建议一致的第5百分位
                            percentile_5 = np.percentile(window_returns, 5)
                            fig_returns.add_vline(
                                x=percentile_5,
                                line_dash="dash",
                                line_color="red",
                                annotation_text=f"建议买入触发点({percentile_5:.4f})"
                            )
                            
                            fig_returns.update_layout(height=400)
                            st.plotly_chart(fig_returns, width='stretch')
                            
                        except Exception as e:
                            # 备用方案：使用简单的统计显示
                            st.error(f"图表生成失败，使用备用显示: {e}")
                            percentile_5 = np.percentile(window_returns, 5)
                            st.info(f"📊 建议买入触发点: {percentile_5:.4f}")
                            
                            col_a, col_b, col_c = st.columns(3)
                            with col_a:
                                st.metric("平均值", f"{np.mean(window_returns):.6f}")
                            with col_b:
                                st.metric("标准差", f"{np.std(window_returns):.6f}")
                            with col_c:
                                st.metric("数据点数", len(window_returns))
                    else:
                        st.warning("收益率数据为空")
                        
                except Exception as e:
                    st.error(f"收益率分布图生成失败: {e}")
                    # 备用显示方案
                    if len(window_returns) > 0:
                        st.write("**收益率统计**:")
                        st.write(f"- 平均值: {np.mean(window_returns):.6f}")
                        st.write(f"- 标准差: {np.std(window_returns):.6f}")
                        st.write(f"- 最大值: {np.max(window_returns):.6f}")
                        st.write(f"- 最小值: {np.min(window_returns):.6f}")
            
            with col2:
                st.subheader(f"{window_size}tick窗口回撤分布")
                try:
                    # 数据验证和清理
                    if len(window_drawdowns) > 0:
                        # 使用简化的plotly配置，避免JavaScript模块问题
                        try:
                            fig_drawdown = px.histogram(
                                x=window_drawdowns,
                                nbins=min(50, len(window_drawdowns)//10 + 1),
                                title=f"{window_size}tick窗口回撤分布",
                                labels={'x': f'{window_size}tick回撤', 'y': '频次'}
                            )
                            
                            # 添加参考线
                            percentile_5 = np.percentile(window_drawdowns, 5)
                            fig_drawdown.add_vline(
                                x=percentile_5,
                                line_dash="dash",
                                line_color="red",
                                annotation_text=f"建议止损线({percentile_5:.4f})"
                            )
                            
                            fig_drawdown.update_layout(height=400)
                            st.plotly_chart(fig_drawdown, width='stretch')
                            
                        except Exception as e:
                            # 备用方案：使用简单的统计显示
                            st.error(f"图表生成失败，使用备用显示: {e}")
                            percentile_5 = np.percentile(window_drawdowns, 5)
                            st.warning(f"⚠️ 建议止损线: {percentile_5:.4f}")
                            
                            col_a, col_b, col_c = st.columns(3)
                            with col_a:
                                st.metric("平均回撤", f"{np.mean(window_drawdowns):.6f}")
                            with col_b:
                                st.metric("最大回撤", f"{np.min(window_drawdowns):.6f}")
                            with col_c:
                                st.metric("数据点数", len(window_drawdowns))
                    else:
                        st.warning("回撤数据为空")
                        
                except Exception as e:
                    st.error(f"回撤分布图生成失败: {e}")
                    # 备用显示方案
                    if len(window_drawdowns) > 0:
                        st.write("**回撤统计**:")
                        st.write(f"- 平均值: {np.mean(window_drawdowns):.6f}")
                        st.write(f"- 标准差: {np.std(window_drawdowns):.6f}")
                        st.write(f"- 最大回撤: {np.min(window_drawdowns):.6f}")
                        st.write(f"- 最小回撤: {np.max(window_drawdowns):.6f}")
        
        st.markdown("---")
    
    else:
        st.info("👆 请配置参数并点击'运行回测'开始分析")
        
        # 显示功能介绍
        st.header("🌟 功能特色")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **🎯 精准回测**
            - 基于真实tick数据
            - 考虑滑点和手续费
            - 完整的风险控制
            """)
        
        with col2:
            st.markdown("""
            **📊 直观展示**
            - 实时价格走势
            - 交易信号可视化
            - 净值曲线分析
            """)
        
        with col3:
            st.markdown("""
            **⚡ 智能策略**
            - 分层买入机制
            - 分批止盈策略
            - 动态风险管理
            """)

if __name__ == "__main__":
    main()