#!/usr/bin/env python3
"""
业务监控系统
监控交易策略、信号质量和业务指标
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import json
import numpy as np

logger = logging.getLogger(__name__)

@dataclass
class TradingMetrics:
    """交易指标"""
    timestamp: datetime
    symbol: str
    signal_strength: float
    signal_count: int
    trade_count: int
    win_count: int
    loss_count: int
    total_return: float
    max_drawdown: float
    sharpe_ratio: float
    win_rate: float
    avg_trade_duration: float  # 分钟
    
@dataclass
class SignalQuality:
    """信号质量指标"""
    timestamp: datetime
    symbol: str
    signal_accuracy: float  # 信号准确率
    signal_frequency: float  # 信号频率(每小时)
    false_positive_rate: float  # 假阳性率
    signal_latency_ms: float  # 信号延迟
    data_freshness_seconds: float  # 数据新鲜度

@dataclass
class BusinessAlert:
    """业务告警"""
    timestamp: datetime
    alert_type: str
    severity: str  # info, warning, critical
    symbol: str
    message: str
    metrics: Dict[str, Any]

class BusinessMonitor:
    """业务监控器"""
    
    def __init__(self, history_size: int = 1000):
        """
        初始化业务监控器
        
        Args:
            history_size: 历史数据保留数量
        """
        self.history_size = history_size
        
        # 业务数据历史
        self.trading_history: deque = deque(maxlen=history_size)
        self.signal_history: deque = deque(maxlen=history_size)
        self.alert_history: deque = deque(maxlen=history_size)
        
        # 实时统计
        self.symbol_stats: Dict[str, Dict] = defaultdict(dict)
        self.daily_stats: Dict[str, Dict] = defaultdict(dict)
        
        # 业务规则配置
        self.business_rules = self._init_business_rules()
        
        # 回调函数
        self.alert_callbacks: List[Callable] = []
        self.metrics_callbacks: List[Callable] = []
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_task = None
        
        logger.info("业务监控器初始化完成")
    
    def _init_business_rules(self) -> Dict[str, Dict]:
        """初始化业务规则"""
        return {
            'signal_quality': {
                'min_accuracy': 0.6,  # 最低信号准确率
                'max_latency_ms': 1000,  # 最大信号延迟
                'max_data_age_seconds': 300,  # 最大数据年龄
                'min_frequency_per_hour': 0.1  # 最低信号频率
            },
            'trading_performance': {
                'min_win_rate': 0.4,  # 最低胜率
                'max_drawdown': -0.1,  # 最大回撤
                'min_sharpe_ratio': 0.5,  # 最低夏普比率
                'max_consecutive_losses': 5  # 最大连续亏损
            },
            'risk_management': {
                'max_position_size': 0.1,  # 最大仓位比例
                'max_daily_loss': -0.05,  # 最大日亏损
                'max_symbol_exposure': 0.2  # 最大单标的敞口
            }
        }
    
    async def start_monitoring(self):
        """开始业务监控"""
        if self.is_monitoring:
            logger.warning("业务监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("业务监控已启动")
    
    async def stop_monitoring(self):
        """停止业务监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("业务监控已停止")
    
    async def _monitoring_loop(self):
        """监控循环"""
        logger.info("开始业务监控循环")
        
        while self.is_monitoring:
            try:
                # 检查业务规则
                await self._check_business_rules()
                
                # 更新统计数据
                await self._update_statistics()
                
                # 等待下次检查
                await asyncio.sleep(30)  # 30秒检查一次
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"业务监控循环异常: {e}")
                await asyncio.sleep(30)
        
        logger.info("业务监控循环结束")
    
    async def record_trading_metrics(self, metrics: TradingMetrics):
        """记录交易指标"""
        self.trading_history.append(metrics)
        
        # 更新标的统计
        symbol = metrics.symbol
        if symbol not in self.symbol_stats:
            self.symbol_stats[symbol] = {
                'total_trades': 0,
                'total_wins': 0,
                'total_return': 0.0,
                'max_drawdown': 0.0,
                'last_update': datetime.now()
            }
        
        stats = self.symbol_stats[symbol]
        stats['total_trades'] += metrics.trade_count
        stats['total_wins'] += metrics.win_count
        stats['total_return'] += metrics.total_return
        stats['max_drawdown'] = min(stats['max_drawdown'], metrics.max_drawdown)
        stats['last_update'] = datetime.now()
        
        # 调用回调
        await self._notify_metrics_callbacks('trading', metrics)
        
        logger.debug(f"记录交易指标: {symbol} - 收益={metrics.total_return:.4f}")
    
    async def record_signal_quality(self, quality: SignalQuality):
        """记录信号质量"""
        self.signal_history.append(quality)
        
        # 调用回调
        await self._notify_metrics_callbacks('signal', quality)
        
        logger.debug(f"记录信号质量: {quality.symbol} - 准确率={quality.signal_accuracy:.2%}")
    
    async def _check_business_rules(self):
        """检查业务规则"""
        # 检查信号质量规则
        await self._check_signal_quality_rules()
        
        # 检查交易性能规则
        await self._check_trading_performance_rules()
        
        # 检查风险管理规则
        await self._check_risk_management_rules()
    
    async def _check_signal_quality_rules(self):
        """检查信号质量规则"""
        if not self.signal_history:
            return
        
        rules = self.business_rules['signal_quality']
        recent_signals = [s for s in self.signal_history 
                         if (datetime.now() - s.timestamp).total_seconds() < 3600]  # 最近1小时
        
        for symbol in set(s.symbol for s in recent_signals):
            symbol_signals = [s for s in recent_signals if s.symbol == symbol]
            if not symbol_signals:
                continue
            
            latest_signal = symbol_signals[-1]
            
            # 检查信号准确率
            if latest_signal.signal_accuracy < rules['min_accuracy']:
                await self._create_alert(
                    'signal_accuracy_low',
                    'warning',
                    symbol,
                    f"信号准确率过低: {latest_signal.signal_accuracy:.2%} < {rules['min_accuracy']:.2%}",
                    {'accuracy': latest_signal.signal_accuracy}
                )
            
            # 检查信号延迟
            if latest_signal.signal_latency_ms > rules['max_latency_ms']:
                await self._create_alert(
                    'signal_latency_high',
                    'warning',
                    symbol,
                    f"信号延迟过高: {latest_signal.signal_latency_ms:.0f}ms > {rules['max_latency_ms']}ms",
                    {'latency_ms': latest_signal.signal_latency_ms}
                )
            
            # 检查数据新鲜度
            if latest_signal.data_freshness_seconds > rules['max_data_age_seconds']:
                await self._create_alert(
                    'data_stale',
                    'critical',
                    symbol,
                    f"数据过期: {latest_signal.data_freshness_seconds:.0f}s > {rules['max_data_age_seconds']}s",
                    {'data_age_seconds': latest_signal.data_freshness_seconds}
                )
    
    async def _check_trading_performance_rules(self):
        """检查交易性能规则"""
        if not self.trading_history:
            return
        
        rules = self.business_rules['trading_performance']
        recent_trades = [t for t in self.trading_history 
                        if (datetime.now() - t.timestamp).total_seconds() < 86400]  # 最近24小时
        
        for symbol in set(t.symbol for t in recent_trades):
            symbol_trades = [t for t in recent_trades if t.symbol == symbol]
            if not symbol_trades:
                continue
            
            latest_trade = symbol_trades[-1]
            
            # 检查胜率
            if latest_trade.win_rate < rules['min_win_rate']:
                await self._create_alert(
                    'win_rate_low',
                    'warning',
                    symbol,
                    f"胜率过低: {latest_trade.win_rate:.2%} < {rules['min_win_rate']:.2%}",
                    {'win_rate': latest_trade.win_rate}
                )
            
            # 检查最大回撤
            if latest_trade.max_drawdown < rules['max_drawdown']:
                await self._create_alert(
                    'drawdown_high',
                    'critical',
                    symbol,
                    f"回撤过大: {latest_trade.max_drawdown:.2%} < {rules['max_drawdown']:.2%}",
                    {'max_drawdown': latest_trade.max_drawdown}
                )
            
            # 检查夏普比率
            if latest_trade.sharpe_ratio < rules['min_sharpe_ratio']:
                await self._create_alert(
                    'sharpe_ratio_low',
                    'warning',
                    symbol,
                    f"夏普比率过低: {latest_trade.sharpe_ratio:.2f} < {rules['min_sharpe_ratio']}",
                    {'sharpe_ratio': latest_trade.sharpe_ratio}
                )
    
    async def _check_risk_management_rules(self):
        """检查风险管理规则"""
        # 这里可以添加风险管理相关的检查
        # 例如：仓位大小、日亏损限制等
        pass
    
    async def _create_alert(self, alert_type: str, severity: str, symbol: str, 
                          message: str, metrics: Dict[str, Any]):
        """创建业务告警"""
        alert = BusinessAlert(
            timestamp=datetime.now(),
            alert_type=alert_type,
            severity=severity,
            symbol=symbol,
            message=message,
            metrics=metrics
        )
        
        self.alert_history.append(alert)
        
        # 记录日志
        log_level = logging.CRITICAL if severity == 'critical' else logging.WARNING
        logger.log(log_level, f"业务告警 [{severity.upper()}] {symbol}: {message}")
        
        # 调用告警回调
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                logger.error(f"业务告警回调执行失败: {e}")
    
    async def _notify_metrics_callbacks(self, metrics_type: str, metrics: Any):
        """通知指标回调"""
        for callback in self.metrics_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(metrics_type, metrics)
                else:
                    callback(metrics_type, metrics)
            except Exception as e:
                logger.error(f"业务指标回调执行失败: {e}")
    
    async def _update_statistics(self):
        """更新统计数据"""
        current_date = datetime.now().date().isoformat()
        
        # 更新日统计
        if current_date not in self.daily_stats:
            self.daily_stats[current_date] = {
                'total_signals': 0,
                'total_trades': 0,
                'total_return': 0.0,
                'alert_count': 0
            }
        
        # 统计今日数据
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_signals = [s for s in self.signal_history if s.timestamp >= today_start]
        today_trades = [t for t in self.trading_history if t.timestamp >= today_start]
        today_alerts = [a for a in self.alert_history if a.timestamp >= today_start]
        
        daily_stat = self.daily_stats[current_date]
        daily_stat['total_signals'] = len(today_signals)
        daily_stat['total_trades'] = sum(t.trade_count for t in today_trades)
        daily_stat['total_return'] = sum(t.total_return for t in today_trades)
        daily_stat['alert_count'] = len(today_alerts)
    
    def add_alert_callback(self, callback: Callable):
        """添加告警回调"""
        self.alert_callbacks.append(callback)
    
    def add_metrics_callback(self, callback: Callable):
        """添加指标回调"""
        self.metrics_callbacks.append(callback)
    
    def get_symbol_summary(self, symbol: str) -> Dict[str, Any]:
        """获取标的摘要"""
        if symbol not in self.symbol_stats:
            return {}
        
        stats = self.symbol_stats[symbol]
        recent_trades = [t for t in self.trading_history 
                        if t.symbol == symbol and 
                        (datetime.now() - t.timestamp).total_seconds() < 86400]
        
        recent_signals = [s for s in self.signal_history 
                         if s.symbol == symbol and 
                         (datetime.now() - s.timestamp).total_seconds() < 3600]
        
        return {
            'symbol': symbol,
            'total_trades': stats['total_trades'],
            'total_wins': stats['total_wins'],
            'win_rate': stats['total_wins'] / stats['total_trades'] if stats['total_trades'] > 0 else 0,
            'total_return': stats['total_return'],
            'max_drawdown': stats['max_drawdown'],
            'recent_trades_24h': len(recent_trades),
            'recent_signals_1h': len(recent_signals),
            'last_update': stats['last_update'].isoformat()
        }
    
    def get_business_summary(self) -> Dict[str, Any]:
        """获取业务摘要"""
        current_date = datetime.now().date().isoformat()
        today_stats = self.daily_stats.get(current_date, {})
        
        # 活跃告警
        recent_alerts = [a for a in self.alert_history 
                        if (datetime.now() - a.timestamp).total_seconds() < 3600]
        
        critical_alerts = [a for a in recent_alerts if a.severity == 'critical']
        warning_alerts = [a for a in recent_alerts if a.severity == 'warning']
        
        return {
            'monitoring_status': 'active' if self.is_monitoring else 'stopped',
            'monitored_symbols': len(self.symbol_stats),
            'today_stats': today_stats,
            'recent_alerts': {
                'total': len(recent_alerts),
                'critical': len(critical_alerts),
                'warning': len(warning_alerts)
            },
            'business_rules': self.business_rules
        }


# 测试函数
async def test_business_monitor():
    """测试业务监控器"""
    logger.info("开始测试业务监控器...")
    
    try:
        # 创建监控器
        monitor = BusinessMonitor(history_size=100)
        
        # 添加回调函数
        def alert_handler(alert):
            logger.info(f"业务告警: {alert.message}")
        
        def metrics_handler(metrics_type, metrics):
            logger.debug(f"业务指标 [{metrics_type}]: {metrics}")
        
        monitor.add_alert_callback(alert_handler)
        monitor.add_metrics_callback(metrics_handler)
        
        # 启动监控
        await monitor.start_monitoring()
        
        # 模拟交易指标
        trading_metrics = TradingMetrics(
            timestamp=datetime.now(),
            symbol="TEST",
            signal_strength=0.8,
            signal_count=10,
            trade_count=5,
            win_count=2,
            loss_count=3,
            total_return=0.02,
            max_drawdown=-0.05,
            sharpe_ratio=0.3,  # 低于阈值，会触发告警
            win_rate=0.4,
            avg_trade_duration=30.0
        )
        
        await monitor.record_trading_metrics(trading_metrics)
        
        # 模拟信号质量
        signal_quality = SignalQuality(
            timestamp=datetime.now(),
            symbol="TEST",
            signal_accuracy=0.5,  # 低于阈值，会触发告警
            signal_frequency=2.0,
            false_positive_rate=0.3,
            signal_latency_ms=500.0,
            data_freshness_seconds=100.0
        )
        
        await monitor.record_signal_quality(signal_quality)
        
        # 等待监控检查
        await asyncio.sleep(5)
        
        # 获取摘要
        summary = monitor.get_business_summary()
        symbol_summary = monitor.get_symbol_summary("TEST")
        
        logger.info(f"业务摘要: {summary}")
        logger.info(f"标的摘要: {symbol_summary}")
        
        # 停止监控
        await monitor.stop_monitoring()
        
        logger.info("✅ 业务监控器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 业务监控器测试失败: {e}")
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_business_monitor())
