"""
通知器模块
提供多种通知渠道的实现
"""

from .email_notifier import EmailNotifier
from .wechat_notifier import WeChatNotifier
from .qq_notifier import QQNotifier
from .webhook_notifier import WebhookNotifier
from .notifier_factory import NotifierFactory

__version__ = "1.0.0"
__author__ = "ETF Arbitrage System"

__all__ = [
    'EmailNotifier',
    'WeChatNotifier', 
    'QQNotifier',
    'WebhookNotifier',
    'NotifierFactory'
]
