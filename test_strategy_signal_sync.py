#!/usr/bin/env python3
"""
测试策略选择与信号生成同步性
验证实时交易面板的策略选择是否正确影响信号生成
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def test_strategy_signal_sync():
    """测试策略选择与信号生成的同步性"""
    print("🔍 策略选择与信号生成同步性测试")
    print("=" * 60)
    
    try:
        # 1. 测试策略管理器导入
        print("📋 测试1: 策略管理器导入")
        try:
            from etf_arbitrage_streamlit_multi.utils.trading_strategies import get_strategy_manager
            strategy_manager = get_strategy_manager()
            print(f"✅ 策略管理器导入成功")
            
            # 获取可用策略
            available_strategies = strategy_manager.list_strategies()
            print(f"✅ 可用策略数量: {len(available_strategies)}")
            for key, name in available_strategies.items():
                print(f"   - {key}: {name}")
                
        except ImportError as e:
            print(f"❌ 策略管理器导入失败: {e}")
            return False
        
        # 2. 测试实时交易器策略集成
        print("\n📋 测试2: 实时交易器策略集成")
        try:
            from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import enhanced_trader
            
            # 检查策略管理器是否正确初始化
            if enhanced_trader.strategy_manager:
                print("✅ 实时交易器策略管理器初始化成功")
                
                # 获取当前策略
                current_strategy = enhanced_trader.strategy_manager.get_current_strategy()
                if current_strategy:
                    print(f"✅ 当前策略: {current_strategy.name}")
                    print(f"   描述: {current_strategy.description}")
                else:
                    print("⚠️ 当前无选中策略")
            else:
                print("❌ 实时交易器策略管理器未初始化")
                return False
                
        except Exception as e:
            print(f"❌ 实时交易器策略集成测试失败: {e}")
            return False
        
        # 3. 测试策略切换同步
        print("\n📋 测试3: 策略切换同步")
        try:
            # 获取第一个策略
            strategy_keys = list(available_strategies.keys())
            if len(strategy_keys) >= 2:
                # 切换到第二个策略
                new_strategy_key = strategy_keys[1]
                print(f"🔄 切换策略到: {available_strategies[new_strategy_key]}")
                
                # 在策略管理器中切换
                if strategy_manager.set_current_strategy(new_strategy_key):
                    print("✅ 策略管理器切换成功")
                    
                    # 刷新交易器的策略管理器
                    if enhanced_trader.refresh_strategy_manager():
                        print("✅ 交易器策略管理器同步成功")
                        
                        # 验证同步结果
                        trader_current_strategy = enhanced_trader.strategy_manager.get_current_strategy()
                        if trader_current_strategy and trader_current_strategy.name == available_strategies[new_strategy_key]:
                            print(f"✅ 策略同步验证成功: {trader_current_strategy.name}")
                        else:
                            print("❌ 策略同步验证失败")
                            return False
                    else:
                        print("❌ 交易器策略管理器同步失败")
                        return False
                else:
                    print("❌ 策略管理器切换失败")
                    return False
            else:
                print("⚠️ 可用策略不足，跳过切换测试")
                
        except Exception as e:
            print(f"❌ 策略切换同步测试失败: {e}")
            return False
        
        # 4. 测试状态获取中的策略信息
        print("\n📋 测试4: 状态获取中的策略信息")
        try:
            status = enhanced_trader.get_status()
            
            if 'current_strategy' in status and status['current_strategy']:
                strategy_info = status['current_strategy']
                print(f"✅ 状态中包含策略信息:")
                print(f"   名称: {strategy_info['name']}")
                print(f"   描述: {strategy_info['description']}")
                print(f"   参数数量: {len(strategy_info['parameters'])}")
            else:
                print("❌ 状态中缺少策略信息")
                return False
                
        except Exception as e:
            print(f"❌ 状态获取测试失败: {e}")
            return False
        
        # 5. 测试信号生成方法
        print("\n📋 测试5: 信号生成方法")
        try:
            # 模拟tick数据
            test_tick_data = {
                'symbol': '159740',
                'price': 0.75,
                'volume': 1000,
                'timestamp': '2025-09-24 10:30:00'
            }
            
            # 测试信号生成（这里只测试方法调用，不期望实际信号）
            signal = enhanced_trader._generate_signal(test_tick_data)
            print(f"✅ 信号生成方法调用成功")
            
            if signal:
                print(f"   生成信号: {signal.signal_type}")
                print(f"   信号原因: {signal.reason}")
                # 检查是否使用了策略管理器
                if "[内置策略]" not in signal.reason:
                    print("✅ 信号来自策略管理器")
                else:
                    print("⚠️ 信号来自内置策略（备用方案）")
            else:
                print("   无信号生成（正常情况）")
                
        except Exception as e:
            print(f"❌ 信号生成测试失败: {e}")
            return False
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！策略选择与信号生成同步性正常")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_strategy_signal_sync()
    if success:
        print("\n✅ 修复验证成功：策略选择现在正确影响信号生成")
    else:
        print("\n❌ 修复验证失败：仍存在同步问题")
