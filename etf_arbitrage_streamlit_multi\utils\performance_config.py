#!/usr/bin/env python3
"""
性能配置管理模块
提供用户友好的性能优化配置界面
"""

import streamlit as st
from typing import Dict, Any

class PerformanceConfig:
    """性能配置管理器"""
    
    def __init__(self):
        self.default_config = {
            'max_chart_points': 2000,
            'max_trade_points': 200,
            'enable_sampling': True,
            'sampling_strategy': 'smart',
            'preserve_extremes': True,
            'volatility_threshold': 0.8,
            'enable_scattergl': True,
            'chart_height': 600,
            'show_optimization_info': True
        }
    
    def render_config_panel(self) -> Dict[str, Any]:
        """渲染性能配置面板"""
        st.markdown("### 📊 数据采样配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 预设模式选择
            preset_mode = st.selectbox(
                "性能模式",
                options=["极速模式", "平衡模式", "详细模式", "自定义"],
                index=1,  # 默认平衡模式
                help="选择预设的性能配置模式"
            )
            
            # 根据预设模式设置参数
            if preset_mode == "极速模式":
                max_points = 500
                sampling_strategy = "uniform"
                use_webgl = True
                chart_height = 400
            elif preset_mode == "平衡模式":
                max_points = 2000
                sampling_strategy = "smart"
                use_webgl = True
                chart_height = 600
            elif preset_mode == "详细模式":
                max_points = 5000
                sampling_strategy = "adaptive"
                use_webgl = False
                chart_height = 800
            else:  # 自定义模式
                max_points = st.slider(
                    "最大数据点数",
                    min_value=100,
                    max_value=10000,
                    value=2000,
                    step=100,
                    help="图表显示的最大数据点数，越少加载越快"
                )
                
                sampling_strategy = st.selectbox(
                    "采样策略",
                    options=['smart', 'uniform', 'adaptive'],
                    index=0,
                    help="smart: 智能采样保留关键点; uniform: 均匀采样; adaptive: 自适应采样"
                )
                
                use_webgl = st.checkbox(
                    "启用WebGL渲染",
                    value=True,
                    help="WebGL可以提升大数据集的渲染性能"
                )
                
                chart_height = st.slider(
                    "图表高度",
                    min_value=300,
                    max_value=1000,
                    value=600,
                    step=50,
                    help="图表的显示高度（像素）"
                )
        
        with col2:
            # 高级设置
            st.markdown("**高级设置**")
            
            preserve_extremes = st.checkbox(
                "保留极值点",
                value=True,
                help="确保价格的最高点和最低点不被采样掉"
            )
            
            show_info = st.checkbox(
                "显示优化信息",
                value=True,
                help="在图表上显示数据优化的统计信息"
            )
            
            # 性能预估
            if preset_mode != "自定义":
                st.markdown("**性能预估**")
                if preset_mode == "极速模式":
                    st.success("🚀 最快加载速度")
                elif preset_mode == "平衡模式":
                    st.info("⚖️ 性能与细节平衡")
                else:
                    st.warning("🔍 详细分析模式")
        
        # 返回配置
        config = {
            'max_points': max_points,
            'sampling_strategy': sampling_strategy,
            'preserve_extremes': preserve_extremes,
            'use_webgl': use_webgl,
            'chart_height': chart_height,
            'show_optimization_info': show_info,
            'preset_mode': preset_mode
        }
        
        return config

def performance_config():
    """性能配置界面函数"""
    config_manager = PerformanceConfig()
    return config_manager.render_config_panel()

# 便捷函数
def get_default_performance_config():
    """获取默认性能配置"""
    return {
        'max_points': 2000,
        'sampling_strategy': 'smart',
        'preserve_extremes': True,
        'use_webgl': True,
        'chart_height': 600,
        'show_optimization_info': True,
        'preset_mode': '平衡模式'
    }

def validate_performance_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """验证和修正性能配置"""
    default_config = get_default_performance_config()
    
    # 确保所有必需的键都存在
    for key, default_value in default_config.items():
        if key not in config:
            config[key] = default_value
    
    # 验证数值范围
    config['max_points'] = max(100, min(10000, config.get('max_points', 2000)))
    config['chart_height'] = max(300, min(1000, config.get('chart_height', 600)))
    
    return config