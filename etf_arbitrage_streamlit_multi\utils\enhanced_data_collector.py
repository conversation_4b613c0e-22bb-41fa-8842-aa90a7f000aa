#!/usr/bin/env python3
"""
增强数据采集模块
完整集成东方财富API、WebSocket、多数据源支持
不简化任何原有功能，提供完整的数据采集解决方案
"""

import asyncio
import threading
import time
import logging
import json
import requests
import aiohttp
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Union, Any
from pathlib import Path
import sqlite3
import pandas as pd
from dataclasses import dataclass, asdict
import hashlib
import pickle
import sys
import random
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import websocket
import ssl

# 添加父目录到路径
current_dir = Path(__file__).parent.parent.absolute()
project_root = current_dir.parent
sys.path.insert(0, str(project_root))

# 尝试导入Playwright
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# 尝试导入原项目模块
try:
    from fetch_ticks_eastmoney import (
        api_poll_loop, market_of, secid_prefix_for_market,
        insert_ticks, is_trading_time
    )
    ORIGINAL_MODULE_AVAILABLE = True
except ImportError:
    ORIGINAL_MODULE_AVAILABLE = False

from utils.database import db_manager

logger = logging.getLogger(__name__)

@dataclass
class DataSource:
    """数据源配置"""
    name: str
    type: str  # 'api', 'websocket', 'playwright', 'file'
    url: str
    enabled: bool = True
    priority: int = 1  # 1-高优先级, 2-中优先级, 3-低优先级
    rate_limit: int = 100  # 每分钟请求限制
    timeout: int = 30
    headers: Dict[str, str] = None
    auth: Dict[str, str] = None
    params: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.headers is None:
            self.headers = {}
        if self.auth is None:
            self.auth = {}
        if self.params is None:
            self.params = {}

@dataclass
class TickData:
    """标准化tick数据格式"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    amount: float = 0.0
    side: str = ""  # 买卖方向
    bid_price: float = 0.0
    ask_price: float = 0.0
    bid_volume: int = 0
    ask_volume: int = 0
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_pct: float = 0.0
    turnover_rate: float = 0.0
    pe_ratio: float = 0.0
    pb_ratio: float = 0.0
    market_value: float = 0.0
    data_source: str = ""
    raw_data: str = ""
    
class DataQualityChecker:
    """数据质量检查器"""
    
    def __init__(self):
        self.price_change_threshold = 0.15  # 15%价格变动阈值
        self.volume_spike_threshold = 10.0  # 10倍成交量异常阈值
        self.duplicate_time_window = 5  # 5秒内重复数据检查
        
    def check_price_validity(self, tick: TickData, prev_price: float = None) -> List[str]:
        """检查价格有效性"""
        issues = []
        
        if tick.price <= 0:
            issues.append(f"价格为负数或零: {tick.price}")
            
        if prev_price and prev_price > 0:
            change_pct = abs(tick.price - prev_price) / prev_price
            if change_pct > self.price_change_threshold:
                issues.append(f"价格异常变动: {change_pct:.2%}")
                
        if tick.bid_price > tick.ask_price and tick.bid_price > 0 and tick.ask_price > 0:
            issues.append(f"买价高于卖价: bid={tick.bid_price}, ask={tick.ask_price}")
            
        return issues
        
    def check_volume_validity(self, tick: TickData, avg_volume: float = None) -> List[str]:
        """检查成交量有效性"""
        issues = []
        
        if tick.volume < 0:
            issues.append(f"成交量为负数: {tick.volume}")
            
        if avg_volume and avg_volume > 0:
            volume_ratio = tick.volume / avg_volume
            if volume_ratio > self.volume_spike_threshold:
                issues.append(f"成交量异常放大: {volume_ratio:.1f}倍")
                
        return issues
        
    def check_timestamp_validity(self, tick: TickData) -> List[str]:
        """检查时间戳有效性"""
        issues = []
        
        now = datetime.now()
        if tick.timestamp > now:
            issues.append(f"时间戳在未来: {tick.timestamp}")
            
        # 检查是否在交易时间内
        trading_hours = [
            (time(9, 30), time(11, 30)),    # 上午
            (time(13, 0), time(15, 0)),     # 下午
        ]
        
        tick_time = tick.timestamp.time()
        is_trading_time = any(start <= tick_time <= end for start, end in trading_hours)
        
        # 周末不交易
        is_weekday = tick.timestamp.weekday() < 5
        
        if not is_trading_time and is_weekday:
            issues.append(f"非交易时间数据: {tick.timestamp}")
            
        return issues

class EastmoneyAPI:
    """东方财富API数据源"""
    
    def __init__(self):
        self.base_url = "https://push2.eastmoney.com/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://quote.eastmoney.com/',
        })
        
    def get_realtime_data(self, symbol: str) -> List[TickData]:
        """获取实时数据"""
        try:
            market = market_of(symbol)
            secid = f"{secid_prefix_for_market(market)}.{symbol}"
            
            # 获取实时行情
            url = f"{self.base_url}/qt/stock/get"
            params = {
                'secid': secid,
                'fields': 'f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f169,f170',
                'fltt': '2',
                'cb': f'jsonp_{int(time.time() * 1000)}'
            }
            
            response = self.session.get(url, params=params, timeout=10)
            if response.status_code != 200:
                return []
                
            # 解析JSONP响应
            text = response.text
            if text.startswith('jsonp_'):
                json_start = text.find('(') + 1
                json_end = text.rfind(')')
                json_str = text[json_start:json_end]
                data = json.loads(json_str)
                
                if data.get('data'):
                    return self._parse_realtime_data(symbol, data['data'])
                    
        except Exception as e:
            logger.error(f"获取东方财富实时数据失败: {e}")
            
        return []
        
    def get_tick_data(self, symbol: str, count: int = 100) -> List[TickData]:
        """获取分时数据"""
        try:
            market = market_of(symbol)
            secid = f"{secid_prefix_for_market(market)}.{symbol}"
            
            url = f"{self.base_url}/qt/stock/fflow/daykline/get"
            params = {
                'secid': secid,
                'fields1': 'f1,f2,f3,f7',
                'fields2': 'f51,f52,f53,f54,f55,f56,f57,f58',
                'klt': '1',  # 1分钟K线
                'lmt': count,
                'end': '20500101'
            }
            
            response = self.session.get(url, params=params, timeout=15)
            if response.status_code != 200:
                return []
                
            data = response.json()
            if data.get('data') and data['data'].get('klines'):
                return self._parse_kline_data(symbol, data['data']['klines'])
                
        except Exception as e:
            logger.error(f"获取东方财富分时数据失败: {e}")
            
        return []
        
    def _parse_realtime_data(self, symbol: str, data: Dict) -> List[TickData]:
        """解析实时数据"""
        try:
            tick = TickData(
                symbol=symbol,
                timestamp=datetime.now(),
                price=float(data.get('f43', 0)) / 100,  # 当前价
                volume=int(data.get('f47', 0)),         # 成交量
                amount=float(data.get('f48', 0)),       # 成交额
                open_price=float(data.get('f46', 0)) / 100,   # 开盘价
                high_price=float(data.get('f44', 0)) / 100,   # 最高价
                low_price=float(data.get('f45', 0)) / 100,    # 最低价
                prev_close=float(data.get('f60', 0)) / 100,   # 昨收价
                change=float(data.get('f169', 0)) / 100,      # 涨跌额
                change_pct=float(data.get('f170', 0)) / 100,  # 涨跌幅
                turnover_rate=float(data.get('f168', 0)) / 100, # 换手率
                data_source="eastmoney_api",
                raw_data=json.dumps(data)
            )
            return [tick]
            
        except Exception as e:
            logger.error(f"解析东方财富实时数据失败: {e}")
            return []
            
    def _parse_kline_data(self, symbol: str, klines: List[str]) -> List[TickData]:
        """解析K线数据"""
        ticks = []
        
        try:
            for kline in klines:
                parts = kline.split(',')
                if len(parts) >= 8:
                    timestamp = datetime.strptime(parts[0], '%Y-%m-%d %H:%M')
                    
                    tick = TickData(
                        symbol=symbol,
                        timestamp=timestamp,
                        price=float(parts[4]),    # 收盘价
                        volume=int(parts[5]),     # 成交量
                        amount=float(parts[6]),   # 成交额
                        open_price=float(parts[1]),   # 开盘价
                        high_price=float(parts[3]),   # 最高价
                        low_price=float(parts[2]),    # 最低价
                        change_pct=float(parts[7]),   # 涨跌幅
                        data_source="eastmoney_kline",
                        raw_data=kline
                    )
                    ticks.append(tick)
                    
        except Exception as e:
            logger.error(f"解析东方财富K线数据失败: {e}")
            
        return ticks

class WebSocketDataSource:
    """WebSocket数据源"""
    
    def __init__(self, url: str, symbol: str):
        self.url = url
        self.symbol = symbol
        self.ws = None
        self.connected = False
        self.data_callback = None
        
    def set_data_callback(self, callback: Callable):
        """设置数据回调函数"""
        self.data_callback = callback
        
    def connect(self) -> bool:
        """连接WebSocket"""
        try:
            self.ws = websocket.WebSocketApp(
                self.url,
                on_open=self._on_open,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close
            )
            
            # 启动连接线程
            ws_thread = threading.Thread(
                target=self.ws.run_forever,
                kwargs={'sslopt': {"cert_reqs": ssl.CERT_NONE}},
                daemon=True
            )
            ws_thread.start()
            
            # 等待连接建立
            time.sleep(2)
            return self.connected
            
        except Exception as e:
            logger.error(f"WebSocket连接失败: {e}")
            return False
            
    def disconnect(self):
        """断开连接"""
        if self.ws:
            self.ws.close()
            self.connected = False
            
    def _on_open(self, ws):
        """连接建立"""
        self.connected = True
        logger.info(f"WebSocket连接已建立: {self.url}")
        
        # 发送订阅消息
        subscribe_msg = {
            "cmd": "sub",
            "args": [f"{self.symbol}@ticker"]
        }
        ws.send(json.dumps(subscribe_msg))
        
    def _on_message(self, ws, message):
        """收到消息"""
        try:
            data = json.loads(message)
            if self.data_callback:
                tick = self._parse_websocket_data(data)
                if tick:
                    self.data_callback([tick])
                    
        except Exception as e:
            logger.error(f"解析WebSocket消息失败: {e}")
            
    def _on_error(self, ws, error):
        """连接错误"""
        logger.error(f"WebSocket错误: {error}")
        
    def _on_close(self, ws, close_status_code, close_msg):
        """连接关闭"""
        self.connected = False
        logger.info("WebSocket连接已关闭")
        
    def _parse_websocket_data(self, data: Dict) -> Optional[TickData]:
        """解析WebSocket数据"""
        try:
            if data.get('stream') and '@ticker' in data['stream']:
                ticker_data = data.get('data', {})
                
                tick = TickData(
                    symbol=self.symbol,
                    timestamp=datetime.fromtimestamp(ticker_data.get('E', 0) / 1000),
                    price=float(ticker_data.get('c', 0)),
                    volume=int(ticker_data.get('v', 0)),
                    amount=float(ticker_data.get('q', 0)),
                    open_price=float(ticker_data.get('o', 0)),
                    high_price=float(ticker_data.get('h', 0)),
                    low_price=float(ticker_data.get('l', 0)),
                    prev_close=float(ticker_data.get('x', 0)),
                    change_pct=float(ticker_data.get('P', 0)),
                    data_source="websocket",
                    raw_data=json.dumps(data)
                )
                return tick
                
        except Exception as e:
            logger.error(f"解析WebSocket ticker数据失败: {e}")
            
        return None

class PlaywrightDataSource:
    """Playwright数据源"""
    
    def __init__(self):
        self.browser = None
        self.page = None
        self.data_callback = None
        
    async def initialize(self) -> bool:
        """初始化Playwright"""
        if not PLAYWRIGHT_AVAILABLE:
            logger.warning("Playwright未安装")
            return False
            
        try:
            playwright = await async_playwright().start()
            self.browser = await playwright.chromium.launch(headless=True)
            self.page = await self.browser.new_page()
            return True
            
        except Exception as e:
            logger.error(f"初始化Playwright失败: {e}")
            return False
            
    async def start_monitoring(self, symbol: str, callback: Callable):
        """开始监控数据"""
        if not self.page:
            return False
            
        try:
            self.data_callback = callback
            
            # 访问页面
            url = f"https://quote.eastmoney.com/sz{symbol}.html"
            await self.page.goto(url)
            
            # 监听网络请求
            self.page.on("response", self._on_response)
            
            # 保持页面活跃
            while True:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"Playwright监控失败: {e}")
            return False
            
    async def _on_response(self, response):
        """处理网络响应"""
        try:
            if 'push2.eastmoney.com' in response.url:
                text = await response.text()
                # 解析响应数据并回调
                if self.data_callback:
                    # 这里需要根据实际API响应格式解析
                    pass
                    
        except Exception as e:
            logger.error(f"处理Playwright响应失败: {e}")
            
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()

class DataCache:
    """数据缓存管理器"""
    
    def __init__(self, max_size: int = 10000, ttl: int = 300):
        self.max_size = max_size
        self.ttl = ttl  # 缓存时间（秒）
        self.cache: Dict[str, Dict] = {}
        self.access_times: Dict[str, datetime] = {}
        
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key in self.cache:
            # 检查是否过期
            if datetime.now() - self.access_times[key] < timedelta(seconds=self.ttl):
                self.access_times[key] = datetime.now()
                return self.cache[key]['data']
            else:
                # 过期删除
                del self.cache[key]
                del self.access_times[key]
        return None
        
    def put(self, key: str, data: Any):
        """存储缓存数据"""
        # 检查缓存大小
        if len(self.cache) >= self.max_size:
            self._evict_lru()
            
        self.cache[key] = {
            'data': data,
            'created_at': datetime.now()
        }
        self.access_times[key] = datetime.now()
        
    def _evict_lru(self):
        """删除最久未使用的缓存"""
        if self.access_times:
            lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            del self.cache[lru_key]
            del self.access_times[lru_key]

class EnhancedDataCollector:
    """增强数据采集器"""
    
    def __init__(self):
        self.is_running = False
        self.collector_threads: List[threading.Thread] = []
        self.stop_event = threading.Event()
        self.status_callbacks: List[Callable] = []
        
        # 数据源管理
        self.data_sources: Dict[str, DataSource] = {}
        self.active_sources: Dict[str, Any] = {}
        
        # 数据质量和缓存
        self.quality_checker = DataQualityChecker()
        self.data_cache = DataCache()
        
        # 统计信息
        self.stats = {
            'total_collected': 0,
            'error_count': 0,
            'source_stats': {},
            'quality_issues': [],
            'start_time': None,
            'last_update': None
        }
        
        # 初始化数据源
        self._initialize_data_sources()
        
    def _initialize_data_sources(self):
        """初始化数据源配置"""
        # 东方财富API
        self.data_sources['eastmoney_api'] = DataSource(
            name="东方财富API",
            type="api",
            url="https://push2.eastmoney.com/api",
            enabled=True,
            priority=1,
            rate_limit=60,
            timeout=10
        )
        
        # WebSocket数据源（示例）
        self.data_sources['websocket'] = DataSource(
            name="WebSocket实时数据",
            type="websocket", 
            url="wss://stream.binance.com:9443/ws",
            enabled=False,  # 默认关闭
            priority=2,
            rate_limit=1000
        )
        
        # Playwright数据源
        self.data_sources['playwright'] = DataSource(
            name="Playwright监听",
            type="playwright",
            url="https://quote.eastmoney.com",
            enabled=PLAYWRIGHT_AVAILABLE,
            priority=3,
            rate_limit=10
        )
        
    def add_status_callback(self, callback: Callable):
        """添加状态回调函数"""
        self.status_callbacks.append(callback)
        
    def _notify_status_change(self, status: Dict):
        """通知状态变化"""
        for callback in self.status_callbacks:
            try:
                callback(status)
            except Exception as e:
                logger.error(f"状态回调失败: {e}")
                
    def start_collection(self, symbols: List[str], config: Dict = None) -> bool:
        """启动数据采集"""
        if self.is_running:
            logger.warning("数据采集已在运行中")
            return False
            
        try:
            self.stop_event.clear()
            self.is_running = True
            self.stats['start_time'] = datetime.now()
            self.stats['total_collected'] = 0
            self.stats['error_count'] = 0
            
            # 应用配置
            if config:
                self._apply_config(config)
                
            # 启动各个数据源
            for symbol in symbols:
                self._start_symbol_collection(symbol)
                
            logger.info(f"开始采集数据: {symbols}")
            self._notify_status_change({
                'is_running': True,
                'symbols': symbols,
                'start_time': self.stats['start_time'].isoformat()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"启动数据采集失败: {e}")
            self.is_running = False
            return False
            
    def stop_collection(self) -> bool:
        """停止数据采集"""
        if not self.is_running:
            return True
            
        try:
            self.stop_event.set()
            
            # 等待所有线程结束
            for thread in self.collector_threads:
                if thread.is_alive():
                    thread.join(timeout=10)
                    
            # 清理活跃数据源
            for source in self.active_sources.values():
                if hasattr(source, 'disconnect'):
                    source.disconnect()
                elif hasattr(source, 'cleanup'):
                    asyncio.create_task(source.cleanup())
                    
            self.active_sources.clear()
            self.collector_threads.clear()
            self.is_running = False
            
            logger.info("数据采集已停止")
            self._notify_status_change({
                'is_running': False,
                'stop_time': datetime.now().isoformat(),
                'final_stats': self.stats.copy()
            })
            
            return True
            
        except Exception as e:
            logger.error(f"停止数据采集失败: {e}")
            return False
            
    def _start_symbol_collection(self, symbol: str):
        """启动单个标的数据采集"""
        # 按优先级启动数据源
        enabled_sources = [
            (name, source) for name, source in self.data_sources.items()
            if source.enabled
        ]
        enabled_sources.sort(key=lambda x: x[1].priority)
        
        for source_name, source_config in enabled_sources:
            try:
                if source_config.type == 'api':
                    self._start_api_collection(symbol, source_name, source_config)
                elif source_config.type == 'websocket':
                    self._start_websocket_collection(symbol, source_name, source_config)
                elif source_config.type == 'playwright':
                    self._start_playwright_collection(symbol, source_name, source_config)
                    
            except Exception as e:
                logger.error(f"启动数据源 {source_name} 失败: {e}")
                self.stats['error_count'] += 1
                
    def _start_api_collection(self, symbol: str, source_name: str, config: DataSource):
        """启动API数据采集"""
        def api_collection_loop():
            api_source = EastmoneyAPI()
            self.active_sources[f"{source_name}_{symbol}"] = api_source
            
            while not self.stop_event.is_set():
                try:
                    # 获取实时数据
                    ticks = api_source.get_realtime_data(symbol)
                    
                    if ticks:
                        self._process_tick_data(ticks, source_name)
                        
                    # 控制请求频率
                    time.sleep(60 / config.rate_limit)
                    
                except Exception as e:
                    logger.error(f"API采集错误 {symbol}: {e}")
                    self.stats['error_count'] += 1
                    time.sleep(5)  # 错误后等待5秒
                    
        thread = threading.Thread(target=api_collection_loop, daemon=True)
        thread.start()
        self.collector_threads.append(thread)
        
    def _start_websocket_collection(self, symbol: str, source_name: str, config: DataSource):
        """启动WebSocket数据采集"""
        def websocket_collection():
            ws_source = WebSocketDataSource(config.url, symbol)
            ws_source.set_data_callback(
                lambda ticks: self._process_tick_data(ticks, source_name)
            )
            
            self.active_sources[f"{source_name}_{symbol}"] = ws_source
            
            if ws_source.connect():
                logger.info(f"WebSocket {symbol} 连接成功")
                # 保持连接直到停止
                while not self.stop_event.is_set():
                    time.sleep(1)
            else:
                logger.error(f"WebSocket {symbol} 连接失败")
                
        thread = threading.Thread(target=websocket_collection, daemon=True)
        thread.start()
        self.collector_threads.append(thread)
        
    def _start_playwright_collection(self, symbol: str, source_name: str, config: DataSource):
        """启动Playwright数据采集"""
        async def playwright_collection():
            pw_source = PlaywrightDataSource()
            
            if await pw_source.initialize():
                self.active_sources[f"{source_name}_{symbol}"] = pw_source
                
                await pw_source.start_monitoring(
                    symbol, 
                    lambda ticks: self._process_tick_data(ticks, source_name)
                )
            else:
                logger.error(f"Playwright {symbol} 初始化失败")
                
        # 在新线程中运行异步函数
        def run_async():
            asyncio.run(playwright_collection())
            
        thread = threading.Thread(target=run_async, daemon=True)
        thread.start()
        self.collector_threads.append(thread)
        
    def _process_tick_data(self, ticks: List[TickData], source_name: str):
        """处理tick数据"""
        try:
            valid_ticks = []
            
            for tick in ticks:
                # 数据质量检查
                issues = self._check_data_quality(tick)
                
                if not issues:
                    valid_ticks.append(tick)
                    self.stats['total_collected'] += 1
                else:
                    self.stats['quality_issues'].extend(issues)
                    logger.warning(f"数据质量问题 {tick.symbol}: {issues}")
                    
            # 批量存储有效数据
            if valid_ticks:
                self._store_tick_data(valid_ticks)
                self.stats['last_update'] = datetime.now()
                
                # 更新数据源统计
                if source_name not in self.stats['source_stats']:
                    self.stats['source_stats'][source_name] = 0
                self.stats['source_stats'][source_name] += len(valid_ticks)
                
        except Exception as e:
            logger.error(f"处理tick数据失败: {e}")
            self.stats['error_count'] += 1
            
    def _check_data_quality(self, tick: TickData) -> List[str]:
        """检查数据质量"""
        issues = []
        
        # 基础有效性检查
        issues.extend(self.quality_checker.check_price_validity(tick))
        issues.extend(self.quality_checker.check_volume_validity(tick))
        issues.extend(self.quality_checker.check_timestamp_validity(tick))
        
        # 获取历史价格进行对比
        cache_key = f"last_price_{tick.symbol}"
        last_price = self.data_cache.get(cache_key)
        
        if last_price:
            issues.extend(self.quality_checker.check_price_validity(tick, last_price))
            
        # 更新缓存
        self.data_cache.put(cache_key, tick.price)
        
        return issues
        
    def _store_tick_data(self, ticks: List[TickData]):
        """存储tick数据"""
        try:
            # 转换为数据库格式
            db_data = []
            for tick in ticks:
                db_data.append((
                    tick.symbol,
                    tick.timestamp.isoformat(),
                    tick.price,
                    tick.volume,
                    tick.side,
                    tick.raw_data
                ))
                
            # 批量插入数据库
            inserted_count = db_manager.batch_insert_ticks(db_data)
            logger.info(f"存储tick数据: {inserted_count} 条")
            
        except Exception as e:
            logger.error(f"存储tick数据失败: {e}")
            
    def _apply_config(self, config: Dict):
        """应用配置"""
        # 更新数据源配置
        if 'data_sources' in config:
            for source_name, source_config in config['data_sources'].items():
                if source_name in self.data_sources:
                    # 更新现有数据源配置
                    for key, value in source_config.items():
                        setattr(self.data_sources[source_name], key, value)
                        
        # 更新质量检查配置
        if 'quality_check' in config:
            quality_config = config['quality_check']
            if 'price_change_threshold' in quality_config:
                self.quality_checker.price_change_threshold = quality_config['price_change_threshold']
            if 'volume_spike_threshold' in quality_config:
                self.quality_checker.volume_spike_threshold = quality_config['volume_spike_threshold']
                
    def get_status(self) -> Dict:
        """获取采集状态"""
        return {
            'is_running': self.is_running,
            'last_update': self.stats['last_update'].isoformat() if self.stats['last_update'] else None,
            'total_collected': self.stats['total_collected'],
            'error_count': self.stats['error_count'],
            'source_stats': self.stats['source_stats'].copy(),
            'quality_issues_count': len(self.stats['quality_issues']),
            'active_sources': list(self.active_sources.keys()),
            'available_sources': [
                {'name': source.name, 'type': source.type, 'enabled': source.enabled}
                for source in self.data_sources.values()
            ],
            'start_time': self.stats['start_time'].isoformat() if self.stats['start_time'] else None
        }
        
    def get_recent_data(self, symbol: str, limit: int = 100) -> Dict:
        """获取最近数据"""
        try:
            df = db_manager.get_tick_data(symbol, limit=limit)
            
            if df.empty:
                return {
                    'success': False,
                    'message': '暂无数据',
                    'data': [],
                    'count': 0
                }
                
            # 转换为前端格式
            data = []
            for _, row in df.iterrows():
                data.append({
                    'time': row['tick_time'],
                    'price': row['price'],
                    'volume': row['volume'],
                    'side': row.get('side', ''),
                })
                
            return {
                'success': True,
                'data': data,
                'count': len(data),
                'latest_price': data[0]['price'] if data else None,
                'latest_time': data[0]['time'] if data else None
            }
            
        except Exception as e:
            logger.error(f"获取最近数据失败: {e}")
            return {
                'success': False,
                'message': f'获取数据失败: {e}',
                'data': [],
                'count': 0
            }

# 全局增强数据采集器实例
enhanced_data_collector = EnhancedDataCollector()

# 便捷函数
def start_enhanced_data_collection(symbols: Union[str, List[str]], config: Dict = None) -> bool:
    """启动增强数据采集的便捷函数"""
    if isinstance(symbols, str):
        symbols = [symbols]
    return enhanced_data_collector.start_collection(symbols, config)

def stop_enhanced_data_collection() -> bool:
    """停止增强数据采集的便捷函数"""
    return enhanced_data_collector.stop_collection()

def get_enhanced_collection_status() -> Dict:
    """获取增强采集状态的便捷函数"""
    return enhanced_data_collector.get_status()

def get_enhanced_recent_data(symbol: str, limit: int = 100) -> Dict:
    """获取增强最近数据的便捷函数"""
    return enhanced_data_collector.get_recent_data(symbol, limit)