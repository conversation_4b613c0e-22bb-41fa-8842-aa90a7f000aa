#!/usr/bin/env python3
"""
安全的图表生成模块
解决Streamlit前端JavaScript模块加载问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from typing import Dict, Optional, Any

def safe_plotly_chart(fig: go.Figure, **kwargs):
    """安全的Plotly图表显示"""
    try:
        # 设置最简化的配置以避免JavaScript错误
        config = {
            'displayModeBar': False,  # 完全禁用工具栏避免JavaScript问题
            'displaylogo': False,
            'staticPlot': False  # 保持交互性但简化功能
        }
        
        # 更新图表配置 - 使用最安全的设置
        fig.update_layout(
            template='plotly_white',
            showlegend=True,
            hovermode='closest'
            # 移除所有可能导致问题的dragmode和selectdirection设置
        )
        
        # 使用Streamlit显示图表
        st.plotly_chart(fig, width='stretch', config=config, **kwargs)
        
    except Exception as e:
        st.error(f"图表显示失败: {e}")
        st.info("尝试使用备用显示方式...")
        
        # 备用方案：显示简化版本
        try:
            simple_fig = go.Figure()
            simple_fig.add_annotation(
                text=f"图表显示异常，请检查数据格式",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False,
                font=dict(size=14, color="red")
            )
            simple_fig.update_layout(
                template='plotly_white',
                height=300
            )
            st.plotly_chart(simple_fig, width='stretch', config={'displayModeBar': False})
        except Exception as backup_error:
            st.warning(f"图表显示完全失败: {backup_error}")
            st.info("请尝试刷新页面或检查数据")

def create_safe_equity_curve(equity_data: pd.Series, title: str = "净值曲线") -> go.Figure:
    """创建安全的净值曲线图"""
    try:
        if equity_data is None or equity_data.empty:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无净值数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )
            return fig
        
        # 确保数据类型正确
        equity_data = equity_data.dropna()
        
        fig = go.Figure()
        
        # 添加净值曲线
        fig.add_trace(
            go.Scatter(
                x=equity_data.index,
                y=equity_data.values,
                mode='lines',
                name='净值',
                line=dict(color='blue', width=2),
                hovertemplate='时间: %{x}<br>净值: %{y:,.2f}<extra></extra>'
            )
        )
        
        # 添加基准线
        initial_value = float(equity_data.iloc[0]) if len(equity_data) > 0 else 100000
        fig.add_hline(
            y=initial_value,
            line_dash="dash",
            line_color="gray",
            annotation_text="初始净值"
        )
        
        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="净值",
            height=400,
            template='plotly_white'
            # 移除可能导致问题的dragmode和selectdirection
        )
        
        return fig
        
    except Exception as e:
        st.error(f"净值曲线生成失败: {e}")
        return go.Figure()

def create_safe_trade_analysis(trades_df: pd.DataFrame) -> go.Figure:
    """创建安全的交易分析图"""
    try:
        if trades_df is None or trades_df.empty:
            fig = go.Figure()
            fig.add_annotation(
                text="暂无交易数据",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )
            return fig
        
        # 确保必要的列存在
        required_cols = ['pnl']
        if not all(col in trades_df.columns for col in required_cols):
            fig = go.Figure()
            fig.add_annotation(
                text="交易数据格式不正确",
                xref="paper", yref="paper",
                x=0.5, y=0.5, showarrow=False
            )
            return fig
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('盈亏分布', '累计盈亏', '交易频率', '胜率统计'),
            specs=[[{"type": "histogram"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "pie"}]]
        )
        
        # 盈亏分布直方图
        pnl_data = trades_df['pnl'].dropna()
        if not pnl_data.empty:
            fig.add_trace(
                go.Histogram(
                    x=pnl_data,
                    name='盈亏分布',
                    nbinsx=20,
                    marker_color='lightblue'
                ),
                row=1, col=1
            )
        
        # 累计盈亏
        cumulative_pnl = pnl_data.cumsum()
        if not cumulative_pnl.empty:
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(cumulative_pnl))),
                    y=cumulative_pnl,
                    mode='lines',
                    name='累计盈亏',
                    line=dict(color='green', width=2)
                ),
                row=1, col=2
            )
        
        # 交易频率（按日期）
        if 'time' in trades_df.columns:
            try:
                trades_df['date'] = pd.to_datetime(trades_df['time']).dt.date
                daily_trades = trades_df.groupby('date').size()
                
                fig.add_trace(
                    go.Bar(
                        x=daily_trades.index,
                        y=daily_trades.values,
                        name='每日交易次数',
                        marker_color='orange'
                    ),
                    row=2, col=1
                )
            except:
                pass
        
        # 胜率统计
        profitable_trades = len(pnl_data[pnl_data > 0])
        losing_trades = len(pnl_data[pnl_data <= 0])
        
        if profitable_trades + losing_trades > 0:
            fig.add_trace(
                go.Pie(
                    labels=['盈利', '亏损'],
                    values=[profitable_trades, losing_trades],
                    name='胜率统计'
                ),
                row=2, col=2
            )
        
        fig.update_layout(
            title="交易分析",
            height=600,
            showlegend=False,
            template='plotly_white'
            # 移除可能导致问题的dragmode和selectdirection
        )
        
        return fig
        
    except Exception as e:
        st.error(f"交易分析图生成失败: {e}")
        return go.Figure()

def display_safe_dataframe(df: pd.DataFrame, title: str = "", **kwargs):
    """安全显示DataFrame"""
    try:
        if df is None or df.empty:
            st.info(f"{title}: 暂无数据")
            return
        
        # 限制显示行数避免性能问题
        max_rows = kwargs.get('max_rows', 1000)
        if len(df) > max_rows:
            st.warning(f"数据量较大({len(df)}行)，仅显示前{max_rows}行")
            df_display = df.head(max_rows)
        else:
            df_display = df
        
        # 格式化数值列
        numeric_cols = df_display.select_dtypes(include=[np.number]).columns
        format_dict = {}
        for col in numeric_cols:
            if 'price' in col.lower() or 'pnl' in col.lower():
                format_dict[col] = '{:.4f}'
            elif 'quantity' in col.lower() or 'volume' in col.lower():
                format_dict[col] = '{:.0f}'
            else:
                format_dict[col] = '{:.2f}'
        
        if title:
            st.subheader(title)
        
        # 使用安全的显示方式
        if format_dict:
            st.dataframe(
                df_display.style.format(format_dict),
                width='stretch',
                **{k: v for k, v in kwargs.items() if k != 'max_rows'}
            )
        else:
            st.dataframe(
                df_display,
                width='stretch',
                **{k: v for k, v in kwargs.items() if k != 'max_rows'}
            )
            
    except Exception as e:
        st.error(f"数据显示失败: {e}")
        st.info("尝试显示原始数据...")
        try:
            st.write(df.head() if df is not None else "无数据")
        except:
            st.error("数据显示完全失败")