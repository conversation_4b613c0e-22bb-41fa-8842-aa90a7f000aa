#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的交易日志显示模块
根据测试用例日志优化交易记录显示，提供详细的计算过程
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, List, Optional

def create_enhanced_trade_log(trades_df: pd.DataFrame, config: Dict) -> pd.DataFrame:
    """
    创建增强的交易日志，包含详细的计算过程
    
    Args:
        trades_df: 原始交易记录DataFrame
        config: 回测配置参数
        
    Returns:
        增强后的交易记录DataFrame
    """
    if trades_df is None or trades_df.empty:
        return pd.DataFrame()
    
    enhanced_trades = trades_df.copy()
    
    # 获取配置参数
    commission_rate = getattr(config, 'commission_rate', 0.0003)
    min_commission = 5.0
    stamp_tax_rate = 0.001  # 印花税0.1%
    slippage = getattr(config, 'slippage', 0.0001)
    
    # 计算详细字段
    enhanced_fields = []
    
    for idx, row in enhanced_trades.iterrows():
        trade_type = row['type']
        quantity = row['quantity']
        price = row['price']
        
        # 基础计算
        trade_amount = quantity * price
        
        # 佣金计算
        commission = max(trade_amount * commission_rate, min_commission)
        
        # 滑点价格
        if trade_type == 'BUY':
            actual_price = price * (1 + slippage)
            stamp_tax = 0.0  # 买入不收印花税
            total_cost = trade_amount + commission
            net_amount = trade_amount + commission
        else:  # SELL
            actual_price = price * (1 - slippage)
            stamp_tax = trade_amount * stamp_tax_rate
            total_fees = commission + stamp_tax
            net_amount = trade_amount - total_fees
        
        # 构建详细信息
        enhanced_info = {
            '交易时间': row['time'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(row['time']) else '',
            '交易类型': '买入' if trade_type == 'BUY' else '卖出',
            '数量': f"{quantity:,}股",
            '价格': f"{price:.4f}元",
            '交易金额': f"{trade_amount:,.2f}元",
            '交易金额计算': f"{quantity:,} × {price:.4f} = {trade_amount:,.2f}",
            '佣金': f"{commission:.2f}元",
            '佣金计算': f"max({trade_amount:,.2f} × {commission_rate:.4f}, {min_commission:.2f}) = {commission:.2f}",
        }
        
        if trade_type == 'SELL':
            enhanced_info.update({
                '印花税': f"{stamp_tax:.2f}元",
                '印花税计算': f"{trade_amount:,.2f} × {stamp_tax_rate:.4f} = {stamp_tax:.2f}",
                '总费用': f"{total_fees:.2f}元",
                '总费用计算': f"{commission:.2f} + {stamp_tax:.2f} = {total_fees:.2f}",
                '净收入': f"{net_amount:,.2f}元",
                '净收入计算': f"{trade_amount:,.2f} - {total_fees:.2f} = {net_amount:,.2f}",
            })
            
            # 如果有盈亏信息
            if 'pnl' in row and pd.notna(row['pnl']):
                pnl = row['pnl']
                enhanced_info.update({
                    '盈亏': f"{pnl:+.2f}元",
                    '盈亏说明': f"净收入 - 成本基础 = {pnl:+.2f}元"
                })
        else:
            enhanced_info.update({
                '印花税': '0.00元',
                '总成本': f"{net_amount:,.2f}元",
                '总成本计算': f"{trade_amount:,.2f} + {commission:.2f} = {net_amount:,.2f}",
                '平均成本': f"{net_amount/quantity:.4f}元",
                '平均成本计算': f"{net_amount:,.2f} ÷ {quantity:,} = {net_amount/quantity:.4f}"
            })
        
        # 添加交易原因
        if 'reason' in row and pd.notna(row['reason']):
            enhanced_info['交易原因'] = row['reason']
        
        enhanced_fields.append(enhanced_info)
    
    return pd.DataFrame(enhanced_fields)

def display_enhanced_trade_log(trades_df: pd.DataFrame, config: Dict):
    """
    显示增强的交易日志
    
    Args:
        trades_df: 交易记录DataFrame
        config: 回测配置
    """
    if trades_df is None or trades_df.empty:
        st.info("暂无交易记录")
        return
    
    st.subheader("📋 详细交易记录")
    
    # 创建增强的交易日志
    enhanced_df = create_enhanced_trade_log(trades_df, config)
    
    if enhanced_df.empty:
        st.info("无法生成详细交易记录")
        return
    
    # 添加说明
    st.info("""
    **交易记录说明**：
    - 📊 **交易金额计算**：数量 × 价格
    - 💰 **佣金计算**：max(交易金额 × 0.03%, 5元)
    - 📄 **印花税**：仅卖出时收取，交易金额 × 0.1%
    - 💵 **净收入**：卖出金额 - 佣金 - 印花税
    - 📈 **盈亏**：净收入 - 成本基础
    """)
    
    # 选择显示模式
    display_mode = st.radio(
        "选择显示模式",
        ["简洁模式", "详细模式", "计算过程"],
        horizontal=True
    )
    
    if display_mode == "简洁模式":
        # 简洁模式：只显示关键信息
        simple_columns = ['交易时间', '交易类型', '数量', '价格', '交易金额']
        if '盈亏' in enhanced_df.columns:
            simple_columns.append('盈亏')
        if '交易原因' in enhanced_df.columns:
            simple_columns.append('交易原因')
        
        display_df = enhanced_df[simple_columns]
        
    elif display_mode == "详细模式":
        # 详细模式：显示所有计算结果
        detail_columns = ['交易时间', '交易类型', '数量', '价格', '交易金额', '佣金', '印花税']
        
        # 根据交易类型添加相应字段
        if '净收入' in enhanced_df.columns:
            detail_columns.extend(['总费用', '净收入'])
        if '总成本' in enhanced_df.columns:
            detail_columns.extend(['总成本', '平均成本'])
        if '盈亏' in enhanced_df.columns:
            detail_columns.append('盈亏')
        if '交易原因' in enhanced_df.columns:
            detail_columns.append('交易原因')
        
        # 过滤存在的列
        available_columns = [col for col in detail_columns if col in enhanced_df.columns]
        display_df = enhanced_df[available_columns]
        
    else:  # 计算过程模式
        # 计算过程模式：显示所有计算公式
        process_columns = ['交易时间', '交易类型', '交易金额计算', '佣金计算']
        
        if '印花税计算' in enhanced_df.columns:
            process_columns.append('印花税计算')
        if '净收入计算' in enhanced_df.columns:
            process_columns.extend(['总费用计算', '净收入计算'])
        if '总成本计算' in enhanced_df.columns:
            process_columns.extend(['总成本计算', '平均成本计算'])
        if '盈亏说明' in enhanced_df.columns:
            process_columns.append('盈亏说明')
        if '交易原因' in enhanced_df.columns:
            process_columns.append('交易原因')
        
        # 过滤存在的列
        available_columns = [col for col in process_columns if col in enhanced_df.columns]
        display_df = enhanced_df[available_columns]
    
    # 显示数据表
    st.dataframe(
        display_df,
        use_container_width=True,
        hide_index=True
    )
    
    # 交易统计摘要
    st.subheader("📊 交易统计摘要")
    
    buy_trades = trades_df[trades_df['type'] == 'BUY']
    sell_trades = trades_df[trades_df['type'] == 'SELL']
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总交易次数", len(trades_df))
        st.metric("买入次数", len(buy_trades))
    
    with col2:
        st.metric("卖出次数", len(sell_trades))
        if len(sell_trades) > 0:
            profitable_trades = len(sell_trades[sell_trades.get('pnl', 0) > 0])
            win_rate = profitable_trades / len(sell_trades) * 100
            st.metric("胜率", f"{win_rate:.1f}%")
    
    with col3:
        # 计算总佣金
        commission_rate = getattr(config, 'commission_rate', 0.0003)
        total_commission = 0
        for _, row in trades_df.iterrows():
            trade_amount = row['quantity'] * row['price']
            commission = max(trade_amount * commission_rate, 5.0)
            total_commission += commission
        
        st.metric("总佣金", f"{total_commission:.2f}元")
        
        # 计算总印花税
        total_stamp_tax = 0
        for _, row in sell_trades.iterrows():
            trade_amount = row['quantity'] * row['price']
            stamp_tax = trade_amount * 0.001
            total_stamp_tax += stamp_tax
        
        st.metric("总印花税", f"{total_stamp_tax:.2f}元")
    
    with col4:
        total_fees = total_commission + total_stamp_tax
        st.metric("总费用", f"{total_fees:.2f}元")
        
        if len(sell_trades) > 0:
            total_pnl = sell_trades.get('pnl', 0).sum() if 'pnl' in sell_trades.columns else 0
            st.metric("总盈亏", f"{total_pnl:+.2f}元")

def add_fund_change_tracking(equity_df: pd.DataFrame, trades_df: pd.DataFrame, config: Dict):
    """
    添加资金变化追踪表
    
    Args:
        equity_df: 净值曲线数据
        trades_df: 交易记录
        config: 回测配置
    """
    if equity_df is None or equity_df.empty:
        return
    
    st.subheader("💰 资金变化追踪")
    
    # 创建资金变化记录
    fund_changes = []
    
    # 初始状态
    initial_capital = getattr(config, 'initial_capital', 1000000)
    fund_changes.append({
        '时间': '初始状态',
        '操作': '初始化',
        '现金余额': f"{initial_capital:,.2f}",
        '持仓市值': "0.00",
        '总权益': f"{initial_capital:,.2f}",
        '变化说明': '起始资金'
    })
    
    # 遍历交易记录，计算每次交易后的资金变化
    if trades_df is not None and not trades_df.empty:
        for idx, trade in trades_df.iterrows():
            trade_time = trade['time']
            
            # 找到对应时间点的净值数据
            equity_row = equity_df[equity_df['time'] <= trade_time].iloc[-1] if len(equity_df[equity_df['time'] <= trade_time]) > 0 else None
            
            if equity_row is not None:
                cash = equity_row.get('cash', 0)
                market_value = equity_row.get('market_value', 0)
                total_equity = equity_row.get('equity', 0)
                
                # 计算变化说明
                if trade['type'] == 'BUY':
                    trade_amount = trade['quantity'] * trade['price']
                    commission = max(trade_amount * getattr(config, 'commission_rate', 0.0003), 5.0)
                    total_cost = trade_amount + commission
                    change_desc = f"买入{trade['quantity']:,}股@{trade['price']:.4f}，成本{total_cost:,.2f}元"
                else:
                    trade_amount = trade['quantity'] * trade['price']
                    commission = max(trade_amount * getattr(config, 'commission_rate', 0.0003), 5.0)
                    stamp_tax = trade_amount * 0.001
                    net_proceeds = trade_amount - commission - stamp_tax
                    pnl = trade.get('pnl', 0)
                    change_desc = f"卖出{trade['quantity']:,}股@{trade['price']:.4f}，净收入{net_proceeds:,.2f}元，盈亏{pnl:+.2f}元"
                
                fund_changes.append({
                    '时间': trade_time.strftime('%H:%M:%S'),
                    '操作': '买入' if trade['type'] == 'BUY' else '卖出',
                    '现金余额': f"{cash:,.2f}",
                    '持仓市值': f"{market_value:,.2f}",
                    '总权益': f"{total_equity:,.2f}",
                    '变化说明': change_desc
                })
    
    # 显示资金变化表
    fund_df = pd.DataFrame(fund_changes)
    st.dataframe(
        fund_df,
        use_container_width=True,
        hide_index=True
    )
    
    # 资金变化图表
    if len(fund_changes) > 1:
        st.subheader("📈 资金变化趋势")
        
        import plotly.graph_objects as go
        
        # 提取数值数据
        times = [item['时间'] for item in fund_changes[1:]]  # 跳过初始状态
        cash_values = [float(item['现金余额'].replace(',', '')) for item in fund_changes[1:]]
        market_values = [float(item['持仓市值'].replace(',', '')) for item in fund_changes[1:]]
        total_values = [float(item['总权益'].replace(',', '')) for item in fund_changes[1:]]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=times,
            y=cash_values,
            mode='lines+markers',
            name='现金余额',
            line=dict(color='blue')
        ))
        
        fig.add_trace(go.Scatter(
            x=times,
            y=market_values,
            mode='lines+markers',
            name='持仓市值',
            line=dict(color='green')
        ))
        
        fig.add_trace(go.Scatter(
            x=times,
            y=total_values,
            mode='lines+markers',
            name='总权益',
            line=dict(color='red', width=3)
        ))
        
        fig.update_layout(
            title="资金变化趋势",
            xaxis_title="时间",
            yaxis_title="金额 (元)",
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, use_container_width=True)