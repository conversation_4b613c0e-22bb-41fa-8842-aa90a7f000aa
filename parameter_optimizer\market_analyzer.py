#!/usr/bin/env python3
"""
市场特征分析器
分析历史数据的波动性、趋势性、流动性等特征
"""

import pandas as pd
import numpy as np
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from database.connection_pool import get_db_connection

logger = logging.getLogger(__name__)

class MarketAnalyzer:
    """市场特征分析器"""
    
    def __init__(self, symbol: str):
        self.symbol = symbol
        
    async def analyze_market_characteristics(self, days: int = 90) -> Dict:
        """
        分析市场特征
        
        Args:
            days: 分析的历史天数
            
        Returns:
            包含各种市场特征的字典
        """
        logger.info(f"开始分析 {self.symbol} 的市场特征，分析期间: {days}天")
        
        # 获取历史数据
        df = await self._get_historical_data(days)
        
        if df.empty:
            raise ValueError(f"没有找到 {self.symbol} 的历史数据")
        
        # 计算各种特征
        characteristics = {
            'data_summary': self._get_data_summary(df),
            'volatility': self._calculate_volatility_metrics(df),
            'trend': self._calculate_trend_metrics(df),
            'liquidity': self._calculate_liquidity_metrics(df),
            'price_patterns': self._analyze_price_patterns(df),
            'trading_hours': self._analyze_trading_hours(df),
            'market_regime': self._classify_market_regime(df),
            'recommendations': self._generate_recommendations(df)
        }
        
        # 保存分析结果
        await self._save_analysis_results(characteristics, days)
        
        logger.info(f"市场特征分析完成")
        return characteristics
    
    async def _get_historical_data(self, days: int) -> pd.DataFrame:
        """获取历史数据"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        query = """
        SELECT tick_time, price, volume 
        FROM ticks 
        WHERE symbol=? AND date(tick_time) BETWEEN ? AND ?
        ORDER BY tick_time ASC
        """
        
        with get_db_connection() as conn:
            df = pd.read_sql_query(
                query, conn,
                params=[self.symbol, start_date.isoformat(), end_date.isoformat()],
                parse_dates=['tick_time']
            )
        
        if not df.empty:
            # 计算收益率
            df['returns'] = df['price'].pct_change()
            df['log_returns'] = np.log(df['price'] / df['price'].shift(1))
            
            # 添加时间特征
            df['hour'] = df['tick_time'].dt.hour
            df['minute'] = df['tick_time'].dt.minute
            df['date'] = df['tick_time'].dt.date
            
        return df
    
    def _get_data_summary(self, df: pd.DataFrame) -> Dict:
        """获取数据摘要"""
        return {
            'total_records': len(df),
            'date_range': {
                'start': df['tick_time'].min().isoformat(),
                'end': df['tick_time'].max().isoformat()
            },
            'price_range': {
                'min': float(df['price'].min()),
                'max': float(df['price'].max()),
                'mean': float(df['price'].mean()),
                'std': float(df['price'].std())
            },
            'volume_stats': {
                'mean': float(df['volume'].mean()),
                'std': float(df['volume'].std()),
                'total': float(df['volume'].sum())
            }
        }
    
    def _calculate_volatility_metrics(self, df: pd.DataFrame) -> Dict:
        """计算波动性指标"""
        returns = df['returns'].dropna()
        
        # 基础波动性指标
        daily_vol = returns.std()
        annual_vol = daily_vol * np.sqrt(252 * 240)  # 假设每天240个tick
        
        # 分位数
        volatility_percentiles = {
            '5th': float(returns.quantile(0.05)),
            '25th': float(returns.quantile(0.25)),
            '50th': float(returns.quantile(0.50)),
            '75th': float(returns.quantile(0.75)),
            '95th': float(returns.quantile(0.95))
        }
        
        # 日内波动性
        daily_data = df.groupby('date').agg({
            'price': ['min', 'max', 'first', 'last'],
            'volume': 'sum'
        })
        
        daily_ranges = (daily_data[('price', 'max')] - daily_data[('price', 'min')]) / daily_data[('price', 'first')]
        
        # GARCH波动性（简化版）
        garch_vol = self._calculate_garch_volatility(returns)
        
        return {
            'daily_volatility': float(daily_vol),
            'annualized_volatility': float(annual_vol),
            'volatility_percentiles': volatility_percentiles,
            'avg_daily_range': float(daily_ranges.mean()),
            'max_daily_range': float(daily_ranges.max()),
            'range_volatility': float(daily_ranges.std()),
            'garch_volatility': float(garch_vol),
            'volatility_clustering': self._detect_volatility_clustering(returns),
            'extreme_moves': self._analyze_extreme_moves(returns)
        }
    
    def _calculate_trend_metrics(self, df: pd.DataFrame) -> Dict:
        """计算趋势指标"""
        prices = df['price']
        
        # 移动平均
        df['sma_20'] = prices.rolling(20).mean()
        df['sma_60'] = prices.rolling(60).mean()
        df['ema_20'] = prices.ewm(span=20).mean()
        
        # 趋势强度
        trend_up_sma = (prices > df['sma_20']).sum() / len(prices)
        trend_strength = abs(prices.iloc[-1] - prices.iloc[0]) / prices.iloc[0]
        
        # 动量指标
        momentum_5 = (prices / prices.shift(5) - 1).dropna()
        momentum_20 = (prices / prices.shift(20) - 1).dropna()
        
        # RSI
        rsi = self._calculate_rsi(prices)
        
        # 趋势持续性
        trend_persistence = self._calculate_trend_persistence(prices)
        
        return {
            'trend_up_ratio': float(trend_up_sma),
            'trend_strength': float(trend_strength),
            'momentum_5_mean': float(momentum_5.mean()),
            'momentum_20_mean': float(momentum_20.mean()),
            'current_rsi': float(rsi.iloc[-1]) if not rsi.empty else 50.0,
            'rsi_overbought_ratio': float((rsi > 70).sum() / len(rsi)) if not rsi.empty else 0.0,
            'rsi_oversold_ratio': float((rsi < 30).sum() / len(rsi)) if not rsi.empty else 0.0,
            'trend_persistence': trend_persistence,
            'price_momentum': float(momentum_20.iloc[-1]) if not momentum_20.empty else 0.0
        }
    
    def _calculate_liquidity_metrics(self, df: pd.DataFrame) -> Dict:
        """计算流动性指标"""
        volume = df['volume']
        
        # 基础流动性指标
        avg_volume = volume.mean()
        volume_volatility = volume.std() / avg_volume if avg_volume > 0 else 0
        
        # 高成交量比例
        high_volume_threshold = volume.quantile(0.8)
        high_volume_ratio = (volume > high_volume_threshold).sum() / len(volume)
        
        # 成交量-价格关系
        price_volume_corr = df['price'].corr(df['volume'])
        
        # 流动性干涸检测
        low_volume_threshold = volume.quantile(0.2)
        liquidity_drought_ratio = (volume < low_volume_threshold).sum() / len(volume)
        
        return {
            'avg_volume': float(avg_volume),
            'volume_volatility': float(volume_volatility),
            'high_volume_ratio': float(high_volume_ratio),
            'liquidity_drought_ratio': float(liquidity_drought_ratio),
            'price_volume_correlation': float(price_volume_corr),
            'volume_percentiles': {
                '10th': float(volume.quantile(0.1)),
                '50th': float(volume.quantile(0.5)),
                '90th': float(volume.quantile(0.9))
            }
        }
    
    def _analyze_price_patterns(self, df: pd.DataFrame) -> Dict:
        """分析价格模式"""
        prices = df['price']
        returns = df['returns'].dropna()
        
        # 价格跳跃检测
        jump_threshold = returns.std() * 3
        jumps = returns[abs(returns) > jump_threshold]
        
        # 均值回归检测
        mean_reversion = self._test_mean_reversion(prices)
        
        # 序列相关性
        autocorr_1 = returns.autocorr(lag=1)
        autocorr_5 = returns.autocorr(lag=5)
        
        return {
            'jump_frequency': float(len(jumps) / len(returns)),
            'avg_jump_size': float(abs(jumps).mean()) if not jumps.empty else 0.0,
            'mean_reversion_strength': mean_reversion,
            'autocorrelation_lag1': float(autocorr_1) if not pd.isna(autocorr_1) else 0.0,
            'autocorrelation_lag5': float(autocorr_5) if not pd.isna(autocorr_5) else 0.0,
            'price_clustering': self._detect_price_clustering(prices)
        }
    
    def _analyze_trading_hours(self, df: pd.DataFrame) -> Dict:
        """分析交易时段特征"""
        hourly_stats = df.groupby('hour').agg({
            'price': 'std',
            'volume': 'mean',
            'returns': 'std'
        }).fillna(0)
        
        # 找出最活跃和最不活跃的时段
        most_volatile_hour = hourly_stats['returns'].idxmax()
        least_volatile_hour = hourly_stats['returns'].idxmin()
        highest_volume_hour = hourly_stats['volume'].idxmax()
        
        return {
            'most_volatile_hour': int(most_volatile_hour),
            'least_volatile_hour': int(least_volatile_hour),
            'highest_volume_hour': int(highest_volume_hour),
            'morning_volatility': float(hourly_stats.loc[9:11, 'returns'].mean()),
            'afternoon_volatility': float(hourly_stats.loc[13:14, 'returns'].mean()),
            'hourly_patterns': {
                str(hour): {
                    'volatility': float(stats['returns']),
                    'avg_volume': float(stats['volume'])
                }
                for hour, stats in hourly_stats.iterrows()
            }
        }
    
    def _classify_market_regime(self, df: pd.DataFrame) -> Dict:
        """分类市场状态"""
        returns = df['returns'].dropna()
        volatility = returns.std()
        
        # 波动性分类
        if volatility > 0.02:
            vol_regime = "高波动"
        elif volatility > 0.01:
            vol_regime = "中等波动"
        else:
            vol_regime = "低波动"
        
        # 趋势分类
        trend_strength = abs(df['price'].iloc[-1] - df['price'].iloc[0]) / df['price'].iloc[0]
        if trend_strength > 0.05:
            trend_regime = "强趋势"
        elif trend_strength > 0.02:
            trend_regime = "中等趋势"
        else:
            trend_regime = "震荡"
        
        return {
            'volatility_regime': vol_regime,
            'trend_regime': trend_regime,
            'market_state': f"{vol_regime}_{trend_regime}",
            'regime_confidence': self._calculate_regime_confidence(df)
        }
    
    def _generate_recommendations(self, df: pd.DataFrame) -> Dict:
        """生成参数建议"""
        volatility = df['returns'].std()
        
        # 基于波动性的参数建议
        if volatility > 0.02:  # 高波动
            recommendations = {
                'buy_trigger_drop': (-0.012, -0.008),
                'profit_target': (0.008, 0.015),
                'stop_loss': (-0.035, -0.025),
                'max_hold_time': (1800, 3600),
                'strategy_type': 'aggressive'
            }
        elif volatility > 0.01:  # 中等波动
            recommendations = {
                'buy_trigger_drop': (-0.010, -0.006),
                'profit_target': (0.005, 0.010),
                'stop_loss': (-0.025, -0.015),
                'max_hold_time': (1800, 5400),
                'strategy_type': 'balanced'
            }
        else:  # 低波动
            recommendations = {
                'buy_trigger_drop': (-0.008, -0.004),
                'profit_target': (0.003, 0.008),
                'stop_loss': (-0.020, -0.010),
                'max_hold_time': (3600, 7200),
                'strategy_type': 'conservative'
            }
        
        return recommendations
    
    # 辅助方法
    def _calculate_garch_volatility(self, returns: pd.Series) -> float:
        """计算GARCH波动性（简化版）"""
        try:
            # 简化的GARCH(1,1)模型
            squared_returns = returns ** 2
            return float(squared_returns.ewm(alpha=0.1).mean().iloc[-1] ** 0.5)
        except:
            return float(returns.std())
    
    def _detect_volatility_clustering(self, returns: pd.Series) -> Dict:
        """检测波动性聚集"""
        squared_returns = returns ** 2
        autocorr = squared_returns.autocorr(lag=1)
        
        return {
            'clustering_strength': float(autocorr) if not pd.isna(autocorr) else 0.0,
            'has_clustering': bool(autocorr > 0.1) if not pd.isna(autocorr) else False
        }
    
    def _analyze_extreme_moves(self, returns: pd.Series) -> Dict:
        """分析极端价格变动"""
        threshold = returns.std() * 2
        extreme_moves = returns[abs(returns) > threshold]
        
        return {
            'extreme_move_frequency': float(len(extreme_moves) / len(returns)),
            'avg_extreme_move_size': float(abs(extreme_moves).mean()) if not extreme_moves.empty else 0.0,
            'max_positive_move': float(returns.max()),
            'max_negative_move': float(returns.min())
        }
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def _calculate_trend_persistence(self, prices: pd.Series) -> float:
        """计算趋势持续性"""
        returns = prices.pct_change().dropna()
        positive_runs = []
        negative_runs = []
        
        current_run = 0
        current_sign = 0
        
        for ret in returns:
            if ret > 0:
                if current_sign == 1:
                    current_run += 1
                else:
                    if current_run > 0:
                        negative_runs.append(current_run)
                    current_run = 1
                    current_sign = 1
            elif ret < 0:
                if current_sign == -1:
                    current_run += 1
                else:
                    if current_run > 0:
                        positive_runs.append(current_run)
                    current_run = 1
                    current_sign = -1
        
        avg_run_length = np.mean(positive_runs + negative_runs) if (positive_runs + negative_runs) else 1
        return float(avg_run_length)
    
    def _test_mean_reversion(self, prices: pd.Series) -> float:
        """测试均值回归强度"""
        # 简化的均值回归测试
        deviations = prices - prices.rolling(20).mean()
        next_returns = prices.pct_change().shift(-1)
        
        correlation = deviations.corr(next_returns)
        return float(correlation) if not pd.isna(correlation) else 0.0
    
    def _detect_price_clustering(self, prices: pd.Series) -> Dict:
        """检测价格聚集"""
        # 检测价格是否倾向于聚集在某些水平
        price_counts = prices.round(3).value_counts()
        max_cluster_size = price_counts.max()
        total_observations = len(prices)
        
        return {
            'max_cluster_ratio': float(max_cluster_size / total_observations),
            'unique_price_ratio': float(len(price_counts) / total_observations)
        }
    
    def _calculate_regime_confidence(self, df: pd.DataFrame) -> float:
        """计算市场状态分类的置信度"""
        # 基于多个指标的一致性来计算置信度
        returns = df['returns'].dropna()
        
        # 波动性一致性
        vol_consistency = 1 - (returns.rolling(20).std().std() / returns.std())
        
        # 趋势一致性
        trend_consistency = abs(df['price'].corr(pd.Series(range(len(df)))))
        
        confidence = (vol_consistency + trend_consistency) / 2
        return float(max(0, min(1, confidence)))
    
    async def _save_analysis_results(self, characteristics: Dict, days: int):
        """保存分析结果到数据库"""
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    INSERT OR REPLACE INTO market_analysis 
                    (symbol, analysis_date, analysis_period_days, volatility_metrics, 
                     trend_metrics, liquidity_metrics, trading_patterns, recommendations, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    self.symbol,
                    datetime.now().date().isoformat(),
                    days,
                    json.dumps(characteristics['volatility']),
                    json.dumps(characteristics['trend']),
                    json.dumps(characteristics['liquidity']),
                    json.dumps({
                        'price_patterns': characteristics['price_patterns'],
                        'trading_hours': characteristics['trading_hours'],
                        'market_regime': characteristics['market_regime']
                    }),
                    json.dumps(characteristics['recommendations']),
                    datetime.now().isoformat()
                ))
                conn.commit()
                
            logger.info(f"市场分析结果已保存到数据库")
            
        except Exception as e:
            logger.error(f"保存市场分析结果失败: {e}")

# 测试函数
async def test_market_analyzer():
    """测试市场分析器"""
    logger.info("开始测试市场分析器...")
    
    analyzer = MarketAnalyzer("159740")
    
    try:
        # 分析最近30天的数据
        characteristics = await analyzer.analyze_market_characteristics(30)
        
        logger.info("✅ 市场特征分析完成")
        logger.info(f"数据摘要: {characteristics['data_summary']['total_records']} 条记录")
        logger.info(f"波动性: {characteristics['volatility']['daily_volatility']:.4f}")
        logger.info(f"趋势强度: {characteristics['trend']['trend_strength']:.4f}")
        logger.info(f"平均成交量: {characteristics['liquidity']['avg_volume']:.0f}")
        logger.info(f"市场状态: {characteristics['market_regime']['market_state']}")
        logger.info(f"推荐策略类型: {characteristics['recommendations']['strategy_type']}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 市场分析器测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 测试市场分析器...")
    print("=" * 50)
    
    success = asyncio.run(test_market_analyzer())
    
    if success:
        print("\n🎉 市场分析器测试成功！")
    else:
        print("\n❌ 市场分析器测试失败")
    
    print("=" * 50)
