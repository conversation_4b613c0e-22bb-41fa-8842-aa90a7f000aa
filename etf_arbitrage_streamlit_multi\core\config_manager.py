"""
统一配置管理器
提供统一的配置加载、管理和访问功能
"""

import json
import yaml
import logging
from typing import Dict, Any, Optional, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """数据库配置"""
    ticks_db: str = 'ticks.db'
    monitoring_db: str = 'monitoring.db'
    backtest_db: str = 'backtest.db'
    backup_interval: int = 3600
    cleanup_days: int = 30

@dataclass
class TradingConfig:
    """交易配置"""
    default_position_size: float = 100000
    max_positions: int = 5
    commission_rate: float = 0.0003
    slippage_rate: float = 0.0001
    risk_free_rate: float = 0.03

@dataclass
class SymbolConfig:
    """标的配置"""
    name: str
    market: str
    description: str
    t0_enabled: bool = False
    price_limit: float = 0.10

@dataclass
class UIConfig:
    """界面配置"""
    theme: str = 'light'
    primary_color: str = '#1f77b4'
    background_color: str = '#ffffff'
    text_color: str = '#262730'
    refresh_interval: int = 2
    chart_height: int = 400

@dataclass
class MonitoringConfig:
    """监控配置"""
    enabled: bool = True
    metrics_interval: int = 60
    alert_enabled: bool = True
    log_level: str = 'INFO'

class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self._config_data = {}
        self._config_file = Path('config.yaml')
        self._load_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if self._config_file.exists():
                with open(self._config_file, 'r', encoding='utf-8') as f:
                    self._config_data = yaml.safe_load(f) or {}
                logger.info(f"配置文件加载成功: {self._config_file}")
            else:
                logger.info("配置文件不存在，使用默认配置")
                self._create_default_config()
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认配置"""
        self._config_data = {
            'app': {
                'title': 'ETF套利交易系统',
                'version': '2.1.0',
                'description': '专业的ETF套利交易解决方案'
            },
            'database': asdict(DatabaseConfig()),
            'trading': asdict(TradingConfig()),
            'ui': asdict(UIConfig()),
            'monitoring': asdict(MonitoringConfig()),
            'symbols': {
                '159740': {
                    'name': '纳指ETF',
                    'market': 'sz',
                    'description': '跟踪纳斯达克100指数',
                    't0_enabled': True,
                    'price_limit': 0.10
                },
                '159915': {
                    'name': '创业板ETF',
                    'market': 'sz', 
                    'description': '跟踪创业板指数',
                    't0_enabled': False,
                    'price_limit': 0.20
                },
                '159919': {
                    'name': '沪深300ETF',
                    'market': 'sz',
                    'description': '跟踪沪深300指数',
                    't0_enabled': False,
                    'price_limit': 0.10
                }
            }
        }
        
        # 保存默认配置
        self.save_config()
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号分隔的嵌套键）"""
        keys = key.split('.')
        value = self._config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """设置配置值（支持点号分隔的嵌套键）"""
        keys = key.split('.')
        config = self._config_data
        
        # 导航到最后一级的父级
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        # 设置值
        config[keys[-1]] = value
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置"""
        db_config = self.get('database', {})
        return DatabaseConfig(**db_config)
    
    def get_trading_config(self) -> TradingConfig:
        """获取交易配置"""
        trading_config = self.get('trading', {})
        return TradingConfig(**trading_config)
    
    def get_ui_config(self) -> UIConfig:
        """获取界面配置"""
        ui_config = self.get('ui', {})
        return UIConfig(**ui_config)
    
    def get_monitoring_config(self) -> MonitoringConfig:
        """获取监控配置"""
        monitoring_config = self.get('monitoring', {})
        return MonitoringConfig(**monitoring_config)
    
    def get_symbol_config(self, symbol: str) -> Optional[SymbolConfig]:
        """获取标的配置"""
        symbol_data = self.get(f'symbols.{symbol}')
        if symbol_data:
            return SymbolConfig(**symbol_data)
        return None
    
    def get_all_symbols(self) -> Dict[str, SymbolConfig]:
        """获取所有标的配置"""
        symbols_data = self.get('symbols', {})
        return {
            symbol: SymbolConfig(**config)
            for symbol, config in symbols_data.items()
        }
    
    def add_symbol(self, symbol: str, config: SymbolConfig):
        """添加标的配置"""
        self.set(f'symbols.{symbol}', asdict(config))
    
    def remove_symbol(self, symbol: str):
        """移除标的配置"""
        symbols = self.get('symbols', {})
        if symbol in symbols:
            del symbols[symbol]
            self.set('symbols', symbols)
    
    def update_config(self, updates: Dict[str, Any]):
        """批量更新配置"""
        for key, value in updates.items():
            self.set(key, value)
    
    def save_config(self):
        """保存配置到文件"""
        try:
            # 添加更新时间戳
            self._config_data['_updated_at'] = datetime.now().isoformat()
            
            with open(self._config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self._config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已保存到: {self._config_file}")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config()
        logger.info("配置已重新加载")
    
    def export_config(self, file_path: Union[str, Path]) -> bool:
        """导出配置到指定文件"""
        try:
            file_path = Path(file_path)
            
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(self._config_data, f, indent=2, ensure_ascii=False)
            elif file_path.suffix.lower() in ['.yaml', '.yml']:
                with open(file_path, 'w', encoding='utf-8') as f:
                    yaml.dump(self._config_data, f, default_flow_style=False, allow_unicode=True)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
            
            logger.info(f"配置已导出到: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: Union[str, Path]) -> bool:
        """从指定文件导入配置"""
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {file_path}")
            
            if file_path.suffix.lower() == '.json':
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)
            elif file_path.suffix.lower() in ['.yaml', '.yml']:
                with open(file_path, 'r', encoding='utf-8') as f:
                    imported_config = yaml.safe_load(f)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
            
            # 合并配置
            self._config_data.update(imported_config)
            self.save_config()
            
            logger.info(f"配置已从 {file_path} 导入")
            return True
            
        except Exception as e:
            logger.error(f"导入配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'app_title': self.get('app.title'),
            'app_version': self.get('app.version'),
            'symbols_count': len(self.get('symbols', {})),
            'database_config': self.get('database'),
            'trading_config': self.get('trading'),
            'last_updated': self.get('_updated_at')
        }

# 全局配置管理器实例
config_manager = ConfigManager()
