#!/usr/bin/env python3
"""
性能优化模块 - 数据采样和图表优化 (完整版)
解决大数据量导致的前端卡顿问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')

# ==================== 主要接口函数 ====================

def optimize_chart_data(df: pd.DataFrame, config: dict) -> pd.DataFrame:
    """优化图表数据 - 主要接口函数"""
    return smart_sample_data(
        df, 
        max_points=config.get('max_points', 2000),
        preserve_extremes=config.get('preserve_extremes', True)
    )

def optimize_trade_data(trades: pd.DataFrame, config: dict) -> pd.DataFrame:
    """优化交易数据"""
    if len(trades) <= config.get('max_points', 2000):
        return trades
    return smart_sample_data(trades, max_points=config.get('max_points', 2000))

def smart_sample_data(df: pd.DataFrame, max_points: int = 2000, preserve_extremes: bool = True) -> pd.DataFrame:
    """智能数据采样 - 保留关键特征"""
    try:
        if df is None or df.empty:
            return df
        
        # 如果数据量不大，直接返回
        if len(df) <= max_points:
            return df
        
        # 移除print语句避免Streamlit冲突
        pass
        
        # 保留原始索引，不要reset
        df_work = df.copy()
        
        # 计算采样间隔
        step = len(df_work) / max_points
        
        # 基础均匀采样 - 使用iloc位置索引
        indices = np.linspace(0, len(df_work) - 1, max_points, dtype=int)
        sampled_df = df_work.iloc[indices].copy()
        
        # 如果需要保留极值点
        if preserve_extremes and len(df_work.columns) > 0:
            # 找到数值列
            numeric_cols = df_work.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                # 对每个数值列找极值
                extreme_indices = set()
                
                for col in numeric_cols:
                    if col in df_work.columns:
                        # 最大值和最小值的索引
                        max_idx = df_work[col].idxmax()
                        min_idx = df_work[col].idxmin()
                        
                        if pd.notna(max_idx):
                            extreme_indices.add(max_idx)
                        if pd.notna(min_idx):
                            extreme_indices.add(min_idx)
                
                # 添加极值点
                if extreme_indices:
                    extreme_df = df_work.loc[list(extreme_indices)]
                    sampled_df = pd.concat([sampled_df, extreme_df]).drop_duplicates()
                    
                    # 按原始顺序排序
                    if 'timestamp' in sampled_df.columns:
                        sampled_df = sampled_df.sort_values('timestamp')
                    elif sampled_df.index.name:
                        sampled_df = sampled_df.sort_index()
        
        # 限制最终大小
        if len(sampled_df) > max_points:
            step = max(1, len(sampled_df) // max_points)  # 确保步长至少为1
            sampled_df = sampled_df.iloc[::step][:max_points]
        
        return sampled_df  # 保留原始索引，不要reset
        
    except Exception as e:
        # 采样失败，返回原数据
        return df

# ==================== 优化图表生成函数 ====================

def create_optimized_price_chart(df: pd.DataFrame, config: dict, signals: pd.DataFrame = None) -> go.Figure:
    """创建性能优化的价格走势图"""
    try:
        # 优化数据
        optimized_df = optimize_chart_data(df, config)
        
        fig = go.Figure()
        
        # 添加价格线
        if 'close' in optimized_df.columns:
            fig.add_trace(go.Scatter(
                x=(optimized_df.index if 'timestamp' not in optimized_df.columns else optimized_df['timestamp']).tolist(),
                y=optimized_df['close'].tolist(),
                mode='lines',
                name='价格',
                line=dict(color='blue', width=1)
            ))
        
        # 添加交易信号
        if signals is not None and not signals.empty:
            optimized_signals = optimize_trade_data(signals, config)
            
            # 买入信号
            buy_signals = optimized_signals[optimized_signals.get('signal', 0) > 0]
            if not buy_signals.empty:
                fig.add_trace(go.Scatter(
                    x=buy_signals.index.tolist(),
                    y=buy_signals.get('price', buy_signals.get('close', 0)),
                    mode='markers',
                    name='买入',
                    marker=dict(color='red', size=8, symbol='triangle-up')
                ))
            
            # 卖出信号
            sell_signals = optimized_signals[optimized_signals.get('signal', 0) < 0]
            if not sell_signals.empty:
                fig.add_trace(go.Scatter(
                    x=sell_signals.index.tolist(),
                    y=sell_signals.get('price', sell_signals.get('close', 0)),
                    mode='markers',
                    name='卖出',
                    marker=dict(color='green', size=8, symbol='triangle-down')
                ))
        
        # 优化布局
        fig.update_layout(
            title="价格走势与交易信号",
            xaxis_title="时间",
            yaxis_title="价格",
            height=config.get('chart_height', 600),
            showlegend=True,
            hovermode='x unified'
        )
        
        # 启用WebGL渲染
        if config.get('use_webgl', True):
            fig.update_traces(line=dict(simplify=True))
        
        return fig
        
    except Exception as e:
        # 图表生成失败
        # 返回简单图表
        return go.Figure().add_annotation(text=f"图表生成失败: {e}", x=0.5, y=0.5)

def create_optimized_equity_curve(equity_data, config: dict) -> go.Figure:
    """创建性能优化的净值曲线"""
    try:
        # 处理不同类型的净值数据
        if isinstance(equity_data, pd.Series):
            # 如果是Series，转换为DataFrame
            df_equity = pd.DataFrame({'equity': equity_data})
        elif isinstance(equity_data, pd.DataFrame):
            df_equity = equity_data.copy()
        elif isinstance(equity_data, (list, np.ndarray)):
            # 如果是列表或数组，创建DataFrame
            df_equity = pd.DataFrame({'equity': equity_data})
        else:
            # 其他情况，尝试转换
            df_equity = pd.DataFrame({'equity': [equity_data] if np.isscalar(equity_data) else equity_data})
        
        # 确保有索引
        if df_equity.index.empty or len(df_equity.index) == 0:
            df_equity.index = range(len(df_equity))
        
        optimized_data = optimize_chart_data(df_equity, config)
        
        fig = go.Figure()
        
        if 'equity' in optimized_data.columns and not optimized_data.empty:
            fig.add_trace(go.Scatter(
                x=optimized_data.index.tolist(),
                y=optimized_data['equity'].tolist(),
                mode='lines',
                name='净值曲线',
                line=dict(color='green', width=2)
            ))
        
        # 添加回撤
        if 'drawdown' in optimized_data.columns:
            fig.add_trace(go.Scatter(
                x=optimized_data.index.tolist(),
                y=optimized_data['drawdown'].tolist(),
                mode='lines',
                name='回撤',
                line=dict(color='red', width=1),
                yaxis='y2'
            ))
        
        fig.update_layout(
            title="净值曲线与回撤",
            xaxis_title="时间",
            yaxis_title="净值",
            yaxis2=dict(title="回撤", overlaying='y', side='right'),
            height=config.get('chart_height', 600),
            showlegend=True
        )
        
        return fig
        
    except Exception as e:
        # 净值图表生成失败
        # 返回空图表而不是错误注释
        return go.Figure()

def create_optimized_trade_chart(trades: pd.DataFrame, config: dict) -> go.Figure:
    """创建性能优化的交易分析图"""
    try:
        optimized_trades = optimize_trade_data(trades, config)
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('交易盈亏', '持仓时间'),
            vertical_spacing=0.1
        )
        
        if 'pnl' in optimized_trades.columns:
            # 盈亏分布
            colors = ['green' if x > 0 else 'red' for x in optimized_trades['pnl']]
            fig.add_trace(
                go.Bar(x=optimized_trades.index.tolist(), y=optimized_trades['pnl'].tolist(), 
                      marker_color=colors, name='盈亏'),
                row=1, col=1
            )
        
        if 'duration' in optimized_trades.columns:
            # 持仓时间
            fig.add_trace(
                go.Scatter(x=optimized_trades.index.tolist(), y=optimized_trades['duration'].tolist(),
                          mode='markers', name='持仓时间'),
                row=2, col=1
            )
        
        fig.update_layout(
            height=config.get('chart_height', 600),
            showlegend=True,
            title_text="交易分析"
        )
        
        return fig
        
    except Exception as e:
        # 交易图表生成失败
        return go.Figure().add_annotation(text=f"交易图表生成失败: {e}", x=0.5, y=0.5)

# ==================== 性能监控函数 ====================

def display_performance_info(original_size: int, optimized_size: int):
    """显示性能优化信息"""
    if original_size > optimized_size:
        compression_ratio = (1 - optimized_size / original_size) * 100
        st.info(f"📊 数据优化: {original_size:,} → {optimized_size:,} 点 (压缩 {compression_ratio:.1f}%)")

def get_performance_stats(df: pd.DataFrame, config: dict) -> dict:
    """获取性能统计信息"""
    return {
        'original_size': len(df),
        'max_points': config.get('max_points', 2000),
        'will_optimize': len(df) > config.get('max_points', 2000),
        'compression_ratio': max(0, (1 - min(len(df), config.get('max_points', 2000)) / len(df)) * 100)
    }

# ==================== 兼容性函数 ====================

class DataSampler:
    """智能数据采样器 - 兼容性类"""
    
    def __init__(self, max_points: int = 2000):
        self.max_points = max_points
    
    def smart_sample(self, df: pd.DataFrame, time_col: str = 'time', 
                    value_cols: List[str] = None) -> pd.DataFrame:
        """智能采样 - 保留关键数据点"""
        return smart_sample_data(df, self.max_points, preserve_extremes=True)
    
    def sample_trades(self, trades_df: pd.DataFrame, max_trades: int = 500) -> pd.DataFrame:
        """交易数据采样 - 保留所有重要交易"""
        try:
            if trades_df is None or trades_df.empty:
                return trades_df
            
            if len(trades_df) <= max_trades:
                return trades_df
            
            # 按盈亏排序，保留最大盈利和最大亏损的交易
            if 'pnl' in trades_df.columns:
                sorted_trades = trades_df.sort_values('pnl')
                
                # 保留前后各25%的极值交易
                extreme_count = max_trades // 4
                top_trades = sorted_trades.tail(extreme_count)  # 最大盈利
                bottom_trades = sorted_trades.head(extreme_count)  # 最大亏损
                
                # 剩余部分均匀采样
                middle_trades = sorted_trades.iloc[extreme_count:-extreme_count]
                remaining_count = max_trades - len(top_trades) - len(bottom_trades)
                
                if len(middle_trades) > remaining_count:
                    step = len(middle_trades) // remaining_count
                    sampled_middle = middle_trades.iloc[::step][:remaining_count]
                else:
                    sampled_middle = middle_trades
                
                result = pd.concat([bottom_trades, sampled_middle, top_trades])
                return result.sort_index()
            else:
                # 简单等间隔采样
                step = len(trades_df) // max_trades
                return trades_df.iloc[::step]
            
        except Exception as e:
            # 交易采样失败
            return trades_df.head(max_trades)

class ChartOptimizer:
    """图表性能优化器"""
    
    def __init__(self):
        self.sampler = DataSampler()
    
    def optimize_price_chart(self, df_signals: pd.DataFrame, df_trades: pd.DataFrame, 
                           config, max_points: int = 2000) -> go.Figure:
        """优化价格走势图"""
        try:
            # 数据采样
            sampled_signals = self.sampler.smart_sample(
                df_signals, 
                time_col='time' if 'time' in df_signals.columns else df_signals.index.name or 'index',
                value_cols=['price', 'signal', 'position']
            )
            
            sampled_trades = self.sampler.sample_trades(df_trades, max_trades=200)
            
            # 创建优化的图表
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                subplot_titles=('价格走势与交易信号', '交易信号强度'),
                vertical_spacing=0.1,
                row_heights=[0.7, 0.3]
            )
            
            # 价格线 - 使用采样数据
            if not sampled_signals.empty and 'price' in sampled_signals.columns:
                time_col = 'time' if 'time' in sampled_signals.columns else sampled_signals.index
                
                fig.add_trace(
                    go.Scattergl(  # 使用Scattergl提高性能
                        x=(sampled_signals[time_col] if 'time' in sampled_signals.columns else sampled_signals.index).tolist(),
                        y=sampled_signals['price'].tolist(),
                        mode='lines',
                        name='价格',
                        line=dict(color='blue', width=1),
                        hovertemplate='价格: %{y:.4f}<extra></extra>'
                    ),
                    row=1, col=1
                )
            
            # 交易点 - 使用采样的交易数据
            if not sampled_trades.empty:
                self._add_trade_markers(fig, sampled_trades, row=1, col=1)
            
            # 信号强度
            if not sampled_signals.empty and 'signal' in sampled_signals.columns:
                fig.add_trace(
                    go.Scattergl(
                        x=(sampled_signals[time_col] if 'time' in sampled_signals.columns else sampled_signals.index).tolist(),
                        y=sampled_signals['signal'].tolist(),
                        mode='lines',
                        name='信号',
                        line=dict(color='purple', width=1),
                        hovertemplate='信号: %{y:.6f}<extra></extra>'
                    ),
                    row=2, col=1
                )
                
                # 添加触发线
                if hasattr(config, 'buy_trigger_drop'):
                    fig.add_hline(
                        y=config.buy_trigger_drop,
                        line_dash="dash",
                        line_color="red",
                        row=2, col=1
                    )
            
            # 优化布局
            fig.update_layout(
                height=600,
                showlegend=True,
                template='plotly_white',
                hovermode='x unified'
            )
            
            # 添加采样信息
            original_count = len(df_signals) if df_signals is not None else 0
            sampled_count = len(sampled_signals) if sampled_signals is not None else 0
            
            if original_count > sampled_count:
                fig.add_annotation(
                    text=f"数据已优化: {original_count} → {sampled_count} 点",
                    xref="paper", yref="paper",
                    x=0.02, y=0.98,
                    showarrow=False,
                    font=dict(size=10, color="gray"),
                    bgcolor="rgba(255,255,255,0.8)"
                )
            
            return fig
            
        except Exception as e:
            st.error(f"优化价格图表失败: {e}")
            return go.Figure()
    
    def _add_trade_markers(self, fig: go.Figure, trades_df: pd.DataFrame, row: int, col: int):
        """添加交易标记"""
        try:
            # 确定交易类型列名
            type_col = None
            for col_name in ['type', 'action', 'side']:
                if col_name in trades_df.columns:
                    type_col = col_name
                    break
            
            if type_col is None:
                return
            
            # 买入交易
            buy_trades = trades_df[trades_df[type_col].isin(['BUY', 'buy', 'Buy'])]
            if not buy_trades.empty and 'price' in buy_trades.columns:
                time_data = buy_trades['time'] if 'time' in buy_trades.columns else buy_trades.index
                fig.add_trace(
                    go.Scatter(
                        x=time_data,
                        y=buy_trades['price'].tolist(),
                        mode='markers',
                        name='买入',
                        marker=dict(
                            symbol='triangle-up',
                            size=8,
                            color='green'
                        ),
                        hovertemplate='买入: %{y:.4f}<extra></extra>'
                    ),
                    row=row, col=col
                )
            
            # 卖出交易
            sell_trades = trades_df[trades_df[type_col].isin(['SELL', 'sell', 'Sell'])]
            if not sell_trades.empty and 'price' in sell_trades.columns:
                time_data = sell_trades['time'] if 'time' in sell_trades.columns else sell_trades.index
                fig.add_trace(
                    go.Scatter(
                        x=time_data,
                        y=sell_trades['price'].tolist(),
                        mode='markers',
                        name='卖出',
                        marker=dict(
                            symbol='triangle-down',
                            size=8,
                            color='red'
                        ),
                        hovertemplate='卖出: %{y:.4f}<extra></extra>'
                    ),
                    row=row, col=col
                )
                
        except Exception as e:
            st.warning(f"添加交易标记失败: {e}")
    
    def optimize_equity_curve(self, equity_data: pd.Series, title: str = "净值曲线") -> go.Figure:
        """优化净值曲线"""
        try:
            if equity_data is None or (hasattr(equity_data, 'empty') and equity_data.empty):
                return go.Figure()
            
            # 处理不同数据类型
            if isinstance(equity_data, pd.Series):
                # 检查是否有时间相关的列或索引
                if hasattr(equity_data, 'index') and hasattr(equity_data.index, 'name'):
                    if equity_data.index.name in ['time', 'timestamp', 'date', 'datetime']:
                        x_data = equity_data.index.tolist()
                    else:
                        # 创建默认时间序列
                        x_data = pd.date_range(start='2020-01-01', periods=len(equity_data), freq='D').tolist()
                else:
                    # 创建默认时间序列
                    x_data = pd.date_range(start='2020-01-01', periods=len(equity_data), freq='D').tolist()
                
                y_data = equity_data.values.tolist()
                
            elif isinstance(equity_data, pd.DataFrame):
                # 提取净值列
                if 'equity' in equity_data.columns:
                    equity_series = equity_data['equity']
                elif len(equity_data.columns) == 1:
                    equity_series = equity_data.iloc[:, 0]
                else:
                    return go.Figure()
                
                # 检查时间列
                time_cols = ['time', 'timestamp', 'date', 'datetime']
                time_col_found = None
                for col in time_cols:
                    if col in equity_data.columns:
                        time_col_found = col
                        break
                
                if time_col_found:
                    x_data = equity_data[time_col_found].tolist()
                elif hasattr(equity_data.index, 'name') and equity_data.index.name in time_cols:
                    x_data = equity_data.index.tolist()
                else:
                    # 创建默认时间序列
                    x_data = pd.date_range(start='2020-01-01', periods=len(equity_data), freq='D').tolist()
                
                y_data = equity_series.values.tolist()
            else:
                return go.Figure()
            
            # 智能采样（保持时间索引对应关系）
            if len(x_data) > 2000:
                step = len(x_data) // 2000
                sampled_indices = list(range(0, len(x_data), step))[:2000]
                x_data = [x_data[i] for i in sampled_indices]
                y_data = [y_data[i] for i in sampled_indices]
            
            # 创建图表
            fig = go.Figure()
            
            fig.add_trace(
                go.Scatter(
                    x=x_data,
                    y=y_data,
                    mode='lines',
                    name='净值',
                    line=dict(color='blue', width=2)
                )
            )
            
            # 基准线
            try:
                if len(equity_data) > 0:
                    initial_val = equity_data.iloc[0]
                    if hasattr(initial_val, 'iloc'):
                        initial_value = float(initial_val.iloc[0])
                    else:
                        initial_value = float(initial_val)
                else:
                    initial_value = 100000
            except (ValueError, TypeError, IndexError):
                initial_value = 100000
                
            fig.add_hline(
                y=initial_value,
                line_dash="dash",
                line_color="gray",
                annotation_text="初始净值"
            )
            
            fig.update_layout(
                title=title,
                xaxis_title="时间",
                yaxis_title="净值",
                height=400,
                template='plotly_white',
                xaxis=dict(
                    tickangle=45,
                ),
                showlegend=True,
                margin=dict(l=50, r=50, t=50, b=100)
            )
            
            # 添加采样信息（仅在实际采样时显示）
            if len(equity_data) > 2000:
                fig.add_annotation(
                    text=f"数据已优化: {len(equity_data)} → {len(y_data)} 点",
                    xref="paper", yref="paper",
                    x=0.02, y=0.98,
                    showarrow=False,
                    font=dict(size=10, color="gray"),
                    bgcolor="rgba(255,255,255,0.8)"
                )
            
            return fig
            
        except Exception as e:
            return go.Figure()
    
    def sample_trades(self, trades_df: pd.DataFrame, max_trades: int = 500) -> pd.DataFrame:
        """交易数据采样 - 保留所有重要交易"""
        try:
            if trades_df is None or trades_df.empty:
                return trades_df
            
            if len(trades_df) <= max_trades:
                return trades_df
            
            # 按盈亏排序，保留最大盈利和最大亏损的交易
            if 'pnl' in trades_df.columns:
                sorted_trades = trades_df.sort_values('pnl')
                
                # 保留前后各25%的极值交易
                extreme_count = max_trades // 4
                top_trades = sorted_trades.tail(extreme_count)  # 最大盈利
                bottom_trades = sorted_trades.head(extreme_count)  # 最大亏损
                
                # 剩余部分均匀采样
                middle_trades = sorted_trades.iloc[extreme_count:-extreme_count]
                remaining_count = max_trades - len(top_trades) - len(bottom_trades)
                
                if len(middle_trades) > remaining_count:
                    step = len(middle_trades) // remaining_count
                    sampled_middle = middle_trades.iloc[::step][:remaining_count]
                else:
                    sampled_middle = middle_trades
                
                result = pd.concat([bottom_trades, sampled_middle, top_trades])
                return result.sort_index()
            else:
                # 简单等间隔采样
                step = len(trades_df) // max_trades
                return trades_df.iloc[::step]
            
        except Exception as e:
            st.warning(f"交易采样失败: {e}")
            return trades_df.head(max_trades)

# 便捷函数
def create_optimized_charts(signals_df: pd.DataFrame, trades_df: pd.DataFrame, 
                          equity_data: pd.Series, config, max_points: int = 2000):
    """创建优化的图表集合"""
    optimizer = ChartOptimizer()
    
    charts = {}
    
    try:
        # 价格走势图
        charts['price_chart'] = optimizer.optimize_price_chart(
            signals_df, trades_df, config, max_points
        )
        
        # 净值曲线
        charts['equity_chart'] = optimizer.optimize_equity_curve(equity_data)
        
        return charts
        
    except Exception as e:
        st.error(f"创建优化图表失败: {e}")
        return {}

# ==================== 导出函数列表 ====================

__all__ = [
    'optimize_chart_data',
    'optimize_trade_data', 
    'smart_sample_data',
    'create_optimized_price_chart',
    'create_optimized_equity_curve',
    'create_optimized_trade_chart',
    'display_performance_info',
    'get_performance_stats',
    'DataSampler',
    'ChartOptimizer',
    'create_optimized_charts'
]