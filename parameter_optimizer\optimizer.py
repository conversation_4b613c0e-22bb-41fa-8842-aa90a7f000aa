#!/usr/bin/env python3
"""
参数优化器主模块
统一管理和调度各种优化算法
"""

import asyncio
import logging
from typing import Dict, List, Tuple, Callable, Optional, Any
from datetime import datetime
import json

from .algorithms.grid_search import GridSearch
from .algorithms.genetic import GeneticAlgorithm
from .algorithms.bayesian import BayesianOptimization
from .market_analyzer import MarketAnalyzer

logger = logging.getLogger(__name__)

class ParameterOptimizer:
    """参数优化器主类"""
    
    # 支持的优化算法
    ALGORITHMS = {
        'grid_search': GridSearch,
        'genetic': GeneticAlgorithm,
        'bayesian': BayesianOptimization
    }
    
    def __init__(self, 
                 param_space: Dict[str, Tuple[float, float, float]] = None,
                 evaluate_func: Callable = None):
        """
        初始化参数优化器
        
        Args:
            param_space: 参数空间定义
            evaluate_func: 评估函数
        """
        self.param_space = param_space or self._get_default_param_space()
        self.evaluate_func = evaluate_func
        self.market_analyzer = None  # 延迟初始化
        
        # 优化历史
        self.optimization_history: List[Dict] = []
        
        logger.info("参数优化器初始化完成")
    
    def _get_default_param_space(self) -> Dict[str, Tuple[float, float, float]]:
        """获取默认参数空间"""
        return {
            'buy_trigger_drop': (-0.012, -0.003, 0.001),
            'profit_target': (0.002, 0.010, 0.001),
            'stop_loss': (-0.030, -0.010, 0.002),
            'max_hold_time': (86400, 86400, 0)  # 固定24小时
        }
    
    async def optimize(self, 
                      symbol: str,
                      days: int,
                      algorithm: str = 'grid_search',
                      strategy_type: str = 'balanced',
                      **algorithm_params) -> List[Dict]:
        """
        执行参数优化
        
        Args:
            symbol: 交易标的
            days: 回测天数
            algorithm: 优化算法
            strategy_type: 策略类型
            **algorithm_params: 算法特定参数
            
        Returns:
            优化结果列表
        """
        logger.info(f"开始参数优化: {symbol}, 算法: {algorithm}, 策略: {strategy_type}")
        
        if algorithm not in self.ALGORITHMS:
            raise ValueError(f"不支持的优化算法: {algorithm}")
        
        if not self.evaluate_func:
            raise ValueError("未设置评估函数")
        
        try:
            # 根据策略类型调整参数空间
            adjusted_param_space = self._adjust_param_space_for_strategy(strategy_type)
            
            # 创建算法实例
            algorithm_class = self.ALGORITHMS[algorithm]
            
            if algorithm == 'grid_search':
                optimizer = algorithm_class(
                    param_space=adjusted_param_space,
                    **algorithm_params
                )
            elif algorithm == 'genetic':
                optimizer = algorithm_class(
                    param_space=adjusted_param_space,
                    population_size=algorithm_params.get('population_size', 50),
                    generations=algorithm_params.get('generations', 30),
                    mutation_rate=algorithm_params.get('mutation_rate', 0.1),
                    crossover_rate=algorithm_params.get('crossover_rate', 0.8),
                    **{k: v for k, v in algorithm_params.items() 
                       if k not in ['population_size', 'generations', 'mutation_rate', 'crossover_rate']}
                )
            elif algorithm == 'bayesian':
                optimizer = algorithm_class(
                    param_space=adjusted_param_space,
                    n_initial_points=algorithm_params.get('n_initial_points', 10),
                    n_iterations=algorithm_params.get('n_iterations', 50),
                    acquisition_function=algorithm_params.get('acquisition_function', 'ei'),
                    **{k: v for k, v in algorithm_params.items() 
                       if k not in ['n_initial_points', 'n_iterations', 'acquisition_function']}
                )
            
            # 执行优化
            start_time = datetime.now()
            results = await optimizer.optimize(symbol, days, self.evaluate_func)
            end_time = datetime.now()
            
            # 记录优化历史
            optimization_record = {
                'timestamp': start_time.isoformat(),
                'symbol': symbol,
                'days': days,
                'algorithm': algorithm,
                'strategy_type': strategy_type,
                'algorithm_params': algorithm_params,
                'duration': (end_time - start_time).total_seconds(),
                'results_count': len(results),
                'best_fitness': results[0]['metrics']['fitness'] if results else -999,
                'param_space': adjusted_param_space
            }
            
            # 添加算法特定的摘要信息
            if hasattr(optimizer, 'get_optimization_summary'):
                optimization_record['summary'] = optimizer.get_optimization_summary()
            
            self.optimization_history.append(optimization_record)
            
            logger.info(f"优化完成: 耗时 {optimization_record['duration']:.2f}秒, "
                       f"获得 {len(results)} 个结果")
            
            return results
            
        except Exception as e:
            logger.error(f"参数优化失败: {e}")
            raise
    
    def _adjust_param_space_for_strategy(self, strategy_type: str) -> Dict[str, Tuple[float, float, float]]:
        """根据策略类型调整参数空间"""
        base_space = self.param_space.copy()
        
        if strategy_type == 'conservative':
            # 保守策略：较小的触发阈值，较小的止盈目标
            base_space['buy_trigger_drop'] = (-0.008, -0.004, 0.001)
            base_space['profit_target'] = (0.003, 0.006, 0.001)
            base_space['stop_loss'] = (-0.025, -0.015, 0.002)
            
        elif strategy_type == 'aggressive':
            # 激进策略：较大的触发阈值，较大的止盈目标
            base_space['buy_trigger_drop'] = (-0.015, -0.008, 0.001)
            base_space['profit_target'] = (0.006, 0.012, 0.001)
            base_space['stop_loss'] = (-0.035, -0.020, 0.002)
            
        elif strategy_type == 'balanced':
            # 平衡策略：中等参数范围
            base_space['buy_trigger_drop'] = (-0.012, -0.005, 0.001)
            base_space['profit_target'] = (0.004, 0.008, 0.001)
            base_space['stop_loss'] = (-0.030, -0.015, 0.002)
        
        logger.info(f"为{strategy_type}策略调整参数空间")
        return base_space
    
    async def compare_algorithms(self, 
                                symbol: str,
                                days: int,
                                algorithms: List[str] = None,
                                strategy_type: str = 'balanced') -> Dict[str, List[Dict]]:
        """
        比较多种算法的优化效果
        
        Args:
            symbol: 交易标的
            days: 回测天数
            algorithms: 要比较的算法列表
            strategy_type: 策略类型
            
        Returns:
            各算法的优化结果
        """
        algorithms = algorithms or ['grid_search', 'genetic', 'bayesian']
        results = {}
        
        logger.info(f"开始算法比较: {algorithms}")
        
        for algorithm in algorithms:
            try:
                logger.info(f"运行算法: {algorithm}")
                
                # 根据算法设置合适的参数
                if algorithm == 'grid_search':
                    algorithm_params = {}
                elif algorithm == 'genetic':
                    algorithm_params = {
                        'population_size': 30,
                        'generations': 20
                    }
                elif algorithm == 'bayesian':
                    algorithm_params = {
                        'n_initial_points': 8,
                        'n_iterations': 25
                    }
                else:
                    algorithm_params = {}
                
                result = await self.optimize(
                    symbol=symbol,
                    days=days,
                    algorithm=algorithm,
                    strategy_type=strategy_type,
                    **algorithm_params
                )
                
                results[algorithm] = result
                
            except Exception as e:
                logger.error(f"算法 {algorithm} 运行失败: {e}")
                results[algorithm] = []
        
        # 生成比较报告
        self._generate_comparison_report(results)
        
        return results
    
    def _generate_comparison_report(self, results: Dict[str, List[Dict]]):
        """生成算法比较报告"""
        logger.info("=" * 50)
        logger.info("算法比较报告")
        logger.info("=" * 50)
        
        for algorithm, result_list in results.items():
            if result_list:
                best_result = result_list[0]
                best_fitness = best_result['metrics']['fitness']
                logger.info(f"{algorithm:12}: 最优适应度 {best_fitness:.4f}, "
                           f"结果数量 {len(result_list)}")
            else:
                logger.info(f"{algorithm:12}: 无有效结果")
        
        logger.info("=" * 50)
    
    def get_optimization_history(self) -> List[Dict]:
        """获取优化历史"""
        return self.optimization_history
    
    def get_best_results_summary(self, top_n: int = 5) -> List[Dict]:
        """获取历史最佳结果摘要"""
        all_results = []
        
        for record in self.optimization_history:
            if record.get('best_fitness', -999) > -999:
                all_results.append({
                    'timestamp': record['timestamp'],
                    'symbol': record['symbol'],
                    'algorithm': record['algorithm'],
                    'strategy_type': record['strategy_type'],
                    'fitness': record['best_fitness'],
                    'duration': record['duration']
                })
        
        # 按适应度排序
        all_results.sort(key=lambda x: x['fitness'], reverse=True)
        
        return all_results[:top_n]
    
    def set_evaluate_function(self, evaluate_func: Callable):
        """设置评估函数"""
        self.evaluate_func = evaluate_func
        logger.info("评估函数已设置")
    
    def set_param_space(self, param_space: Dict[str, Tuple[float, float, float]]):
        """设置参数空间"""
        self.param_space = param_space
        logger.info("参数空间已更新")
    
    @classmethod
    def get_available_algorithms(cls) -> List[str]:
        """获取可用的优化算法"""
        return list(cls.ALGORITHMS.keys())
    
    def get_algorithm_info(self, algorithm: str) -> Dict[str, Any]:
        """获取算法信息"""
        if algorithm not in self.ALGORITHMS:
            return {}
        
        algorithm_class = self.ALGORITHMS[algorithm]
        
        info = {
            'name': algorithm,
            'class': algorithm_class.__name__,
            'description': algorithm_class.__doc__ or "无描述",
            'suitable_for': self._get_algorithm_suitability(algorithm)
        }
        
        return info
    
    def _get_algorithm_suitability(self, algorithm: str) -> str:
        """获取算法适用性描述"""
        suitability = {
            'grid_search': "适用于参数空间较小的精确搜索，保证找到全局最优解",
            'genetic': "适用于复杂参数空间的全局优化，具有良好的探索能力",
            'bayesian': "适用于评估成本高的优化问题，能够智能选择评估点"
        }
        
        return suitability.get(algorithm, "未知适用性")


# 测试函数
async def test_parameter_optimizer():
    """测试参数优化器"""
    logger.info("开始测试参数优化器...")
    
    # 模拟评估函数
    async def mock_evaluate_func(config: Dict, symbol: str, days: int) -> Dict:
        """模拟评估函数"""
        await asyncio.sleep(0.01)  # 模拟计算时间
        
        # 复杂的适应度计算
        fitness = (
            abs(config['buy_trigger_drop']) * 20 +
            config['profit_target'] * 150 +
            abs(config['stop_loss']) * 8 +
            0.1  # 基础分数
        )
        
        return {
            'fitness': fitness,
            'total_return': fitness * 0.01,
            'max_drawdown': -fitness * 0.003,
            'sharpe_ratio': fitness * 0.15
        }
    
    try:
        # 创建优化器
        optimizer = ParameterOptimizer(evaluate_func=mock_evaluate_func)
        
        # 测试单个算法
        logger.info("测试网格搜索...")
        grid_results = await optimizer.optimize(
            symbol="TEST",
            days=30,
            algorithm='grid_search'
        )
        logger.info(f"网格搜索结果: {len(grid_results)} 个")
        
        # 测试算法比较
        logger.info("测试算法比较...")
        comparison_results = await optimizer.compare_algorithms(
            symbol="TEST",
            days=30,
            algorithms=['grid_search', 'genetic']
        )
        
        # 显示历史摘要
        history = optimizer.get_optimization_history()
        logger.info(f"优化历史: {len(history)} 条记录")
        
        best_results = optimizer.get_best_results_summary()
        logger.info(f"最佳结果: {best_results}")
        
        logger.info("✅ 参数优化器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 参数优化器测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_parameter_optimizer())
