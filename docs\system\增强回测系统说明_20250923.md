# 增强版策略回测使用指南

## 概述

增强版回测系统支持测试所有增强策略功能，包括：
- 风险控制（止损、时间止损、风险限制）
- 分批交易（分层买入、分批卖出）
- 手续费和滑点计算
- 详细的性能分析

## 使用方法

### 1. 基本回测
```bash
# 使用默认配置
python run_enhanced_backtest.py default

# 使用保守配置
python run_enhanced_backtest.py conservative

# 使用激进配置  
python run_enhanced_backtest.py aggressive
```

### 2. 自定义回测
```bash
# 直接运行回测脚本，可指定更多参数
python backtest_enhanced.py --symbol 159740 --start-date 2025-08-25 --end-date 2025-08-27 --plot
```

### 3. 配置对比
```python
# 在Python中运行
from run_enhanced_backtest import compare_strategies
compare_strategies()
```

## 配置说明

### 默认配置 (default)
- 买入触发: -0.6%
- 止盈目标: 0.6% 
- 止损线: -2.0%
- 最大持仓时间: 3600秒

### 保守配置 (conservative)
- 买入触发: -0.4%
- 止损线: -1.5%
- 最大持仓时间: 1800秒

### 激进配置 (aggressive)
- 买入触发: -0.8%
- 止损线: -2.5%
- 最大持仓时间: 7200秒

## 回测结果

回测系统会输出以下指标：
- **总收益率**: 策略总体收益
- **最大回撤**: 最大亏损幅度
- **夏普比率**: 风险调整后收益
- **胜率**: 盈利交易占比
- **交易次数**: 总交易数量
- **手续费**: 交易成本

## 图表分析

回测会生成4个图表：
1. **净值曲线**: 显示策略净值变化
2. **价格与仓位**: 显示价格走势和持仓变化
3. **回撤曲线**: 显示回撤情况
4. **交易点位**: 显示买卖点位置

## 注意事项

1. **数据要求**: 需要数据库中有对应时间段的tick数据
2. **参数调整**: 可以通过修改BacktestConfig来调整策略参数
3. **性能分析**: 关注夏普比率和最大回撤，不只是总收益率
4. **实盘差异**: 回测结果可能与实盘有差异，需要考虑滑点和延迟

## 扩展功能

可以通过修改代码添加：
- 更多技术指标
- 不同的风险管理规则
- 多标的组合回测
- 参数优化功能