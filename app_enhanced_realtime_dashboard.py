#!/usr/bin/env python3
"""
增强版实时交易模拟仪表板
支持增强版策略和自定义持仓情况
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from datetime import datetime, timedelta
import sqlite3
import time
import threading
from typing import Dict, List, Optional, Tuple
import json
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


# 导入增强版策略组件
from strategy_engine_enhanced import (
    Position, RiskManager, MarketRegime, EnhancedStrategy
)
from strategy_config import StrategyConfig
# from test_rule_engine_simple import SimpleRuleEngine

# 优化结果加载函数
def load_saved_optimal_configs(symbol: str) -> List[Dict]:
    """加载已保存的优化配置"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute("""
            SELECT config_name, parameters, performance_metrics,
                   optimization_method, fitness_score, created_at
            FROM optimal_configs
            WHERE symbol = ?
            ORDER BY fitness_score DESC, created_at DESC
        """, (symbol,))

        results = cursor.fetchall()
        conn.close()

        configs = []
        for row in results:
            config = {
                'config_name': row[0],
                'parameters': row[1],
                'performance_metrics': row[2],
                'optimization_method': row[3],
                'fitness_score': row[4],
                'created_at': row[5],
                'symbol': symbol
            }
            configs.append(config)

        return configs

    except Exception as e:
        st.error(f"加载优化配置失败: {e}")
        return []

# 预警系统相关函数
def get_db_connection():
    """获取数据库连接"""
    db_path = "ticks.db"
    conn = sqlite3.connect(db_path)
    conn.row_factory = sqlite3.Row
    return conn

def load_alert_rules(symbol: str):
    """加载预警规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT id, rule_name, condition_expr, channels, template,
                   cooldown_seconds, enabled, priority
            FROM alert_rules
            WHERE symbol=?
            ORDER BY priority, rule_name
        """, (symbol,))

        rules = []
        for row in cursor.fetchall():
            rules.append({
                'id': row['id'],
                'rule_name': row['rule_name'],
                'condition_expr': row['condition_expr'],
                'channels': json.loads(row['channels']),
                'template': row['template'],
                'cooldown_seconds': row['cooldown_seconds'],
                'enabled': row['enabled'],
                'priority': row['priority']
            })

        # 连接池自动管理连接
        return rules
    except Exception as e:
        st.error(f"加载预警规则失败: {e}")
        return []

def update_alert_rule_status(rule_id: int, enabled: bool):
    """更新预警规则状态"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            UPDATE alert_rules
            SET enabled=?, updated_at=?
            WHERE id=?
        """, (enabled, datetime.now().isoformat(), rule_id))
        conn.commit()
        # 连接池自动管理连接
    except Exception as e:
        st.error(f"更新预警规则失败: {e}")

def save_custom_alert_rule(symbol: str, rule_name: str, condition: str, priority: int):
    """保存自定义预警规则"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 验证条件表达式
        engine = SimpleRuleEngine()
        test_context = {
            'signal': -0.007,
            'price': 0.75,
            'volume': 15000,
            'position_profit_rate': 0.01
        }

        # 测试表达式是否有效
        try:
            engine.evaluate(condition, test_context)
        except Exception as e:
            st.error(f"预警条件表达式无效: {e}")
            return False

        cursor.execute("""
            INSERT INTO alert_rules
            (symbol, rule_name, condition_expr, channels, template,
             cooldown_seconds, enabled, priority, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            rule_name,
            condition,
            '["email"]',  # 默认邮件通知
            f'🚨 {rule_name}\\n条件: {condition}\\n时间: {{time}}',
            300,  # 默认5分钟冷却期
            True,
            priority,
            datetime.now().isoformat(),
            datetime.now().isoformat()
        ))

        conn.commit()
        # 连接池自动管理连接
        return True

    except Exception as e:
        st.error(f"保存预警规则失败: {e}")
        return False

def get_recent_alerts(symbol: str, limit: int = 10):
    """获取最近的预警历史"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("""
            SELECT rule_name, message, alert_time, success, channels
            FROM alert_history
            WHERE symbol=?
            ORDER BY alert_time DESC
            LIMIT ?
        """, (symbol, limit))

        alerts = []
        for row in cursor.fetchall():
            alerts.append({
                'rule_name': row['rule_name'],
                'message': row['message'],
                'alert_time': row['alert_time'],
                'success': row['success'],
                'channels': json.loads(row['channels']) if row['channels'] else []
            })

        # 连接池自动管理连接
        return alerts
    except Exception as e:
        st.error(f"获取预警历史失败: {e}")
        return []

def check_alert_conditions(symbol: str, current_data: dict):
    """检查预警条件"""
    try:
        # 加载活跃的预警规则
        rules = load_alert_rules(symbol)
        active_rules = [rule for rule in rules if rule['enabled']]

        if not active_rules:
            return []

        # 导入规则引擎
        engine = SimpleRuleEngine()

        triggered_alerts = []

        for rule in active_rules:
            try:
                # 评估规则条件
                if engine.evaluate(rule['condition_expr'], current_data):
                    # 检查冷却期
                    if not is_in_cooldown(rule['id'], rule['cooldown_seconds']):
                        triggered_alerts.append(rule)
                        # 记录预警历史
                        record_alert_history(symbol, rule, current_data)

            except Exception as e:
                st.error(f"评估预警规则 '{rule['rule_name']}' 失败: {e}")

        return triggered_alerts

    except Exception as e:
        st.error(f"检查预警条件失败: {e}")
        return []

def is_in_cooldown(rule_id: int, cooldown_seconds: int) -> bool:
    """检查规则是否在冷却期内"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取最近一次预警时间
        cursor.execute("""
            SELECT MAX(alert_time) as last_alert
            FROM alert_history
            WHERE rule_id=? AND success=1
        """, (rule_id,))

        result = cursor.fetchone()
        # 连接池自动管理连接

        if result and result['last_alert']:
            last_alert_time = datetime.fromisoformat(result['last_alert'])
            time_diff = (datetime.now() - last_alert_time).total_seconds()
            return time_diff < cooldown_seconds

        return False

    except Exception:
        return False

def record_alert_history(symbol: str, rule: dict, trigger_data: dict):
    """记录预警历史"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 格式化消息
        message = rule['template'].format(
            symbol=symbol,
            time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            **trigger_data
        )

        cursor.execute("""
            INSERT INTO alert_history
            (symbol, rule_name, rule_id, message, alert_time,
             channels, success, trigger_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            symbol,
            rule['rule_name'],
            rule['id'],
            message,
            datetime.now().isoformat(),
            json.dumps(rule['channels']),
            True,  # 假设发送成功
            json.dumps(trigger_data)
        ))

        conn.commit()
        # 连接池自动管理连接

    except Exception as e:
        st.error(f"记录预警历史失败: {e}")

# 页面配置
st.set_page_config(
    page_title="增强版实时交易模拟",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main-header {
        font-size: 2.0rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: rgba(240, 242, 246, 0.7); /* 添加透明度 */
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
        transition: transform 0.2s ease, box-shadow 0.2s ease; /* 添加过渡动画 */
    }
    
    .metric-card:hover {
        transform: translateY(-2px); /* 悬停时上移 */
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); /* 悬停时增强阴影 */
    }
    
    .metric-card.blue {
        background-color: rgba(31, 119, 180, 0.15); /* 蓝色半透明背景 */
        border-left: 4px solid #1f77b4;
    }
    
    .metric-card.green {
        background-color: rgba(44, 160, 44, 0.15); /* 绿色半透明背景 */
        border-left: 4px solid #2ca02c;
    }
    
    .metric-card.red {
        background-color: rgba(214, 40, 40, 0.15); /* 红色半透明背景 */
        border-left: 4px solid #d62728;
    }
    
    .metric-card.orange {
        background-color: rgba(255, 127, 14, 0.15); /* 橙色半透明背景 */
        border-left: 4px solid #ff7f0e;
    }
    
    .metric-card.purple {
        background-color: rgba(148, 103, 189, 0.15); /* 紫色半透明背景 */
        border-left: 4px solid #9467bd;
    }
    
    .positive {
        color: #00cc00;
        font-weight: bold;
    }
    .negative {
        color: #ff0000;
        font-weight: bold;
    }
    .neutral {
        color: #666666;
    }
    .trading-status {
        padding: 0.5rem;
        border-radius: 0.3rem;
        text-align: center;
        font-weight: bold;
        margin: 1rem 0;
    }
    .status-running {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    .status-stopped {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    .status-paused {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
</style>
""", unsafe_allow_html=True)

class RealtimeSimulator:
    """实时交易模拟器"""
    
    def __init__(self):
        self.strategy = None
        self.is_running = False
        self.is_paused = False
        self.current_data = None
        self.trade_history = []
        self.price_history = []
        self.signal_history = []
        self.equity_history = []
        self.last_update = None
        self.error_count = 0
        self.max_errors = 10
        self.last_successful_data = None
        
    def initialize_strategy(self, symbol: str, config_dict: Dict, initial_position: Dict, initial_capital: float):
        """初始化策略"""
        # 导入策略引擎
        # 创建一个简单的配置对象来存储参数
        class SimpleConfig:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
        
        self.config = SimpleConfig(config_dict)
        self.initial_capital = initial_capital
        
        # 初始化策略引擎 - 使用动态symbol参数
        self.strategy = EnhancedStrategy(symbol, initial_capital)
        
        # 设置策略参数
        self.strategy.current_params = {
            'buy_drop': config_dict.get('buy_trigger_drop', -0.006),
            'profit_target': config_dict.get('profit_target', 0.006),
            'stop_loss': config_dict.get('stop_loss', -0.02)
        }
        
        # 设置position_size参数
        if 'position_size' in config_dict:
            self.strategy.position_size = config_dict['position_size']
            
        # 设置exit_at_close参数
        if 'exit_at_close' in config_dict:
            self.strategy.exit_at_close = config_dict['exit_at_close']

        # 设置新的交易时段和平仓时间参数
        if 'trading_session' in config_dict:
            self.strategy.trading_session = config_dict['trading_session']
        if 'close_before_morning' in config_dict:
            self.strategy.close_before_morning = config_dict['close_before_morning']
        if 'close_before_afternoon' in config_dict:
            self.strategy.close_before_afternoon = config_dict['close_before_afternoon']

        # 初始化持仓
        if initial_position.get('quantity', 0) > 0:
            self.strategy.position.total_quantity = initial_position['quantity']
            self.strategy.position.avg_cost = initial_position['avg_cost']
            self.strategy.position.total_cost = initial_position['quantity'] * initial_position['avg_cost']
            self.strategy.position.first_buy_time = datetime.now()
        
        # 初始化风险管理
        self.strategy.risk_manager.update_equity(initial_capital)
        self.current_equity = initial_capital
        

    def _validate_tick_data(self, tick_data):
        """验证tick数据的有效性"""
        try:
            # 检查必要字段
            if not all(key in tick_data for key in ['time', 'price', 'volume']):
                return False
            
            # 检查价格合理性
            price = tick_data['price']
            if price <= 0 or price > 1000:
                return False
            
            # 检查成交量合理性
            volume = tick_data['volume']
            if volume < 0:
                return False
            
            return True
        except:
            return False

    def get_latest_tick(self, symbol: str) -> Optional[Dict]:
        """获取最新tick数据"""
        try:
            # 使用现有的数据库连接函数
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT tick_time, price, volume
                FROM ticks
                WHERE symbol=?
                ORDER BY tick_time DESC
                LIMIT 1
            """, (symbol,))
            
            result = cursor.fetchone()
            # 连接池自动管理连接
            
            if result:
                tick_data = {
                    'time': result[0],
                    'price': float(result[1]),
                    'volume': int(result[2])
                }
                
                # 验证数据有效性
                if self._validate_tick_data(tick_data):
                    self.last_successful_data = tick_data
                    self.error_count = 0  # 重置错误计数
                    return tick_data
                else:
                    return self.last_successful_data
            return self.last_successful_data
        except Exception as e:
            self.error_count += 1
            st.error(f"获取数据失败 (错误次数: {self.error_count}): {e}")
            
            # 如果错误次数过多，停止运行
            if self.error_count >= self.max_errors:
                st.error(f"数据获取连续失败 {self.max_errors} 次，已自动停止")
                self.is_running = False
            
            return self.last_successful_data
    
    def get_recent_ticks_by_count(self, symbol: str, tick_count: int = 20) -> pd.DataFrame:
        """
        根据tick数量获取最近的数据
        
        理论逻辑实现：
        1. 获取当前时间之前的20个ticks + 最新的tick（总共21个）
        2. 然后取最新的20个ticks用于信号计算
        """
        try:
            # print(f"🔍 [DATA DEBUG] 开始获取历史数据...")
            # print(f"🔍 [DATA DEBUG] 查询参数: symbol={symbol}, tick_count={tick_count}")
            
            conn = sqlite3.connect("ticks.db")

            # 首先检查数据库中是否有该symbol的数据
            check_query = "SELECT COUNT(*) FROM ticks WHERE symbol=?"
            cursor = conn.execute(check_query, [symbol])
            total_count = cursor.fetchone()[0]
            # print(f"🔍 [DATA DEBUG] 数据库中{symbol}总记录数: {total_count}")

            if total_count == 0:
                # print(f"🔍 [DATA DEBUG] 数据库中没有{symbol}的数据")
                conn.close()
                return pd.DataFrame()

            # 获取21个ticks（前20个 + 最新1个），然后取最新的20个
            # 这样确保我们有足够的数据来实现理论要求
            query = """
            SELECT tick_time, price, volume
            FROM ticks
            WHERE symbol=?
            ORDER BY tick_time DESC
            LIMIT ?
            """

            # 获取21个ticks以确保有足够数据
            df = pd.read_sql_query(query, conn, params=[symbol, tick_count + 1])
            # print(f"🔍 [DATA DEBUG] 查询返回{len(df)}条记录")
            
            conn.close()

            if not df.empty:
                # print(f"🔍 [DATA DEBUG] 原始数据前几行:")
                # print(df.head())
                
                # 按时间正序排列（最早的在前）
                df = df.sort_values('tick_time').reset_index(drop=True)
                df['tick_time'] = pd.to_datetime(df['tick_time'])
                df['price'] = pd.to_numeric(df['price'], errors='coerce')
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
                
                # print(f"🔍 [DATA DEBUG] 处理后数据: {len(df)}条")
                # print(f"🔍 [DATA DEBUG] 价格范围: {df['price'].min():.4f} - {df['price'].max():.4f}")
                
                # 取最新的20个ticks（如果数据超过20个）
                if len(df) > tick_count:
                    df = df.tail(tick_count).reset_index(drop=True)
                    # print(f"🔍 [DATA DEBUG] 截取到最新{tick_count}条数据")
            # else:
                # print(f"🔍 [DATA DEBUG] 查询结果为空")

            return df
        except Exception as e:
            # print(f"🔍 [DATA DEBUG] 异常: {e}")
            st.error(f"获取历史数据失败: {e}")
            import traceback
            traceback.print_exc()
            return pd.DataFrame()

    def get_recent_ticks(self, symbol: str, minutes: int = 5) -> pd.DataFrame:
        """获取最近几分钟的tick数据（保留兼容性）"""
        # 这个方法保留用于向后兼容，但建议使用get_recent_ticks_by_count
        try:
            conn = sqlite3.connect("ticks.db")

            # 修复时间格式问题：数据库中的时间格式是 'YYYY-MM-DD HH:MM:SS'
            since_time = (datetime.now() - timedelta(minutes=minutes)).strftime('%Y-%m-%d %H:%M:%S')

            query = """
            SELECT tick_time, price, volume
            FROM ticks
            WHERE symbol=? AND tick_time>=?
            ORDER BY tick_time ASC
            """

            df = pd.read_sql_query(query, conn, params=[symbol, since_time])
            conn.close()

            if not df.empty:
                df['tick_time'] = pd.to_datetime(df['tick_time'])
                df['price'] = pd.to_numeric(df['price'], errors='coerce')
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce')

            return df
        except Exception as e:
            st.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()

    def process_tick(self, tick_data: Dict, signal_window: int = 20) -> Dict:
        """处理单个tick数据"""
        if not self.strategy:
            return {}

        # 计算信号 - 严格按照理论要求实现
        # 理论逻辑：抽取当前时间之前的20个ticks再加上最新的tick，取最新的20个ticks
        
        # print(f"🔍 [REALTIME DEBUG] 开始计算信号...")
        # print(f"🔍 [REALTIME DEBUG] 当前tick数据: {tick_data}")
        # print(f"🔍 [REALTIME DEBUG] 信号窗口大小: {signal_window}")
        
        # 第一步：获取当前时间之前的历史数据（不包括当前tick）
        try:
            recent_ticks = self.get_recent_ticks_by_count(self.strategy.symbol, signal_window)
            print(f"🔍 [REALTIME DEBUG] 获取到历史数据: {len(recent_ticks)}条")
            if not recent_ticks.empty:
                print(f"🔍 [REALTIME DEBUG] 历史数据价格范围: {recent_ticks['price'].min():.4f} - {recent_ticks['price'].max():.4f}")
            else:
                print(f"🔍 [REALTIME DEBUG] ❌ 历史数据获取失败或为空！")
                # 尝试使用备用方法获取数据
                print(f"🔍 [REALTIME DEBUG] 尝试使用备用方法获取数据...")
                recent_ticks = self.get_recent_ticks(self.strategy.symbol, 5)  # 获取最近5分钟数据
                if not recent_ticks.empty:
                    # 转换列名并取最新20条
                    recent_ticks = recent_ticks.rename(columns={'tick_time': 'tick_time'})
                    if len(recent_ticks) > signal_window:
                        recent_ticks = recent_ticks.tail(signal_window).reset_index(drop=True)
                    print(f"🔍 [REALTIME DEBUG] 备用方法获取到: {len(recent_ticks)}条数据")
                else:
                    print(f"🔍 [REALTIME DEBUG] ❌ 备用方法也失败了！")
        except Exception as e:
            print(f"🔍 [REALTIME DEBUG] ❌ 数据获取异常: {e}")
            recent_ticks = pd.DataFrame()

        # 第二步：添加最新的tick数据
        if recent_ticks.empty:
            # 如果没有历史数据，只使用当前tick（无法计算信号）
            temp_df = pd.DataFrame([{
                'time': datetime.fromisoformat(tick_data['time'].replace('Z', '+00:00')) if isinstance(tick_data['time'], str) else tick_data['time'],
                'price': tick_data['price']
            }])
            print(f"🔍 [REALTIME DEBUG] 无历史数据，只使用当前tick")
        else:
            # 将当前tick添加到历史数据中
            current_tick_df = pd.DataFrame([{
                'tick_time': tick_data['time'],
                'price': tick_data['price'],
                'volume': tick_data['volume']
            }])

            print(f"🔍 [REALTIME DEBUG] 当前tick DataFrame: {current_tick_df}")

            # 合并历史数据和当前tick
            combined_ticks = pd.concat([recent_ticks, current_tick_df], ignore_index=True)
            print(f"🔍 [REALTIME DEBUG] 合并后数据: {len(combined_ticks)}条")

            # 第三步：取最新的20个ticks（确保包含当前tick）
            if len(combined_ticks) > signal_window:
                combined_ticks = combined_ticks.tail(signal_window).reset_index(drop=True)
                print(f"🔍 [REALTIME DEBUG] 截取到最新{signal_window}条数据")

            # 重命名列以匹配策略引擎的期望格式
            temp_df = combined_ticks.rename(columns={'tick_time': 'time'})
            print(f"🔍 [REALTIME DEBUG] 重命名后DataFrame列: {list(temp_df.columns)}")

        print(f"🔍 [REALTIME DEBUG] 传递给信号计算的数据:")
        print(f"   数据点数: {len(temp_df)}")
        if not temp_df.empty:
            print(f"   价格范围: {temp_df['price'].min():.4f} - {temp_df['price'].max():.4f}")
            print(f"   最新价格: {temp_df['price'].iloc[-1]:.4f}")
            print(f"   DataFrame:{temp_df}")
        
        # 第四步：计算信号（波动率）
        # 使用最新20个ticks中的最高价和最新价格计算波动率
        print(f"🔍 [REALTIME DEBUG] 调用信号计算函数...")
        signal = self.strategy._safe_return_20ticks(temp_df)
        print(f"🔍 [REALTIME DEBUG] 计算得到信号: {signal:.6f}")
        
        # 执行策略逻辑
        # 这里我们简化处理，直接根据信号和价格判断操作
        action = None
        current_price = tick_data['price']
        current_time = datetime.now()

        # 首先检查是否应该在时段结束前平仓
        should_close, close_reason = self.strategy.should_close_before_session_end(current_time)
        if should_close and self.strategy.position.total_quantity > 0:
            action = {
                'action': 'SELL',
                'quantity': self.strategy.position.total_quantity,
                'reason': close_reason
            }
            # 执行平仓
            self.strategy.position.total_quantity = 0
            self.strategy.position.avg_cost = 0
            self.strategy.position.total_cost = 0
            self.strategy.position.first_buy_time = None

        # 检查是否在允许的交易时段内
        elif not self.strategy.is_trading_time_allowed(current_time):
            action = {
                'action': 'HOLD',
                'reason': f'非{self.strategy.trading_session}交易时段'
            }

        # 判断是否买入（波动率大于阈值时买入）
        elif self.strategy.should_buy(signal, current_price):
            action = {
                'action': 'BUY',
                'reason': f'波动率触发买入: {signal*100:.4f}%'
            }
            # 使用策略引擎的execute_buy方法执行买入
            conn = sqlite3.connect(":memory:")  # 创建一个临时连接用于模拟
            self.strategy.execute_buy(conn, current_price, current_time)
            # 连接池自动管理连接
        # 判断是否需要收盘前平仓（兼容模式）
        elif (hasattr(self.strategy, 'exit_at_close') and self.strategy.exit_at_close and
              self.strategy.position.total_quantity > 0):
            # 检查是否接近收盘时间（14:55以后）
            if (current_time.hour == 14 and current_time.minute >= 55) or current_time.hour == 15:
                action = {
                    'action': 'SELL',
                    'quantity': self.strategy.position.total_quantity,
                    'reason': '收盘前平仓（兼容模式）'
                }
                # 执行卖出
                self.strategy.position.total_quantity = 0
                self.strategy.position.avg_cost = 0
                self.strategy.position.total_cost = 0
                self.strategy.position.first_buy_time = None
        # 判断是否卖出
        elif self.strategy.position.total_quantity > 0:
            should_sell, sell_ratio, sell_reason = self.strategy.should_sell(current_price)
            if should_sell:
                action = {
                    'action': 'SELL',
                    'quantity': int(self.strategy.position.total_quantity * sell_ratio),
                    'reason': sell_reason
                }
                # 更新持仓信息（模拟卖出）
                sell_qty = int(self.strategy.position.total_quantity * sell_ratio)
                self.strategy.position.total_quantity -= sell_qty
                if self.strategy.position.total_quantity <= 0:
                    self.strategy.position.total_quantity = 0
                    self.strategy.position.avg_cost = 0
                    self.strategy.position.total_cost = 0
                    self.strategy.position.first_buy_time = None
        
        if not action:
            action = {'action': 'HOLD'}
        
        # 记录历史数据
        self.price_history.append({
            'time': tick_data['time'],
            'price': tick_data['price'],
            'volume': tick_data['volume']
        })
        
        self.signal_history.append({
            'time': tick_data['time'],
            'signal': signal,
            'position': self.strategy.position.total_quantity
        })
        
        # 计算当前净值
        current_equity = self.strategy.risk_manager.current_equity
        if self.strategy.position.total_quantity > 0:
            market_value = self.strategy.position.total_quantity * current_price
            current_equity = self.strategy.risk_manager.current_equity - self.strategy.position.total_cost + market_value
            self.strategy.risk_manager.update_equity(current_equity)
        else:
            # 如果没有持仓，净值就是初始资金
            current_equity = self.strategy.risk_manager.initial_capital
            self.strategy.risk_manager.update_equity(current_equity)
        
        self.equity_history.append({
            'time': tick_data['time'],
            'equity': current_equity,
            'position_value': self.strategy.position.total_quantity * tick_data['price'],
            'cash': current_equity - (self.strategy.position.total_quantity * tick_data['price'])
        })
        self.current_equity = current_equity
        
        # 记录交易
        if action and action.get('action') != 'HOLD':
            trade_record = {
                'time': tick_data['time'],
                'action': action['action'],
                'quantity': action.get('quantity', self.strategy.position.total_quantity if action['action'] == 'SELL' else 0),
                'price': tick_data['price'],
                'reason': action.get('reason', ''),
                'position_after': self.strategy.position.total_quantity
            }
            self.trade_history.append(trade_record)
        
        return {
            'signal': signal,
            'action': action,
            'position': self.strategy.position.total_quantity,
            'equity': current_equity,
            'pnl': current_equity - self.strategy.risk_manager.initial_capital
        }

@st.cache_data
def load_available_symbols():
    """加载可用的交易标的"""
    try:
        conn = sqlite3.connect("ticks.db")
        cursor = conn.execute("SELECT DISTINCT symbol FROM ticks ORDER BY symbol")
        symbols = [row[0] for row in cursor.fetchall()]
        # 连接池自动管理连接
        return symbols
    except:
        return ["159740"]

def create_realtime_chart(simulator: RealtimeSimulator, lookback_minutes: int = 30) -> go.Figure:
    """创建实时图表 - 参照回测界面格式"""
    if not simulator.price_history:
        fig = go.Figure()
        fig.add_annotation(
            text="等待数据...",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=20)
        )
        return fig
    
    # 获取最近的数据
    cutoff_time = datetime.now() - timedelta(minutes=lookback_minutes)
    
    price_data = [p for p in simulator.price_history 
                  if datetime.fromisoformat(p['time'].replace('Z', '+00:00')) > cutoff_time]
    signal_data = [s for s in simulator.signal_history 
                   if datetime.fromisoformat(s['time'].replace('Z', '+00:00')) > cutoff_time]
    trade_data = [t for t in simulator.trade_history 
                  if datetime.fromisoformat(t['time'].replace('Z', '+00:00')) > cutoff_time]
    
    if not price_data:
        fig = go.Figure()
        fig.add_annotation(text="暂无数据", x=0.5, y=0.5, showarrow=False)
        return fig
    
    # 参照回测界面的布局格式
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        # subplot_titles=('实时价格走势与交易信号', '持仓变化', '交易信号强度'),
        row_heights=[0.5, 0.25, 0.25]
    )
    
    # 价格走势 - 参照回测界面样式
    times = [p['time'] for p in price_data]
    prices = [p['price'] for p in price_data]
    
    fig.add_trace(
        go.Scatter(
            x=times, y=prices,
            mode='lines',
            name='价格',
            line=dict(color='blue', width=1),  # 与回测界面一致的线宽
            hovertemplate='时间: %{x}<br>价格: %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 交易点位 - 参照回测界面样式
    if trade_data:
        buy_trades = [t for t in trade_data if t['action'] == 'BUY']
        sell_trades = [t for t in trade_data if t['action'] == 'SELL']
        
        if buy_trades:
            fig.add_trace(
                go.Scatter(
                    x=[t['time'] for t in buy_trades],
                    y=[t['price'] for t in buy_trades],
                    mode='markers',
                    name='买入',
                    marker=dict(
                        symbol='triangle-up',
                        size=12,  # 与回测界面一致的大小
                        color='green',
                        line=dict(width=2, color='darkgreen')  # 添加边框
                    ),
                    hovertemplate='买入<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=[t.get('quantity', 0) for t in buy_trades]
                ),
                row=1, col=1
            )
        
        if sell_trades:
            fig.add_trace(
                go.Scatter(
                    x=[t['time'] for t in sell_trades],
                    y=[t['price'] for t in sell_trades],
                    mode='markers',
                    name='卖出',
                    marker=dict(
                        symbol='triangle-down',
                        size=12,  # 与回测界面一致的大小
                        color='red',
                        line=dict(width=2, color='darkred')  # 添加边框
                    ),
                    hovertemplate='卖出<br>时间: %{x}<br>价格: %{y:.4f}<br>数量: %{customdata}<extra></extra>',
                    customdata=[t.get('quantity', 0) for t in sell_trades]
                ),
                row=1, col=1
            )
    
    # 持仓变化 - 参照回测界面样式
    if signal_data:
        fig.add_trace(
            go.Scatter(
                x=[s['time'] for s in signal_data],
                y=[s['position'] for s in signal_data],
                mode='lines',
                name='持仓',
                line=dict(color='orange', width=2),
                fill='tonexty',
                hovertemplate='时间: %{x}<br>持仓: %{y}<extra></extra>'
            ),
            row=2, col=1
        )
    
    # 交易信号强度 - 参照回测界面样式，使用动态阈值
    if signal_data and simulator.strategy:
        signals = [s['signal'] for s in signal_data]
        
        # 使用策略配置的动态阈值
        # 对于波动率信号，买入阈值应该是较高的波动率，卖出阈值是较低的波动率
        # 从策略配置中获取相关参数
        buy_threshold = simulator.strategy.current_params.get('buy_drop', -0.006)  # 使用买入触发跌幅的绝对值作为买入阈值
        profit_threshold = simulator.strategy.current_params.get('profit_target', 0.0025)  # 使用止盈目标作为较高阈值线
        
        # 根据动态阈值设置颜色
        signal_colors = [
            'red' if s >= profit_threshold 
            else 'green' if s <= buy_threshold 
            else 'gray' 
            for s in signals
        ]
        
        fig.add_trace(
            go.Scatter(
                x=[s['time'] for s in signal_data],
                y=signals,
                mode='markers',
                name='信号',
                marker=dict(
                    color=signal_colors,
                    size=4,  # 与回测界面一致的大小
                    opacity=0.6
                ),
                hovertemplate='时间: %{x}<br>信号: %{y:.4%}<extra></extra>'
            ),
            row=3, col=1
        )
        
        # 添加参数化的信号阈值线 - 参照回测界面
        fig.add_hline(y=buy_threshold, line_dash="dash", line_color="green",
                      annotation_text=f"买入阈值: {buy_threshold*100:.4f}%", row=3, col=1)
        fig.add_hline(y=profit_threshold, line_dash="dash", line_color="red",
                      annotation_text=f"止盈阈值: {profit_threshold*100:.4f}%", row=3, col=1)
    
    # 更新布局 - 参照回测界面样式，针对3秒tick间隔优化时间轴
    fig.update_layout(
        title="实时策略执行详情",  # 与回测界面标题风格一致
        height=800,  # 与回测界面一致的高度
        showlegend=True,
        hovermode='x unified',
        xaxis3=dict(
            title="时间",
            rangeslider=dict(visible=True, thickness=0.05),  # 添加时间滑块
            type='date',
            dtick=30000,  # 每30秒一个刻度（适配3秒tick间隔，每10个tick显示一个刻度）
            tickformat='%H:%M:%S',  # 显示时分秒格式
            tickangle=45  # 倾斜显示避免重叠
        )
    )

    # 为所有子图统一设置时间轴格式
    fig.update_xaxes(
        dtick=30000,  # 每30秒一个刻度
        tickformat='%H:%M:%S',  # 时分秒格式
        tickangle=45  # 倾斜45度避免重叠
    )
    
    fig.update_yaxes(title_text="价格", row=1, col=1)
    fig.update_yaxes(title_text="持仓数量", row=2, col=1)
    fig.update_yaxes(title_text="信号强度 (%)", row=3, col=1, tickformat='.4%')
    
    return fig

def create_equity_chart(simulator: RealtimeSimulator) -> go.Figure:
    """创建净值图表 - 参照回测界面格式"""
    if not simulator.equity_history:
        fig = go.Figure()
        fig.add_annotation(text="等待数据...", x=0.5, y=0.5, showarrow=False)
        return fig
    
    # 参照回测界面的双子图布局
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('实时净值曲线', '实时回撤曲线'),
        row_heights=[0.7, 0.3]
    )
    
    times = [e['time'] for e in simulator.equity_history]
    equities = [e['equity'] for e in simulator.equity_history]
    
    # 净值曲线
    fig.add_trace(
        go.Scatter(
            x=times, y=equities,
            mode='lines',
            name='净值',
            line=dict(color='blue', width=2),
            hovertemplate='时间: %{x}<br>净值: %{y:,.2f}<extra></extra>'
        ),
        row=1, col=1
    )
    
    # 初始资金线
    if simulator.strategy and hasattr(simulator.strategy, 'config'):
        initial_capital = getattr(simulator.strategy.config, 'initial_capital', simulator.initial_capital)
        fig.add_hline(
            y=initial_capital,
            line_dash="dash",
            line_color="gray",
            annotation_text=f"初始资金: {initial_capital:,.0f}",
            row=1, col=1
        )
    
    # 回撤曲线
    if len(equities) > 1:
        equity_series = pd.Series(equities)
        peak = equity_series.cummax()
        drawdown = (equity_series - peak) / peak
        
        fig.add_trace(
            go.Scatter(
                x=times,
                y=drawdown,
                mode='lines',
                name='回撤',
                line=dict(color='red', width=1),
                fill='tonexty',
                fillcolor='rgba(255,0,0,0.3)',
                hovertemplate='时间: %{x}<br>回撤: %{y:.2%}<extra></extra>'
            ),
            row=2, col=1
        )
    
    # 更新布局 - 参照回测界面样式，针对3秒tick间隔优化时间轴
    fig.update_layout(
        title="实时策略净值表现",
        height=600,  # 与回测界面一致的高度
        showlegend=True,
        hovermode='x unified',
        xaxis2=dict(
            title="时间",
            type='date',
            dtick=30000,  # 每30秒一个刻度（适配3秒tick间隔）
            tickformat='%H:%M:%S',  # 显示时分秒格式
            tickangle=45  # 倾斜显示避免重叠
        )
    )

    # 为所有子图统一设置时间轴格式
    fig.update_xaxes(
        dtick=30000,  # 每30秒一个刻度
        tickformat='%H:%M:%S',  # 时分秒格式
        tickangle=45  # 倾斜45度避免重叠
    )

    fig.update_yaxes(title_text="净值", row=1, col=1)
    fig.update_yaxes(title_text="回撤", tickformat='.2%', row=2, col=1)
    
    return fig

def main():
    """主函数"""
    st.markdown('<h1 class="main-header">🚀 增强版实时交易模拟</h1>', unsafe_allow_html=True)
    
    # 初始化session state
    if 'simulator' not in st.session_state:
        st.session_state.simulator = RealtimeSimulator()
    
    simulator = st.session_state.simulator
    
    # 侧边栏配置
    st.sidebar.header("🎛️ 交易配置")
    
    # 基本设置
    symbols = load_available_symbols()
    symbol = st.sidebar.selectbox("交易标的", symbols, index=0)
    
    initial_capital = st.sidebar.number_input(
        "初始资金", 
        min_value=100000, 
        max_value=10000000, 
        value=1000000, 
        step=100000
    )
    
    # 策略参数 - 参照回测界面补充完整
    st.sidebar.subheader("🎯 策略参数")
    
    # 预设配置选择 - 包含优化结果
    preset_options = ["自定义"] + list(StrategyConfig.STRATEGY_PRESETS.keys())

    # 加载优化结果作为预设选项
    saved_configs = load_saved_optimal_configs(symbol)
    optimization_options = []
    if saved_configs:
        optimization_options = [f"🎯 {config['config_name']} (适应度: {config['fitness_score']:.3f})"
                              for config in saved_configs]
        preset_options.extend(optimization_options)

    config_preset = st.sidebar.selectbox(
        "预设配置",
        preset_options,
        help="选择预设配置或优化结果"
    )

    # 刷新优化结果按钮
    if saved_configs and st.sidebar.button("🔄 刷新优化结果", help="重新加载最新的优化结果"):
        st.rerun()
    
    # 根据预设配置设置默认值
    if config_preset.startswith("🎯"):
        # 优化结果配置
        # 从配置名称中提取索引
        config_name = config_preset.split(" ")[1]  # 提取配置名称
        selected_config = None
        for config in saved_configs:
            if config['config_name'] == config_name:
                selected_config = config
                break

        if selected_config:
            # 解析优化参数
            params = json.loads(selected_config['parameters'])
            buy_trigger = params.get('buy_trigger_drop', -0.006)
            profit_target = params.get('profit_target', 0.005)
            stop_loss = params.get('stop_loss', -0.02)
            max_hold_time = params.get('max_hold_time', 86400)

            # 显示优化结果信息
            st.sidebar.info(f"""
            **📊 已加载优化结果:**
            - 配置: {selected_config['config_name']}
            - 适应度: {selected_config['fitness_score']:.3f}
            - 优化方法: {selected_config['optimization_method']}
            """)
        else:
            # 如果找不到配置，使用默认值
            defaults = StrategyConfig.get_default_values()
            buy_trigger = defaults['buy_trigger_drop']
            profit_target = defaults['profit_target']
            stop_loss = defaults['stop_loss']
            max_hold_time = defaults['max_hold_time']
    elif config_preset in StrategyConfig.STRATEGY_PRESETS:
        # 传统预设配置
        preset_config = StrategyConfig.get_preset_config(config_preset)
        buy_trigger = preset_config['buy_trigger_drop']
        profit_target = preset_config['profit_target']
        stop_loss = preset_config['stop_loss']
        max_hold_time = preset_config['max_hold_time']
    else:  # 自定义
        defaults = StrategyConfig.get_default_values()
        buy_trigger = defaults['buy_trigger_drop']
        profit_target = defaults['profit_target']
        stop_loss = defaults['stop_loss']
        max_hold_time = defaults['max_hold_time']
    
    # 参数调整控件 - 自定义模式或优化结果微调
    if config_preset == "自定义" or config_preset.startswith("🎯"):
        # 显示参数调整控件
        if config_preset.startswith("🎯"):
            st.sidebar.markdown("**🔧 参数微调:**")
            st.sidebar.caption("可以在优化结果基础上微调参数")

        # 使用统一配置的参数边界
        trigger_config = StrategyConfig.get_streamlit_config('buy_trigger_drop')
        buy_trigger = st.sidebar.slider(
            "买入触发跌幅",
            min_value=trigger_config['min_value'] * 100,
            max_value=trigger_config['max_value'] * 100,
            value=buy_trigger * 100,
            step=trigger_config['step'] * 100,
            format="%.2f%%",
            help=trigger_config['help']
        ) / 100
        
        profit_config = StrategyConfig.get_streamlit_config('profit_target')
        profit_target = st.sidebar.slider(
            "止盈目标", 
            min_value=profit_config['min_value'] * 100,
            max_value=profit_config['max_value'] * 100,
            value=profit_target * 100, 
            step=profit_config['step'] * 100,
            format="%.2f%%",
            help=profit_config['help']
        ) / 100
        
        loss_config = StrategyConfig.get_streamlit_config('stop_loss')
        stop_loss = st.sidebar.slider(
            "止损线", 
            min_value=loss_config['min_value'] * 100,
            max_value=loss_config['max_value'] * 100,
            value=stop_loss * 100, 
            step=loss_config['step'] * 100,
            format="%.2f%%",
            help=loss_config['help']
        ) / 100
        
        holding_config = StrategyConfig.get_streamlit_config('max_hold_time')
        max_hold_time = st.sidebar.slider(
            "最大持仓时间", 
            min_value=int(holding_config['min_value'] ),
            max_value=int(holding_config['max_value'] ),
            value=int(max_hold_time), 
            step=int(holding_config['step']),
            format="%d秒",
            help=holding_config['help']
        )
    else:
        # 预设配置的只读显示
        st.sidebar.markdown("**📋 当前参数:**")
        if config_preset.startswith("🎯"):
            st.sidebar.write(f"🎯 买入触发跌幅: {buy_trigger:.2%}")
            st.sidebar.write(f"💰 止盈目标: {profit_target:.2%}")
            st.sidebar.write(f"🛡️ 止损线: {abs(stop_loss):.2%}")
            st.sidebar.write(f"⏱️ 最大持仓时间: {max_hold_time}秒")
            st.sidebar.caption("💡 选择'自定义'或优化结果可微调参数")
        else:
            st.sidebar.write(f"买入触发跌幅: {buy_trigger:.2%}")
            st.sidebar.write(f"止盈目标: {profit_target:.2%}")
            st.sidebar.write(f"止损线: {abs(stop_loss):.2%}")
            st.sidebar.write(f"最大持仓时间: {max_hold_time}秒")
    
    # 使用统一配置的手续费和滑点参数
    commission_config = StrategyConfig.get_streamlit_config('commission_rate')
    commission_rate = st.sidebar.slider(
        "手续费率", 
        min_value=commission_config['min_value'],
        max_value=commission_config['max_value'],
        value=commission_config['value'],
        step=commission_config['step'],
        format="%.4f",
        help=commission_config['help']
    )
    
    slippage_config = StrategyConfig.get_streamlit_config('slippage')
    slippage = st.sidebar.slider(
        "滑点", 
        min_value=slippage_config['min_value'],
        max_value=slippage_config['max_value'],
        value=slippage_config['value'],
        step=slippage_config['step'],
        format="%.4f",
        help=slippage_config['help']
    )
    
    # 高级参数设置
    st.sidebar.markdown("---")
    st.sidebar.header("🔧 高级参数")
    
    # 技术指标参数
    signal_window_config = StrategyConfig.get_streamlit_config('signal_window')
    signal_window = st.sidebar.slider(
        "信号计算窗口(tick数)",
        min_value=int(signal_window_config['min_value']),
        max_value=int(signal_window_config['max_value']),
        value=int(signal_window_config['value']),
        step=int(signal_window_config['step']),
        format="%d个tick",
        help=signal_window_config['help']
    )

    # 最小持仓时间
    min_hold_time = st.sidebar.slider(
        "最小持仓时间",
        min_value=10,
        max_value=300,
        value=30,
        step=10,
        format="%d秒",
        help="防止频繁交易的最小持仓时间保护"
    )
    
    # 分层买入参数
    st.sidebar.subheader("📊 分层买入设置")
    layer1_config = StrategyConfig.get_streamlit_config('layer1_ratio')
    layer1_ratio = st.sidebar.slider(
        "第一层买入比例",
        min_value=int(layer1_config['min_value'] * 100),
        max_value=int(layer1_config['max_value'] * 100),
        value=int(layer1_config['value'] * 100),
        step=int(layer1_config['step'] * 100),
        format="%.0f%%",
        help=layer1_config['help']
    ) / 100
    
    layer2_config = StrategyConfig.get_streamlit_config('layer2_ratio')
    layer2_ratio = st.sidebar.slider(
        "第二层买入比例", 
        min_value=int(layer2_config['min_value'] * 100),
        max_value=int(layer2_config['max_value'] * 100),
        value=int(layer2_config['value'] * 100),
        step=int(layer2_config['step'] * 100),
        format="%.0f%%",
        help=layer2_config['help']
    ) / 100
    
    layer3_config = StrategyConfig.get_streamlit_config('layer3_ratio')
    layer3_ratio = st.sidebar.slider(
        "第三层买入比例",
        min_value=int(layer3_config['min_value'] * 100),
        max_value=int(layer3_config['max_value'] * 100),
        value=int(layer3_config['value'] * 100),
        step=int(layer3_config['step'] * 100),
        format="%.0f%%",
        help=layer3_config['help']
    ) / 100
    
    # 分批止盈参数
    st.sidebar.subheader("💰 分批止盈设置")
    
    # 获取分批止盈参数配置
    partial_profit_config1 = StrategyConfig.get_param_config('partial_profit_multiplier1')
    partial_profit_config2 = StrategyConfig.get_param_config('partial_profit_multiplier2')
    partial_profit_config3 = StrategyConfig.get_param_config('partial_profit_multiplier3')
    
    partial_profit_multiplier1 = st.sidebar.slider(
        "第一次止盈倍数",
        min_value=partial_profit_config1.min_value,
        max_value=partial_profit_config1.max_value,
        value=float(partial_profit_config1.default_value),
        step=partial_profit_config1.step,
        format="%.1fx",
        help="相对于止盈目标的倍数"
    )
    
    partial_profit_multiplier2 = st.sidebar.slider(
        "第二次止盈倍数",
        min_value=partial_profit_config2.min_value,
        max_value=partial_profit_config2.max_value,
        value=float(partial_profit_config2.default_value),
        step=partial_profit_config2.step,
        format="%.1fx"
    )
    
    partial_profit_multiplier3 = st.sidebar.slider(
        "第三次止盈倍数",
        min_value=partial_profit_config3.min_value,
        max_value=partial_profit_config3.max_value,
        value=float(partial_profit_config3.default_value),
        step=partial_profit_config3.step,
        format="%.1fx"
    )
    
    # 获取分批卖出比例参数配置
    partial_sell_config1 = StrategyConfig.get_param_config('partial_sell_ratio1')
    partial_sell_config2 = StrategyConfig.get_param_config('partial_sell_ratio2')
    partial_sell_config3 = StrategyConfig.get_param_config('partial_sell_ratio3')
    
    partial_sell_ratio1 = st.sidebar.slider(
        "第一次卖出比例",
        min_value=partial_sell_config1.min_value * 100,
        max_value=partial_sell_config1.max_value * 100,
        value=partial_sell_config1.default_value * 100,
        step=partial_sell_config1.step * 100,
        format="%.0f%%"
    ) / 100
    
    partial_sell_ratio2 = st.sidebar.slider(
        "第二次卖出比例",
        min_value=partial_sell_config2.min_value * 100,
        max_value=partial_sell_config2.max_value * 100,
        value=partial_sell_config2.default_value * 100,
        step=partial_sell_config2.step * 100,
        format="%.0f%%"
    ) / 100
    
    partial_sell_ratio3 = st.sidebar.slider(
        "第三次卖出比例",
        min_value=partial_sell_config3.min_value * 100,
        max_value=partial_sell_config3.max_value * 100,
        value=partial_sell_config3.default_value * 100,
        step=partial_sell_config3.step * 100,
        format="%.0f%%"
    ) / 100
    
    # 风险控制参数
    st.sidebar.subheader("⚠️ 风险控制")

    # 新增：允许交易时段参数
    st.sidebar.markdown("**📅 交易时段控制**")
    trading_session = st.sidebar.selectbox(
        "允许交易时段",
        options=["全天", "上午", "下午"],
        index=0,
        help="选择允许进行交易的时段\n• 全天: 9:30-11:30 和 13:00-15:00\n• 上午: 9:30-11:30\n• 下午: 13:00-15:00"
    )

    # 新增：平仓时间参数
    st.sidebar.markdown("**⏰ 平仓时间控制**")
    col1, col2 = st.sidebar.columns(2)
    with col1:
        close_before_morning = st.checkbox(
            "上午收盘前",
            value=False,
            help="在上午收盘前（11:25）自动平仓"
        )
    with col2:
        close_before_afternoon = st.checkbox(
            "下午收盘前",
            value=True,
            help="在下午收盘前（14:55）自动平仓"
        )

    # 原有的收盘前平仓参数（保持兼容性）
    exit_at_close_config = StrategyConfig.get_param_config('exit_at_close')
    exit_at_close = st.sidebar.checkbox(
        "收盘前平仓（兼容模式）",
        value=bool(exit_at_close_config.default_value),
        help="兼容原有的收盘前平仓设置"
    )
    
    # 获取风险控制参数配置
    daily_loss_config = StrategyConfig.get_param_config('daily_loss_limit')
    max_drawdown_config = StrategyConfig.get_param_config('max_drawdown_limit')
    
    daily_loss_limit = st.sidebar.slider(
        "日损失限制",
        min_value=daily_loss_config.min_value * 100,
        max_value=daily_loss_config.max_value * 100,
        value=daily_loss_config.default_value * 100,
        step=daily_loss_config.step * 100,
        format="%.0f%%",
        help="单日最大允许损失"
    ) / 100
    
    max_drawdown_limit = st.sidebar.slider(
        "最大回撤限制",
        min_value=max_drawdown_config.min_value * 100,
        max_value=max_drawdown_config.max_value * 100,
        value=max_drawdown_config.default_value * 100,
        step=max_drawdown_config.step * 100,
        format="%.0f%%",
        help="最大允许回撤"
    ) / 100
    
    # 获取最大持仓数量参数配置
    max_position_config = StrategyConfig.get_param_config('max_position')
    max_position = st.sidebar.number_input(
        "最大持仓数量",
        min_value=int(max_position_config.min_value),
        max_value=int(max_position_config.max_value),
        value=int(max_position_config.default_value),
        step=int(max_position_config.step),
        format="%d"
    )
    
    # 获取单次买入仓位大小参数配置
    position_size_config = StrategyConfig.get_param_config('position_size')
    position_size = st.sidebar.number_input(
        "单次买入仓位大小",
        min_value=int(position_size_config.min_value),
        max_value=int(position_size_config.max_value),
        value=int(position_size_config.default_value),
        step=int(position_size_config.step),
        format="%d"
    )
    
    # 初始持仓设置
    st.sidebar.subheader("💼 初始持仓")
    
    # 获取初始持仓参数配置
    initial_position_config = StrategyConfig.get_param_config('max_position')
    
    initial_position_qty = st.sidebar.number_input(
        "初始持仓数量", 
        min_value=0, 
        max_value=int(initial_position_config.max_value),
        value=0, 
        step=10000
    )
    
    initial_avg_cost = st.sidebar.number_input(
        "初始持仓成本",
        min_value=0.0,
        max_value=200.0,
        value=0.0,
        step=0.001,
        format="%.4f",
        key="initial_avg_cost"
    )

    # 智能预警系统
    st.sidebar.subheader("🚨 智能预警系统")

    # 预警开关
    alert_enabled = st.sidebar.checkbox(
        "启用预警系统",
        value=True,
        help="开启后将根据设定的规则发送预警"
    )

    if alert_enabled:
        # 预警规则选择
        alert_rules = load_alert_rules(symbol)

        if alert_rules:
            st.sidebar.markdown("**活跃预警规则:**")
            for rule in alert_rules:
                with st.sidebar.expander(f"📋 {rule['rule_name']}", expanded=False):
                    st.write(f"**条件:** {rule['condition_expr']}")
                    st.write(f"**优先级:** {'🔴' if rule['priority'] == 1 else '🟡' if rule['priority'] == 2 else '🟢'}")
                    st.write(f"**冷却期:** {rule['cooldown_seconds']}秒")

                    # 启用/禁用开关
                    enabled = st.checkbox(
                        "启用此规则",
                        value=bool(rule['enabled']),
                        key=f"rule_{rule['id']}"
                    )

                    if enabled != bool(rule['enabled']):
                        update_alert_rule_status(rule['id'], enabled)
        else:
            st.sidebar.info("暂无预警规则，使用默认规则")

        # 添加自定义预警规则
        with st.sidebar.expander("➕ 添加自定义预警", expanded=False):
            custom_rule_name = st.text_input("规则名称", placeholder="例如：强买入信号")
            custom_condition = st.text_input(
                "预警条件",
                placeholder="例如：signal <= -0.008 and volume > 15000",
                help="支持的变量：signal (小数格式，如-0.008表示-0.8%), price, volume, position_profit_rate"
            )
            custom_priority = st.selectbox("优先级", [1, 2, 3], index=1)

            if st.button("💾 保存规则"):
                if custom_rule_name and custom_condition:
                    save_custom_alert_rule(symbol, custom_rule_name, custom_condition, custom_priority)
                    st.success("预警规则已保存！")
                    st.rerun()
                else:
                    st.error("请填写完整的规则信息")

        # 预警历史
        with st.sidebar.expander("📊 预警历史", expanded=False):
            recent_alerts = get_recent_alerts(symbol, limit=5)
            if recent_alerts:
                for alert in recent_alerts:
                    st.write(f"**{alert['rule_name']}**")
                    st.write(f"时间: {alert['alert_time']}")
                    st.write(f"状态: {'✅' if alert['success'] else '❌'}")
                    st.write("---")
            else:
                st.info("暂无预警历史")

    # 显示设置
    st.sidebar.subheader("📈 显示设置")
    
    lookback_minutes = st.sidebar.slider(
        "图表时间窗口(分钟)", 
        min_value=5, 
        max_value=360, 
        value=120, 
        step=5
    )
    
    auto_refresh = st.sidebar.checkbox("自动刷新", value=True)
    refresh_interval = st.sidebar.slider(
        "刷新间隔(秒)", 
        min_value=1, 
        max_value=10, 
        value=3, 
        step=1
    )
    
    # 控制按钮
    st.sidebar.subheader("🎮 交易控制")
    
    col1, col2 = st.sidebar.columns(2)
    
    with col1:
        if st.button("🚀 启动", type="primary"):
            # 创建完整的策略配置字典
            config_dict = {
                'buy_trigger_drop': buy_trigger,
                'profit_target': profit_target,
                'stop_loss': stop_loss,
                'max_hold_time': max_hold_time,
                'commission_rate': commission_rate,
                'slippage': slippage,
                'signal_window': signal_window,
                'min_hold_time': min_hold_time,
                'max_position': max_position,
                'position_size': position_size,
                'layer1_ratio': layer1_ratio,
                'layer2_ratio': layer2_ratio,
                'layer3_ratio': layer3_ratio,
                'partial_profit_multiplier1': partial_profit_multiplier1,
                'partial_profit_multiplier2': partial_profit_multiplier2,
                'partial_profit_multiplier3': partial_profit_multiplier3,
                'partial_sell_ratio1': partial_sell_ratio1,
                'partial_sell_ratio2': partial_sell_ratio2,
                'partial_sell_ratio3': partial_sell_ratio3,
                'daily_loss_limit': daily_loss_limit,
                'max_drawdown_limit': max_drawdown_limit,
                'exit_at_close': 1 if exit_at_close else 0,
                'initial_capital': initial_capital,
                # 新增的交易时段和平仓时间参数
                'trading_session': trading_session,
                'close_before_morning': close_before_morning,
                'close_before_afternoon': close_before_afternoon
            }
            
            # 验证配置
            errors = StrategyConfig.validate_config(config_dict)
            if errors:
                st.error(f"配置参数错误: {errors}")
                return
            
            # 初始持仓配置
            initial_position = {
                'quantity': initial_position_qty,
                'avg_cost': initial_avg_cost
            }
            
            # 初始化策略
            simulator.initialize_strategy(symbol, config_dict, initial_position, initial_capital)
            simulator.is_running = True
            simulator.is_paused = False
            
            st.success("交易模拟已启动！")
    
    with col2:
        if st.button("⏹️ 停止"):
            simulator.is_running = False
            simulator.is_paused = False
            st.info("交易模拟已停止")
    
    col3, col4 = st.sidebar.columns(2)
    
    with col3:
        if st.button("⏸️ 暂停"):
            simulator.is_paused = True
            st.warning("交易模拟已暂停")
    
    with col4:
        if st.button("▶️ 继续"):
            simulator.is_paused = False
            st.info("交易模拟已继续")
    
    if st.sidebar.button("🔄 重置"):
        st.session_state.simulator = RealtimeSimulator()
        st.rerun()
    
    # 主界面
    # 状态显示
    if simulator.is_running and not simulator.is_paused:
        status_class = "status-running"
        status_text = "🟢 运行中"
    elif simulator.is_running and simulator.is_paused:
        status_class = "status-paused"
        status_text = "🟡 已暂停"
    else:
        status_class = "status-stopped"
        status_text = "🔴 已停止"
    
    st.markdown(f'<div class="trading-status {status_class}">{status_text}</div>', 
                unsafe_allow_html=True)
    
    # 实时数据处理
    if simulator.is_running and not simulator.is_paused:
        # 获取最新数据
        latest_tick = simulator.get_latest_tick(symbol)
        
        if latest_tick:
            # 处理tick数据 - 传递signal_window参数
            result = simulator.process_tick(latest_tick, signal_window)
            
            # 显示当前状态
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                current_price = latest_tick['price']
                st.markdown(f"""
                <div class="metric-card blue">
                    <h4>当前价格</h4>
                    <h2 class="neutral">{current_price:.4f}</h2>
                    <p>{latest_tick['time']}</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col2:
                current_position = simulator.strategy.position.total_quantity if simulator.strategy else 0
                position_value = current_position * current_price
                st.markdown(f"""
                <div class="metric-card green">
                    <h4>当前持仓</h4>
                    <h2 class="neutral">{current_position:,}</h2>
                    <p>市值: {position_value:,.2f}</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col3:
                # 修复浮动盈亏的计算逻辑
                current_equity = simulator.current_equity if hasattr(simulator, 'current_equity') else initial_capital
                current_pnl = current_equity - initial_capital
                pnl_class = "positive" if current_pnl > 0 else "negative" if current_pnl < 0 else "neutral"
                pnl_pct = (current_pnl / initial_capital) * 100 if initial_capital > 0 else 0
                card_class = "red" if current_pnl < 0 else "green" if current_pnl > 0 else "blue"
                st.markdown(f"""
                <div class="metric-card {card_class}">
                    <h4>浮动盈亏</h4>
                    <h2 class="{pnl_class}">{current_pnl:,.2f}</h2>
                    <p>({pnl_pct:+.2f}%)</p>
                </div>
                """, unsafe_allow_html=True)
            
            with col4:
                current_signal = result.get('signal', 0) if result else 0
                # 信号类别的判断逻辑需要适配波动率指标
                # 从策略参数中获取阈值
                buy_threshold = simulator.strategy.current_params.get('buy_drop', -0.006) if simulator.strategy else -0.006
                profit_threshold = simulator.strategy.current_params.get('profit_target', 0.0025) if simulator.strategy else 0.0025
                
                # 波动率越高表示市场越活跃，买入信号越强
                signal_class = "positive" if current_signal >= profit_threshold else "negative" if current_signal <= buy_threshold else "neutral"
                card_class = "green" if current_signal >= profit_threshold else "red" if current_signal <= buy_threshold else "orange"
                st.markdown(f"""
                <div class="metric-card {card_class}">
                    <h4>交易信号</h4>
                    <h2 class="{signal_class}">{current_signal*100:.4f}%</h2>
                    <p>波动率强度</p>
                </div>
                """, unsafe_allow_html=True)
            
            # 实时日志显示
            if 'action' in result and result['action'] != 'HOLD':
                log_entry = {
                    'time': latest_tick['time'],
                    'price': current_price,
                    'signal': current_signal,
                    'action': result['action']['action'],
                    'reason': result.get('reason', ''),
                }
                
                # 初始化日志列表
                if 'signal_logs' not in st.session_state:
                    st.session_state.signal_logs = []
                
                # 添加新的日志条目
                st.session_state.signal_logs.append(log_entry)
                
                # 限制日志数量，只保留最近的50条
                if len(st.session_state.signal_logs) > 50:
                    st.session_state.signal_logs = st.session_state.signal_logs[-50:]
            
            # 显示实时日志
            if 'signal_logs' in st.session_state and st.session_state.signal_logs:
                st.subheader("📝 实时信号日志")
                logs_df = pd.DataFrame(st.session_state.signal_logs[-6:])  # 显示最近6条日志
                st.dataframe(logs_df.style.format({
                    'price': '{:.4f}',
                    'signal': '{:.4%}'
                }), height=210, width='stretch')
    
    # 图表显示 - 参照回测界面布局
    if simulator.is_running:
        # 主要图表：实时策略执行详情
        st.subheader("📊 实时策略执行详情")
        fig1 = create_realtime_chart(simulator, lookback_minutes)
        st.plotly_chart(fig1, width='stretch')
        
        # 净值表现图表
        st.subheader("💰 实时净值表现")
        fig2 = create_equity_chart(simulator)
        st.plotly_chart(fig2, width='stretch')
        
        # 交易记录
        if simulator.trade_history:
            st.subheader("📋 交易记录")
            trades_df = pd.DataFrame(simulator.trade_history)
            
            # 只显示最近的交易
            recent_trades = trades_df.tail(10)
            st.dataframe(
                recent_trades.style.format({
                    'price': '{:.4f}',
                    'quantity': '{:,}'
                }),
                width='stretch'
            )
        
        # 策略状态 - 参照回测界面的详细展示
        st.subheader("🎯 策略状态详情")
        
        # 当前持仓状态
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown('<div class="metric-card purple">', unsafe_allow_html=True)
            st.write("**📊 持仓信息**")
            if simulator.strategy and simulator.strategy.position.total_quantity > 0:
                current_price = simulator.price_history[-1]['price'] if simulator.price_history else 0
                market_value = simulator.strategy.position.total_quantity * current_price
                profit_rate = simulator.strategy.position.get_profit_rate(current_price) if current_price > 0 else 0
                
                st.metric("持仓数量", f"{simulator.strategy.position.total_quantity:,}")
                st.metric("平均成本", f"{simulator.strategy.position.avg_cost:.4f}")
                st.metric("总成本", f"{simulator.strategy.position.total_cost:,.2f}")
                st.metric("市值", f"{market_value:,.2f}")
                
                # 修正浮动盈亏率的颜色显示逻辑
                profit_color = "normal" if profit_rate == 0 else ("inverse" if profit_rate > 0 else "off")
                st.metric("浮动盈亏率", f"{profit_rate:.2%}", delta=f"{profit_rate:.2%}", delta_color=profit_color)
                
                if simulator.strategy.position.first_buy_time:
                    hold_time = (datetime.now() - simulator.strategy.position.first_buy_time).total_seconds()
                    st.metric("持仓时间", f"{hold_time:.0f}秒")
            else:
                st.info("当前无持仓")
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col2:
            st.markdown('<div class="metric-card blue">', unsafe_allow_html=True)
            st.write("**⚠️ 风险管理**")
            if simulator.strategy and hasattr(simulator.strategy, 'risk_manager'):
                rm = simulator.strategy.risk_manager
                st.metric("当前净值", f"{rm.current_equity:,.2f}")
                st.metric("峰值净值", f"{rm.peak_equity:,.2f}")
                
                # 计算当前回撤 - 修复计算逻辑
                if rm.peak_equity > 0 and rm.current_equity < rm.peak_equity:
                    current_drawdown = (rm.current_equity - rm.peak_equity) / rm.peak_equity
                else:
                    current_drawdown = 0.0
                    
                drawdown_color = "normal" if current_drawdown >= -0.02 else ("off" if current_drawdown >= -0.05 else "inverse")
                st.metric("当前回撤", f"{current_drawdown:.2%}", delta=f"{current_drawdown:.2%}", delta_color=drawdown_color)
                
                st.metric("日内盈亏", f"{rm.daily_pnl:.2%}")
                
                # 风险状态指示
                risk_status = "🟢 正常" if rm.check_risk_limits() else "🔴 风险"
                st.metric(f"**风险状态**", f" {risk_status}")
                
                # 添加风险参数显示 
                st.metric(f"日损限制", f" {daily_loss_limit:.2%}")
                st.metric(f"回撤限制", f" {max_drawdown_limit:.2%}")
            st.markdown('</div>', unsafe_allow_html=True)
        
        with col3:
            st.markdown('<div class="metric-card orange">', unsafe_allow_html=True)
            st.write("**📈 策略参数**")
            if simulator.strategy and hasattr(simulator.strategy, 'current_params'):
                params = simulator.strategy.current_params
                st.write(f"买入触发: {params.get('buy_drop', -0.006):.2%}")
                st.write(f"止盈目标: {params.get('profit_target', 0.006):.2%}")
                st.write(f"止损线: {abs(params.get('stop_loss', -0.02)):.2%}")
            
            # 交易统计
            if simulator.trade_history:
                buy_count = len([t for t in simulator.trade_history if t['action'] == 'BUY'])
                sell_count = len([t for t in simulator.trade_history if t['action'] == 'SELL'])
                st.write(f"**交易统计**")
                st.write(f"买入次数: {buy_count}")
                st.write(f"卖出次数: {sell_count}")
                st.write(f"总交易次数: {len(simulator.trade_history)}")
            st.markdown('</div>', unsafe_allow_html=True)
    
    else:
        st.info("👈 请在左侧配置参数并点击'启动'开始模拟交易")
        
        # 功能介绍
        st.header("🌟 功能特色")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown("""
            **🚀 实时模拟**
            - 基于真实tick数据
            - 增强版策略执行
            - 实时风险控制
            """)
        
        with col2:
            st.markdown("""
            **💼 自定义持仓**
            - 设置初始持仓数量
            - 指定持仓成本
            - 灵活的资金配置
            """)
        
        with col3:
            st.markdown("""
            **📊 直观监控**
            - 实时价格图表
            - 交易信号可视化
            - 净值变化跟踪
            """)
    

    # 系统监控面板
    with st.expander("🔍 系统监控", expanded=False):
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            error_count = getattr(simulator, 'error_count', 0)
            st.metric("错误次数", error_count)
        
        with col2:
            max_errors = getattr(simulator, 'max_errors', 10)
            st.metric("错误阈值", max_errors)
        
        with col3:
            last_update = getattr(simulator, 'last_update', '未知')
            st.metric("最后更新", str(last_update)[:19] if last_update else '未知')
        
        with col4:
            data_status = "正常" if getattr(simulator, 'last_successful_data', None) else "异常"
            st.metric("数据状态", data_status)
        
        # 重置错误计数按钮
        if st.button("🔄 重置错误计数"):
            if hasattr(simulator, 'error_count'):
                simulator.error_count = 0
                st.success("错误计数已重置")

    # 自动刷新
    if auto_refresh and simulator.is_running and not simulator.is_paused:
        time.sleep(refresh_interval)
        st.rerun()

if __name__ == "__main__":
    main()