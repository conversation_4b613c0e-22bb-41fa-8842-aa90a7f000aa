#!/usr/bin/env python3
"""
测试核心基础架构
验证统一的基础服务是否正常工作
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def test_core_infrastructure():
    """测试核心基础架构"""
    print("🏗️ 核心基础架构测试")
    print("=" * 50)
    
    try:
        # 1. 测试配置管理器
        print("📋 测试1: 配置管理器")
        try:
            from etf_arbitrage_streamlit_multi.core.config_manager import config_manager
            
            # 测试基本配置获取
            app_title = config_manager.get('app.title', '默认标题')
            print(f"✅ 应用标题: {app_title}")
            
            # 测试配置设置
            config_manager.set('test.value', 'test_data')
            test_value = config_manager.get('test.value')
            print(f"✅ 配置设置/获取: {test_value}")
            
            # 测试结构化配置
            trading_config = config_manager.get_trading_config()
            print(f"✅ 交易配置: 默认仓位大小={trading_config.default_position_size}")
            
            # 测试标的配置
            symbols = config_manager.get_all_symbols()
            print(f"✅ 标的配置: 共{len(symbols)}个标的")
            
        except Exception as e:
            print(f"❌ 配置管理器测试失败: {e}")
            return False
        
        # 2. 测试数据库管理器
        print("\n📋 测试2: 数据库管理器")
        try:
            from etf_arbitrage_streamlit_multi.core.database_manager import db_manager
            
            # 测试数据库连接
            with db_manager.get_connection('ticks') as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                tables = cursor.fetchall()
                print(f"✅ 数据库连接成功，表数量: {len(tables)}")
            
            # 测试查询功能
            result = db_manager.execute_query("SELECT COUNT(*) as count FROM ticks", (), 'ticks')
            print(f"✅ 查询功能正常，ticks表记录数: {result[0]['count'] if result else 0}")
            
            # 测试插入功能
            test_ticks = [
                {
                    'symbol': 'TEST001',
                    'tick_time': '2025-09-24 10:00:00',
                    'price': 1.0,
                    'volume': 1000,
                    'side': 'buy',
                    'raw': 'test_data'
                }
            ]
            inserted_count = db_manager.insert_ticks(test_ticks)
            print(f"✅ 插入功能正常，插入记录数: {inserted_count}")
            
        except Exception as e:
            print(f"❌ 数据库管理器测试失败: {e}")
            return False
        
        # 3. 测试日志管理器
        print("\n📋 测试3: 日志管理器")
        try:
            from etf_arbitrage_streamlit_multi.core.logger_manager import (
                logger_manager, get_logger, get_trading_logger
            )
            
            # 测试基本日志器
            test_logger = get_logger('test_module')
            test_logger.info("测试日志消息")
            print("✅ 基本日志器创建成功")
            
            # 测试专用日志器
            trading_logger = get_trading_logger()
            trading_logger.info("测试交易日志")
            print("✅ 交易日志器创建成功")
            
            # 测试日志统计
            log_stats = logger_manager.get_log_stats()
            print(f"✅ 日志统计: {log_stats['total_loggers']}个日志器")
            
        except Exception as e:
            print(f"❌ 日志管理器测试失败: {e}")
            return False
        
        # 4. 测试异常处理器
        print("\n📋 测试4: 异常处理器")
        try:
            from etf_arbitrage_streamlit_multi.core.exception_handler import (
                exception_handler, handle_exceptions, ErrorCategory, ErrorSeverity
            )
            
            # 测试异常处理
            test_exception = ValueError("测试异常")
            result = exception_handler.handle_exception(
                test_exception,
                context={'test': 'context'},
                severity=ErrorSeverity.LOW
            )
            print("✅ 异常处理功能正常")
            
            # 测试装饰器
            @handle_exceptions(category=ErrorCategory.VALIDATION_ERROR, severity=ErrorSeverity.LOW)
            def test_function():
                raise ValueError("装饰器测试异常")
                return "success"
            
            result = test_function()
            print("✅ 异常处理装饰器正常")
            
            # 测试统计
            stats = exception_handler.get_error_statistics()
            print(f"✅ 异常统计: 总错误数={stats['total_errors']}")
            
        except Exception as e:
            print(f"❌ 异常处理器测试失败: {e}")
            return False
        
        # 5. 测试服务注册器
        print("\n📋 测试5: 服务注册器")
        try:
            from etf_arbitrage_streamlit_multi.core.service_registry import (
                service_registry, BaseService, ServiceType, ServiceStatus
            )
            
            # 创建测试服务
            class TestService(BaseService):
                def __init__(self):
                    super().__init__("test_service", ServiceType.DATABASE)
                    self.started = False
                
                def start(self) -> bool:
                    self.started = True
                    return True
                
                def stop(self) -> bool:
                    self.started = False
                    return True
                
                def health_check(self) -> bool:
                    return self.started
            
            # 注册服务
            test_service = TestService()
            success = service_registry.register_service(test_service)
            print(f"✅ 服务注册: {success}")
            
            # 启动服务
            success = service_registry.start_service("test_service")
            print(f"✅ 服务启动: {success}")
            
            # 获取服务状态
            status = service_registry.get_service_status("test_service")
            print(f"✅ 服务状态: {status['status']}")
            
            # 停止服务
            success = service_registry.stop_service("test_service")
            print(f"✅ 服务停止: {success}")
            
        except Exception as e:
            print(f"❌ 服务注册器测试失败: {e}")
            return False
        
        # 6. 测试集成功能
        print("\n📋 测试6: 集成功能")
        try:
            # 测试配置与数据库集成
            db_config = config_manager.get_database_config()
            print(f"✅ 配置数据库集成: 清理天数={db_config.cleanup_days}")
            
            # 测试日志与异常处理集成
            logger = get_logger('integration_test')
            try:
                raise RuntimeError("集成测试异常")
            except Exception as e:
                exception_handler.handle_exception(e, {'module': 'integration_test'})
                logger.info("异常已处理")
            
            print("✅ 日志异常处理集成正常")
            
        except Exception as e:
            print(f"❌ 集成功能测试失败: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 核心基础架构测试全部通过！")
        
        # 输出架构总结
        print("\n📊 架构总结:")
        print("✅ 配置管理器: 统一配置加载、管理和访问")
        print("✅ 数据库管理器: 统一数据库连接、操作和管理")
        print("✅ 日志管理器: 统一日志配置、格式化和管理")
        print("✅ 异常处理器: 统一异常处理、错误记录和恢复")
        print("✅ 服务注册器: 统一服务注册、发现和管理")
        print("✅ 集成功能: 各组件间协同工作正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 核心基础架构测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_core_infrastructure()
    if success:
        print("\n✅ 核心基础架构验证成功")
        print("现在可以基于这个统一架构重构其他模块")
    else:
        print("\n❌ 核心基础架构验证失败")
        print("需要修复基础架构问题后再继续")
