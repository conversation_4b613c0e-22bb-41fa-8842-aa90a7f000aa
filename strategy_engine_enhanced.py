import sqlite3
import sqlite3
import time
import logging
from typing import List, Dict, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import numpy as np
import pandas as pd
import argparse


# 导入统一配置
from strategy_config import (
    StrategyConfig,
    BUY_TRIGGER_DROP, PROFIT_TARGET, STOP_LOSS, MAX_HOLD_TIME,
    MAX_POSITION, DAILY_LOSS_LIMIT, MAX_DRAWDOWN_LIMIT,
    LAYERS, PARTIAL_SELL_LEVELS, PARTIAL_SELL_RATIOS, MIN_HOLD_TIME,_DEFAULT_CONFIG
)

# 添加缺失的常量定义
INITIAL_CAPITAL = 1_000_000.0
COMMISSION_RATE = 0.0003

DB_PATH: str = "ticks.db"

logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"


@dataclass
class PositionBatch:
    """持仓批次信息"""
    quantity: int
    cost: float  # 该批次的总成本
    buy_time: datetime
    partial_sold: List[bool] = None
    
    def __post_init__(self):
        if self.partial_sold is None:
            self.partial_sold = [False] * len(PARTIAL_SELL_LEVELS)
    
    @property
    def avg_cost(self) -> float:
        """计算该批次的平均成本"""
        return self.cost / self.quantity if self.quantity > 0 else 0.0
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算该批次的收益率"""
        if self.quantity <= 0 or self.avg_cost <= 0:
            return 0.0
        return (current_price - self.avg_cost) / self.avg_cost


@dataclass
class Position:
    """持仓信息"""
    batches: List[PositionBatch] = None
    total_quantity: int = 0
    total_cost: float = 0.0
    first_buy_time: Optional[datetime] = None
    # 简化的止盈状态跟踪
    profit_level_reached: int = 0  # 0=未止盈, 1=第一级, 2=第二级, 3=第三级

    def __post_init__(self):
        if self.batches is None:
            self.batches = []
    
    def add_position(self, qty: int, price: float, timestamp: datetime):
        """增加持仓"""
        batch = PositionBatch(
            quantity=qty,
            cost=qty * price,
            buy_time=timestamp
        )
        self.batches.append(batch)

        self.total_quantity += qty
        self.total_cost += qty * price
        if self.first_buy_time is None:
            self.first_buy_time = timestamp

        # 智能重置机制：如果是重新建仓或大幅加仓，重置止盈级别和时间
        if self._should_reset_profit_level(qty):
            self.profit_level_reached = 0
            # 重置时间为当前买入时间（重新开始计时）
            self.first_buy_time = timestamp
    
    def reduce_position(self, qty: int) -> float:
        """减少持仓，返回减少的成本"""
        if qty >= self.total_quantity:
            # 全部卖出
            cost_reduced = self.total_cost
            self.batches.clear()
            self.total_quantity = 0
            self.total_cost = 0.0
            self.first_buy_time = None
            return cost_reduced
        else:
            # 部分卖出 - 按照先进先出原则
            cost_reduced = 0.0
            qty_to_sell = qty
            
            while qty_to_sell > 0 and self.batches:
                batch = self.batches[0]
                if batch.quantity <= qty_to_sell:
                    # 卖出整个批次
                    cost_reduced += batch.cost
                    qty_to_sell -= batch.quantity
                    self.batches.pop(0)
                else:
                    # 部分卖出批次
                    ratio = qty_to_sell / batch.quantity
                    cost_reduced += batch.cost * ratio
                    batch.quantity -= qty_to_sell
                    batch.cost -= batch.cost * ratio
                    qty_to_sell = 0
            
            self.total_quantity -= qty
            self.total_cost -= cost_reduced

            # 优化：清理空批次，减少内存占用
            self._cleanup_empty_batches()

            return cost_reduced
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算整体收益率"""
        if self.total_quantity <= 0 or self.total_cost <= 0:
            return 0.0
        avg_cost = self.total_cost / self.total_quantity
        return (current_price - avg_cost) / avg_cost
    
    def get_hold_time(self, current_time: datetime = None) -> int:
        """获取持仓时间（秒）"""
        if self.first_buy_time is None:
            return 0
        # 如果提供了当前时间（回测模式），使用提供的时间；否则使用系统时间（实时模式）
        reference_time = current_time if current_time is not None else datetime.now()
        return int((reference_time - self.first_buy_time).total_seconds())
    
    def reset_partial_sold_flags(self):
        """重置止盈状态"""
        self.profit_level_reached = 0

    def _should_reset_profit_level(self, new_qty: int) -> bool:
        """判断是否应该重置止盈级别"""
        # 如果是首次买入，不需要重置
        if len(self.batches) <= 1:
            return False

        # 如果新买入数量占总仓位的比例超过30%，重置止盈级别
        old_quantity = self.total_quantity - new_qty
        if old_quantity == 0:
            return True

        new_ratio = new_qty / self.total_quantity
        return new_ratio >= 0.3  # 新买入占30%以上时重置

    def _cleanup_empty_batches(self):
        """清理空批次，优化内存使用"""
        self.batches = [batch for batch in self.batches if batch.quantity > 0]

    def check_partial_sell(self, current_price: float, config=None) -> Tuple[bool, int, float, str]:
        """
        简化的分批止盈检查
        返回 (是否卖出, 级别, 卖出比例, 原因)

        Args:
            current_price: 当前价格
            config: 可选的配置对象，如果提供则使用其参数，否则使用默认配置
        """
        if self.total_quantity <= 0:
            return False, -1, 0.0, ""

        # 计算整体收益率
        profit_rate = self.get_profit_rate(current_price)

        # 根据是否提供config来选择参数来源
        if config is not None:
            # 使用传入的配置参数
            profit_target = config.profit_target
            multiplier1 = config.partial_profit_multiplier1
            multiplier2 = config.partial_profit_multiplier2
            multiplier3 = config.partial_profit_multiplier3
            ratio1 = config.partial_sell_ratio1
            ratio2 = config.partial_sell_ratio2
            ratio3 = config.partial_sell_ratio3
        else:
            # 使用默认配置
            
            profit_target = _DEFAULT_CONFIG['profit_target']
            multiplier1 = _DEFAULT_CONFIG['partial_profit_multiplier1']
            multiplier2 = _DEFAULT_CONFIG['partial_profit_multiplier2']
            multiplier3 = _DEFAULT_CONFIG['partial_profit_multiplier3']
            ratio1 = _DEFAULT_CONFIG['partial_sell_ratio1']
            ratio2 = _DEFAULT_CONFIG['partial_sell_ratio2']
            ratio3 = _DEFAULT_CONFIG['partial_sell_ratio3']

        # 定义止盈级别和对应的卖出比例
        profit_levels = [
            (profit_target * multiplier1, ratio1, "第一次止盈"),
            (profit_target * multiplier2, ratio2, "第二次止盈"),
            (profit_target * multiplier3, ratio3, "第三次止盈")
        ]

        # 检查是否达到新的止盈级别
        for level_idx, (threshold, sell_ratio, reason) in enumerate(profit_levels):
            if profit_rate >= threshold and self.profit_level_reached <= level_idx:
                self.profit_level_reached = level_idx + 1
                return True, level_idx, sell_ratio, f"{reason}: {profit_rate:.4f}"

        return False, -1, 0.0, ""


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float = 1_000_000.0):
        self.initial_capital = initial_capital
        self.daily_pnl = 0.0
        self.peak_equity = initial_capital
        self.current_equity = initial_capital
        self.daily_start_equity = initial_capital
        self.last_reset_date = datetime.now().date()
    
    def update_equity(self, new_equity: float):
        """更新净值"""
        # 检查是否需要重置日内统计
        current_date = datetime.now().date()
        if current_date != self.last_reset_date:
            self.daily_start_equity = self.current_equity
            self.last_reset_date = current_date
        
        self.current_equity = new_equity
        self.peak_equity = max(self.peak_equity, new_equity)
        self.daily_pnl = (new_equity - self.daily_start_equity) / self.daily_start_equity
    
    def check_risk_limits(self) -> bool:
        """检查是否触发风险限制"""
        # 计算当前回撤
        drawdown = (self.current_equity - self.peak_equity) / self.peak_equity
        
        # 检查风险限制
        if self.daily_pnl <= DAILY_LOSS_LIMIT:
            logger.warning(f"触发日损失限制: {self.daily_pnl:.4f}")
            return False
        
        if drawdown <= MAX_DRAWDOWN_LIMIT:
            logger.warning(f"触发最大回撤限制: {drawdown:.4f}")
            return False
        
        return True


class EnhancedStrategy:
    """增强版策略引擎"""
    
    def __init__(self, symbol: str, initial_capital: float = 1_000_000.0):
        self.symbol = symbol
        self.position = Position()
        self.risk_manager = RiskManager(initial_capital)
        self.current_params = {
            'buy_drop': BUY_TRIGGER_DROP,
            'profit_target': PROFIT_TARGET,
            'stop_loss': STOP_LOSS
        }
        self.last_market_update = datetime.now()
        self.poll_sec = 1.0
        self.is_running = False

        # 新增：交易时段和平仓时间控制参数
        self.trading_session = "全天"  # 默认全天交易
        self.close_before_morning = False  # 默认不在上午收盘前平仓
        self.close_before_afternoon = True  # 默认在下午收盘前平仓
        
        # 添加配置参数
        self.current_params.update({
            'initial_capital': initial_capital,
            'commission_rate': COMMISSION_RATE,
            'position_size': MAX_POSITION
        })
    
    def start(self):
        """启动策略"""
        self.is_running = True
        logger.info(f"策略引擎已启动: {self.symbol}")
    
    def stop(self):
        """停止策略"""
        self.is_running = False
        logger.info(f"策略引擎已停止: {self.symbol}")

    def is_trading_time_allowed(self, current_time: datetime = None) -> bool:
        """检查当前时间是否允许交易"""
        if current_time is None:
            current_time = datetime.now()

        # 获取当前时间
        current_hour = current_time.hour
        current_minute = current_time.minute
        current_time_minutes = current_hour * 60 + current_minute

        # 定义交易时段（分钟表示）
        morning_start = 9 * 60 + 30   # 9:30
        morning_end = 11 * 60 + 30    # 11:30
        afternoon_start = 13 * 60     # 13:00
        afternoon_end = 15 * 60       # 15:00

        # 检查是否在交易时段内
        is_morning_session = morning_start <= current_time_minutes <= morning_end
        is_afternoon_session = afternoon_start <= current_time_minutes <= afternoon_end

        # 根据设置的交易时段进行判断
        if self.trading_session == "全天":
            return is_morning_session or is_afternoon_session
        elif self.trading_session == "上午":
            return is_morning_session
        elif self.trading_session == "下午":
            return is_afternoon_session
        else:
            return False

    def should_close_before_session_end(self, current_time: datetime = None) -> Tuple[bool, str]:
        """检查是否应该在时段结束前平仓"""
        if current_time is None:
            current_time = datetime.now()

        current_hour = current_time.hour
        current_minute = current_time.minute
        current_time_minutes = current_hour * 60 + current_minute

        # 上午收盘前平仓检查（11:25）
        if self.close_before_morning:
            morning_close_time = 11 * 60 + 25  # 11:25
            if current_time_minutes >= morning_close_time and current_time_minutes <= 11 * 60 + 30:
                return True, "上午收盘前平仓"

        # 下午收盘前平仓检查（14:55）
        if self.close_before_afternoon:
            afternoon_close_time = 14 * 60 + 55  # 14:55
            if current_time_minutes >= afternoon_close_time and current_time_minutes <= 15 * 60:
                return True, "下午收盘前平仓"

        return False, ""

    def _safe_return_20ticks(self, df: pd.DataFrame) -> float:
        """
        计算近20ticks价格波动率作为交易信号
        
        理论逻辑：
        1. 抽取当前时间之前的20个ticks再加上最新的tick
        2. 取最新的20个ticks
        3. 计算其中的最高价
        4. 用这个价格和最新价格计算波动率（即交易信号的大小）
        """
        try:
            print(f"🔍 [DEBUG] 信号计算开始...")
            print(f"🔍 [DEBUG] 输入DataFrame: {df is not None}, 形状: {df.shape if df is not None else 'None'}")
            
            if df is None or df.shape[0] < 1:
                print(f"🔍 [DEBUG] DataFrame为空或无数据，返回0.0")
                return 0.0

            print(f"🔍 [DEBUG] DataFrame列名: {list(df.columns)}")
            print("🔍 [DEBUG] DataFrame前几行:")
            print(df.head())

            prices = df["price"].astype(float)
            print(f"🔍 [DEBUG] 价格数据: {len(prices)}个价格点")
            print(f"🔍 [DEBUG] 价格范围: {prices.min():.4f} - {prices.max():.4f}")
            print(f"🔍 [DEBUG] 是否有NaN: {prices.isnull().any()}")
            
            if prices.isnull().any() or len(prices) < 1:
                print(f"🔍 [DEBUG] 价格数据有NaN或长度<1，返回0.0")
                return 0.0

            # 如果只有一个价格点，无法计算波动率，返回0
            if len(prices) == 1:
                print(f"🔍 [DEBUG] 只有1个价格点，无法计算波动率，返回0.0")
                return 0.0

            # 确保我们使用的是最新的20个ticks（如果数据超过20个，取最后20个）
            original_len = len(prices)
            if len(prices) > 20:
                prices = prices.tail(20)
                print(f"🔍 [DEBUG] 数据点从{original_len}个截取到最新{len(prices)}个")
            
            # 获取最新价格（最后一个价格点）
            latest_price = float(prices.iloc[-1])
            print(f"🔍 [DEBUG] 最新价格: {latest_price:.4f}")

            if latest_price <= 0:
                print(f"🔍 [DEBUG] 最新价格<=0，返回0.0")
                return 0.0

            # 计算这20个ticks中的最高价
            max_price = float(prices.max())
            min_price = float(prices.min())
            print(f"🔍 [DEBUG] 窗口内最高价: {max_price:.4f}")
            print(f"🔍 [DEBUG] 窗口内最低价: {min_price:.4f}")
            
            if max_price <= 0:
                print(f"🔍 [DEBUG] 最高价<=0，返回0.0")
                return 0.0

            # 计算波动率：用最新价格和最高价计算波动率（即交易信号的大小）
            # 负值表示从最高价下跌，正值表示价格在最高价附近或超过最高价
            signal = (latest_price - max_price) / max_price
            print(f"🔍 [DEBUG] 信号计算: ({latest_price:.4f} - {max_price:.4f}) / {max_price:.4f} = {signal:.6f}")

            # 检查计算结果的有效性
            if np.isnan(signal) or np.isinf(signal):
                print(f"🔍 [DEBUG] 信号为NaN或无穷大，返回0.0")
                return 0.0

            print(f"🔍 [DEBUG] 最终信号: {signal:.6f} ({signal*100:.4f}%)")
            return float(signal)
            
        except Exception as e:
            print(f"🔍 [DEBUG] 异常: {e}")
            logger.error(f"计算20ticks波动率失败: {e}")
            import traceback
            traceback.print_exc()
            return 0.0
    
    def _load_recent_ticks(self, conn: sqlite3.Connection, span_seconds: int = 600) -> pd.DataFrame:
        """读取近期tick数据"""
        try:
            since_iso = (datetime.now() - timedelta(seconds=span_seconds)).isoformat(timespec="seconds")
            # 使用signal_window参数限制获取的tick数量
            signal_window = self.current_params.get('signal_window', 20)  # 默认20个ticks
            df = pd.read_sql_query(
                "SELECT tick_time AS time, price, volume FROM ticks "
                "WHERE symbol=? AND tick_time>=? ORDER BY tick_time DESC LIMIT ?",
                conn,
                params=[self.symbol, since_iso, signal_window],
                parse_dates=["time"]
            )
            # 按时间升序排列，确保最新的数据在最后
            if not df.empty:
                df = df.sort_values("time")
            if df.empty:
                return df
            df["price"] = pd.to_numeric(df["price"], errors="coerce")
            df["volume"] = pd.to_numeric(df["volume"], errors="coerce").fillna(0).astype(int)
            return df.dropna(subset=["price"])
        except Exception as e:
            logger.error(f"读取近窗口ticks失败: {e}")
            return pd.DataFrame(columns=["time", "price", "volume"])
    
    def _ensure_signal_table(self, conn: sqlite3.Connection) -> None:
        try:
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute(
                """CREATE TABLE IF NOT EXISTS strategy_signals(
                    symbol TEXT NOT NULL,
                    ts TEXT NOT NULL,
                    signal TEXT NOT NULL,
                    quantity INTEGER,
                    price REAL,
                    reason TEXT,
                    PRIMARY KEY(symbol, ts)
                )"""
            )
            conn.commit()
        except Exception as e:
            logger.error(f"初始化signal表失败: {e}")
    
    def _upsert_signal(self, conn: sqlite3.Connection, ts: datetime, sig: str, 
                      qty: int = 0, price: float = 0.0, reason: str = "") -> None:
        """写入策略信号"""
        try:
            conn.execute(
                "INSERT OR REPLACE INTO strategy_signals(symbol, ts, signal, quantity, price, reason) VALUES (?, ?, ?, ?, ?, ?)",
                (self.symbol, ts.isoformat(timespec="seconds"), sig.upper(), qty, price, reason)
            )
            conn.commit()
        except Exception as e:
            logger.error(f"写入策略信号失败: {e}")
    
    def get_latest_tick(self) -> Optional[Dict]:
        """获取最新tick数据"""
        try:
            conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES)
            df = pd.read_sql_query(
                "SELECT tick_time AS time, price, volume FROM ticks "
                "WHERE symbol=? ORDER BY tick_time DESC LIMIT 1",
                conn,
                params=[self.symbol],
                parse_dates=["time"]
            )
            conn.close()
            
            if df.empty:
                return None
                
            return {
                'time': df.iloc[0]['time'],
                'price': float(df.iloc[0]['price']),
                'volume': int(df.iloc[0]['volume'])
            }
        except Exception as e:
            logger.error(f"获取最新tick失败: {e}")
            return None

    def update_market_state(self, conn: sqlite3.Connection):
        """更新市场状态和动态参数"""
        # 每5分钟更新一次市场状态
        if (datetime.now() - self.last_market_update).total_seconds() < 300:
            return
        
        try:
            logger.info("更新市场状态...")
            self.last_market_update = datetime.now()
        except Exception as e:
            logger.error(f"更新市场状态失败: {e}")
    
    def should_buy(self, signal: float, current_price: float) -> bool:
        """判断是否应该买入（增强版资金检查）"""
        if not self.risk_manager.check_risk_limits():
            return False
        
        if self.position.total_quantity >= MAX_POSITION:
            return False
        
        if current_price <= 0:
            return False
        
        # 如果当前有仓位，检查是否应该买入
        if self.position.total_quantity > 0:
            profit_rate = self.position.get_profit_rate(current_price)
            # 修改逻辑：只有在大幅盈利时才禁止买入，小幅盈利允许加仓
            if profit_rate > 0.02:  # 盈利超过2%时不买入
                return False
            # 如果当前小幅亏损，允许加仓（降低平均成本）
            # 只有在亏损很小时才禁止（避免频繁交易）
            if -0.005 < profit_rate <= 0:  # 亏损小于0.5%时不加仓
                return False
        
        # 资金充足性预检查 - 检查是否有足够资金进行最小买入
        min_buy_qty = int(self.current_params.get('position_size', 100000) * 0.1)  # 最小买入量（10%）
        if not self._check_fund_sufficiency_simple(min_buy_qty, current_price):
            logger.warning(f"资金不足，无法执行最小买入量 {min_buy_qty} 股")
            return False
        
        buy_threshold = self.current_params.get('buy_drop', BUY_TRIGGER_DROP)
        return signal <= buy_threshold
    
    def should_sell(self, current_price: float) -> Tuple[bool, float, str]:
        """判断是否应该卖出，返回(是否卖出, 卖出比例, 原因)"""
        if self.position.total_quantity <= 0:
            return False, 0.0, ""
        
        profit_rate = self.position.get_profit_rate(current_price)
        hold_time = self.position.get_hold_time()  # 实时模式使用系统时间
        
        # 止损检查
        stop_loss_threshold = self.current_params.get('stop_loss', STOP_LOSS)
        if profit_rate <= stop_loss_threshold:
            return True, 1.0, f"止损: {profit_rate:.4f}"
        
        # 时间止损
        if hold_time >= MAX_HOLD_TIME:
            return True, 1.0, f"时间止损: {hold_time}s"
        
        # 分批止盈检查 - 实时模式使用默认配置
        partial_sell_result = self.position.check_partial_sell(current_price)
        if partial_sell_result[0]:
            # 找到满足条件的分批止盈
            _, level_idx, sell_ratio, reason = partial_sell_result
            return True, sell_ratio, reason
        
        return False, 0.0, ""
    
    def _check_fund_sufficiency_simple(self, buy_qty: int, current_price: float) -> bool:
        """简单的资金充足性检查"""
        try:
            # 估算当前可用资金（简化版）
            initial_capital = self.current_params.get('initial_capital', INITIAL_CAPITAL)
            commission_rate = self.current_params.get('commission_rate', COMMISSION_RATE)
            slippage = self.current_params.get('slippage', 0.001)
            
            # 估算已投入资金
            invested_capital = self.position.total_cost if hasattr(self.position, 'total_cost') else 0
            
            # 估算可用资金（保守估计）
            available_cash = initial_capital - invested_capital
            
            # 计算所需资金
            actual_price = current_price * (1 + slippage)
            required_fund = buy_qty * actual_price * (1 + commission_rate)
            
            return available_cash >= required_fund
            
        except Exception as e:
            logger.error(f"资金充足性检查出错: {e}")
            return False

    def execute_buy(self, conn: sqlite3.Connection, current_price: float, ts: datetime):
        """执行买入（增强版资金管理）"""
        # 从配置中获取position_size参数
        position_size = getattr(self, 'position_size', MAX_POSITION)
        remaining = MAX_POSITION - self.position.total_quantity
        total_bought = 0
        
        # 获取资金管理参数
        initial_capital = self.current_params.get('initial_capital', INITIAL_CAPITAL)
        commission_rate = self.current_params.get('commission_rate', COMMISSION_RATE)
        slippage = self.current_params.get('slippage', 0.001)
        fund_buffer_ratio = self.current_params.get('fund_buffer_ratio', 0.05)
        
        for i, pct in enumerate(LAYERS):
            # 计算目标买入数量
            target_qty = int(position_size * pct)
            if target_qty <= 0 or remaining <= 0:
                continue
            
            # 根据资金情况调整实际买入数量
            affordable_qty = self._calculate_affordable_quantity(target_qty, current_price, 
                                                               initial_capital, commission_rate, 
                                                               slippage, fund_buffer_ratio)
            alloc = min(affordable_qty, remaining)
            
            if alloc <= 0:
                logger.debug(f"资金不足，跳过第{i+1}层买入（目标{target_qty}股）")
                continue
            
            self.position.add_position(alloc, current_price, ts)
            total_bought += alloc
            remaining -= alloc
            
            logger.debug(f"第{i+1}层买入：目标{target_qty}股，实际{alloc}股，价格{current_price:.4f}")
        
        if total_bought > 0:
            self._upsert_signal(conn, ts, "B", total_bought, current_price, "分层买入")
            logger.info(f"买入执行: 数量={total_bought}, 价格={current_price:.4f}, "
                       f"总仓位={self.position.total_quantity}, 平均成本={self.position.get_profit_rate(current_price):.4f}")
    
    def _calculate_affordable_quantity(self, target_qty: int, current_price: float,
                                     initial_capital: float, commission_rate: float,
                                     slippage: float, fund_buffer_ratio: float) -> int:
        """根据可用资金计算实际可买入数量"""
        try:
            if target_qty <= 0:
                return 0
                
            # 估算已投入资金
            invested_capital = self.position.total_cost if hasattr(self.position, 'total_cost') else 0
            available_cash = initial_capital - invested_capital
            
            # 保留资金缓冲
            usable_cash = available_cash * (1 - fund_buffer_ratio)
            
            if usable_cash <= 0:
                return 0
            
            actual_price = current_price * (1 + slippage)
            
            # 考虑手续费的最大可买数量
            if actual_price <= 0:
                return 0
                
            max_affordable = int(usable_cash / (actual_price * (1 + commission_rate)))
            
            return min(target_qty, max_affordable)
            
        except Exception as e:
            logger.error(f"计算可负担数量出错: {e}")
            return 0
    
    def execute_sell(self, conn: sqlite3.Connection, current_price: float, ts: datetime, 
                    sell_ratio: float, reason: str):
        """执行卖出"""
        sell_qty = int(self.position.total_quantity * sell_ratio)
        if sell_qty <= 0:
            return
        
        self.position.reduce_position(sell_qty)
        self._upsert_signal(conn, ts, "S", sell_qty, current_price, reason)
        
        logger.info(f"卖出执行: 数量={sell_qty}, 价格={current_price:.4f}, "
                   f"剩余仓位={self.position.total_quantity}, 原因={reason}")
    
    def calculate_dynamic_stop_loss(self, entry_price: float, volatility: float, position_type: str) -> Tuple[float, float]:
        """
        计算动态止损价和止盈价

        Args:
            entry_price: 入仓价格
            volatility: 波动率
            position_type: 仓位类型，'long' 或 'short'

        Returns:
            Tuple[float, float]: (止损价, 止盈价)
        """
        if position_type == 'long':
            stop_loss_price = entry_price * (1 - STOP_LOSS)
            profit_target_price = entry_price * (1 + PROFIT_TARGET)
        elif position_type == 'short':
            stop_loss_price = entry_price * (1 + STOP_LOSS)
            profit_target_price = entry_price * (1 - PROFIT_TARGET)
        else:
            raise ValueError("Invalid position type. Must be 'long' or 'short'.")
        
        return stop_loss_price, profit_target_price
    
    def get_market_regime(self, data: Dict[str, Any]) -> Tuple[str, float]:
        """
        获取市场状态

        Args:
            data: 输入数据，包含价格、时间等信息

        Returns:
            Tuple[str, float]: 市场状态（'trending', 'ranging', 'volatile'）和波动率
        """
        # 示例实现：根据价格波动率判断市场状态
        price = data.get('price')
        signal = self._safe_return_20ticks(data)
        if abs(signal) > 0.02:
            return 'volatile', signal
        elif abs(signal) > 0.01:
            return 'ranging', signal
        else:
            return 'trending', signal
    
    def generate_signal(self, data: Dict[str, Any], current_position: Optional[Position] = None) -> Tuple[str, Dict[str, Any]]:
        """
        生成交易信号

        Args:
            data: 输入数据，包含价格、时间等信息
            current_position: 当前持仓信息

        Returns:
            Tuple[str, Dict[str, Any]]: 交易信号（'B'表示买入，'S'表示卖出）和相关参数
        """
        # 示例实现：根据价格波动率生成信号
        price = data.get('price')
        signal = self._safe_return_20ticks(data)
        if signal <= self.current_params.get('buy_drop', BUY_TRIGGER_DROP):
            return 'B', {'price': price, 'quantity': 100}
        elif signal >= self.current_params.get('profit_target', PROFIT_TARGET):
            return 'S', {'price': price, 'quantity': 100}
        else:
            return '', {}
    
    def run_strategy_loop(self, poll_sec: float = 1.0) -> None:
        """运行策略主循环"""
        try:
            conn = sqlite3.connect(DB_PATH, detect_types=sqlite3.PARSE_DECLTYPES)
        except Exception as e:
            logger.error(f"打开数据库失败: {e}")
            return
        
        self._ensure_signal_table(conn)
        logger.info(f"启动增强策略引擎: {self.symbol}")
        
        # 检查数据库中是否有数据
        try:
            cursor = conn.execute("SELECT COUNT(*) FROM ticks WHERE symbol=?", (self.symbol,))
            total_records = cursor.fetchone()[0]
            logger.info(f"数据库中 {self.symbol} 共有 {total_records} 条记录")
            
            # 检查是否需要收盘前平仓
            if (getattr(self, 'exit_at_close', False) and 
                self.position.total_quantity > 0):
                # 检查是否接近收盘时间（14:55以后）
                current_time = datetime.now()
                if (current_time.hour == 14 and current_time.minute >= 55) or current_time.hour == 15:
                    logger.info("收盘前平仓")
                    self.execute_sell(current_price, current_time, 1.0, "收盘前平仓")
                    return

            # 策略主循环
            loop_count = 0
            consecutive_no_data = 0
            max_no_data_count = 100  # 最多连续100次无数据后退出
            
            while self.is_running:
                loop_count += 1
                if loop_count % 100 == 0:
                    logger.info(f"策略运行中... 循环次数: {loop_count}, 当前仓位: {self.position.total_quantity}")
                
                # 获取最新tick数据
                latest_tick = self.get_latest_tick()
                if not latest_tick:
                    consecutive_no_data += 1
                    if consecutive_no_data >= max_no_data_count:
                        logger.warning(f"连续{max_no_data_count}次无数据，退出策略循环")
                        break
                    time.sleep(self.poll_sec)
                    continue
                
                # 重置无数据计数器
                consecutive_no_data = 0
                
                current_price = float(latest_tick["price"])
                ts = latest_tick["time"]
                if hasattr(ts, 'to_pydatetime'):
                    ts = ts.to_pydatetime()
                
                # 获取历史数据用于信号计算
                recent_ticks = self._load_recent_ticks(conn, 600)
                if recent_ticks.empty:
                    logger.debug("无历史数据，跳过信号计算")
                    time.sleep(self.poll_sec)
                    continue
                    
                signal = self._safe_return_20ticks(recent_ticks)
                
                # 输出当前状态
                if loop_count % 50 == 0:
                    logger.info(f"当前状态: 价格={current_price:.4f}, 信号={signal:.4f}, "
                              f"仓位={self.position.total_quantity}, 成本={self.position.get_profit_rate(current_price):.4f}")
                
                # 更新风险管理器
                market_value = self.position.total_quantity * current_price if self.position.total_quantity > 0 else 0.0
                initial_capital = self.current_params.get('initial_capital', INITIAL_CAPITAL)
                current_equity = initial_capital - self.position.total_cost + market_value
                self.risk_manager.update_equity(current_equity)
                
                # 卖出判断
                # 优先处理卖出逻辑（避免同时买卖）
                should_sell, sell_ratio, sell_reason = self.should_sell(current_price)
                if should_sell:
                    self.execute_sell(conn, current_price, ts, sell_ratio, sell_reason)
                    time.sleep(1.0)  # 卖出后等待更长时间
                    continue
                
                # 买入判断（只有在没有卖出时才执行）
                if self.should_buy(signal, current_price):
                    # 买入前重置止盈标记，确保新买入的仓位可以享受完整的止盈策略
                    if self.position.total_quantity > 0:
                        # 如果已有仓位，重置部分止盈标记
                        self.position.reset_partial_sold_flags()
                    self.execute_buy(conn, current_price, ts)
                    time.sleep(1.0)
                    continue
                
                # 常规休眠
                time.sleep(poll_sec if poll_sec > 0 else 1.0)
                
        except Exception as e:
            logger.error(f"策略循环异常: {e}")
            time.sleep(1.0)


def _init_logger(level: int = logging.INFO) -> None:
    logging.basicConfig(level=level, format="%(asctime)s %(levelname)s %(message)s")


if __name__ == "__main__":
    _init_logger()
    
    parser = argparse.ArgumentParser(description="增强版策略引擎")
    parser.add_argument("--symbol", required=True, type=str, help="交易标的")
    parser.add_argument("--poll-sec", type=float, default=1.0, help="轮询间隔")
    parser.add_argument("--initial-capital", type=float, default=1_000_000.0, help="初始资金")
    
    args = parser.parse_args()
    
    strategy = EnhancedStrategy(args.symbol, args.initial_capital)
    strategy.run_strategy_loop(args.poll_sec)