#!/usr/bin/env python3
"""
测试增强版交易器重构
验证核心基础设施集成是否正常工作
"""

import sys
import os
import tempfile
import sqlite3
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

def test_core_infrastructure_integration():
    """测试核心基础设施集成"""
    print("🔍 测试核心基础设施集成...")
    
    try:
        # 测试导入
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import (
            RealTimeDataFeed, CORE_INFRASTRUCTURE_AVAILABLE
        )
        
        print(f"✅ 核心基础设施可用: {CORE_INFRASTRUCTURE_AVAILABLE}")
        
        # 测试数据源初始化
        data_feed = RealTimeDataFeed()
        print(f"✅ 数据源初始化成功，数据库路径: {data_feed.db_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 核心基础设施集成测试失败: {e}")
        return False

def test_database_manager_integration():
    """测试数据库管理器集成"""
    print("🔍 测试数据库管理器集成...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.database_manager import DatabaseManager
        
        # 获取数据库管理器实例
        db_manager = DatabaseManager.get_instance()
        
        # 测试数据库连接
        with db_manager.get_connection('ticks') as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = cursor.fetchall()
            
        print(f"✅ 数据库连接成功，发现 {len(tables)} 个表")
        
        # 测试保存tick数据
        success = db_manager.save_tick_data(
            symbol='TEST',
            tick_time='2024-01-01 09:30:00',
            price=1.234,
            volume=1000,
            side='BUY'
        )
        
        if success:
            print("✅ 数据保存测试通过")
        else:
            print("⚠️ 数据保存测试失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库管理器集成测试失败: {e}")
        return False

def test_config_manager_integration():
    """测试配置管理器集成"""
    print("🔍 测试配置管理器集成...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        
        # 测试获取数据库配置
        db_config = config_manager.get_database_config()
        print(f"✅ 数据库配置: ticks_db={db_config.ticks_db}")
        
        # 测试获取交易配置
        trading_config = config_manager.get_trading_config()
        print(f"✅ 交易配置: 默认仓位={trading_config.default_position_size:,.0f}")
        
        # 测试获取标的配置
        symbol_config = config_manager.get_symbol_config('159740')
        if symbol_config:
            print(f"✅ 标的配置: {symbol_config.name} - {symbol_config.description}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理器集成测试失败: {e}")
        return False

def test_logger_manager_integration():
    """测试日志管理器集成"""
    print("🔍 测试日志管理器集成...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.logger_manager import LoggerManager
        
        logger_manager = LoggerManager()
        
        # 创建测试日志器
        test_logger = logger_manager.create_module_logger('test_enhanced_trader')
        test_logger.info("测试日志消息")
        test_logger.warning("测试警告消息")
        
        print("✅ 日志管理器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 日志管理器集成测试失败: {e}")
        return False

def test_exception_handler_integration():
    """测试异常处理器集成"""
    print("🔍 测试异常处理器集成...")
    
    try:
        from etf_arbitrage_streamlit_multi.core.exception_handler import (
            ExceptionHandler, ErrorCategory, ErrorSeverity
        )
        
        exception_handler = ExceptionHandler()
        
        # 测试异常处理
        try:
            raise ValueError("测试异常")
        except Exception as e:
            exception_handler.handle_exception(
                e, ErrorCategory.TRADING_ERROR, ErrorSeverity.LOW
            )
        
        print("✅ 异常处理器集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 异常处理器集成测试失败: {e}")
        return False

def test_data_feed_functionality():
    """测试数据源功能"""
    print("🔍 测试数据源功能...")
    
    try:
        from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import RealTimeDataFeed
        
        # 创建数据源
        data_feed = RealTimeDataFeed()
        
        # 测试订阅功能
        received_data = []
        
        def test_callback(data):
            received_data.append(data)
        
        data_feed.subscribe(test_callback)
        print(f"✅ 订阅功能测试通过，订阅者数量: {len(data_feed.subscribers)}")
        
        # 测试数据库访问
        tick_data = data_feed._get_tick_from_database()
        if tick_data:
            print(f"✅ 数据库访问测试通过，获取到数据: {tick_data['symbol']}")
        else:
            print("⚠️ 数据库访问测试：未获取到数据（可能是正常情况）")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据源功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始增强版交易器重构测试")
    print("=" * 60)
    
    tests = [
        test_core_infrastructure_integration,
        test_database_manager_integration,
        test_config_manager_integration,
        test_logger_manager_integration,
        test_exception_handler_integration,
        test_data_feed_functionality
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 异常: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有重构测试通过！")
        print("✅ 增强版交易器已成功集成核心基础设施")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
