#!/usr/bin/env python3
"""
内存优化管理器
实现智能内存管理、数据压缩、缓存策略等
"""

import gc
import sys
import threading
import time
import logging
from typing import Dict, List, Optional, Any, Union, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from functools import wraps, lru_cache
from collections import OrderedDict
import weakref
import pickle
import gzip
import numpy as np
import pandas as pd

# 尝试导入内存分析工具
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import pympler
    from pympler import tracker, muppy, summary
    PYMPLER_AVAILABLE = True
except ImportError:
    PYMPLER_AVAILABLE = False

logger = logging.getLogger(__name__)

@dataclass
class MemoryUsageMetrics:
    """内存使用指标"""
    timestamp: datetime
    total_memory_mb: float
    available_memory_mb: float
    process_memory_mb: float
    memory_percent: float
    gc_collections: Dict[int, int]
    cache_size: int
    cache_hit_rate: float

@dataclass
class DataCompressionStats:
    """数据压缩统计"""
    original_size_mb: float
    compressed_size_mb: float
    compression_ratio: float
    compression_time: float
    decompression_time: float

class SmartCache:
    """智能缓存系统"""
    
    def __init__(self, max_size: int = 1000, max_memory_mb: int = 500):
        self.max_size = max_size
        self.max_memory_mb = max_memory_mb
        self._cache = OrderedDict()
        self._access_times = {}
        self._memory_usage = {}
        self._lock = threading.RLock()
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'memory_evictions': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self._lock:
            if key in self._cache:
                # 更新访问时间
                self._access_times[key] = time.time()
                # 移到末尾（LRU）
                self._cache.move_to_end(key)
                self.stats['hits'] += 1
                return self._cache[key]
            else:
                self.stats['misses'] += 1
                return None
    
    def set(self, key: str, value: Any, compress: bool = True):
        """设置缓存项"""
        with self._lock:
            # 计算内存使用
            memory_size = self._estimate_memory_size(value)
            
            # 压缩大对象
            if compress and memory_size > 1:  # 1MB以上压缩
                try:
                    compressed_value = self._compress_data(value)
                    if compressed_value:
                        value = compressed_value
                        memory_size = self._estimate_memory_size(value)
                except Exception as e:
                    logger.warning(f"数据压缩失败: {e}")
            
            # 检查内存限制
            current_memory = sum(self._memory_usage.values())
            if current_memory + memory_size > self.max_memory_mb:
                self._evict_by_memory()
            
            # 检查大小限制
            if len(self._cache) >= self.max_size:
                self._evict_lru()
            
            # 添加到缓存
            self._cache[key] = value
            self._access_times[key] = time.time()
            self._memory_usage[key] = memory_size
    
    def _estimate_memory_size(self, obj: Any) -> float:
        """估算对象内存大小（MB）"""
        try:
            if isinstance(obj, pd.DataFrame):
                return obj.memory_usage(deep=True).sum() / 1024 / 1024
            elif isinstance(obj, np.ndarray):
                return obj.nbytes / 1024 / 1024
            else:
                return sys.getsizeof(obj) / 1024 / 1024
        except:
            return 1.0  # 默认1MB
    
    def _compress_data(self, data: Any) -> Optional[bytes]:
        """压缩数据"""
        try:
            pickled_data = pickle.dumps(data)
            compressed_data = gzip.compress(pickled_data)
            return compressed_data
        except Exception as e:
            logger.warning(f"数据压缩失败: {e}")
            return None
    
    def _decompress_data(self, compressed_data: bytes) -> Any:
        """解压数据"""
        try:
            pickled_data = gzip.decompress(compressed_data)
            return pickle.loads(pickled_data)
        except Exception as e:
            logger.error(f"数据解压失败: {e}")
            return None
    
    def _evict_lru(self):
        """LRU淘汰"""
        if self._cache:
            key, _ = self._cache.popitem(last=False)
            self._access_times.pop(key, None)
            self._memory_usage.pop(key, None)
            self.stats['evictions'] += 1
    
    def _evict_by_memory(self):
        """按内存使用淘汰"""
        # 按内存使用量排序，优先淘汰大对象
        sorted_items = sorted(
            self._memory_usage.items(),
            key=lambda x: (x[1], -self._access_times.get(x[0], 0)),
            reverse=True
        )
        
        for key, memory_size in sorted_items:
            if key in self._cache:
                del self._cache[key]
                self._access_times.pop(key, None)
                self._memory_usage.pop(key, None)
                self.stats['memory_evictions'] += 1
                
                # 检查是否释放了足够内存
                current_memory = sum(self._memory_usage.values())
                if current_memory < self.max_memory_mb * 0.8:  # 释放到80%以下
                    break
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._access_times.clear()
            self._memory_usage.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = self.stats['hits'] / max(1, total_requests) * 100
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'memory_usage_mb': sum(self._memory_usage.values()),
                'max_memory_mb': self.max_memory_mb,
                'hit_rate': hit_rate,
                'total_requests': total_requests,
                **self.stats
            }

class DataFrameOptimizer:
    """DataFrame内存优化器"""
    
    @staticmethod
    def optimize_dtypes(df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame数据类型"""
        if df.empty:
            return df
        
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            if col_type == 'object':
                # 尝试转换为数值类型
                try:
                    optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
                except:
                    try:
                        optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
                    except:
                        # 保持为字符串，但使用category类型（如果重复值多）
                        if optimized_df[col].nunique() / len(optimized_df) < 0.5:
                            optimized_df[col] = optimized_df[col].astype('category')
            
            elif col_type in ['int64', 'int32']:
                # 降级整数类型
                optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='integer')
            
            elif col_type in ['float64', 'float32']:
                # 降级浮点类型
                optimized_df[col] = pd.to_numeric(optimized_df[col], downcast='float')
        
        return optimized_df
    
    @staticmethod
    def compress_dataframe(df: pd.DataFrame) -> Tuple[bytes, DataCompressionStats]:
        """压缩DataFrame"""
        start_time = time.time()
        
        # 原始大小
        original_size = df.memory_usage(deep=True).sum()
        
        # 序列化和压缩
        pickled_data = pickle.dumps(df)
        compressed_data = gzip.compress(pickled_data)
        
        compression_time = time.time() - start_time
        compressed_size = len(compressed_data)
        
        stats = DataCompressionStats(
            original_size_mb=original_size / 1024 / 1024,
            compressed_size_mb=compressed_size / 1024 / 1024,
            compression_ratio=original_size / compressed_size,
            compression_time=compression_time,
            decompression_time=0.0
        )
        
        return compressed_data, stats
    
    @staticmethod
    def decompress_dataframe(compressed_data: bytes) -> Tuple[pd.DataFrame, float]:
        """解压DataFrame"""
        start_time = time.time()
        
        pickled_data = gzip.decompress(compressed_data)
        df = pickle.loads(pickled_data)
        
        decompression_time = time.time() - start_time
        
        return df, decompression_time

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, check_interval: int = 60):
        self.check_interval = check_interval
        self.is_monitoring = False
        self.monitor_thread = None
        self.metrics_history = []
        self._lock = threading.Lock()
        
        # 内存阈值
        self.warning_threshold = 80  # 80%
        self.critical_threshold = 90  # 90%
        
        # 回调函数
        self.warning_callbacks = []
        self.critical_callbacks = []
    
    def start_monitoring(self):
        """开始内存监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("内存监控已启动")
    
    def stop_monitoring(self):
        """停止内存监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("内存监控已停止")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                metrics = self._collect_metrics()
                
                with self._lock:
                    self.metrics_history.append(metrics)
                    # 保持最近100条记录
                    if len(self.metrics_history) > 100:
                        self.metrics_history = self.metrics_history[-100:]
                
                # 检查阈值
                self._check_thresholds(metrics)
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"内存监控异常: {e}")
                time.sleep(self.check_interval)
    
    def _collect_metrics(self) -> MemoryUsageMetrics:
        """收集内存指标"""
        # 系统内存信息
        if PSUTIL_AVAILABLE:
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024
            
            total_memory = memory.total / 1024 / 1024
            available_memory = memory.available / 1024 / 1024
            memory_percent = memory.percent
        else:
            total_memory = 0
            available_memory = 0
            process_memory = 0
            memory_percent = 0
        
        # GC统计
        gc_stats = {}
        for i in range(3):
            gc_stats[i] = gc.get_count()[i]
        
        return MemoryUsageMetrics(
            timestamp=datetime.now(),
            total_memory_mb=total_memory,
            available_memory_mb=available_memory,
            process_memory_mb=process_memory,
            memory_percent=memory_percent,
            gc_collections=gc_stats,
            cache_size=0,  # 由具体缓存实现填充
            cache_hit_rate=0.0
        )
    
    def _check_thresholds(self, metrics: MemoryUsageMetrics):
        """检查内存阈值"""
        if metrics.memory_percent >= self.critical_threshold:
            logger.critical(f"内存使用率达到临界值: {metrics.memory_percent:.1f}%")
            for callback in self.critical_callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    logger.error(f"临界回调执行失败: {e}")
        
        elif metrics.memory_percent >= self.warning_threshold:
            logger.warning(f"内存使用率达到警告值: {metrics.memory_percent:.1f}%")
            for callback in self.warning_callbacks:
                try:
                    callback(metrics)
                except Exception as e:
                    logger.error(f"警告回调执行失败: {e}")
    
    def add_warning_callback(self, callback: Callable[[MemoryUsageMetrics], None]):
        """添加警告回调"""
        self.warning_callbacks.append(callback)
    
    def add_critical_callback(self, callback: Callable[[MemoryUsageMetrics], None]):
        """添加临界回调"""
        self.critical_callbacks.append(callback)
    
    def get_current_metrics(self) -> Optional[MemoryUsageMetrics]:
        """获取当前内存指标"""
        with self._lock:
            return self.metrics_history[-1] if self.metrics_history else None
    
    def get_metrics_history(self) -> List[MemoryUsageMetrics]:
        """获取历史指标"""
        with self._lock:
            return self.metrics_history.copy()

class MemoryOptimizer:
    """内存优化器主类"""
    
    def __init__(self):
        self.cache = SmartCache()
        self.monitor = MemoryMonitor()
        self.df_optimizer = DataFrameOptimizer()
        
        # 注册内存压力回调
        self.monitor.add_warning_callback(self._on_memory_warning)
        self.monitor.add_critical_callback(self._on_memory_critical)
        
        logger.info("内存优化器初始化完成")
    
    def start(self):
        """启动内存优化器"""
        self.monitor.start_monitoring()
    
    def stop(self):
        """停止内存优化器"""
        self.monitor.stop_monitoring()
    
    def _on_memory_warning(self, metrics: MemoryUsageMetrics):
        """内存警告处理"""
        logger.warning("执行内存清理...")
        
        # 清理缓存
        cache_stats = self.cache.get_stats()
        if cache_stats['memory_usage_mb'] > 100:  # 缓存超过100MB
            self.cache.clear()
            logger.info("缓存已清理")
        
        # 强制垃圾回收
        collected = gc.collect()
        logger.info(f"垃圾回收释放了 {collected} 个对象")
    
    def _on_memory_critical(self, metrics: MemoryUsageMetrics):
        """内存临界处理"""
        logger.critical("执行紧急内存清理...")
        
        # 清空所有缓存
        self.cache.clear()
        
        # 多次垃圾回收
        for i in range(3):
            collected = gc.collect()
            logger.info(f"第{i+1}次垃圾回收释放了 {collected} 个对象")
    
    def optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame"""
        return self.df_optimizer.optimize_dtypes(df)
    
    def cache_data(self, key: str, data: Any, compress: bool = True):
        """缓存数据"""
        self.cache.set(key, data, compress)
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        return self.cache.get(key)
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        current_metrics = self.monitor.get_current_metrics()
        cache_stats = self.cache.get_stats()
        
        return {
            'memory_metrics': current_metrics.__dict__ if current_metrics else {},
            'cache_stats': cache_stats,
            'gc_stats': {f'generation_{i}': gc.get_count()[i] for i in range(3)},
            'optimization_recommendations': self._get_recommendations()
        }
    
    def _get_recommendations(self) -> List[str]:
        """获取优化建议"""
        recommendations = []
        
        current_metrics = self.monitor.get_current_metrics()
        if current_metrics:
            if current_metrics.memory_percent > 70:
                recommendations.append("内存使用率较高，建议清理缓存")
            
            if current_metrics.process_memory_mb > 1000:
                recommendations.append("进程内存使用超过1GB，建议优化数据结构")
        
        cache_stats = self.cache.get_stats()
        if cache_stats['hit_rate'] < 50:
            recommendations.append("缓存命中率较低，建议调整缓存策略")
        
        return recommendations

# 全局内存优化器
_memory_optimizer = None
_optimizer_lock = threading.Lock()

def get_memory_optimizer() -> MemoryOptimizer:
    """获取内存优化器单例"""
    global _memory_optimizer
    
    if _memory_optimizer is None:
        with _optimizer_lock:
            if _memory_optimizer is None:
                _memory_optimizer = MemoryOptimizer()
    
    return _memory_optimizer

# 装饰器：自动内存优化
def memory_optimized(func):
    """内存优化装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        optimizer = get_memory_optimizer()
        
        # 执行前检查内存
        start_metrics = optimizer.monitor._collect_metrics()
        
        try:
            result = func(*args, **kwargs)
            
            # 优化返回的DataFrame
            if isinstance(result, pd.DataFrame):
                result = optimizer.optimize_dataframe(result)
            
            return result
            
        finally:
            # 执行后检查内存增长
            end_metrics = optimizer.monitor._collect_metrics()
            memory_growth = end_metrics.process_memory_mb - start_metrics.process_memory_mb
            
            if memory_growth > 100:  # 增长超过100MB
                logger.warning(f"函数 {func.__name__} 内存增长 {memory_growth:.1f}MB")
                gc.collect()
    
    return wrapper
