-- 新功能数据库表创建脚本
-- 执行时间：约30分钟

-- 1. 参数优化结果表
CREATE TABLE IF NOT EXISTS optimal_configs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    config_name TEXT NOT NULL,
    parameters TEXT NOT NULL,           -- JSON格式存储参数
    performance_metrics TEXT NOT NULL,  -- JSON格式存储性能指标
    backtest_period TEXT,              -- 回测时间段
    optimization_method TEXT,          -- 优化方法：genetic/grid_search/bayesian
    market_characteristics TEXT,       -- JSON格式存储市场特征
    fitness_score REAL,               -- 适应度分数
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL,
    UNIQUE(symbol, config_name)
);

-- 2. 预警规则表
CREATE TABLE IF NOT EXISTS alert_rules (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    rule_name TEXT NOT NULL,
    condition_expr TEXT NOT NULL,      -- 条件表达式，如 "signal <= -0.006"
    channels TEXT NOT NULL,            -- JSON格式存储通知渠道
    template TEXT NOT NULL,            -- 消息模板
    cooldown_seconds INTEGER DEFAULT 300,  -- 冷却期（秒）
    enabled BOOLEAN DEFAULT 1,         -- 是否启用
    priority INTEGER DEFAULT 1,        -- 优先级 1-5
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 3. 预警历史表
CREATE TABLE IF NOT EXISTS alert_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    rule_name TEXT NOT NULL,
    rule_id INTEGER,                   -- 关联规则ID
    message TEXT NOT NULL,             -- 发送的消息内容
    alert_time TEXT NOT NULL,          -- 预警时间
    channels TEXT NOT NULL,            -- JSON格式存储发送渠道
    success BOOLEAN DEFAULT 1,         -- 是否发送成功
    response_data TEXT,                -- 响应数据（JSON格式）
    trigger_data TEXT                  -- 触发时的市场数据（JSON格式）
);

-- 4. 市场特征分析表
CREATE TABLE IF NOT EXISTS market_analysis (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    analysis_date TEXT NOT NULL,
    analysis_period_days INTEGER NOT NULL,
    volatility_metrics TEXT NOT NULL,  -- JSON格式
    trend_metrics TEXT NOT NULL,       -- JSON格式
    liquidity_metrics TEXT NOT NULL,   -- JSON格式
    trading_patterns TEXT NOT NULL,    -- JSON格式
    recommendations TEXT,              -- JSON格式存储参数建议
    created_at TEXT NOT NULL,
    UNIQUE(symbol, analysis_date)
);

-- 5. 优化任务表（用于跟踪长时间运行的优化任务）
CREATE TABLE IF NOT EXISTS optimization_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    symbol TEXT NOT NULL,
    task_name TEXT NOT NULL,
    optimization_method TEXT NOT NULL,
    strategy_type TEXT NOT NULL,
    status TEXT DEFAULT 'pending',     -- pending/running/completed/failed
    progress REAL DEFAULT 0.0,         -- 进度百分比
    start_time TEXT,
    end_time TEXT,
    error_message TEXT,
    result_config_ids TEXT,            -- JSON数组，存储结果配置ID
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 创建索引以提升查询性能

-- optimal_configs 表索引
CREATE INDEX IF NOT EXISTS idx_optimal_configs_symbol ON optimal_configs(symbol);
CREATE INDEX IF NOT EXISTS idx_optimal_configs_method ON optimal_configs(optimization_method);
CREATE INDEX IF NOT EXISTS idx_optimal_configs_fitness ON optimal_configs(fitness_score DESC);
CREATE INDEX IF NOT EXISTS idx_optimal_configs_created ON optimal_configs(created_at DESC);

-- alert_rules 表索引
CREATE INDEX IF NOT EXISTS idx_alert_rules_symbol ON alert_rules(symbol, enabled);
CREATE INDEX IF NOT EXISTS idx_alert_rules_priority ON alert_rules(priority DESC);

-- alert_history 表索引
CREATE INDEX IF NOT EXISTS idx_alert_history_symbol ON alert_history(symbol);
CREATE INDEX IF NOT EXISTS idx_alert_history_time ON alert_history(alert_time DESC);
CREATE INDEX IF NOT EXISTS idx_alert_history_rule ON alert_history(rule_id);
CREATE INDEX IF NOT EXISTS idx_alert_history_success ON alert_history(success);

-- market_analysis 表索引
CREATE INDEX IF NOT EXISTS idx_market_analysis_symbol ON market_analysis(symbol);
CREATE INDEX IF NOT EXISTS idx_market_analysis_date ON market_analysis(analysis_date DESC);

-- optimization_tasks 表索引
CREATE INDEX IF NOT EXISTS idx_optimization_tasks_symbol ON optimization_tasks(symbol);
CREATE INDEX IF NOT EXISTS idx_optimization_tasks_status ON optimization_tasks(status);
CREATE INDEX IF NOT EXISTS idx_optimization_tasks_created ON optimization_tasks(created_at DESC);

-- 现有表的性能优化索引
-- 为 ticks 表添加复合索引以提升查询性能
CREATE INDEX IF NOT EXISTS idx_ticks_symbol_time_desc ON ticks(symbol, tick_time DESC);
CREATE INDEX IF NOT EXISTS idx_ticks_time_price ON ticks(tick_time, price);

-- 为 strategy_signals 表添加索引
CREATE INDEX IF NOT EXISTS idx_signals_symbol_ts_desc ON strategy_signals(symbol, ts DESC);
CREATE INDEX IF NOT EXISTS idx_signals_signal_type ON strategy_signals(signal);

-- 插入一些默认的预警规则模板
INSERT OR IGNORE INTO alert_rules (
    symbol, rule_name, condition_expr, channels, template, 
    cooldown_seconds, priority, created_at, updated_at
) VALUES 
(
    '159740', 
    '买入信号预警', 
    'signal <= -0.006', 
    '["email"]', 
    '🔥 {symbol} 触发买入信号！\n当前价格: {price}\n信号强度: {signal:.4f}\n时间: {time}', 
    300, 
    2,
    datetime('now'),
    datetime('now')
),
(
    '159740', 
    '强烈买入信号', 
    'signal <= -0.008', 
    '["email", "wechat"]', 
    '🚨 {symbol} 强烈买入信号！\n当前价格: {price}\n信号强度: {signal:.4f}\n建议立即关注！\n时间: {time}', 
    180, 
    1,
    datetime('now'),
    datetime('now')
),
(
    '159740', 
    '止盈信号预警', 
    'position_profit_rate >= 0.005', 
    '["email"]', 
    '💰 {symbol} 达到止盈条件！\n当前收益率: {position_profit_rate:.2%}\n建议考虑止盈\n时间: {time}', 
    600, 
    3,
    datetime('now'),
    datetime('now')
),
(
    '159740', 
    '风险预警', 
    'position_profit_rate <= -0.015', 
    '["email", "wechat"]', 
    '⚠️ {symbol} 风险预警！\n当前亏损: {position_profit_rate:.2%}\n请注意风险控制\n时间: {time}', 
    300, 
    1,
    datetime('now'),
    datetime('now')
);

-- 创建视图以简化常用查询
CREATE VIEW IF NOT EXISTS v_latest_optimal_configs AS
SELECT 
    symbol,
    config_name,
    parameters,
    performance_metrics,
    fitness_score,
    optimization_method,
    created_at
FROM optimal_configs 
WHERE id IN (
    SELECT MAX(id) 
    FROM optimal_configs 
    GROUP BY symbol, optimization_method
)
ORDER BY symbol, fitness_score DESC;

CREATE VIEW IF NOT EXISTS v_active_alert_rules AS
SELECT 
    id,
    symbol,
    rule_name,
    condition_expr,
    channels,
    template,
    cooldown_seconds,
    priority
FROM alert_rules 
WHERE enabled = 1
ORDER BY symbol, priority;

CREATE VIEW IF NOT EXISTS v_recent_alerts AS
SELECT 
    ah.symbol,
    ah.rule_name,
    ah.message,
    ah.alert_time,
    ah.success,
    ar.priority
FROM alert_history ah
LEFT JOIN alert_rules ar ON ah.rule_id = ar.id
WHERE ah.alert_time >= datetime('now', '-7 days')
ORDER BY ah.alert_time DESC;

-- 数据库性能优化设置
PRAGMA journal_mode = WAL;
PRAGMA synchronous = NORMAL;
PRAGMA cache_size = 10000;
PRAGMA temp_store = MEMORY;
PRAGMA mmap_size = 268435456; -- 256MB
