#!/usr/bin/env python3
"""
统一监控结果查看面板
提供Web界面查看所有监控系统的实时状态和历史数据
"""

import streamlit as st
import sqlite3
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import json
import os
import logging
import time
from typing import Dict, List, Any, Optional

# 设置页面配置
st.set_page_config(
    page_title="ETF套利系统 - 监控面板",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

logger = logging.getLogger(__name__)

class MonitoringDashboard:
    """监控结果查看面板"""
    
    def __init__(self):
        self.db_path = "monitoring.db"
        self.init_database()
    
    def init_database(self):
        """初始化监控数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # 创建性能监控表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    cpu_percent REAL,
                    memory_percent REAL,
                    disk_io_read REAL,
                    disk_io_write REAL,
                    network_io_sent REAL,
                    network_io_recv REAL,
                    process_count INTEGER
                )
            """)
            
            # 创建业务监控表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS business_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT,
                    total_return REAL,
                    sharpe_ratio REAL,
                    max_drawdown REAL,
                    win_rate REAL,
                    trade_count INTEGER,
                    signal_accuracy REAL
                )
            """)
            
            # 创建告警表
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    source TEXT,
                    severity TEXT,
                    title TEXT,
                    message TEXT,
                    acknowledged BOOLEAN DEFAULT 0,
                    resolved BOOLEAN DEFAULT 0,
                    acknowledged_by TEXT,
                    resolved_by TEXT,
                    resolved_at DATETIME
                )
            """)
            
            conn.commit()
            conn.close()
            logger.info("监控数据库初始化完成")
            
        except Exception as e:
            logger.error(f"初始化监控数据库失败: {e}")
    
    def get_performance_data(self, hours: int = 24) -> pd.DataFrame:
        """获取性能监控数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
                SELECT * FROM performance_metrics 
                WHERE timestamp >= datetime('now', '-{hours} hours')
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取性能数据失败: {e}")
            return pd.DataFrame()
    
    def get_business_data(self, symbol: str = "159740", hours: int = 24) -> pd.DataFrame:
        """获取业务监控数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
                SELECT * FROM business_metrics 
                WHERE symbol = ? AND timestamp >= datetime('now', '-{hours} hours')
                ORDER BY timestamp DESC
                LIMIT 1000
            """
            df = pd.read_sql_query(query, conn, params=(symbol,))
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取业务数据失败: {e}")
            return pd.DataFrame()
    
    def get_active_alerts(self) -> pd.DataFrame:
        """获取活跃告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = """
                SELECT * FROM alerts 
                WHERE resolved = 0 
                ORDER BY timestamp DESC
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取活跃告警失败: {e}")
            return pd.DataFrame()
    
    def get_recent_alerts(self, hours: int = 24) -> pd.DataFrame:
        """获取最近告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            query = f"""
                SELECT * FROM alerts 
                WHERE timestamp >= datetime('now', '-{hours} hours')
                ORDER BY timestamp DESC
                LIMIT 100
            """
            df = pd.read_sql_query(query, conn)
            conn.close()
            return df
        except Exception as e:
            logger.error(f"获取最近告警失败: {e}")
            return pd.DataFrame()
    
    def acknowledge_alert(self, alert_id: int, user: str = "admin"):
        """确认告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute(
                "UPDATE alerts SET acknowledged = 1, acknowledged_by = ? WHERE id = ?",
                (user, alert_id)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"确认告警失败: {e}")
            return False
    
    def resolve_alert(self, alert_id: int, user: str = "admin"):
        """解决告警"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.execute(
                "UPDATE alerts SET resolved = 1, resolved_by = ?, resolved_at = CURRENT_TIMESTAMP WHERE id = ?",
                (user, alert_id)
            )
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            logger.error(f"解决告警失败: {e}")
            return False

def create_performance_charts(df: pd.DataFrame):
    """创建性能监控图表"""
    if df.empty:
        st.warning("暂无性能监控数据")
        return
    
    # CPU和内存使用率
    fig1 = go.Figure()
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['cpu_percent'], 
                            name='CPU使用率', line=dict(color='blue')))
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['memory_percent'], 
                            name='内存使用率', line=dict(color='red')))
    fig1.update_layout(title='CPU和内存使用率', xaxis_title='时间', yaxis_title='百分比 (%)')
    st.plotly_chart(fig1, width='stretch')
    
    # 磁盘IO
    col1, col2 = st.columns(2)
    with col1:
        fig2 = px.line(df, x='timestamp', y='disk_io_read', 
                      title='磁盘读取速度 (MB/s)')
        st.plotly_chart(fig2, width='stretch')
    with col2:
        fig3 = px.line(df, x='timestamp', y='disk_io_write', 
                      title='磁盘写入速度 (MB/s)')
        st.plotly_chart(fig3, width='stretch')
    
    # 网络IO
    col3, col4 = st.columns(2)
    with col3:
        fig4 = px.line(df, x='timestamp', y='network_io_sent', 
                      title='网络发送速度 (MB/s)')
        st.plotly_chart(fig4, width='stretch')
    with col4:
        fig5 = px.line(df, x='timestamp', y='network_io_recv', 
                      title='网络接收速度 (MB/s)')
        st.plotly_chart(fig5, width='stretch')

def create_business_charts(df: pd.DataFrame):
    """创建业务监控图表"""
    if df.empty:
        st.warning("暂无业务监控数据")
        return
    
    # 收益率和夏普比率
    fig1 = go.Figure()
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['total_return'], 
                            name='总收益率', line=dict(color='green')))
    fig1.add_trace(go.Scatter(x=df['timestamp'], y=df['sharpe_ratio'], 
                            name='夏普比率', line=dict(color='orange')))
    fig1.update_layout(title='收益表现', xaxis_title='时间', yaxis_title='数值')
    st.plotly_chart(fig1, width='stretch')
    
    # 风险指标
    col1, col2 = st.columns(2)
    with col1:
        fig2 = px.line(df, x='timestamp', y='max_drawdown', 
                      title='最大回撤')
        st.plotly_chart(fig2, width='stretch')
    with col2:
        fig3 = px.line(df, x='timestamp', y='win_rate', 
                      title='胜率')
        st.plotly_chart(fig3, width='stretch')
    
    # 交易统计
    col3, col4 = st.columns(2)
    with col3:
        fig4 = px.bar(df, x='timestamp', y='trade_count', 
                     title='交易次数')
        st.plotly_chart(fig4, width='stretch')
    with col4:
        fig5 = px.line(df, x='timestamp', y='signal_accuracy', 
                      title='信号准确率')
        st.plotly_chart(fig5, width='stretch')

def display_alerts(alerts_df: pd.DataFrame, dashboard: MonitoringDashboard):
    """显示告警信息"""
    if alerts_df.empty:
        st.success("✅ 当前没有活跃告警")
        return
    
    st.subheader("🚨 活跃告警")
    
    for _, alert in alerts_df.iterrows():
        severity = alert['severity']
        severity_color = {
            'critical': 'red',
            'error': 'orange',
            'warning': 'yellow',
            'info': 'blue'
        }.get(severity, 'gray')
        
        with st.expander(f"{severity.upper()} - {alert['title']} - {alert['timestamp']}"):
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"**来源**: {alert['source']}")
                st.markdown(f"**严重程度**: {severity}")
                st.markdown(f"**时间**: {alert['timestamp']}")
            with col2:
                st.markdown(f"**消息**: {alert['message']}")
                if alert['acknowledged']:
                    st.success(f"✅ 已确认 by {alert['acknowledged_by']}")
                else:
                    if st.button("✅ 确认", key=f"ack_{alert['id']}"):
                        if dashboard.acknowledge_alert(alert['id'], "admin"):
                            st.success("告警已确认")
                            st.rerun()
                
                if alert['resolved']:
                    st.success(f"✅ 已解决 by {alert['resolved_by']}")
                else:
                    if st.button("✅ 解决", key=f"resolve_{alert['id']}"):
                        if dashboard.resolve_alert(alert['id'], "admin"):
                            st.success("告警已解决")
                            st.rerun()

def main():
    """主函数"""
    st.title("📊 ETF套利系统 - 统一监控面板")
    st.markdown("---")
    
    # 初始化监控面板
    dashboard = MonitoringDashboard()
    
    # 侧边栏 - 时间范围选择
    st.sidebar.header("⏰ 时间范围")
    time_range = st.sidebar.selectbox(
        "选择时间范围",
        ["1小时", "6小时", "12小时", "24小时", "48小时"],
        index=3
    )
    hours_map = {"1小时": 1, "6小时": 6, "12小时": 12, "24小时": 24, "48小时": 48}
    selected_hours = hours_map[time_range]
    
    # 侧边栏 - 标的选择
    st.sidebar.header("🎯 监控标的")
    symbol = st.sidebar.text_input("交易标的", value="159740")
    
    # 侧边栏 - 刷新控制
    st.sidebar.header("🔄 刷新控制")
    if st.sidebar.button("🔄 刷新数据"):
        st.rerun()
    
    auto_refresh = st.sidebar.checkbox("⏱️ 自动刷新", value=True)
    if auto_refresh:
        refresh_interval = st.sidebar.slider("刷新间隔(秒)", 5, 60, 30)
        st.sidebar.info(f"下次刷新: {refresh_interval}秒后")
    
    # 主界面 - 性能监控
    st.header("💻 系统性能监控")
    perf_data = dashboard.get_performance_data(selected_hours)
    create_performance_charts(perf_data)
    
    # 业务监控
    st.header("📈 业务性能监控")
    business_data = dashboard.get_business_data(symbol, selected_hours)
    create_business_charts(business_data)
    
    # 告警监控
    st.header("🚨 告警监控")
    active_alerts = dashboard.get_active_alerts()
    display_alerts(active_alerts, dashboard)
    
    # 最近告警
    with st.expander("📋 最近告警历史"):
        recent_alerts = dashboard.get_recent_alerts(24)
        if not recent_alerts.empty:
            st.dataframe(recent_alerts[['timestamp', 'source', 'severity', 'title', 'acknowledged', 'resolved']])
        else:
            st.info("暂无告警历史")
    
    # 系统状态
    st.sidebar.header("📋 系统状态")
    st.sidebar.metric("性能数据点", len(perf_data))
    st.sidebar.metric("业务数据点", len(business_data))
    st.sidebar.metric("活跃告警", len(active_alerts))
    
    # 页脚
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: gray;'>
    <p>📊 ETF套利系统监控面板 v1.0 | 实时监控所有系统组件</p>
    <p>💡 使用说明：选择时间范围和标的查看相应监控数据</p>
    </div>
    """, unsafe_allow_html=True)
    
    # 自动刷新
    if auto_refresh:
        time.sleep(refresh_interval)
        st.rerun()

if __name__ == "__main__":
    main()