#!/usr/bin/env python3
"""
安全的Plotly图表显示模块
解决图表ID冲突和显示问题
"""

import streamlit as st
import plotly.graph_objects as go
import uuid
from typing import Optional

def safe_plotly_chart(fig: go.Figure, use_container_width: bool = True, 
                     height: Optional[int] = None, key: Optional[str] = None) -> None:
    """
    安全显示Plotly图表，自动生成唯一key避免ID冲突
    
    Args:
        fig: Plotly图表对象
        use_container_width: 是否使用容器宽度
        height: 图表高度
        key: 自定义key，如果不提供则自动生成
    """
    try:
        # 如果没有提供key，自动生成唯一key
        if key is None:
            key = f"plotly_chart_{uuid.uuid4().hex[:8]}"
        
        # 检查图表是否有效
        if fig is None:
            st.error("图表对象为空")
            return
        
        # 检查图表是否有数据
        if not fig.data:
            st.warning("图表没有数据")
            return
        
        # 显示图表
        st.plotly_chart(
            fig, 
            use_container_width=use_container_width,
            height=height,
            key=key
        )
        
    except Exception as e:
        st.error(f"图表显示失败: {e}")
        
        # 显示备用信息
        st.info("正在使用备用显示方案...")
        
        # 尝试显示图表的基本信息
        try:
            if fig and hasattr(fig, 'data') and fig.data:
                st.write(f"图表包含 {len(fig.data)} 个数据系列")
                for i, trace in enumerate(fig.data):
                    if hasattr(trace, 'name') and trace.name:
                        st.write(f"- 系列 {i+1}: {trace.name}")
            else:
                st.write("无法显示图表数据")
        except:
            st.write("无法显示图表信息")

def ultra_safe_plotly_chart(fig: go.Figure, use_container_width: bool = True, 
                           height: Optional[int] = None) -> None:
    """
    超级安全的图表显示，带有多重错误处理
    """
    try:
        # 生成唯一key
        unique_key = f"ultra_safe_{uuid.uuid4().hex[:12]}"
        
        # 验证图表
        if fig is None:
            st.error("❌ 图表对象为空")
            return
            
        if not hasattr(fig, 'data') or not fig.data:
            st.warning("⚠️ 图表没有数据")
            return
        
        # 尝试显示图表
        st.plotly_chart(
            fig,
            use_container_width=use_container_width,
            height=height,
            key=unique_key
        )
        
    except Exception as e:
        st.error(f"❌ 图表显示失败: {str(e)}")
        
        # 显示调试信息
        with st.expander("🔍 调试信息", expanded=False):
            st.write("**错误详情:**")
            st.code(str(e))
            
            if fig:
                st.write("**图表信息:**")
                st.write(f"- 数据系列数量: {len(fig.data) if hasattr(fig, 'data') else 0}")
                st.write(f"- 布局信息: {bool(fig.layout) if hasattr(fig, 'layout') else False}")

def create_empty_chart(title: str = "暂无数据", message: str = "请配置参数并运行回测") -> go.Figure:
    """创建空白图表"""
    fig = go.Figure()
    
    fig.add_annotation(
        text=message,
        x=0.5, y=0.5,
        xref="paper", yref="paper",
        showarrow=False,
        font=dict(size=16, color="gray")
    )
    
    fig.update_layout(
        title=title,
        xaxis=dict(showgrid=False, showticklabels=False),
        yaxis=dict(showgrid=False, showticklabels=False),
        height=400
    )
    
    return fig

def display_chart_with_fallback(fig: go.Figure, title: str = "图表", 
                               fallback_message: str = "图表显示失败") -> None:
    """
    带有回退机制的图表显示
    """
    try:
        ultra_safe_plotly_chart(fig)
    except Exception as e:
        st.error(f"❌ {title}显示失败: {e}")
        
        # 显示空白图表作为回退
        empty_fig = create_empty_chart(title, fallback_message)
        try:
            ultra_safe_plotly_chart(empty_fig)
        except:
            st.write(f"📊 {title}: {fallback_message}")

# 导出函数
__all__ = [
    'safe_plotly_chart',
    'ultra_safe_plotly_chart', 
    'create_empty_chart',
    'display_chart_with_fallback'
]