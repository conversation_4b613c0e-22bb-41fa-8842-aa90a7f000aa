#!/usr/bin/env python3
"""
邮件通知器
支持SMTP邮件发送功能
"""

import asyncio
import smtplib
import ssl
import logging
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
from typing import Dict, List, Optional
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class EmailNotifier:
    """邮件通知器"""
    
    def __init__(self, 
                 smtp_server: str = None,
                 smtp_port: int = 587,
                 username: str = None,
                 password: str = None,
                 use_tls: bool = True):
        """
        初始化邮件通知器
        
        Args:
            smtp_server: SMTP服务器地址
            smtp_port: SMTP端口
            username: 邮箱用户名
            password: 邮箱密码或应用密码
            use_tls: 是否使用TLS加密
        """
        # 从环境变量或参数获取配置
        self.smtp_server = smtp_server or os.getenv('EMAIL_SMTP_SERVER', 'smtp.qq.com')
        self.smtp_port = smtp_port or int(os.getenv('EMAIL_SMTP_PORT', '587'))
        self.username = username or os.getenv('EMAIL_USERNAME')
        self.password = password or os.getenv('EMAIL_PASSWORD')
        self.use_tls = use_tls
        
        # 默认收件人列表
        self.default_recipients = self._load_default_recipients()
        
        # 邮件模板
        self.templates = {
            'alert': self._get_alert_template(),
            'summary': self._get_summary_template(),
            'error': self._get_error_template()
        }
        
        logger.info(f"邮件通知器初始化: {self.smtp_server}:{self.smtp_port}")
    
    def _load_default_recipients(self) -> List[str]:
        """加载默认收件人列表"""
        recipients_str = os.getenv('EMAIL_RECIPIENTS', '')
        if recipients_str:
            return [email.strip() for email in recipients_str.split(',')]
        return []
    
    def _get_alert_template(self) -> str:
        """获取预警邮件模板"""
        return """
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f44336; color: white; padding: 10px; border-radius: 5px; }
                .content { margin: 20px 0; }
                .footer { color: #666; font-size: 12px; margin-top: 20px; }
                .highlight { background-color: #ffeb3b; padding: 2px 4px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>🚨 ETF套利预警通知</h2>
            </div>
            <div class="content">
                <p><strong>交易标的:</strong> {symbol}</p>
                <p><strong>预警类型:</strong> {alert_type}</p>
                <p><strong>触发条件:</strong> {condition}</p>
                <p><strong>当前价格:</strong> <span class="highlight">{price}</span></p>
                <p><strong>信号强度:</strong> <span class="highlight">{signal}</span></p>
                <p><strong>触发时间:</strong> {timestamp}</p>
                
                <h3>详细信息:</h3>
                <p>{details}</p>
                
                <h3>建议操作:</h3>
                <p>{recommendation}</p>
            </div>
            <div class="footer">
                <p>此邮件由ETF套利系统自动发送，请勿回复。</p>
                <p>发送时间: {send_time}</p>
            </div>
        </body>
        </html>
        """
    
    def _get_summary_template(self) -> str:
        """获取总结邮件模板"""
        return """
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #4CAF50; color: white; padding: 10px; border-radius: 5px; }
                .content { margin: 20px 0; }
                .stats { background-color: #f5f5f5; padding: 15px; border-radius: 5px; }
                .footer { color: #666; font-size: 12px; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>📊 ETF套利系统日报</h2>
            </div>
            <div class="content">
                <h3>今日交易总结</h3>
                <div class="stats">
                    <p><strong>总收益率:</strong> {total_return}</p>
                    <p><strong>交易次数:</strong> {trade_count}</p>
                    <p><strong>胜率:</strong> {win_rate}</p>
                    <p><strong>最大回撤:</strong> {max_drawdown}</p>
                </div>
                
                <h3>预警统计</h3>
                <p><strong>触发预警:</strong> {alert_count} 次</p>
                <p><strong>成功交易:</strong> {successful_trades} 次</p>
                
                <h3>系统状态</h3>
                <p><strong>运行时间:</strong> {uptime}</p>
                <p><strong>数据更新:</strong> {last_update}</p>
            </div>
            <div class="footer">
                <p>ETF套利系统自动生成</p>
                <p>生成时间: {send_time}</p>
            </div>
        </body>
        </html>
        """
    
    def _get_error_template(self) -> str:
        """获取错误邮件模板"""
        return """
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #FF5722; color: white; padding: 10px; border-radius: 5px; }
                .content { margin: 20px 0; }
                .error { background-color: #ffebee; padding: 15px; border-left: 4px solid #f44336; }
                .footer { color: #666; font-size: 12px; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>⚠️ ETF套利系统错误通知</h2>
            </div>
            <div class="content">
                <h3>错误详情</h3>
                <div class="error">
                    <p><strong>错误类型:</strong> {error_type}</p>
                    <p><strong>错误信息:</strong> {error_message}</p>
                    <p><strong>发生时间:</strong> {timestamp}</p>
                    <p><strong>影响模块:</strong> {module}</p>
                </div>
                
                <h3>建议处理</h3>
                <p>{suggestion}</p>
            </div>
            <div class="footer">
                <p>请及时处理系统错误</p>
                <p>发送时间: {send_time}</p>
            </div>
        </body>
        </html>
        """
    
    async def send(self, message: str, subject: str = None, 
                   recipients: List[str] = None, 
                   message_type: str = 'alert',
                   **kwargs) -> bool:
        """
        发送邮件
        
        Args:
            message: 邮件内容
            subject: 邮件主题
            recipients: 收件人列表
            message_type: 消息类型 ('alert', 'summary', 'error')
            **kwargs: 模板参数
            
        Returns:
            发送是否成功
        """
        if not self.username or not self.password:
            logger.warning("邮件配置不完整，跳过发送")
            return False
        
        try:
            # 设置默认值
            recipients = recipients or self.default_recipients
            if not recipients:
                logger.warning("没有配置收件人，跳过发送")
                return False
            
            subject = subject or f"ETF套利系统通知 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 创建邮件
            msg = MimeMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.username
            msg['To'] = ', '.join(recipients)
            
            # 添加纯文本内容
            text_content = self._strip_html(message)
            text_part = MimeText(text_content, 'plain', 'utf-8')
            msg.attach(text_part)
            
            # 添加HTML内容
            if message_type in self.templates:
                html_content = self._format_html_message(message_type, message, **kwargs)
                html_part = MimeText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            else:
                # 简单HTML格式
                html_content = f"<html><body><pre>{message}</pre></body></html>"
                html_part = MimeText(html_content, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 发送邮件
            await self._send_email(msg, recipients)
            
            logger.info(f"邮件发送成功: {subject} -> {recipients}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {e}")
            return False
    
    def _format_html_message(self, message_type: str, message: str, **kwargs) -> str:
        """格式化HTML邮件内容"""
        template = self.templates[message_type]
        
        # 设置默认参数
        params = {
            'send_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'message': message,
            **kwargs
        }
        
        try:
            return template.format(**params)
        except KeyError as e:
            logger.warning(f"模板参数缺失: {e}")
            # 返回简单格式
            return f"<html><body><h3>{message_type.title()}</h3><p>{message}</p></body></html>"
    
    def _strip_html(self, html_text: str) -> str:
        """移除HTML标签，获取纯文本"""
        import re
        clean = re.compile('<.*?>')
        return re.sub(clean, '', html_text)
    
    async def _send_email(self, msg: MimeMultipart, recipients: List[str]):
        """发送邮件的核心逻辑"""
        # 在线程池中执行同步的SMTP操作
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._send_smtp, msg, recipients)
    
    def _send_smtp(self, msg: MimeMultipart, recipients: List[str]):
        """同步SMTP发送"""
        context = ssl.create_default_context()
        
        with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
            if self.use_tls:
                server.starttls(context=context)
            
            server.login(self.username, self.password)
            server.send_message(msg, to_addrs=recipients)
    
    async def send_alert(self, symbol: str, alert_type: str, condition: str,
                        price: float, signal: float, details: str = "",
                        recommendation: str = "") -> bool:
        """发送预警邮件"""
        subject = f"🚨 {symbol} {alert_type}预警"
        
        return await self.send(
            message=f"{symbol} 触发 {alert_type}",
            subject=subject,
            message_type='alert',
            symbol=symbol,
            alert_type=alert_type,
            condition=condition,
            price=f"{price:.4f}",
            signal=f"{signal:.4f}",
            details=details or "无额外信息",
            recommendation=recommendation or "请根据策略执行相应操作",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    async def send_summary(self, total_return: float, trade_count: int,
                          win_rate: float, max_drawdown: float,
                          alert_count: int, successful_trades: int) -> bool:
        """发送日报邮件"""
        subject = f"📊 ETF套利系统日报 - {datetime.now().strftime('%Y-%m-%d')}"
        
        return await self.send(
            message="ETF套利系统日报",
            subject=subject,
            message_type='summary',
            total_return=f"{total_return:.2%}",
            trade_count=trade_count,
            win_rate=f"{win_rate:.2%}",
            max_drawdown=f"{max_drawdown:.2%}",
            alert_count=alert_count,
            successful_trades=successful_trades,
            uptime="24小时",
            last_update=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    async def send_error(self, error_type: str, error_message: str,
                        module: str, suggestion: str = "") -> bool:
        """发送错误通知邮件"""
        subject = f"⚠️ ETF套利系统错误 - {error_type}"
        
        return await self.send(
            message=f"系统错误: {error_type}",
            subject=subject,
            message_type='error',
            error_type=error_type,
            error_message=error_message,
            module=module,
            suggestion=suggestion or "请检查系统日志并联系技术支持",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    
    def test_connection(self) -> bool:
        """测试邮件连接"""
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.use_tls:
                    server.starttls(context=context)
                server.login(self.username, self.password)
            
            logger.info("邮件连接测试成功")
            return True
            
        except Exception as e:
            logger.error(f"邮件连接测试失败: {e}")
            return False


# 测试函数
async def test_email_notifier():
    """测试邮件通知器"""
    logger.info("开始测试邮件通知器...")
    
    # 创建邮件通知器实例
    notifier = EmailNotifier()
    
    # 测试连接
    if not notifier.test_connection():
        logger.warning("邮件连接测试失败，跳过发送测试")
        return False
    
    try:
        # 测试预警邮件
        success1 = await notifier.send_alert(
            symbol="159740",
            alert_type="买入信号",
            condition="signal <= -0.006",
            price=0.7523,
            signal=-0.0078,
            details="信号强度较强，建议关注",
            recommendation="考虑买入操作"
        )
        
        # 测试简单消息
        success2 = await notifier.send(
            message="这是一条测试消息",
            subject="ETF套利系统测试"
        )
        
        logger.info(f"邮件测试结果: 预警邮件={success1}, 简单消息={success2}")
        return success1 or success2
        
    except Exception as e:
        logger.error(f"邮件测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_email_notifier())
