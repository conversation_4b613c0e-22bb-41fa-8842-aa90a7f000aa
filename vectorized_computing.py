#!/usr/bin/env python3
"""
向量化计算模块
使用NumPy和Pandas实现高性能的向量化计算
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any
from datetime import datetime, timedelta
import time
from dataclasses import dataclass
try:
    from numba import jit, njit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    # 如果没有numba，创建空装饰器
    def njit(func):
        return func
    def jit(func):
        return func
    def prange(x):
        return range(x)
    NUMBA_AVAILABLE = False

import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetrics:
    """性能指标"""
    execution_time: float
    memory_usage: float
    speedup_ratio: float = 1.0
    operations_per_second: float = 0.0

class VectorizedComputing:
    """向量化计算器"""
    
    def __init__(self, use_numba: bool = True):
        """
        初始化向量化计算器

        Args:
            use_numba: 是否使用Numba加速
        """
        self.use_numba = use_numba and NUMBA_AVAILABLE
        self.performance_cache = {}

        if use_numba and not NUMBA_AVAILABLE:
            logger.warning("Numba未安装，将使用NumPy实现")

        logger.info(f"向量化计算器初始化: Numba={'启用' if self.use_numba else '禁用'}")
    
    def vectorized_signal_calculation(self, 
                                    prices: np.ndarray,
                                    volumes: np.ndarray,
                                    window_size: int = 20) -> np.ndarray:
        """
        向量化信号计算
        
        Args:
            prices: 价格数组
            volumes: 成交量数组
            window_size: 窗口大小
            
        Returns:
            信号数组
        """
        if self.use_numba:
            return self._numba_signal_calculation(prices, volumes, window_size)
        else:
            return self._numpy_signal_calculation(prices, volumes, window_size)
    
    def _numpy_signal_calculation(self, prices: np.ndarray, volumes: np.ndarray, window_size: int) -> np.ndarray:
        """NumPy版本的信号计算"""
        # 计算价格变化率
        price_changes = np.diff(prices) / prices[:-1]
        
        # 计算移动平均
        price_ma = pd.Series(prices).rolling(window=window_size).mean().values
        volume_ma = pd.Series(volumes).rolling(window=window_size).mean().values
        
        # 计算标准差
        price_std = pd.Series(prices).rolling(window=window_size).std().values
        
        # 计算Z-score
        z_scores = np.where(price_std > 0, (prices - price_ma) / price_std, 0)
        
        # 计算成交量权重
        volume_weights = volumes / (volume_ma + 1e-8)
        
        # 综合信号
        signals = z_scores * volume_weights * 0.1
        
        return np.nan_to_num(signals, 0)
    
    @staticmethod
    @njit
    def _numba_signal_calculation(prices: np.ndarray, volumes: np.ndarray, window_size: int) -> np.ndarray:
        """Numba加速版本的信号计算"""
        n = len(prices)
        signals = np.zeros(n)
        
        for i in prange(window_size, n):
            # 计算窗口内的统计量
            window_prices = prices[i-window_size:i]
            window_volumes = volumes[i-window_size:i]
            
            # 价格均值和标准差
            price_mean = np.mean(window_prices)
            price_std = np.std(window_prices)
            
            # 成交量均值
            volume_mean = np.mean(window_volumes)
            
            # Z-score
            if price_std > 0:
                z_score = (prices[i] - price_mean) / price_std
            else:
                z_score = 0.0
            
            # 成交量权重
            volume_weight = volumes[i] / (volume_mean + 1e-8)
            
            # 综合信号
            signals[i] = z_score * volume_weight * 0.1
        
        return signals
    
    def batch_backtest_vectorized(self, 
                                 price_data: pd.DataFrame,
                                 parameter_sets: List[Dict[str, float]]) -> pd.DataFrame:
        """
        批量向量化回测
        
        Args:
            price_data: 价格数据
            parameter_sets: 参数集合列表
            
        Returns:
            回测结果DataFrame
        """
        results = []
        
        # 预计算常用指标
        prices = price_data['close'].values
        volumes = price_data.get('volume', np.ones(len(prices))).values
        
        # 向量化计算所有信号
        signals = self.vectorized_signal_calculation(prices, volumes)
        
        for i, params in enumerate(parameter_sets):
            try:
                # 向量化回测单个参数集
                result = self._vectorized_single_backtest(
                    prices, signals, params
                )
                result['param_set_id'] = i
                results.append(result)
                
            except Exception as e:
                logger.error(f"参数集 {i} 回测失败: {e}")
                continue
        
        return pd.DataFrame(results)
    
    def _vectorized_single_backtest(self, 
                                   prices: np.ndarray,
                                   signals: np.ndarray,
                                   params: Dict[str, float]) -> Dict[str, float]:
        """向量化单个参数集回测"""
        buy_trigger = params.get('buy_trigger_drop', -0.006)
        profit_target = params.get('profit_target', 0.005)
        stop_loss = params.get('stop_loss', -0.02)
        
        if self.use_numba:
            return self._numba_backtest(prices, signals, buy_trigger, profit_target, stop_loss)
        else:
            return self._numpy_backtest(prices, signals, buy_trigger, profit_target, stop_loss)
    
    def _numpy_backtest(self, prices: np.ndarray, signals: np.ndarray,
                       buy_trigger: float, profit_target: float, stop_loss: float) -> Dict[str, float]:
        """NumPy版本的回测"""
        # 买入信号
        buy_signals = signals <= buy_trigger
        
        # 模拟交易
        positions = np.zeros(len(prices))
        returns = np.zeros(len(prices))
        
        in_position = False
        entry_price = 0.0
        
        for i in range(1, len(prices)):
            if not in_position and buy_signals[i]:
                # 买入
                in_position = True
                entry_price = prices[i]
                positions[i] = 1
            elif in_position:
                # 检查卖出条件
                current_return = (prices[i] - entry_price) / entry_price
                
                if current_return >= profit_target or current_return <= stop_loss:
                    # 卖出
                    in_position = False
                    returns[i] = current_return
                    positions[i] = 0
                else:
                    positions[i] = 1
        
        # 计算性能指标
        valid_returns = returns[returns != 0]
        
        if len(valid_returns) > 0:
            total_return = np.sum(valid_returns)
            win_rate = np.sum(valid_returns > 0) / len(valid_returns)
            max_drawdown = np.min(np.cumsum(returns))
            sharpe_ratio = np.mean(valid_returns) / (np.std(valid_returns) + 1e-8) * np.sqrt(252)
        else:
            total_return = 0.0
            win_rate = 0.0
            max_drawdown = 0.0
            sharpe_ratio = 0.0
        
        return {
            'total_return': total_return,
            'win_rate': win_rate,
            'max_drawdown': max_drawdown,
            'sharpe_ratio': sharpe_ratio,
            'trade_count': len(valid_returns),
            'fitness': total_return * 0.4 + win_rate * 0.3 + (1 + max_drawdown) * 0.3
        }
    
    @staticmethod
    @njit
    def _numba_backtest(prices: np.ndarray, signals: np.ndarray,
                       buy_trigger: float, profit_target: float, stop_loss: float) -> Dict:
        """Numba加速版本的回测"""
        n = len(prices)
        returns = np.zeros(n)
        
        in_position = False
        entry_price = 0.0
        trade_count = 0
        
        for i in range(1, n):
            if not in_position and signals[i] <= buy_trigger:
                # 买入
                in_position = True
                entry_price = prices[i]
            elif in_position:
                # 检查卖出条件
                current_return = (prices[i] - entry_price) / entry_price
                
                if current_return >= profit_target or current_return <= stop_loss:
                    # 卖出
                    in_position = False
                    returns[i] = current_return
                    trade_count += 1
        
        # 计算性能指标
        valid_returns = returns[returns != 0]
        
        if len(valid_returns) > 0:
            total_return = np.sum(valid_returns)
            win_count = np.sum(valid_returns > 0)
            win_rate = win_count / len(valid_returns)
            
            # 计算最大回撤
            cumulative_returns = np.cumsum(returns)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = cumulative_returns - running_max
            max_drawdown = np.min(drawdowns)
            
            # 夏普比率
            if np.std(valid_returns) > 0:
                sharpe_ratio = np.mean(valid_returns) / np.std(valid_returns) * np.sqrt(252)
            else:
                sharpe_ratio = 0.0
        else:
            total_return = 0.0
            win_rate = 0.0
            max_drawdown = 0.0
            sharpe_ratio = 0.0
        
        # 注意：Numba不支持返回字典，需要在外部包装
        return total_return, win_rate, max_drawdown, sharpe_ratio, trade_count
    
    def parallel_parameter_optimization(self, 
                                      price_data: pd.DataFrame,
                                      param_ranges: Dict[str, Tuple[float, float, float]],
                                      max_combinations: int = 10000) -> pd.DataFrame:
        """
        并行参数优化
        
        Args:
            price_data: 价格数据
            param_ranges: 参数范围
            max_combinations: 最大组合数
            
        Returns:
            优化结果
        """
        start_time = time.time()
        
        # 生成参数组合
        parameter_sets = self._generate_parameter_combinations(param_ranges, max_combinations)
        
        logger.info(f"开始并行优化 {len(parameter_sets)} 个参数组合...")
        
        # 批量向量化回测
        results_df = self.batch_backtest_vectorized(price_data, parameter_sets)
        
        # 按适应度排序
        results_df = results_df.sort_values('fitness', ascending=False)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        logger.info(f"并行优化完成: {len(results_df)} 个结果, 耗时 {execution_time:.2f}秒")
        
        # 记录性能
        self.performance_cache['last_optimization'] = PerformanceMetrics(
            execution_time=execution_time,
            memory_usage=results_df.memory_usage(deep=True).sum() / 1024 / 1024,  # MB
            operations_per_second=len(parameter_sets) / execution_time
        )
        
        return results_df
    
    def _generate_parameter_combinations(self, 
                                       param_ranges: Dict[str, Tuple[float, float, float]],
                                       max_combinations: int) -> List[Dict[str, float]]:
        """生成参数组合"""
        import itertools
        
        # 生成每个参数的值列表
        param_values = {}
        for param_name, (min_val, max_val, step) in param_ranges.items():
            if step > 0:
                values = np.arange(min_val, max_val + step, step)
            else:
                values = np.array([min_val])  # 固定值
            param_values[param_name] = values
        
        # 生成所有组合
        param_names = list(param_values.keys())
        combinations = list(itertools.product(*[param_values[name] for name in param_names]))
        
        # 限制组合数量
        if len(combinations) > max_combinations:
            # 随机采样
            indices = np.random.choice(len(combinations), max_combinations, replace=False)
            combinations = [combinations[i] for i in indices]
        
        # 转换为字典列表
        parameter_sets = []
        for combo in combinations:
            param_dict = {name: value for name, value in zip(param_names, combo)}
            parameter_sets.append(param_dict)
        
        return parameter_sets
    
    def benchmark_performance(self, data_size: int = 10000) -> Dict[str, PerformanceMetrics]:
        """性能基准测试"""
        logger.info(f"开始性能基准测试: 数据大小 {data_size}")
        
        # 生成测试数据
        np.random.seed(42)
        prices = 100 + np.cumsum(np.random.randn(data_size) * 0.01)
        volumes = np.random.randint(1000, 10000, data_size)
        
        results = {}
        
        # 测试NumPy版本
        start_time = time.time()
        numpy_signals = self._numpy_signal_calculation(prices, volumes, 20)
        numpy_time = time.time() - start_time
        
        results['numpy'] = PerformanceMetrics(
            execution_time=numpy_time,
            memory_usage=numpy_signals.nbytes / 1024 / 1024,
            operations_per_second=data_size / numpy_time
        )
        
        # 测试Numba版本（如果可用）
        if self.use_numba:
            start_time = time.time()
            numba_signals = self._numba_signal_calculation(prices, volumes, 20)
            numba_time = time.time() - start_time
            
            results['numba'] = PerformanceMetrics(
                execution_time=numba_time,
                memory_usage=numba_signals.nbytes / 1024 / 1024,
                speedup_ratio=numpy_time / numba_time,
                operations_per_second=data_size / numba_time
            )
        
        logger.info("性能基准测试完成")
        return results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            'use_numba': self.use_numba,
            'cached_metrics': self.performance_cache
        }


# 测试函数
def test_vectorized_computing():
    """测试向量化计算"""
    logger.info("开始测试向量化计算...")
    
    try:
        # 创建计算器
        computer = VectorizedComputing(use_numba=True)
        
        # 生成测试数据
        np.random.seed(42)
        n_points = 1000
        dates = pd.date_range('2024-01-01', periods=n_points, freq='1min')
        prices = 100 + np.cumsum(np.random.randn(n_points) * 0.01)
        volumes = np.random.randint(1000, 10000, n_points)
        
        price_data = pd.DataFrame({
            'datetime': dates,
            'close': prices,
            'volume': volumes
        })
        
        # 测试信号计算
        logger.info("测试信号计算...")
        signals = computer.vectorized_signal_calculation(prices, volumes)
        logger.info(f"✅ 信号计算完成: {len(signals)} 个信号")
        
        # 测试批量回测
        logger.info("测试批量回测...")
        parameter_sets = [
            {'buy_trigger_drop': -0.006, 'profit_target': 0.005, 'stop_loss': -0.02},
            {'buy_trigger_drop': -0.008, 'profit_target': 0.006, 'stop_loss': -0.025},
            {'buy_trigger_drop': -0.010, 'profit_target': 0.007, 'stop_loss': -0.03}
        ]
        
        results_df = computer.batch_backtest_vectorized(price_data, parameter_sets)
        logger.info(f"✅ 批量回测完成: {len(results_df)} 个结果")
        
        # 性能基准测试
        logger.info("性能基准测试...")
        benchmark_results = computer.benchmark_performance(5000)
        
        for method, metrics in benchmark_results.items():
            logger.info(f"📊 {method}: {metrics.execution_time:.4f}秒, "
                       f"{metrics.operations_per_second:.0f} ops/s")
            if hasattr(metrics, 'speedup_ratio') and metrics.speedup_ratio > 1:
                logger.info(f"   加速比: {metrics.speedup_ratio:.2f}x")
        
        logger.info("✅ 向量化计算测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 向量化计算测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    test_vectorized_computing()
