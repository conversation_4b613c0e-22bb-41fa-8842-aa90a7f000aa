#!/usr/bin/env python3
"""
统一监控管理器
整合性能监控、业务监控和预警监控
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

try:
    # 用于直接运行测试
    from performance_monitor import PerformanceMonitor, PerformanceMetrics
    from business_monitor import BusinessMonitor, TradingMetrics, SignalQuality
    from alert_monitor import AlertMonitor, AlertSeverity
except ImportError:
    # 用于模块导入
    from .performance_monitor import PerformanceMonitor, PerformanceMetrics
    from .business_monitor import BusinessMonitor, TradingMetrics, SignalQuality
    from .alert_monitor import AlertMonitor, AlertSeverity

logger = logging.getLogger(__name__)

class MonitoringManager:
    """统一监控管理器"""
    
    def __init__(self, 
                 performance_interval: int = 5,
                 enable_performance_monitoring: bool = True,
                 enable_business_monitoring: bool = True,
                 enable_alert_monitoring: bool = True):
        """
        初始化监控管理器
        
        Args:
            performance_interval: 性能监控间隔
            enable_performance_monitoring: 启用性能监控
            enable_business_monitoring: 启用业务监控
            enable_alert_monitoring: 启用预警监控
        """
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_business_monitoring = enable_business_monitoring
        self.enable_alert_monitoring = enable_alert_monitoring
        
        # 初始化各监控器
        self.performance_monitor = PerformanceMonitor(
            collection_interval=performance_interval
        ) if enable_performance_monitoring else None
        
        self.business_monitor = BusinessMonitor() if enable_business_monitoring else None
        
        self.alert_monitor = AlertMonitor() if enable_alert_monitoring else None
        
        # 监控状态
        self.is_running = False
        
        # 设置监控器之间的集成
        self._setup_integrations()
        
        logger.info("统一监控管理器初始化完成")
    
    def _setup_integrations(self):
        """设置监控器之间的集成"""
        if self.performance_monitor and self.alert_monitor:
            # 性能监控告警回调
            self.performance_monitor.add_alert_callback(self._handle_performance_alert)
        
        if self.business_monitor and self.alert_monitor:
            # 业务监控告警回调
            self.business_monitor.add_alert_callback(self._handle_business_alert)
        
        # 设置通知渠道
        if self.alert_monitor:
            self.alert_monitor.register_notification_channel('console', self._console_notifier)
    
    async def start_all_monitoring(self):
        """启动所有监控"""
        if self.is_running:
            logger.warning("监控系统已在运行")
            return
        
        logger.info("启动统一监控系统...")
        
        # 启动各个监控器
        if self.performance_monitor:
            await self.performance_monitor.start_monitoring()
            logger.info("✅ 性能监控已启动")
        
        if self.business_monitor:
            await self.business_monitor.start_monitoring()
            logger.info("✅ 业务监控已启动")
        
        if self.alert_monitor:
            await self.alert_monitor.start_monitoring()
            logger.info("✅ 预警监控已启动")
        
        self.is_running = True
        logger.info("🚀 统一监控系统启动完成")
    
    async def stop_all_monitoring(self):
        """停止所有监控"""
        if not self.is_running:
            return
        
        logger.info("停止统一监控系统...")
        
        # 停止各个监控器
        if self.performance_monitor:
            await self.performance_monitor.stop_monitoring()
            logger.info("⏹️ 性能监控已停止")
        
        if self.business_monitor:
            await self.business_monitor.stop_monitoring()
            logger.info("⏹️ 业务监控已停止")
        
        if self.alert_monitor:
            await self.alert_monitor.stop_monitoring()
            logger.info("⏹️ 预警监控已停止")
        
        self.is_running = False
        logger.info("🛑 统一监控系统已停止")
    
    async def _handle_performance_alert(self, alert_data: Dict[str, Any]):
        """处理性能告警"""
        if not self.alert_monitor:
            return
        
        severity = AlertSeverity.CRITICAL if alert_data.get('level') == 'critical' else AlertSeverity.WARNING
        
        await self.alert_monitor.create_alert(
            source='performance',
            alert_type=alert_data.get('metric_name', 'unknown'),
            severity=severity,
            title=f"性能告警: {alert_data.get('metric_name', '未知指标')}",
            message=alert_data.get('message', ''),
            metrics=alert_data
        )
    
    async def _handle_business_alert(self, alert):
        """处理业务告警"""
        if not self.alert_monitor:
            return
        
        severity_map = {
            'info': AlertSeverity.INFO,
            'warning': AlertSeverity.WARNING,
            'critical': AlertSeverity.CRITICAL
        }
        
        severity = severity_map.get(alert.severity, AlertSeverity.WARNING)
        
        await self.alert_monitor.create_alert(
            source='business',
            alert_type=alert.alert_type,
            severity=severity,
            title=f"业务告警: {alert.alert_type}",
            message=alert.message,
            symbol=alert.symbol,
            metrics=alert.metrics
        )
    
    def _console_notifier(self, alert):
        """控制台通知器"""
        severity_emoji = {
            'info': 'ℹ️',
            'warning': '⚠️',
            'critical': '🚨'
        }
        
        emoji = severity_emoji.get(alert.severity.value, '📢')
        logger.info(f"{emoji} 告警通知: [{alert.severity.value.upper()}] {alert.title}")
    
    # 业务数据记录接口
    async def record_trading_metrics(self, metrics: TradingMetrics):
        """记录交易指标"""
        if self.business_monitor:
            await self.business_monitor.record_trading_metrics(metrics)
    
    async def record_signal_quality(self, quality: SignalQuality):
        """记录信号质量"""
        if self.business_monitor:
            await self.business_monitor.record_signal_quality(quality)
    
    # 手动创建告警接口
    async def create_alert(self, 
                          source: str,
                          alert_type: str,
                          severity: str,
                          title: str,
                          message: str,
                          symbol: Optional[str] = None,
                          metrics: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """创建告警"""
        if self.alert_monitor:
            return await self.alert_monitor.create_alert(
                source=source,
                alert_type=alert_type,
                severity=severity,
                title=title,
                message=message,
                symbol=symbol,
                metrics=metrics
            )
        return None
    
    # 获取监控数据接口
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        if self.performance_monitor:
            return self.performance_monitor.get_performance_summary()
        return {}
    
    def get_business_summary(self) -> Dict[str, Any]:
        """获取业务摘要"""
        if self.business_monitor:
            return self.business_monitor.get_business_summary()
        return {}
    
    def get_alert_summary(self) -> Dict[str, Any]:
        """获取告警摘要"""
        if self.alert_monitor:
            return self.alert_monitor.get_alert_summary()
        return {}
    
    def get_comprehensive_summary(self) -> Dict[str, Any]:
        """获取综合摘要"""
        return {
            'monitoring_status': {
                'is_running': self.is_running,
                'performance_enabled': self.enable_performance_monitoring,
                'business_enabled': self.enable_business_monitoring,
                'alert_enabled': self.enable_alert_monitoring
            },
            'performance': self.get_performance_summary(),
            'business': self.get_business_summary(),
            'alerts': self.get_alert_summary(),
            'timestamp': datetime.now().isoformat()
        }
    
    # 配置管理接口
    def update_performance_thresholds(self, thresholds: Dict[str, Dict[str, float]]):
        """更新性能阈值"""
        if self.performance_monitor:
            for metric_name, values in thresholds.items():
                self.performance_monitor.update_threshold(
                    metric_name,
                    values.get('warning', 0),
                    values.get('critical', 0)
                )
    
    def update_business_rules(self, rules: Dict[str, Dict[str, Any]]):
        """更新业务规则"""
        if self.business_monitor:
            self.business_monitor.business_rules.update(rules)
    
    # 健康检查
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        health = {
            'overall_status': 'healthy',
            'components': {}
        }
        
        # 检查性能监控
        if self.performance_monitor:
            perf_healthy = self.performance_monitor.is_monitoring
            health['components']['performance_monitor'] = {
                'status': 'healthy' if perf_healthy else 'unhealthy',
                'is_monitoring': perf_healthy
            }
        
        # 检查业务监控
        if self.business_monitor:
            biz_healthy = self.business_monitor.is_monitoring
            health['components']['business_monitor'] = {
                'status': 'healthy' if biz_healthy else 'unhealthy',
                'is_monitoring': biz_healthy
            }
        
        # 检查预警监控
        if self.alert_monitor:
            alert_healthy = self.alert_monitor.is_monitoring
            health['components']['alert_monitor'] = {
                'status': 'healthy' if alert_healthy else 'unhealthy',
                'is_monitoring': alert_healthy
            }
        
        # 检查整体状态
        component_statuses = [comp['status'] for comp in health['components'].values()]
        if any(status == 'unhealthy' for status in component_statuses):
            health['overall_status'] = 'degraded'
        
        if not self.is_running:
            health['overall_status'] = 'stopped'
        
        health['timestamp'] = datetime.now().isoformat()
        return health


# 全局监控管理器实例
monitoring_manager = MonitoringManager()

# 便捷函数
async def start_monitoring():
    """启动监控"""
    await monitoring_manager.start_all_monitoring()

async def stop_monitoring():
    """停止监控"""
    await monitoring_manager.stop_all_monitoring()

def get_monitoring_summary():
    """获取监控摘要"""
    return monitoring_manager.get_comprehensive_summary()


# 测试函数
async def test_monitoring_manager():
    """测试监控管理器"""
    logger.info("开始测试统一监控管理器...")
    
    try:
        # 创建监控管理器
        manager = MonitoringManager(
            performance_interval=2,
            enable_performance_monitoring=True,
            enable_business_monitoring=True,
            enable_alert_monitoring=True
        )
        
        # 启动监控
        await manager.start_all_monitoring()
        
        # 模拟业务数据
        trading_metrics = TradingMetrics(
            timestamp=datetime.now(),
            symbol="TEST",
            signal_strength=0.8,
            signal_count=10,
            trade_count=5,
            win_count=3,
            loss_count=2,
            total_return=0.05,
            max_drawdown=-0.02,
            sharpe_ratio=1.2,
            win_rate=0.6,
            avg_trade_duration=30.0
        )
        
        await manager.record_trading_metrics(trading_metrics)
        
        # 创建手动告警
        await manager.create_alert(
            source='system',
            alert_type='test_alert',
            severity='warning',
            title='测试告警',
            message='这是一个测试告警',
            symbol='TEST'
        )
        
        # 等待一段时间
        await asyncio.sleep(5)
        
        # 获取综合摘要
        summary = manager.get_comprehensive_summary()
        logger.info(f"监控摘要: {json.dumps(summary, indent=2, ensure_ascii=False, default=str)}")
        
        # 健康检查
        health = manager.health_check()
        logger.info(f"健康状态: {health}")
        
        # 停止监控
        await manager.stop_all_monitoring()
        
        logger.info("✅ 统一监控管理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 统一监控管理器测试失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

if __name__ == "__main__":
    import logging
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_monitoring_manager())
