#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的交易日志显示模块
根据测试用例日志优化交易记录显示，提供详细的计算过程
"""

import streamlit as st
import pandas as pd
import numpy as np
from typing import Dict, List, Optional

def create_enhanced_trade_log(trades_df: pd.DataFrame, config: Dict) -> pd.DataFrame:
    """
    创建增强的交易日志，包含详细的计算过程
    
    Args:
        trades_df: 原始交易记录DataFrame
        config: 回测配置参数
        
    Returns:
        增强后的交易记录DataFrame
    """
    if trades_df is None or trades_df.empty:
        return pd.DataFrame()
    
    enhanced_trades = trades_df.copy()
    
    # 获取配置参数
    commission_rate = getattr(config, 'commission_rate', 0.0003)
    min_commission = 5.0
    stamp_tax_rate = 0.001  # 印花税0.1%
    transfer_fee_rate = 0.00001  # 过户费0.001%（十万分之一）
    slippage = getattr(config, 'slippage', 0.0001)
    
    # 计算详细字段
    enhanced_fields = []
    
    for idx, row in enhanced_trades.iterrows():
        trade_type = row['type']
        quantity = row['quantity']
        price = row['price']
        
        # 基础计算
        trade_amount = quantity * price
        
        # 佣金计算
        commission = max(trade_amount * commission_rate, min_commission)
        
        # 过户费计算（买卖双向收取）
        transfer_fee = trade_amount * transfer_fee_rate
        
        # 滑点价格
        if trade_type == 'BUY':
            actual_price = price * (1 + slippage)
            stamp_tax = 0.0  # 买入不收印花税
            total_fees = commission + transfer_fee
            total_cost = trade_amount + total_fees
            net_amount = trade_amount + total_fees
        else:  # SELL
            actual_price = price * (1 - slippage)
            stamp_tax = trade_amount * stamp_tax_rate
            total_fees = commission + stamp_tax + transfer_fee
            net_amount = trade_amount - total_fees
        
        # 构建详细信息
        enhanced_info = {
            '交易时间': row['time'].strftime('%Y-%m-%d %H:%M:%S') if pd.notna(row['time']) else '',
            '交易类型': '买入' if trade_type == 'BUY' else '卖出',
            '数量': f"{quantity:,}股",
            '价格': f"{price:.4f}元",
            '交易金额': f"{trade_amount:,.2f}元",
            '交易金额计算': f"{quantity:,} × {price:.4f} = {trade_amount:,.2f}",
            '佣金': f"{commission:.2f}元",
            '佣金计算': f"max({trade_amount:,.2f} × {commission_rate:.4f}, {min_commission:.2f}) = {commission:.2f}",
            '过户费': f"{transfer_fee:.2f}元",
            '过户费计算': f"{trade_amount:,.2f} × {transfer_fee_rate:.5f} = {transfer_fee:.2f}",
        }
        
        if trade_type == 'SELL':
            enhanced_info.update({
                '印花税': f"{stamp_tax:.2f}元",
                '印花税计算': f"{trade_amount:,.2f} × {stamp_tax_rate:.4f} = {stamp_tax:.2f}",
                '总费用': f"{total_fees:.2f}元",
                '总费用计算': f"{commission:.2f} + {stamp_tax:.2f} + {transfer_fee:.2f} = {total_fees:.2f}",
                '净收入': f"{net_amount:,.2f}元",
                '净收入计算': f"{trade_amount:,.2f} - {total_fees:.2f} = {net_amount:,.2f}",
            })
            
            # 如果有盈亏信息
            if 'pnl' in row and pd.notna(row['pnl']):
                pnl = row['pnl']
                enhanced_info.update({
                    '盈亏': f"{pnl:+.2f}元",
                    '盈亏说明': f"净收入 - 成本基础 = {pnl:+.2f}元"
                })
        else:
            enhanced_info.update({
                '印花税': '0.00元',
                '总费用': f"{total_fees:.2f}元",
                '总费用计算': f"{commission:.2f} + 0.00 + {transfer_fee:.2f} = {total_fees:.2f}",
                '总成本': f"{net_amount:,.2f}元",
                '总成本计算': f"{trade_amount:,.2f} + {commission:.2f} = {net_amount:,.2f}",
                '平均成本': f"{net_amount/quantity:.4f}元",
                '平均成本计算': f"{net_amount:,.2f} ÷ {quantity:,} = {net_amount/quantity:.4f}"
            })
        
        # 添加交易原因
        if 'reason' in row and pd.notna(row['reason']):
            enhanced_info['交易原因'] = row['reason']
        
        enhanced_fields.append(enhanced_info)
    
    return pd.DataFrame(enhanced_fields)

def display_enhanced_trade_log(trades_df: pd.DataFrame, config: Dict):
    """
    显示增强的交易日志
    
    Args:
        trades_df: 交易记录DataFrame
        config: 回测配置
    """
    if trades_df is None or trades_df.empty:
        st.info("暂无交易记录")
        return
    
    st.subheader("📋 详细交易记录")
    
    # 创建增强的交易日志
    enhanced_df = create_enhanced_trade_log(trades_df, config)
    
    if enhanced_df.empty:
        st.info("无法生成详细交易记录")
        return
    
    # 添加说明
    st.info("""
    **交易记录说明**：
    - 📊 **交易金额计算**：数量 × 价格
    - 💰 **佣金计算**：max(交易金额 × 0.03%, 5元)
    - 📄 **印花税**：仅卖出时收取，交易金额 × 0.1%
    - 🏛️ **过户费**：买卖双向收取，交易金额 × 0.001%
    - 💵 **净收入**：卖出金额 - 佣金 - 印花税 - 过户费
    - 📈 **盈亏**：净收入 - 成本基础
    """)
    
    # 选择显示模式
    display_mode = st.radio(
        "选择显示模式",
        ["简洁模式", "详细模式", "计算过程"],
        horizontal=True
    )
    
    if display_mode == "简洁模式":
        # 简洁模式：只显示关键信息
        simple_columns = ['交易时间', '交易类型', '数量', '价格', '交易金额']
        if '盈亏' in enhanced_df.columns:
            simple_columns.append('盈亏')
        if '交易原因' in enhanced_df.columns:
            simple_columns.append('交易原因')
        
        display_df = enhanced_df[simple_columns]
        
    elif display_mode == "详细模式":
        # 详细模式：显示所有计算结果
        detail_columns = ['交易时间', '交易类型', '数量', '价格', '交易金额', '佣金', '过户费', '印花税']
        
        # 根据交易类型添加相应字段
        if '净收入' in enhanced_df.columns:
            detail_columns.extend(['总费用', '净收入'])
        if '总成本' in enhanced_df.columns:
            detail_columns.extend(['总成本', '平均成本'])
        if '盈亏' in enhanced_df.columns:
            detail_columns.append('盈亏')
        if '交易原因' in enhanced_df.columns:
            detail_columns.append('交易原因')
        
        # 过滤存在的列
        available_columns = [col for col in detail_columns if col in enhanced_df.columns]
        display_df = enhanced_df[available_columns]
        
    else:  # 计算过程模式
        # 计算过程模式：显示所有计算公式
        process_columns = ['交易时间', '交易类型', '交易金额计算', '佣金计算', '过户费计算']
        
        if '印花税计算' in enhanced_df.columns:
            process_columns.append('印花税计算')
        if '净收入计算' in enhanced_df.columns:
            process_columns.extend(['总费用计算', '净收入计算'])
        if '总成本计算' in enhanced_df.columns:
            process_columns.extend(['总成本计算', '平均成本计算'])
        if '盈亏说明' in enhanced_df.columns:
            process_columns.append('盈亏说明')
        if '交易原因' in enhanced_df.columns:
            process_columns.append('交易原因')
        
        # 过滤存在的列
        available_columns = [col for col in process_columns if col in enhanced_df.columns]
        display_df = enhanced_df[available_columns]
    
    # 显示数据表
    st.dataframe(
        display_df,
        width='stretch',
        hide_index=True
    )
    
    # 交易统计摘要
    st.subheader("📊 交易统计摘要")
    
    buy_trades = trades_df[trades_df['type'] == 'BUY']
    sell_trades = trades_df[trades_df['type'] == 'SELL']
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("总交易次数", len(trades_df))
        st.metric("买入次数", len(buy_trades))
    
    with col2:
        st.metric("卖出次数", len(sell_trades))
        if len(sell_trades) > 0:
            profitable_trades = len(sell_trades[sell_trades.get('pnl', 0) > 0])
            win_rate = profitable_trades / len(sell_trades) * 100
            st.metric("胜率", f"{win_rate:.1f}%")
    
    with col3:
        # 计算总佣金和印花税（与回测引擎保持完全一致）
        # 修复：确保配置参数获取正确
        if hasattr(config, 'commission_rate'):
            commission_rate = config.commission_rate
        else:
            commission_rate = getattr(config, 'commission_rate', 0.0003)
        
        if hasattr(config, 'slippage'):
            slippage = config.slippage
        else:
            slippage = getattr(config, 'slippage', 0.0001)
        
        # 🔍 详细调试信息 - 交易统计摘要计算
        print(f"🔍 DEBUG: 交易统计摘要开始计算")
        print(f"🔍 DEBUG: 配置参数 - commission_rate={commission_rate}, slippage={slippage}")
        print(f"🔍 DEBUG: 总交易记录数: {len(trades_df)}")
        
        total_commission_only = 0  # 纯佣金
        total_stamp_tax = 0        # 纯印花税
        total_transfer_fee = 0     # 纯过户费
        total_fees = 0             # 总费用（佣金+印花税+过户费）
        
        print(f"🔍 DEBUG: 开始逐笔计算手续费（交易统计摘要方法）...")
        
        for idx, row in trades_df.iterrows():
            # 🔍 关键修复：优先使用交易记录中的实际手续费
            if 'commission' in row and pd.notna(row['commission']) and row['commission'] > 0:
                # 使用交易记录中的实际手续费
                commission = float(row['commission'])
                print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} 使用记录的实际手续费: {commission:.2f}元")
            else:
                # 回退到计算方式
                trade_amount = row['quantity'] * row['price']
                commission = max(trade_amount * commission_rate, 5.0)
                print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} 计算手续费: {commission:.2f}元")
            
            # 计算过户费（买卖双向收取）
            trade_amount = row['quantity'] * row['price']
            transfer_fee = trade_amount * 0.00001  # 十万分之一
            total_transfer_fee += transfer_fee
            
            if row['type'] == 'BUY':
                total_commission_only += commission
                total_fees += commission + transfer_fee
                
                print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} {row['quantity']}股@{row['price']:.4f}, 佣金: {commission:.2f}元, 过户费: {transfer_fee:.2f}元")
                
            else:  # SELL
                # 🔍 优先使用记录的印花税
                if 'stamp_tax' in row and pd.notna(row['stamp_tax']) and row['stamp_tax'] > 0:
                    stamp_tax = float(row['stamp_tax'])
                    print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} 使用记录的实际印花税: {stamp_tax:.2f}元")
                else:
                    # 回退到计算方式
                    trade_amount = row['quantity'] * row['price']
                    stamp_tax = trade_amount * 0.001
                    print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} 计算印花税: {stamp_tax:.2f}元")
                
                total_commission_only += commission
                total_stamp_tax += stamp_tax
                total_fees += (commission + stamp_tax + transfer_fee)
                
                print(f"🔍 DEBUG: 第{idx+1}笔 {row['type']} {row['quantity']}股@{row['price']:.4f}, 佣金: {commission:.2f}元, 印花税: {stamp_tax:.2f}元, 过户费: {transfer_fee:.2f}元")
        
        st.metric("总佣金", f"{total_commission_only:.2f}元")
        st.metric("总印花税", f"{total_stamp_tax:.2f}元")
        st.metric("总过户费", f"{total_transfer_fee:.2f}元")
        
        # 🔍 详细调试信息
        print(f"🔍 DEBUG: 交易统计摘要总计:")
        print(f"🔍 DEBUG:   总佣金: {total_commission_only:.2f}元")
        print(f"🔍 DEBUG:   总印花税: {total_stamp_tax:.2f}元")
        print(f"🔍 DEBUG:   总过户费: {total_transfer_fee:.2f}元")
        print(f"🔍 DEBUG:   总费用: {total_fees:.2f}元")
    
    with col4:
        # 使用重新计算的总费用，确保与回测引擎一致
        st.metric("总费用", f"{total_fees:.2f}元")
        
        # 添加一致性验证提示
        expected_total = total_commission_only + total_stamp_tax + total_transfer_fee
        if abs(total_fees - expected_total) < 0.01:
            print(f"✅ 交易统计摘要内部计算一致")
        else:
            print(f"❌ 交易统计摘要内部计算不一致: {total_fees:.2f} vs {expected_total:.2f}")
        
        if len(sell_trades) > 0:
            total_pnl = sell_trades.get('pnl', 0).sum() if 'pnl' in sell_trades.columns else 0
            st.metric("总盈亏", f"{total_pnl:+.2f}元")

def add_fund_change_tracking(equity_df: pd.DataFrame, trades_df: pd.DataFrame, config: Dict):
    """
    添加资金变化追踪表
    
    Args:
        equity_df: 净值曲线数据
        trades_df: 交易记录
        config: 回测配置
    """
    if equity_df is None or equity_df.empty:
        return
    
    st.subheader("💰 资金变化追踪")
    
    # 创建资金变化记录
    fund_changes = []
    
    # 初始状态
    initial_capital = getattr(config, 'initial_capital', 1000000)
    fund_changes.append({
        '时间': '初始状态',
        '操作': '初始化',
        '现金余额': f"{initial_capital:,.2f}",
        '持仓市值': "0.00",
        '总权益': f"{initial_capital:,.2f}",
        '变化说明': '起始资金'
    })
    
    # 遍历交易记录，计算每次交易后的资金变化
    if trades_df is not None and not trades_df.empty:
        for idx, trade in trades_df.iterrows():
            trade_time = trade['time']
            
            # 找到对应时间点的净值数据
            equity_row = equity_df[equity_df['time'] <= trade_time].iloc[-1] if len(equity_df[equity_df['time'] <= trade_time]) > 0 else None
            
            if equity_row is not None:
                cash = equity_row.get('cash', 0)
                market_value = equity_row.get('market_value', 0)
                total_equity = equity_row.get('equity', 0)
                
                # 计算变化说明
                if trade['type'] == 'BUY':
                    trade_amount = trade['quantity'] * trade['price']
                    commission = max(trade_amount * getattr(config, 'commission_rate', 0.0003), 5.0)
                    total_cost = trade_amount + commission
                    change_desc = f"买入{trade['quantity']:,}股@{trade['price']:.4f}，成本{total_cost:,.2f}元"
                else:
                    trade_amount = trade['quantity'] * trade['price']
                    commission = max(trade_amount * getattr(config, 'commission_rate', 0.0003), 5.0)
                    stamp_tax = trade_amount * 0.001
                    net_proceeds = trade_amount - commission - stamp_tax
                    pnl = trade.get('pnl', 0)
                    change_desc = f"卖出{trade['quantity']:,}股@{trade['price']:.4f}，净收入{net_proceeds:,.2f}元，盈亏{pnl:+.2f}元"
                
                fund_changes.append({
                    '时间': trade_time.strftime('%H:%M:%S'),
                    '操作': '买入' if trade['type'] == 'BUY' else '卖出',
                    '现金余额': f"{cash:,.2f}",
                    '持仓市值': f"{market_value:,.2f}",
                    '总权益': f"{total_equity:,.2f}",
                    '变化说明': change_desc
                })
    
    # 显示资金变化表
    fund_df = pd.DataFrame(fund_changes)
    st.dataframe(
        fund_df,
        width='stretch',
        hide_index=True
    )
    
    # 资金变化图表
    if len(fund_changes) > 1:
        st.subheader("📈 资金变化趋势")
        
        import plotly.graph_objects as go
        
        # 提取数值数据
        times = [item['时间'] for item in fund_changes[1:]]  # 跳过初始状态
        cash_values = [float(item['现金余额'].replace(',', '')) for item in fund_changes[1:]]
        market_values = [float(item['持仓市值'].replace(',', '')) for item in fund_changes[1:]]
        total_values = [float(item['总权益'].replace(',', '')) for item in fund_changes[1:]]
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=times,
            y=cash_values,
            mode='lines+markers',
            name='现金余额',
            line=dict(color='blue')
        ))
        
        fig.add_trace(go.Scatter(
            x=times,
            y=market_values,
            mode='lines+markers',
            name='持仓市值',
            line=dict(color='green')
        ))
        
        fig.add_trace(go.Scatter(
            x=times,
            y=total_values,
            mode='lines+markers',
            name='总权益',
            line=dict(color='red', width=3)
        ))
        
        fig.update_layout(
            title="资金变化趋势",
            xaxis_title="时间",
            yaxis_title="金额 (元)",
            hovermode='x unified'
        )
        
        st.plotly_chart(fig, width='stretch')