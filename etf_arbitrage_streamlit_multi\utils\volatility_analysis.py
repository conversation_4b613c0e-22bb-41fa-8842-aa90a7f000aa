#!/usr/bin/env python3
"""
波动分析功能模块
从 app_enhanced_backtest_dashboard.py 提取的波动分析功能
"""

import pandas as pd
import numpy as np
import sqlite3
from typing import Dict

def analyze_tick_volatility(symbol: str, start_date: str, end_date: str, window: int = 20) -> Dict:
    """分析tick数据波动统计（基于策略窗口）"""
    try:
        conn = sqlite3.connect("ticks.db")
        
        query = """
        SELECT tick_time as time, price, volume 
        FROM ticks 
        WHERE symbol=? AND tick_time BETWEEN ? AND ?
        ORDER BY tick_time ASC
        """
        
        df = pd.read_sql_query(
            query, conn, 
            params=[symbol, start_date, end_date],
            parse_dates=['time']
        )
        conn.close()
        
        if df.empty:
            return {'error': '无数据'}
        
        df['price'] = pd.to_numeric(df['price'], errors='coerce')
        df = df.dropna(subset=['price'])
        
        if len(df) < window:
            return {'error': f'数据不足，需要至少{window}个tick'}
        
        # 基于20个tick窗口的分析（与策略一致）
        window_returns = []
        window_drawdowns = []
        
        for i in range(window, len(df)):
            # 计算20个tick窗口的收益率（与策略信号计算一致）
            window_data = df.iloc[i-window:i+1]
            p0 = float(window_data['price'].iloc[0])
            p1 = float(window_data['price'].iloc[-1])
            
            if p0 > 0:
                window_return = (p1 - p0) / p0
                window_returns.append(window_return)
            
            # 计算20个tick窗口内的最大回撤
            window_high = float(window_data['price'].max())
            window_current = float(window_data['price'].iloc[-1])
            
            if window_high > 0:
                window_drawdown = (window_current - window_high) / window_high
                window_drawdowns.append(window_drawdown)
        
        window_returns = np.array(window_returns)
        window_drawdowns = np.array(window_drawdowns)
        
        # 统计分析
        analysis = {
            '数据概况': {
                'tick总数': len(df),
                f'{window}tick窗口数': len(window_returns),
                '价格范围': f"{df['price'].min():.4f} - {df['price'].max():.4f}",
                '平均价格': f"{df['price'].mean():.4f}"
            },
            f'{window}tick窗口波动分析': {
                f'平均{window}tick收益率': f"{window_returns.mean():.6f} ({window_returns.mean()*100:.4f}%)",
                f'{window}tick收益率标准差': f"{window_returns.std():.6f} ({window_returns.std()*100:.4f}%)",
                f'最大{window}tick涨幅': f"{window_returns.max():.4f} ({window_returns.max()*100:.2f}%)",
                f'最小{window}tick跌幅': f"{window_returns.min():.4f} ({window_returns.min()*100:.2f}%)"
            },
            f'{window}tick窗口回撤分析': {
                f'最大{window}tick回撤': f"{window_drawdowns.min():.4f} ({window_drawdowns.min()*100:.2f}%)",
                f'平均{window}tick回撤': f"{window_drawdowns.mean():.4f} ({window_drawdowns.mean()*100:.2f}%)",
                f'{window}tick回撤标准差': f"{window_drawdowns.std():.4f} ({window_drawdowns.std()*100:.2f}%)"
            },
            '策略参数建议': {
                '建议买入触发跌幅': f"{np.percentile(window_returns, 5):.4f} ({np.percentile(window_returns, 5)*100:.2f}%)",
                '建议止盈目标': f"{np.percentile(window_returns, 90):.4f} ({np.percentile(window_returns, 90)*100:.2f}%)",
                '建议止损线': f"{np.percentile(window_drawdowns, 5):.4f} ({np.percentile(window_drawdowns, 5)*100:.2f}%)"
            },
            'raw_data': {
                'original_df': df,
                'window_returns': window_returns,
                'window_drawdowns': window_drawdowns
            }
        }
        
        return analysis
        
    except Exception as e:
        return {'error': f'分析失败: {str(e)}'}