# 统一参数配置指南

## 概述

`strategy_config.py` 提供了一个统一的参数配置管理系统，确保回测界面和实时监控界面使用相同的参数边界值和默认值。

## 主要特性

### 1. 统一参数管理
- 所有策略参数的边界值、默认值、步长都在一个文件中定义
- 支持参数分类：基础参数、技术指标、套利参数、风险管理参数
- 提供参数描述和帮助信息

### 2. Streamlit集成
- 直接提供Streamlit组件配置
- 自动处理参数验证
- 支持预设配置快速切换

### 3. 参数验证
- 自动验证参数值是否在有效范围内
- 提供边界值查询功能
- 支持批量参数验证

## 配置文件结构

```python
# 参数配置类
@dataclass
class ParameterConfig:
    min_value: float      # 最小值
    max_value: float      # 最大值
    default_value: float  # 默认值
    step: float          # 步长
    description: str     # 描述

# 参数分类
class StrategyConfig:
    BASIC_PARAMS = {...}      # 基础参数
    TECHNICAL_PARAMS = {...}  # 技术指标参数
    ARBITRAGE_PARAMS = {...}  # 套利参数
    RISK_PARAMS = {...}       # 风险管理参数
```

## 使用方法

### 1. 在Streamlit界面中使用

```python
from strategy_config import StrategyConfig

# 获取参数配置
config = StrategyConfig.get_streamlit_config('spread_threshold')

# 创建滑块组件
spread_threshold = st.sidebar.slider(
    "价差阈值",
    **config  # 自动展开所有配置参数
)
```

### 2. 参数验证

```python
# 验证单个参数
is_valid = StrategyConfig.validate_param_value('spread_threshold', 0.01)

# 获取参数边界
min_val, max_val = StrategyConfig.get_param_bounds('spread_threshold')

# 获取默认值
defaults = StrategyConfig.get_default_values()
```

### 3. 预设配置

```python
from strategy_config import STRATEGY_PRESETS

# 获取预设配置
conservative_params = STRATEGY_PRESETS['conservative']['params']

# 应用预设配置
for param_name, value in conservative_params.items():
    # 设置参数值
    pass
```

## 参数类别

### 基础参数 (BASIC_PARAMS)
- `initial_capital`: 初始资金
- `commission_rate`: 手续费率
- `slippage`: 滑点

### 技术指标参数 (TECHNICAL_PARAMS)
- `ma_short`: 短期均线周期
- `ma_long`: 长期均线周期
- `rsi_period`: RSI周期
- `rsi_overbought`: RSI超买阈值
- `rsi_oversold`: RSI超卖阈值
- `bb_period`: 布林带周期
- `bb_std`: 布林带标准差倍数
- `macd_fast`: MACD快线周期
- `macd_slow`: MACD慢线周期
- `macd_signal`: MACD信号线周期

### 套利参数 (ARBITRAGE_PARAMS)
- `spread_threshold`: 价差阈值
- `position_size`: 仓位大小
- `stop_loss`: 止损比例
- `take_profit`: 止盈比例
- `max_holding_days`: 最大持仓天数

### 风险管理参数 (RISK_PARAMS)
- `max_drawdown`: 最大回撤限制
- `var_confidence`: VaR置信度
- `lookback_days`: 风险计算回望天数

## 预设配置

### conservative (保守策略)
- 较大的价差阈值
- 较小的仓位大小
- 较严格的止损止盈
- 较短的持仓时间

### aggressive (激进策略)
- 较小的价差阈值
- 较大的仓位大小
- 较宽松的止损止盈
- 较长的持仓时间

### balanced (平衡策略)
- 中等的参数设置
- 平衡风险和收益

## 在现有代码中集成

### 1. 修改回测界面

```python
# 原来的硬编码参数
initial_capital = st.sidebar.number_input(
    "初始资金", 
    min_value=100000, 
    max_value=10000000, 
    value=1000000, 
    step=100000
)

# 使用统一配置
config = StrategyConfig.get_streamlit_config('initial_capital')
initial_capital = st.sidebar.number_input("初始资金", **config)
```

### 2. 修改实时监控界面

```python
# 确保使用相同的参数配置
from strategy_config import StrategyConfig

# 所有参数都使用统一配置
config = StrategyConfig.get_streamlit_config('commission_rate')
commission_rate = st.sidebar.slider("手续费率", **config)
```

## 优势

### 1. 一致性保证
- 回测和实时监控界面参数完全一致
- 避免因参数边界不同导致的问题
- 统一的参数验证逻辑

### 2. 维护便利
- 集中管理所有参数配置
- 修改参数边界只需要改一个地方
- 易于添加新参数

### 3. 用户体验
- 提供预设配置快速切换
- 参数描述和帮助信息
- 自动参数验证和错误提示

### 4. 代码质量
- 减少重复代码
- 提高代码可维护性
- 统一的配置管理模式

## 扩展指南

### 添加新参数

```python
# 在相应的参数类别中添加
NEW_PARAMS = {
    'new_parameter': ParameterConfig(
        min_value=0.0,
        max_value=1.0,
        default_value=0.5,
        step=0.1,
        description="新参数描述"
    )
}
```

### 添加新预设配置

```python
STRATEGY_PRESETS['new_strategy'] = {
    'name': '新策略',
    'params': {
        'spread_threshold': 0.02,
        'position_size': 0.6,
        # ... 其他参数
    }
}
```

## 最佳实践

1. **参数命名**: 使用清晰、一致的参数名称
2. **边界设置**: 根据实际业务需求设置合理的边界值
3. **默认值**: 选择经过验证的、相对安全的默认值
4. **描述信息**: 提供清晰的参数描述和使用建议
5. **预设配置**: 为不同风险偏好的用户提供预设配置

通过使用统一的参数配置系统，可以显著提高代码质量和用户体验，同时降低维护成本。