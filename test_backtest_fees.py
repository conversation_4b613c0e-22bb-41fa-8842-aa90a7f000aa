#!/usr/bin/env python3
"""
测试回测费用计算的正确性
"""

import sys
import logging
from backtest_enhanced import BacktestConfig, EnhancedBacktest

# 设置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
logger = logging.getLogger(__name__)

def test_fee_calculation():
    """测试费用计算逻辑"""
    print("=" * 60)
    print("回测费用计算测试")
    print("=" * 60)
    
    # 创建测试配置
    config = BacktestConfig(
        symbol="159740",
        start_date="2025-08-25",
        end_date="2025-08-27",
        initial_capital=1000000,
        commission_rate=0.0003,  # 万三佣金
        position_size=10000,
        max_position=50000
    )
    
    # 创建回测实例
    backtest = EnhancedBacktest(config)
    
    print(f"初始配置:")
    print(f"  初始资金: ¥{config.initial_capital:,.2f}")
    print(f"  佣金率: {config.commission_rate:.4f} ({config.commission_rate*10000:.1f}‰)")
    print(f"  单次买入: {config.position_size:,}股")
    print(f"  最大仓位: {config.max_position:,}股")
    
    # 模拟一次买入交易
    print(f"\n模拟买入交易:")
    current_price = 1.0
    buy_qty = 10000
    actual_price = current_price * (1 + config.slippage)
    
    # 计算预期费用
    trade_amount = buy_qty * actual_price
    expected_commission = max(trade_amount * config.commission_rate, 5.0)
    expected_transfer_fee = trade_amount * 0.00001
    
    print(f"  买入数量: {buy_qty:,}股")
    print(f"  买入价格: ¥{actual_price:.4f}")
    print(f"  交易金额: ¥{trade_amount:,.2f}")
    print(f"  预期佣金: ¥{expected_commission:.2f}")
    print(f"  预期过户费: ¥{expected_transfer_fee:.2f}")
    
    # 模拟一次卖出交易
    print(f"\n模拟卖出交易:")
    sell_qty = 10000
    sell_price = 1.05  # 5%涨幅
    actual_sell_price = sell_price * (1 - config.slippage)
    
    # 计算预期卖出费用
    sell_amount = sell_qty * actual_sell_price
    expected_sell_commission = max(sell_amount * config.commission_rate, 5.0)
    expected_stamp_tax = sell_amount * 0.001  # 印花税0.1%
    expected_sell_transfer_fee = sell_amount * 0.00001
    
    print(f"  卖出数量: {sell_qty:,}股")
    print(f"  卖出价格: ¥{actual_sell_price:.4f}")
    print(f"  交易金额: ¥{sell_amount:,.2f}")
    print(f"  预期佣金: ¥{expected_sell_commission:.2f}")
    print(f"  预期印花税: ¥{expected_stamp_tax:.2f}")
    print(f"  预期过户费: ¥{expected_sell_transfer_fee:.2f}")
    
    # 计算总预期费用
    total_expected_commission = expected_commission + expected_sell_commission
    total_expected_stamp_tax = expected_stamp_tax
    total_expected_transfer_fee = expected_transfer_fee + expected_sell_transfer_fee
    total_expected_fees = total_expected_commission + total_expected_stamp_tax + total_expected_transfer_fee
    
    print(f"\n预期费用汇总:")
    print(f"  总佣金: ¥{total_expected_commission:.2f}")
    print(f"  总印花税: ¥{total_expected_stamp_tax:.2f}")
    print(f"  总过户费: ¥{total_expected_transfer_fee:.2f}")
    print(f"  总费用: ¥{total_expected_fees:.2f}")
    
    print(f"\n费用计算验证完成！")
    print(f"请在回测面板中对比以上预期值与实际显示值。")
    
    return {
        'expected_commission': total_expected_commission,
        'expected_stamp_tax': total_expected_stamp_tax,
        'expected_transfer_fee': total_expected_transfer_fee,
        'expected_total_fees': total_expected_fees
    }

if __name__ == "__main__":
    test_fee_calculation()