"""
会话状态管理工具
用于在多页面间共享数据和状态
"""

import streamlit as st
from typing import Any, Dict, Optional
from datetime import datetime
import json

def init_session_state():
    """初始化会话状态"""
    
    # 系统基础状态
    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = True
        st.session_state.app_start_time = datetime.now()
    
    # 当前选择的标的
    if 'current_symbol' not in st.session_state:
        st.session_state.current_symbol = "159740"
    
    # 用户设置
    if 'user_settings' not in st.session_state:
        st.session_state.user_settings = {
            'theme': '浅色',
            'auto_refresh': True,
            'refresh_interval': 10,
            'notification_enabled': True,
            'sound_enabled': False
        }
    
    # 数据采集状态
    if 'data_collection_status' not in st.session_state:
        st.session_state.data_collection_status = {
            'is_running': False,
            'last_update': None,
            'total_records': 0,
            'error_count': 0
        }
    
    # 回测状态
    if 'backtest_status' not in st.session_state:
        st.session_state.backtest_status = {
            'is_running': False,
            'last_result': None,
            'current_params': None,
            'optimization_running': False
        }
    
    # 实时交易状态
    if 'trading_status' not in st.session_state:
        st.session_state.trading_status = {
            'is_running': False,
            'positions': [],
            'today_pnl': 0.0,
            'signal_count': 0,
            'last_signal': None
        }
    
    # 监控状态
    if 'monitoring_status' not in st.session_state:
        st.session_state.monitoring_status = {
            'is_monitoring': False,
            'alerts': [],
            'system_health': 'good',
            'last_check': None
        }
    
    # 页面导航历史
    if 'navigation_history' not in st.session_state:
        st.session_state.navigation_history = ['main']

def get_session_data(key: str, default: Any = None) -> Any:
    """获取会话数据"""
    keys = key.split('.')
    data = st.session_state
    
    try:
        for k in keys:
            if hasattr(data, k):
                data = getattr(data, k)
            elif isinstance(data, dict) and k in data:
                data = data[k]
            else:
                return default
        return data
    except (KeyError, AttributeError):
        return default

def set_session_data(key: str, value: Any) -> None:
    """设置会话数据"""
    keys = key.split('.')
    
    if len(keys) == 1:
        setattr(st.session_state, key, value)
    else:
        # 处理嵌套键
        current = st.session_state
        for k in keys[:-1]:
            if not hasattr(current, k):
                setattr(current, k, {})
            current = getattr(current, k)
        
        if isinstance(current, dict):
            current[keys[-1]] = value
        else:
            setattr(current, keys[-1], value)

def update_status(module: str, status_data: Dict[str, Any]) -> None:
    """更新模块状态"""
    status_key = f"{module}_status"
    current_status = getattr(st.session_state, status_key, {})
    current_status.update(status_data)
    setattr(st.session_state, status_key, current_status)

def add_to_navigation_history(page: str) -> None:
    """添加页面到导航历史"""
    if 'navigation_history' not in st.session_state:
        st.session_state.navigation_history = []
    
    # 避免重复添加相同页面
    if not st.session_state.navigation_history or st.session_state.navigation_history[-1] != page:
        st.session_state.navigation_history.append(page)
        
    # 保持历史长度不超过10
    if len(st.session_state.navigation_history) > 10:
        st.session_state.navigation_history = st.session_state.navigation_history[-10:]

def get_navigation_history() -> list:
    """获取导航历史"""
    return getattr(st.session_state, 'navigation_history', [])

def clear_session_data(keys_to_keep: Optional[list] = None) -> None:
    """清理会话数据"""
    if keys_to_keep is None:
        keys_to_keep = ['system_initialized', 'current_symbol', 'user_settings']
    
    # 保存需要保留的数据
    preserved_data = {}
    for key in keys_to_keep:
        if hasattr(st.session_state, key):
            preserved_data[key] = getattr(st.session_state, key)
    
    # 清理所有会话状态
    for key in list(st.session_state.keys()):
        delattr(st.session_state, key)
    
    # 恢复保留的数据
    for key, value in preserved_data.items():
        setattr(st.session_state, key, value)

def export_session_data() -> str:
    """导出会话数据为JSON字符串"""
    data = {}
    for key in st.session_state.keys():
        try:
            value = getattr(st.session_state, key)
            # 只导出可序列化的数据
            json.dumps(value)  # 测试是否可序列化
            data[key] = value
        except (TypeError, ValueError):
            # 跳过不可序列化的数据
            continue
    
    return json.dumps(data, default=str, indent=2)

def import_session_data(json_str: str) -> bool:
    """从JSON字符串导入会话数据"""
    try:
        data = json.loads(json_str)
        for key, value in data.items():
            setattr(st.session_state, key, value)
        return True
    except (json.JSONDecodeError, ValueError):
        return False

def is_session_expired(timeout_minutes: int = 60) -> bool:
    """检查会话是否过期"""
    if 'app_start_time' not in st.session_state:
        return True
    
    start_time = st.session_state.app_start_time
    if not isinstance(start_time, datetime):
        return True
    
    elapsed = (datetime.now() - start_time).total_seconds() / 60
    return elapsed > timeout_minutes

def refresh_session() -> None:
    """刷新会话时间"""
    st.session_state.last_activity = datetime.now()

# 装饰器：自动刷新会话
def auto_refresh_session(func):
    """自动刷新会话的装饰器"""
    def wrapper(*args, **kwargs):
        refresh_session()
        return func(*args, **kwargs)
    return wrapper