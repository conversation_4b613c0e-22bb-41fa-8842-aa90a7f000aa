#!/usr/bin/env python3
"""
增强版策略启动脚本
提供简单的命令行界面来启动不同配置的策略
"""

import argparse
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.append(str(Path(__file__).parent))

from strategy_engine_enhanced import EnhancedStrategy
from strategy_config import load_config, MARKET_CONFIGS, PresetConfigs
from strategy_monitor import StrategyMonitor


def main():
    parser = argparse.ArgumentParser(
        description="增强版策略引擎启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
配置选项:
  default     - 默认配置
  conservative - 保守型配置（低风险）
  aggressive  - 激进型配置（高风险）
  scalping    - 超短线配置（高频交易）
  bull        - 牛市配置
  bear        - 熊市配置
  sideways    - 震荡市配置

使用示例:
  python run_enhanced_strategy.py --symbol 159740 --config conservative
  python run_enhanced_strategy.py --symbol 159740 --config aggressive --capital 500000
  python run_enhanced_strategy.py --symbol 159740 --monitor-only --days 7
        """
    )
    
    parser.add_argument("--symbol", required=True, help="交易标的代码")
    parser.add_argument("--config", default="default", 
                       choices=["default", "conservative", "aggressive", "scalping", 
                               "bull", "bear", "sideways"],
                       help="策略配置方案")
    parser.add_argument("--capital", type=float, default=1_000_000.0, help="初始资金")
    parser.add_argument("--poll-sec", type=float, default=1.0, help="轮询间隔（秒）")
    parser.add_argument("--monitor-only", action="store_true", help="仅运行监控，不执行交易")
    parser.add_argument("--days", type=int, default=7, help="监控报告天数")
    parser.add_argument("--db-path", default="ticks.db", help="数据库路径")
    
    args = parser.parse_args()
    
    print(f"🚀 启动增强版策略引擎")
    print(f"📊 标的: {args.symbol}")
    print(f"⚙️  配置: {args.config}")
    print(f"💰 初始资金: {args.capital:,.2f}")
    
    if args.monitor_only:
        print(f"📈 仅运行监控模式，统计周期: {args.days}天")
        monitor = StrategyMonitor(args.db_path)
        monitor.generate_full_report(args.symbol, args.days)
        return
    
    # 加载配置
    try:
        config = load_config(args.config)
        print(f"✅ 配置加载成功")
        
        # 显示关键参数
        print(f"📋 关键参数:")
        print(f"   买入触发: {config.buy_trigger_drop:.3f}")
        print(f"   收益目标: {config.profit_target:.3f}")
        print(f"   止损线: {config.stop_loss:.3f}")
        print(f"   最大持仓时间: {config.max_hold_time}秒")
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return
    
    # 启动策略
    try:
        strategy = EnhancedStrategy(args.symbol, args.capital)
        
        # 应用配置（这里需要修改EnhancedStrategy来支持配置注入）
        print(f"🎯 策略启动中...")
        print(f"⏰ 轮询间隔: {args.poll_sec}秒")
        print(f"🛑 按 Ctrl+C 停止策略")
        print("-" * 50)
        
        strategy.run_strategy_loop(args.poll_sec)
        
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断，策略已停止")
    except Exception as e:
        print(f"❌ 策略运行失败: {e}")


def show_config_details():
    """显示所有配置的详细信息"""
    print("📋 可用配置详情:")
    print("-" * 60)
    
    configs = {
        'default': load_config('default'),
        'conservative': PresetConfigs.conservative(),
        'aggressive': PresetConfigs.aggressive(),
        'scalping': PresetConfigs.scalping()
    }
    
    for name, config in configs.items():
        print(f"\n🔧 {name.upper()} 配置:")
        print(f"   买入触发: {config.buy_trigger_drop:.3f}")
        print(f"   收益目标: {config.profit_target:.3f}")
        print(f"   止损线: {config.stop_loss:.3f}")
        print(f"   最大持仓时间: {config.max_hold_time}秒")
        print(f"   日损失限制: {config.daily_loss_limit:.3f}")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("增强版策略引擎")
        print("使用 --help 查看帮助信息")
        print("使用 --show-configs 查看所有配置详情")
        sys.exit(1)
    
    if "--show-configs" in sys.argv:
        show_config_details()
        sys.exit(0)
    
    main()