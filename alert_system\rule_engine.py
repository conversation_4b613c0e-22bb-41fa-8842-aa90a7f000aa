#!/usr/bin/env python3
"""
规则引擎
支持复杂条件表达式的编译和执行
"""

import re
import ast
import operator
import logging
from typing import Dict, Any, Union, List
import math

logger = logging.getLogger(__name__)

class RuleEngine:
    """规则引擎"""
    
    def __init__(self):
        # 支持的操作符
        self.operators = {
            ast.Add: operator.add,
            ast.Sub: operator.sub,
            ast.Mult: operator.mul,
            ast.Div: operator.truediv,
            ast.Pow: operator.pow,
            ast.Mod: operator.mod,
            ast.USub: operator.neg,
            ast.UAdd: operator.pos,
            ast.Eq: operator.eq,
            ast.NotEq: operator.ne,
            ast.Lt: operator.lt,
            ast.LtE: operator.le,
            ast.Gt: operator.gt,
            ast.GtE: operator.ge,
            ast.And: operator.and_,
            ast.Or: operator.or_,
            ast.Not: operator.not_,
            ast.In: lambda x, y: x in y,
            ast.NotIn: lambda x, y: x not in y,
        }
        
        # 支持的函数
        self.functions = {
            'abs': abs,
            'min': min,
            'max': max,
            'round': round,
            'sqrt': math.sqrt,
            'pow': pow,
            'log': math.log,
            'exp': math.exp,
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
        }
        
        # 编译后的规则缓存
        self.compiled_rules = {}
    
    def compile_rule(self, rule_expr: str) -> ast.AST:
        """
        编译规则表达式
        
        Args:
            rule_expr: 规则表达式字符串
            
        Returns:
            编译后的AST节点
        """
        if rule_expr in self.compiled_rules:
            return self.compiled_rules[rule_expr]
        
        try:
            # 预处理表达式
            processed_expr = self._preprocess_expression(rule_expr)
            
            # 解析为AST
            tree = ast.parse(processed_expr, mode='eval')
            
            # 验证AST安全性
            self._validate_ast(tree)
            
            # 缓存编译结果
            self.compiled_rules[rule_expr] = tree
            
            logger.debug(f"规则编译成功: {rule_expr}")
            return tree
            
        except Exception as e:
            logger.error(f"规则编译失败: {rule_expr}, 错误: {e}")
            raise ValueError(f"规则表达式无效: {rule_expr}")
    
    def _preprocess_expression(self, expr: str) -> str:
        """预处理表达式"""
        # 移除多余的空格
        expr = re.sub(r'\s+', ' ', expr.strip())
        
        # 替换一些常见的表达式
        replacements = {
            ' and ': ' and ',
            ' or ': ' or ',
            ' not ': ' not ',
            '>=': '>=',
            '<=': '<=',
            '!=': '!=',
            '==': '==',
        }
        
        for old, new in replacements.items():
            expr = expr.replace(old, new)
        
        return expr
    
    def _validate_ast(self, tree: ast.AST):
        """验证AST安全性，防止恶意代码执行"""
        for node in ast.walk(tree):
            # 只允许特定类型的节点
            allowed_nodes = (
                ast.Expression, ast.BoolOp, ast.BinOp, ast.UnaryOp, ast.Compare,
                ast.Constant, ast.Num, ast.Str, ast.Name, ast.Load,
                ast.And, ast.Or, ast.Not, ast.Eq, ast.NotEq, ast.Lt, ast.LtE,
                ast.Gt, ast.GtE, ast.Add, ast.Sub, ast.Mult, ast.Div, ast.Mod,
                ast.Pow, ast.USub, ast.UAdd, ast.Call, ast.In, ast.NotIn,
                ast.List, ast.Tuple
            )
            
            if not isinstance(node, allowed_nodes):
                raise ValueError(f"不允许的AST节点类型: {type(node).__name__}")
            
            # 检查函数调用
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    func_name = node.func.id
                    if func_name not in self.functions:
                        raise ValueError(f"不允许的函数调用: {func_name}")
                else:
                    raise ValueError("不允许的函数调用形式")
    
    def evaluate(self, rule_expr: str, context: Dict[str, Any]) -> bool:
        """
        评估规则表达式
        
        Args:
            rule_expr: 规则表达式
            context: 上下文变量字典
            
        Returns:
            评估结果（布尔值）
        """
        try:
            # 编译规则
            tree = self.compile_rule(rule_expr)
            
            # 执行评估
            result = self._eval_ast(tree.body, context)
            
            # 确保返回布尔值
            return bool(result)
            
        except Exception as e:
            logger.error(f"规则评估失败: {rule_expr}, 错误: {e}")
            return False
    
    def _eval_ast(self, node: ast.AST, context: Dict[str, Any]) -> Any:
        """递归评估AST节点"""
        if isinstance(node, ast.Constant):
            return node.value
        elif isinstance(node, ast.Num):  # Python < 3.8 兼容性
            return node.n
        elif isinstance(node, ast.Str):  # Python < 3.8 兼容性
            return node.s
        elif isinstance(node, ast.Name):
            var_name = node.id
            if var_name in context:
                return context[var_name]
            else:
                raise NameError(f"未定义的变量: {var_name}")
        elif isinstance(node, ast.BinOp):
            left = self._eval_ast(node.left, context)
            right = self._eval_ast(node.right, context)
            op = self.operators[type(node.op)]
            return op(left, right)
        elif isinstance(node, ast.UnaryOp):
            operand = self._eval_ast(node.operand, context)
            op = self.operators[type(node.op)]
            return op(operand)
        elif isinstance(node, ast.Compare):
            left = self._eval_ast(node.left, context)
            for op, comparator in zip(node.ops, node.comparators):
                right = self._eval_ast(comparator, context)
                op_func = self.operators[type(op)]
                if not op_func(left, right):
                    return False
                left = right
            return True
        elif isinstance(node, ast.BoolOp):
            values = [self._eval_ast(value, context) for value in node.values]
            if isinstance(node.op, ast.And):
                return all(values)
            elif isinstance(node.op, ast.Or):
                return any(values)
        elif isinstance(node, ast.Call):
            func_name = node.func.id
            if func_name in self.functions:
                args = [self._eval_ast(arg, context) for arg in node.args]
                return self.functions[func_name](*args)
            else:
                raise ValueError(f"未知函数: {func_name}")
        elif isinstance(node, ast.List):
            return [self._eval_ast(item, context) for item in node.elts]
        elif isinstance(node, ast.Tuple):
            return tuple(self._eval_ast(item, context) for item in node.elts)
        else:
            raise ValueError(f"不支持的AST节点类型: {type(node).__name__}")
    
    def validate_rule(self, rule_expr: str, sample_context: Dict[str, Any] = None) -> Dict:
        """
        验证规则表达式的有效性
        
        Args:
            rule_expr: 规则表达式
            sample_context: 示例上下文（可选）
            
        Returns:
            验证结果字典
        """
        result = {
            'valid': False,
            'error': None,
            'variables_used': [],
            'test_result': None
        }
        
        try:
            # 编译规则
            tree = self.compile_rule(rule_expr)
            
            # 提取使用的变量
            variables = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                    variables.add(node.id)
            
            result['variables_used'] = list(variables)
            result['valid'] = True
            
            # 如果提供了示例上下文，进行测试评估
            if sample_context:
                test_result = self.evaluate(rule_expr, sample_context)
                result['test_result'] = test_result
            
        except Exception as e:
            result['error'] = str(e)
        
        return result
    
    def get_supported_operators(self) -> Dict[str, str]:
        """获取支持的操作符列表"""
        return {
            '算术运算': '+ - * / % **',
            '比较运算': '== != < <= > >=',
            '逻辑运算': 'and or not',
            '成员运算': 'in not in',
            '函数调用': 'abs() min() max() round() sqrt() pow() log() exp()'
        }
    
    def get_example_rules(self) -> List[str]:
        """获取示例规则"""
        return [
            "signal <= -0.006",
            "signal <= -0.008 and volume > 10000",
            "position_profit_rate >= 0.005",
            "position_profit_rate <= -0.015",
            "abs(signal) > 0.01 or volume > 50000",
            "price > 0.75 and price < 0.80",
            "hour >= 9 and hour <= 15",
            "sqrt(abs(signal)) > 0.08"
        ]

# 测试函数
def test_rule_engine():
    """测试规则引擎"""
    logger.info("开始测试规则引擎...")
    
    engine = RuleEngine()
    
    # 测试用例
    test_cases = [
        {
            'rule': 'signal <= -0.006',
            'context': {'signal': -0.007},
            'expected': True
        },
        {
            'rule': 'signal <= -0.006 and volume > 10000',
            'context': {'signal': -0.007, 'volume': 15000},
            'expected': True
        },
        {
            'rule': 'position_profit_rate >= 0.005',
            'context': {'position_profit_rate': 0.008},
            'expected': True
        },
        {
            'rule': 'abs(signal) > 0.01',
            'context': {'signal': -0.012},
            'expected': True
        },
        {
            'rule': 'price > 0.75 and price < 0.80',
            'context': {'price': 0.77},
            'expected': True
        },
        {
            'rule': 'hour >= 9 and hour <= 15',
            'context': {'hour': 14},
            'expected': True
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            result = engine.evaluate(test_case['rule'], test_case['context'])
            if result == test_case['expected']:
                logger.info(f"✅ 测试 {i} 通过: {test_case['rule']}")
                success_count += 1
            else:
                logger.error(f"❌ 测试 {i} 失败: {test_case['rule']}, 期望: {test_case['expected']}, 实际: {result}")
        except Exception as e:
            logger.error(f"❌ 测试 {i} 异常: {test_case['rule']}, 错误: {e}")
    
    # 测试规则验证
    logger.info("测试规则验证功能...")
    validation_result = engine.validate_rule(
        "signal <= -0.006 and volume > 10000",
        {'signal': -0.007, 'volume': 15000}
    )
    
    if validation_result['valid']:
        logger.info(f"✅ 规则验证通过，使用变量: {validation_result['variables_used']}")
        logger.info(f"测试结果: {validation_result['test_result']}")
    else:
        logger.error(f"❌ 规则验证失败: {validation_result['error']}")
    
    # 测试错误处理
    logger.info("测试错误处理...")
    try:
        engine.evaluate("invalid_function()", {})
        logger.error("❌ 错误处理测试失败：应该抛出异常")
    except:
        logger.info("✅ 错误处理测试通过")
        success_count += 1
    
    total_tests = len(test_cases) + 2  # 包括验证和错误处理测试
    logger.info(f"规则引擎测试完成: {success_count}/{total_tests} 通过")
    
    return success_count == total_tests

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 测试规则引擎...")
    print("=" * 50)
    
    success = test_rule_engine()
    
    if success:
        print("\n🎉 规则引擎测试成功！")
        print("✅ 表达式编译功能正常")
        print("✅ 规则评估功能正常")
        print("✅ 安全验证功能正常")
        print("✅ 错误处理功能正常")
    else:
        print("\n❌ 规则引擎测试失败")
    
    print("=" * 50)
