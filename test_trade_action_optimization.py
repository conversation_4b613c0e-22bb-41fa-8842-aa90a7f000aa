#!/usr/bin/env python3
"""
交易动作优化测试
测试新的交易动作处理逻辑
"""

import sys
import logging
from pathlib import Path
from datetime import datetime
import pandas as pd

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_trade_action_constants():
    """测试交易动作常量和方法"""
    print("🔍 测试交易动作常量...")

    try:
        # 直接导入模块
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "trade_module",
            "etf_arbitrage_streamlit_multi/pages/3_🚀_实时交易_增强版.py"
        )
        trade_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(trade_module)

        TradeAction = trade_module.TradeAction
        
        # 测试动作分类
        assert TradeAction.is_buy_action(TradeAction.BUY) == True
        assert TradeAction.is_sell_action(TradeAction.SELL) == True
        assert TradeAction.is_sell_action(TradeAction.STOP_LOSS) == True
        assert TradeAction.is_valid_action(TradeAction.BUY) == True
        assert TradeAction.is_valid_action("INVALID") == False
        
        # 测试动作标准化
        assert TradeAction.normalize_action("buy") == TradeAction.BUY
        assert TradeAction.normalize_action("B") == TradeAction.BUY
        assert TradeAction.normalize_action("STOP") == TradeAction.STOP_LOSS
        assert TradeAction.normalize_action("") == TradeAction.HOLD
        assert TradeAction.normalize_action("INVALID") == TradeAction.HOLD
        
        print("✅ 交易动作常量测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易动作常量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_action_display():
    """测试交易动作显示"""
    print("🔍 测试交易动作显示...")
    
    try:
        from etf_arbitrage_streamlit_multi.pages.实时交易_增强版 import TradeAction, TradeActionDisplay
        
        # 测试显示信息获取
        buy_info = TradeActionDisplay.get_display_info(TradeAction.BUY)
        assert buy_info['text'] == "买入"
        assert buy_info['icon'] == "🟢"
        assert 'color' in buy_info
        
        # 测试格式化显示
        buy_display = TradeActionDisplay.format_action_display(TradeAction.BUY, include_icon=True)
        assert "🟢" in buy_display and "买入" in buy_display
        
        buy_display_no_icon = TradeActionDisplay.format_action_display(TradeAction.BUY, include_icon=False)
        assert "🟢" not in buy_display_no_icon and "买入" in buy_display_no_icon
        
        # 测试彩色显示
        colored_display = TradeActionDisplay.format_action_display(
            TradeAction.STOP_LOSS, 
            include_icon=True, 
            include_color=True
        )
        assert "🛑" in colored_display and "止损" in colored_display
        
        print("✅ 交易动作显示测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易动作显示测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_processor():
    """测试交易处理器"""
    print("🔍 测试交易处理器...")
    
    try:
        from etf_arbitrage_streamlit_multi.pages.实时交易_增强版 import TradeProcessor, TradeAction
        
        # 测试有效交易记录
        valid_trade = {
            'timestamp': datetime.now().isoformat(),
            'symbol': '159740',
            'action': 'BUY',
            'quantity': 1000,
            'price': 1.234,
            'amount': 1234.0,
            'commission': 5.0,
            'stamp_tax': 0.0,
            'transfer_fee': 0.6
        }
        
        is_valid, msg = TradeProcessor.validate_trade_record(valid_trade)
        assert is_valid == True, f"有效交易验证失败: {msg}"
        
        # 测试无效交易记录
        invalid_trade = {
            'timestamp': datetime.now().isoformat(),
            'symbol': '159740',
            'action': 'INVALID_ACTION',
            'quantity': -1000,  # 负数量
            'price': 1.234,
            'amount': 1234.0
        }
        
        is_valid, msg = TradeProcessor.validate_trade_record(invalid_trade)
        assert is_valid == False, "无效交易应该验证失败"
        
        # 测试交易指标计算
        metrics = TradeProcessor.calculate_trade_metrics(valid_trade)
        
        assert metrics['action_normalized'] == TradeAction.BUY
        assert metrics['is_buy'] == True
        assert metrics['is_sell'] == False
        assert metrics['total_fees'] == 5.6  # 5.0 + 0.0 + 0.6
        assert metrics['cash_impact'] < 0  # 买入应该是负现金流
        
        # 测试卖出交易
        sell_trade = valid_trade.copy()
        sell_trade['action'] = 'SELL'
        sell_trade['pnl'] = 100.0
        
        sell_metrics = TradeProcessor.calculate_trade_metrics(sell_trade)
        assert sell_metrics['action_normalized'] == TradeAction.SELL
        assert sell_metrics['is_sell'] == True
        assert sell_metrics['cash_impact'] > 0  # 卖出应该是正现金流
        
        print("✅ 交易处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_record_table():
    """测试交易记录表格创建"""
    print("🔍 测试交易记录表格创建...")
    
    try:
        from etf_arbitrage_streamlit_multi.pages.实时交易_增强版 import create_trade_record_table
        
        # 创建测试交易数据
        test_trades = [
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': '159740',
                'action': 'BUY',
                'quantity': 1000,
                'price': 1.234,
                'amount': 1234.0,
                'commission': 5.0,
                'stamp_tax': 0.0,
                'transfer_fee': 0.6,
                'reason': '策略买入'
            },
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': '159740',
                'action': 'SELL',
                'quantity': 1000,
                'price': 1.250,
                'amount': 1250.0,
                'commission': 5.0,
                'stamp_tax': 1.25,
                'transfer_fee': 0.6,
                'pnl': 9.55,  # 1250 - 1234 - 5 - 1.25 - 0.6 - 5 (买入手续费)
                'reason': '止盈卖出'
            },
            {
                'timestamp': datetime.now().isoformat(),
                'symbol': '159740',
                'action': 'STOP_LOSS',
                'quantity': 500,
                'price': 1.200,
                'amount': 600.0,
                'commission': 5.0,
                'stamp_tax': 0.6,
                'transfer_fee': 0.3,
                'pnl': -40.9,  # 止损亏损
                'reason': '止损卖出'
            }
        ]
        
        # 创建表格
        df = create_trade_record_table(test_trades)
        
        assert not df.empty, "表格不应为空"
        assert len(df) == 3, f"应该有3行数据，实际有{len(df)}行"
        
        # 检查列是否存在
        expected_columns = ['时间', '代码', '操作', '数量', '价格', '金额', '净金额', 
                          '手续费', '印花税', '过户费', '总费用', '费用率', '盈亏', '现金影响', '原因']
        for col in expected_columns:
            assert col in df.columns, f"缺少列: {col}"
        
        # 检查操作列是否包含图标
        operations = df['操作'].tolist()
        assert any("🟢" in op for op in operations), "买入操作应该包含绿色图标"
        assert any("🔴" in op for op in operations), "卖出操作应该包含红色图标"
        assert any("🛑" in op for op in operations), "止损操作应该包含止损图标"
        
        print("✅ 交易记录表格创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易记录表格创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_trade_pattern_analysis():
    """测试交易模式分析"""
    print("🔍 测试交易模式分析...")
    
    try:
        from etf_arbitrage_streamlit_multi.pages.实时交易_增强版 import analyze_trade_patterns
        
        # 创建测试数据
        test_trades = []
        for i in range(10):
            # 买入交易
            test_trades.append({
                'timestamp': datetime.now().isoformat(),
                'symbol': '159740',
                'action': 'BUY',
                'quantity': 1000,
                'price': 1.234,
                'amount': 1234.0,
                'commission': 5.0
            })
            
            # 卖出交易（一半盈利，一半亏损）
            pnl = 50.0 if i % 2 == 0 else -30.0
            test_trades.append({
                'timestamp': datetime.now().isoformat(),
                'symbol': '159740',
                'action': 'SELL',
                'quantity': 1000,
                'price': 1.250,
                'amount': 1250.0,
                'commission': 5.0,
                'pnl': pnl
            })
        
        # 分析交易模式
        analysis = analyze_trade_patterns(test_trades)
        
        assert analysis['total_trades'] == 20, f"总交易数应为20，实际为{analysis['total_trades']}"
        assert 'BUY' in analysis['action_distribution'], "应该包含买入动作统计"
        assert 'SELL' in analysis['action_distribution'], "应该包含卖出动作统计"
        assert analysis['action_distribution']['BUY'] == 10, "买入交易应为10笔"
        assert analysis['action_distribution']['SELL'] == 10, "卖出交易应为10笔"
        
        # 检查成功率计算
        assert 'SELL' in analysis['success_rates'], "应该包含卖出成功率"
        sell_success_rate = analysis['success_rates']['SELL']
        assert 0.4 <= sell_success_rate <= 0.6, f"卖出成功率应约为50%，实际为{sell_success_rate:.2%}"
        
        print("✅ 交易模式分析测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 交易模式分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始交易动作优化测试")
    print("=" * 60)
    
    tests = [
        ("交易动作常量", test_trade_action_constants),
        ("交易动作显示", test_trade_action_display),
        ("交易处理器", test_trade_processor),
        ("交易记录表格", test_trade_record_table),
        ("交易模式分析", test_trade_pattern_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 测试: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有交易动作优化测试通过！")
        print("✅ 交易动作处理逻辑工作正常")
        print("\n💡 优化效果:")
        print("  - 支持更多交易动作类型（买入、卖出、部分卖出、止损、止盈、强制平仓）")
        print("  - 增强的数据验证和错误处理")
        print("  - 改进的显示格式和用户体验")
        print("  - 更准确的资金计算和统计分析")
        return True
    else:
        print("\n⚠️ 部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
