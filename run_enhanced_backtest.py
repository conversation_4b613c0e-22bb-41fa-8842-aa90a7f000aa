#!/usr/bin/env python3
"""
增强版策略回测启动脚本
"""

from backtest_enhanced import BacktestConfig, EnhancedBacktest
import logging

def run_backtest_with_config(config_name: str = "default"):
    """使用指定配置运行回测"""
    
    logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
    
    # 预设配置
    configs = {
        "default": BacktestConfig(
            symbol="159740",
            start_date="2025-08-25",
            end_date="2025-08-27",
            initial_capital=1_000_000.0
        ),
        "conservative": BacktestConfig(
            symbol="159740",
            start_date="2025-08-25", 
            end_date="2025-08-27",
            initial_capital=1_000_000.0,
            buy_trigger_drop=-0.004,
            stop_loss=-0.015,
            max_hold_time=1800
        ),
        "aggressive": BacktestConfig(
            symbol="159740",
            start_date="2025-08-25",
            end_date="2025-08-27", 
            initial_capital=1_000_000.0,
            buy_trigger_drop=-0.008,
            stop_loss=-0.025,
            max_hold_time=7200
        )
    }
    
    if config_name not in configs:
        print(f"未知配置: {config_name}, 使用默认配置")
        config_name = "default"
    
    config = configs[config_name]
    
    print(f"=== 使用 {config_name} 配置运行增强版回测 ===")
    print(f"标的: {config.symbol}")
    print(f"时间范围: {config.start_date} 到 {config.end_date}")
    
    # 运行回测
    backtest = EnhancedBacktest(config)
    results = backtest.run_backtest()
    
    if 'error' in results:
        print(f"回测失败: {results['error']}")
        return None
    
    # 显示结果
    print("\n=== 回测结果 ===")
    for key, value in results['performance'].items():
        print(f"  {key}: {value}")
    
    # 绘制图表
    try:
        backtest.plot_results(results)
    except Exception as e:
        print(f"绘图失败: {e}")
    
    return results

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        run_backtest_with_config(sys.argv[1])
    else:
        print("使用默认配置运行...")
        run_backtest_with_config("default")