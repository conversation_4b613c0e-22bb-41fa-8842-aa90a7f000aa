#!/usr/bin/env python3
"""
测试运行时错误修复
验证策略信号生成和TradeSignal属性问题是否已解决
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

def test_runtime_fixes():
    """测试运行时错误修复"""
    print("🔧 运行时错误修复测试")
    print("=" * 50)
    
    try:
        # 1. 测试策略信号生成方法参数
        print("📋 测试1: 策略信号生成方法参数")
        try:
            from etf_arbitrage_streamlit_multi.utils.trading_strategies import get_strategy_manager
            import pandas as pd
            import numpy as np
            from datetime import datetime
            
            strategy_manager = get_strategy_manager()
            current_strategy = strategy_manager.get_current_strategy()
            
            # 创建测试数据
            test_data = pd.DataFrame({
                'timestamp': pd.date_range('2025-09-24 09:30:00', periods=20, freq='1min'),
                'price': np.random.normal(0.75, 0.01, 20),
                'volume': np.random.randint(1000, 5000, 20)
            })
            
            # 测试信号生成（只传入一个参数）
            signal_result = current_strategy.generate_signal(test_data)
            print(f"✅ 策略信号生成成功")
            print(f"   信号类型: {signal_result.signal}")
            print(f"   置信度: {signal_result.confidence}")
            print(f"   原因: {signal_result.reason}")
            
        except Exception as e:
            print(f"❌ 策略信号生成测试失败: {e}")
            return False
        
        # 2. 测试TradeSignal属性
        print("\n📋 测试2: TradeSignal属性")
        try:
            from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import TradeSignal
            from datetime import datetime
            
            # 创建测试信号
            test_signal = TradeSignal(
                timestamp=datetime.now(),
                symbol='159740',
                signal_type='BUY',
                price=0.75,
                confidence=0.8,
                reason='测试信号',
                quantity=100
            )
            
            # 测试属性访问
            print(f"✅ TradeSignal创建成功")
            print(f"   signal_type: {test_signal.signal_type}")
            print(f"   symbol: {test_signal.symbol}")
            print(f"   price: {test_signal.price}")
            
            # 验证没有action属性（应该使用signal_type）
            if hasattr(test_signal, 'action'):
                print("⚠️ TradeSignal仍有action属性，可能存在冗余")
            else:
                print("✅ TradeSignal正确使用signal_type属性")
                
        except Exception as e:
            print(f"❌ TradeSignal属性测试失败: {e}")
            return False
        
        # 3. 测试TradeRecord创建
        print("\n📋 测试3: TradeRecord创建")
        try:
            from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import TradeRecord
            from datetime import datetime
            
            # 创建测试交易记录
            test_record = TradeRecord(
                id='test_001',
                timestamp=datetime.now(),
                symbol='159740',
                action='BUY',
                quantity=100,
                price=0.75,
                amount=75.0,
                commission=0.0225
            )
            
            print(f"✅ TradeRecord创建成功")
            print(f"   action: {test_record.action}")
            print(f"   symbol: {test_record.symbol}")
            print(f"   amount: {test_record.amount}")
            
        except Exception as e:
            print(f"❌ TradeRecord创建测试失败: {e}")
            return False
        
        # 4. 测试A股规则验证返回值
        print("\n📋 测试4: A股规则验证返回值")
        try:
            from etf_arbitrage_streamlit_multi.utils.trading_rules_manager import get_trading_rules_manager
            from datetime import datetime
            
            rules_manager = get_trading_rules_manager()
            
            # 测试合规性验证
            compliance_result = rules_manager.validate_trade_compliance(
                symbol='159740',
                trade_type='BUY',
                quantity=150,
                current_price=0.75,
                prev_close=0.74,
                current_time=datetime.now()
            )
            
            print(f"✅ A股规则验证成功")
            print(f"   返回类型: {type(compliance_result)}")
            print(f"   is_compliant: {compliance_result['is_compliant']}")
            print(f"   normalized_quantity: {compliance_result['normalized_quantity']}")
            
            if 'violations' in compliance_result:
                print(f"   violations: {compliance_result['violations']}")
            if 'warnings' in compliance_result:
                print(f"   warnings: {compliance_result['warnings']}")
                
        except Exception as e:
            print(f"❌ A股规则验证测试失败: {e}")
            return False
        
        # 5. 测试实时交易器初始化
        print("\n📋 测试5: 实时交易器初始化")
        try:
            from etf_arbitrage_streamlit_multi.utils.enhanced_real_time_trader import enhanced_trader
            
            # 检查策略管理器是否正确初始化
            if enhanced_trader.strategy_manager:
                print("✅ 实时交易器策略管理器初始化成功")
                current_strategy = enhanced_trader.strategy_manager.get_current_strategy()
                if current_strategy:
                    print(f"   当前策略: {current_strategy.name}")
            else:
                print("⚠️ 实时交易器策略管理器未初始化")
            
            # 检查A股规则管理器
            if enhanced_trader.trading_rules:
                print("✅ A股交易规则管理器初始化成功")
            else:
                print("⚠️ A股交易规则管理器未初始化")
                
        except Exception as e:
            print(f"❌ 实时交易器初始化测试失败: {e}")
            return False
        
        print("\n" + "=" * 50)
        print("🎉 所有运行时错误修复测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = test_runtime_fixes()
    if success:
        print("\n✅ 修复验证成功：运行时错误已解决")
        print("现在可以重新启动Streamlit应用测试")
    else:
        print("\n❌ 修复验证失败：仍存在运行时问题")
