#!/usr/bin/env python3
"""
并发处理优化器
实现高效的并发任务处理和资源管理
"""

import asyncio
import logging
from typing import Dict, List, Optional, Callable, Any, Union
from datetime import datetime
import time
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing
import threading
from functools import wraps
import weakref

logger = logging.getLogger(__name__)

@dataclass
class TaskResult:
    """任务结果"""
    task_id: str
    success: bool
    result: Any = None
    error: str = None
    start_time: datetime = None
    end_time: datetime = None
    duration: float = 0.0

class ConcurrentProcessor:
    """并发处理器"""
    
    def __init__(self, 
                 max_workers: int = None,
                 use_processes: bool = False,
                 enable_monitoring: bool = True):
        """
        初始化并发处理器
        
        Args:
            max_workers: 最大工作线程/进程数
            use_processes: 是否使用进程池
            enable_monitoring: 是否启用监控
        """
        self.max_workers = max_workers or min(32, (multiprocessing.cpu_count() or 1) + 4)
        self.use_processes = use_processes
        self.enable_monitoring = enable_monitoring
        
        # 执行器
        if use_processes:
            self.executor = ProcessPoolExecutor(max_workers=self.max_workers)
        else:
            self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 任务管理
        self.active_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, TaskResult] = {}
        
        # 统计信息
        self.stats = {
            'total_tasks': 0,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'active_tasks': 0,
            'total_duration': 0.0,
            'avg_duration': 0.0
        }
        
        # 监控
        self.monitoring_task = None
        self.enable_monitoring = enable_monitoring
        
        logger.info(f"并发处理器初始化: {'进程池' if use_processes else '线程池'}, "
                   f"最大工作数: {self.max_workers}")

    def start_monitoring(self):
        """启动监控"""
        if self.enable_monitoring and not self.monitoring_task:
            try:
                self.monitoring_task = asyncio.create_task(self._monitor_tasks())
            except RuntimeError:
                # 没有运行的事件循环，稍后启动
                pass
    
    async def submit_async_task(self, 
                               task_func: Callable,
                               *args,
                               task_id: str = None,
                               timeout: float = None,
                               **kwargs) -> str:
        """
        提交异步任务
        
        Args:
            task_func: 任务函数
            *args: 位置参数
            task_id: 任务ID
            timeout: 超时时间
            **kwargs: 关键字参数
            
        Returns:
            任务ID
        """
        if task_id is None:
            task_id = f"task_{int(time.time() * 1000000)}"
        
        # 创建任务结果
        result = TaskResult(
            task_id=task_id,
            success=False,
            start_time=datetime.now()
        )
        self.task_results[task_id] = result
        
        # 创建异步任务
        task = asyncio.create_task(
            self._execute_async_task(task_func, result, timeout, *args, **kwargs)
        )
        
        self.active_tasks[task_id] = task
        self.stats['total_tasks'] += 1
        self.stats['active_tasks'] += 1
        
        logger.debug(f"提交异步任务: {task_id}")
        return task_id
    
    async def submit_sync_task(self,
                              task_func: Callable,
                              *args,
                              task_id: str = None,
                              timeout: float = None,
                              **kwargs) -> str:
        """
        提交同步任务（在执行器中运行）
        
        Args:
            task_func: 同步任务函数
            *args: 位置参数
            task_id: 任务ID
            timeout: 超时时间
            **kwargs: 关键字参数
            
        Returns:
            任务ID
        """
        if task_id is None:
            task_id = f"sync_task_{int(time.time() * 1000000)}"
        
        # 创建任务结果
        result = TaskResult(
            task_id=task_id,
            success=False,
            start_time=datetime.now()
        )
        self.task_results[task_id] = result
        
        # 在执行器中运行同步任务
        loop = asyncio.get_event_loop()
        task = asyncio.create_task(
            self._execute_sync_task(loop, task_func, result, timeout, *args, **kwargs)
        )
        
        self.active_tasks[task_id] = task
        self.stats['total_tasks'] += 1
        self.stats['active_tasks'] += 1
        
        logger.debug(f"提交同步任务: {task_id}")
        return task_id
    
    async def submit_batch_tasks(self,
                                tasks: List[Dict[str, Any]],
                                max_concurrent: int = None) -> List[str]:
        """
        批量提交任务
        
        Args:
            tasks: 任务列表，每个任务包含func, args, kwargs等
            max_concurrent: 最大并发数
            
        Returns:
            任务ID列表
        """
        max_concurrent = max_concurrent or self.max_workers
        semaphore = asyncio.Semaphore(max_concurrent)
        
        task_ids = []
        
        async def submit_single_task(task_info):
            async with semaphore:
                task_func = task_info['func']
                args = task_info.get('args', ())
                kwargs = task_info.get('kwargs', {})
                task_id = task_info.get('task_id')
                timeout = task_info.get('timeout')
                is_async = task_info.get('is_async', True)
                
                if is_async:
                    return await self.submit_async_task(task_func, *args, task_id=task_id, timeout=timeout, **kwargs)
                else:
                    return await self.submit_sync_task(task_func, *args, task_id=task_id, timeout=timeout, **kwargs)
        
        # 并发提交所有任务
        submitted_ids = await asyncio.gather(*[submit_single_task(task) for task in tasks])
        task_ids.extend(submitted_ids)
        
        logger.info(f"批量提交 {len(task_ids)} 个任务")
        return task_ids
    
    async def wait_for_task(self, task_id: str, timeout: float = None) -> TaskResult:
        """
        等待任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            任务结果
        """
        if task_id not in self.active_tasks:
            # 检查是否已完成
            if task_id in self.task_results:
                return self.task_results[task_id]
            else:
                raise ValueError(f"任务不存在: {task_id}")
        
        task = self.active_tasks[task_id]
        
        try:
            if timeout:
                await asyncio.wait_for(task, timeout=timeout)
            else:
                await task
        except asyncio.TimeoutError:
            logger.warning(f"等待任务超时: {task_id}")
            task.cancel()
        
        return self.task_results.get(task_id, TaskResult(task_id, False, error="任务不存在"))
    
    async def wait_for_all_tasks(self, task_ids: List[str], timeout: float = None) -> List[TaskResult]:
        """
        等待所有任务完成
        
        Args:
            task_ids: 任务ID列表
            timeout: 超时时间
            
        Returns:
            任务结果列表
        """
        tasks = []
        for task_id in task_ids:
            if task_id in self.active_tasks:
                tasks.append(self.wait_for_task(task_id, timeout))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return [r if isinstance(r, TaskResult) else TaskResult(task_ids[i], False, error=str(r)) 
                   for i, r in enumerate(results)]
        else:
            return [self.task_results.get(task_id, TaskResult(task_id, False, error="任务不存在")) 
                   for task_id in task_ids]
    
    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            是否成功取消
        """
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            task.cancel()
            
            # 更新结果
            if task_id in self.task_results:
                result = self.task_results[task_id]
                result.success = False
                result.error = "任务被取消"
                result.end_time = datetime.now()
                result.duration = (result.end_time - result.start_time).total_seconds()
            
            logger.info(f"任务已取消: {task_id}")
            return True
        
        return False
    
    async def _execute_async_task(self, 
                                 task_func: Callable,
                                 result: TaskResult,
                                 timeout: float,
                                 *args, **kwargs):
        """执行异步任务"""
        try:
            if timeout:
                task_result = await asyncio.wait_for(task_func(*args, **kwargs), timeout=timeout)
            else:
                task_result = await task_func(*args, **kwargs)
            
            result.success = True
            result.result = task_result
            
        except asyncio.TimeoutError:
            result.error = "任务超时"
            logger.warning(f"异步任务超时: {result.task_id}")
        except Exception as e:
            result.error = str(e)
            logger.error(f"异步任务失败: {result.task_id} - {e}")
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            
            # 更新统计
            self._update_stats(result)
            
            # 清理活跃任务
            if result.task_id in self.active_tasks:
                del self.active_tasks[result.task_id]
    
    async def _execute_sync_task(self,
                                loop: asyncio.AbstractEventLoop,
                                task_func: Callable,
                                result: TaskResult,
                                timeout: float,
                                *args, **kwargs):
        """执行同步任务"""
        try:
            if timeout:
                task_result = await asyncio.wait_for(
                    loop.run_in_executor(self.executor, task_func, *args, **kwargs),
                    timeout=timeout
                )
            else:
                task_result = await loop.run_in_executor(self.executor, task_func, *args, **kwargs)
            
            result.success = True
            result.result = task_result
            
        except asyncio.TimeoutError:
            result.error = "任务超时"
            logger.warning(f"同步任务超时: {result.task_id}")
        except Exception as e:
            result.error = str(e)
            logger.error(f"同步任务失败: {result.task_id} - {e}")
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            
            # 更新统计
            self._update_stats(result)
            
            # 清理活跃任务
            if result.task_id in self.active_tasks:
                del self.active_tasks[result.task_id]
    
    def _update_stats(self, result: TaskResult):
        """更新统计信息"""
        self.stats['active_tasks'] -= 1
        
        if result.success:
            self.stats['completed_tasks'] += 1
        else:
            self.stats['failed_tasks'] += 1
        
        self.stats['total_duration'] += result.duration
        
        if self.stats['completed_tasks'] > 0:
            self.stats['avg_duration'] = self.stats['total_duration'] / self.stats['completed_tasks']
    
    async def _monitor_tasks(self):
        """监控任务状态"""
        while True:
            try:
                await asyncio.sleep(10)  # 每10秒监控一次
                
                if self.active_tasks:
                    logger.info(f"活跃任务数: {len(self.active_tasks)}")
                    
                    # 检查长时间运行的任务
                    current_time = datetime.now()
                    for task_id, result in self.task_results.items():
                        if task_id in self.active_tasks and result.start_time:
                            duration = (current_time - result.start_time).total_seconds()
                            if duration > 300:  # 5分钟
                                logger.warning(f"长时间运行的任务: {task_id} ({duration:.1f}秒)")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"任务监控异常: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'executor_type': 'ProcessPool' if self.use_processes else 'ThreadPool',
            'max_workers': self.max_workers,
            'success_rate': (self.stats['completed_tasks'] / self.stats['total_tasks'] 
                           if self.stats['total_tasks'] > 0 else 0.0)
        }
    
    def get_task_result(self, task_id: str) -> Optional[TaskResult]:
        """获取任务结果"""
        return self.task_results.get(task_id)
    
    def cleanup_completed_tasks(self, keep_recent: int = 100):
        """清理已完成的任务结果"""
        if len(self.task_results) <= keep_recent:
            return
        
        # 按完成时间排序，保留最近的结果
        completed_tasks = [
            (task_id, result) for task_id, result in self.task_results.items()
            if task_id not in self.active_tasks and result.end_time
        ]
        
        completed_tasks.sort(key=lambda x: x[1].end_time, reverse=True)
        
        # 删除旧的结果
        for task_id, _ in completed_tasks[keep_recent:]:
            del self.task_results[task_id]
        
        logger.info(f"清理了 {len(completed_tasks) - keep_recent} 个旧任务结果")
    
    async def shutdown(self):
        """关闭处理器"""
        logger.info("关闭并发处理器...")
        
        # 取消监控任务
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        # 取消所有活跃任务
        for task_id in list(self.active_tasks.keys()):
            await self.cancel_task(task_id)
        
        # 关闭执行器
        self.executor.shutdown(wait=True)
        
        logger.info("并发处理器已关闭")


# 全局处理器实例
default_processor = None

def get_default_processor():
    """获取默认处理器"""
    global default_processor
    if default_processor is None:
        default_processor = ConcurrentProcessor(enable_monitoring=False)
    return default_processor

# 便捷函数
async def submit_async(task_func: Callable, *args, **kwargs) -> str:
    """提交异步任务的便捷函数"""
    processor = get_default_processor()
    return await processor.submit_async_task(task_func, *args, **kwargs)

async def submit_sync(task_func: Callable, *args, **kwargs) -> str:
    """提交同步任务的便捷函数"""
    processor = get_default_processor()
    return await processor.submit_sync_task(task_func, *args, **kwargs)

async def wait_for(task_id: str, timeout: float = None) -> TaskResult:
    """等待任务的便捷函数"""
    processor = get_default_processor()
    return await processor.wait_for_task(task_id, timeout)


# 测试函数
async def test_concurrent_processor():
    """测试并发处理器"""
    logger.info("开始测试并发处理器...")
    
    try:
        # 创建处理器
        processor = ConcurrentProcessor(max_workers=4, use_processes=False)
        
        # 测试异步任务
        async def async_task(n: int) -> int:
            await asyncio.sleep(0.1)
            return n * n
        
        # 测试同步任务
        def sync_task(n: int) -> int:
            time.sleep(0.1)
            return n * 2
        
        # 提交异步任务
        logger.info("提交异步任务...")
        async_task_ids = []
        for i in range(5):
            task_id = await processor.submit_async_task(async_task, i)
            async_task_ids.append(task_id)
        
        # 提交同步任务
        logger.info("提交同步任务...")
        sync_task_ids = []
        for i in range(5):
            task_id = await processor.submit_sync_task(sync_task, i)
            sync_task_ids.append(task_id)
        
        # 等待所有任务完成
        all_task_ids = async_task_ids + sync_task_ids
        results = await processor.wait_for_all_tasks(all_task_ids, timeout=5.0)
        
        # 检查结果
        successful_count = sum(1 for r in results if r.success)
        logger.info(f"✅ 任务完成: {successful_count}/{len(results)} 成功")
        
        # 显示统计信息
        stats = processor.get_stats()
        logger.info(f"📊 处理器统计: 成功率 {stats['success_rate']:.2%}, "
                   f"平均耗时 {stats['avg_duration']:.3f}秒")
        
        # 关闭处理器
        await processor.shutdown()
        
        logger.info("✅ 并发处理器测试完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 并发处理器测试失败: {e}")
        return False

if __name__ == "__main__":
    import asyncio
    logging.basicConfig(level=logging.INFO)
    asyncio.run(test_concurrent_processor())
