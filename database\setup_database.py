#!/usr/bin/env python3
"""
数据库设置脚本
执行新功能所需的数据库表创建和优化
"""

import sqlite3
import os
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def setup_database():
    """设置数据库"""
    
    # 数据库路径
    db_path = Path(__file__).parent.parent / "ticks.db"
    sql_script_path = Path(__file__).parent / "create_new_tables.sql"
    
    logger.info(f"开始设置数据库: {db_path}")
    logger.info(f"SQL脚本路径: {sql_script_path}")
    
    # 检查SQL脚本是否存在
    if not sql_script_path.exists():
        logger.error(f"SQL脚本文件不存在: {sql_script_path}")
        return False
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 读取SQL脚本
        with open(sql_script_path, 'r', encoding='utf-8') as f:
            sql_script = f.read()
        
        # 使用executescript方法执行整个脚本
        try:
            # 先清理脚本，移除注释和空行
            lines = sql_script.split('\n')
            cleaned_lines = []
            for line in lines:
                line = line.strip()
                if line and not line.startswith('--'):
                    cleaned_lines.append(line)

            cleaned_script = '\n'.join(cleaned_lines)

            # 执行脚本
            cursor.executescript(cleaned_script)
            logger.info("✅ SQL脚本执行成功")

        except sqlite3.Error as e:
            logger.error(f"❌ SQL脚本执行失败: {e}")
            # 如果executescript失败，尝试逐条执行
            logger.info("尝试逐条执行SQL语句...")

            # 分割SQL语句（以分号分割）
            sql_statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]

            logger.info(f"准备执行 {len(sql_statements)} 条SQL语句")

            # 执行SQL语句
            for i, statement in enumerate(sql_statements, 1):
                try:
                    # 跳过注释行
                    if statement.startswith('--') or not statement:
                        continue

                    cursor.execute(statement)
                    logger.info(f"✅ 执行第 {i} 条语句成功")

                except sqlite3.Error as e:
                    # 某些语句可能因为已存在而失败，这是正常的
                    if "already exists" in str(e).lower():
                        logger.info(f"⚠️  第 {i} 条语句: {e} (已存在，跳过)")
                    else:
                        logger.error(f"❌ 第 {i} 条语句执行失败: {e}")
                        logger.error(f"语句内容: {statement[:100]}...")
        
        # 提交更改
        conn.commit()
        
        # 验证表是否创建成功
        verify_tables(cursor)
        
        # 显示数据库统计信息
        show_database_stats(cursor)
        
        conn.close()
        logger.info("✅ 数据库设置完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 数据库设置失败: {e}")
        return False

def verify_tables(cursor):
    """验证表是否创建成功"""
    logger.info("验证数据库表...")
    
    expected_tables = [
        'optimal_configs',
        'alert_rules', 
        'alert_history',
        'market_analysis',
        'optimization_tasks'
    ]
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = [row[0] for row in cursor.fetchall()]
    
    for table in expected_tables:
        if table in existing_tables:
            logger.info(f"✅ 表 {table} 创建成功")
        else:
            logger.error(f"❌ 表 {table} 创建失败")
    
    # 验证视图
    expected_views = [
        'v_latest_optimal_configs',
        'v_active_alert_rules',
        'v_recent_alerts'
    ]
    
    cursor.execute("SELECT name FROM sqlite_master WHERE type='view'")
    existing_views = [row[0] for row in cursor.fetchall()]
    
    for view in expected_views:
        if view in existing_views:
            logger.info(f"✅ 视图 {view} 创建成功")
        else:
            logger.error(f"❌ 视图 {view} 创建失败")

def show_database_stats(cursor):
    """显示数据库统计信息"""
    logger.info("数据库统计信息:")
    
    # 显示所有表的记录数
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [row[0] for row in cursor.fetchall()]
    
    for table in tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            logger.info(f"  📊 {table}: {count} 条记录")
        except sqlite3.Error as e:
            logger.error(f"  ❌ 无法查询表 {table}: {e}")
    
    # 显示数据库大小
    try:
        cursor.execute("PRAGMA page_count")
        page_count = cursor.fetchone()[0]
        cursor.execute("PRAGMA page_size")
        page_size = cursor.fetchone()[0]
        db_size = page_count * page_size / (1024 * 1024)  # MB
        logger.info(f"  💾 数据库大小: {db_size:.2f} MB")
    except Exception as e:
        logger.error(f"  ❌ 无法获取数据库大小: {e}")

def test_database_operations():
    """测试数据库操作"""
    logger.info("测试数据库操作...")
    
    db_path = Path(__file__).parent.parent / "ticks.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 测试插入优化配置
        test_config = {
            'symbol': 'TEST',
            'config_name': 'test_config',
            'parameters': '{"buy_trigger_drop": -0.006, "profit_target": 0.005}',
            'performance_metrics': '{"total_return": 0.05, "max_drawdown": -0.02}',
            'optimization_method': 'test',
            'fitness_score': 0.85,
            'created_at': '2025-01-01T00:00:00',
            'updated_at': '2025-01-01T00:00:00'
        }
        
        cursor.execute("""
            INSERT OR REPLACE INTO optimal_configs 
            (symbol, config_name, parameters, performance_metrics, optimization_method, 
             fitness_score, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (
            test_config['symbol'],
            test_config['config_name'],
            test_config['parameters'],
            test_config['performance_metrics'],
            test_config['optimization_method'],
            test_config['fitness_score'],
            test_config['created_at'],
            test_config['updated_at']
        ))
        
        # 测试查询
        cursor.execute("SELECT * FROM optimal_configs WHERE symbol=?", ('TEST',))
        result = cursor.fetchone()
        
        if result:
            logger.info("✅ 数据库操作测试成功")
            # 清理测试数据
            cursor.execute("DELETE FROM optimal_configs WHERE symbol=?", ('TEST',))
        else:
            logger.error("❌ 数据库操作测试失败")
        
        conn.commit()
        conn.close()
        
    except Exception as e:
        logger.error(f"❌ 数据库操作测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始设置ETF套利系统数据库...")
    print("=" * 50)
    
    success = setup_database()
    
    if success:
        print("\n🎉 数据库设置成功完成！")
        print("✅ 新功能数据表已创建")
        print("✅ 性能优化索引已添加")
        print("✅ 默认预警规则已插入")
        
        # 运行测试
        test_database_operations()
        
        print("\n📋 下一步:")
        print("1. 实现数据库连接池")
        print("2. 开发参数优化器")
        print("3. 开发预警系统")
        
    else:
        print("\n❌ 数据库设置失败，请检查错误信息")
    
    print("=" * 50)
