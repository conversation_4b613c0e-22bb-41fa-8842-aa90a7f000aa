# 多股票数据采集功能使用指南

## 🚀 快速开始

### 1. 启动系统
```bash
cd etf_arbitrage_streamlit_multi
streamlit run main.py
```

### 2. 进入数据采集页面
点击侧边栏的 "📊 数据采集" 进入数据采集页面

## 📈 使用多股票采集

### 单股票模式（传统）
1. 选择 **"单股票采集"**
2. 从下拉框选择目标股票
3. 设置采集间隔和批次大小
4. 点击 **"🚀 启动采集"**

### 多股票模式（新功能）
1. 选择 **"多股票采集"**
2. 勾选要采集的股票：
   - ✅ 159740 - 纳指ETF
   - ✅ 159915 - 创业板ETF  
   - ✅ 159919 - 沪深300ETF
   - ✅ 510300 - 沪深300ETF
   - ✅ 510500 - 中证500ETF

3. 快速操作：
   - **全选**: 一键选择所有股票
   - **全不选**: 清除所有选择
   - **反选**: 反转当前选择

4. 设置参数：
   - **采集间隔**: 推荐5秒（所有股票共用）
   - **批次大小**: 推荐100条（每只股票）
   - **最大并发数**: 根据选择股票数自动设置

5. 点击 **"🚀 启动采集(X只)"**

## 📊 查看采集状态

### 汇总统计
- **总记录数**: 所有股票的记录总和
- **运行中股票**: 当前采集的股票数量
- **平均质量**: 所有股票的数据质量平均值
- **总错误次数**: 所有股票的错误总和

### 详细股票卡片（≤6只股票时显示）
每只股票显示：
- 🟢 采集中 / 🔵 未运行
- 历史记录总数
- 最新价格
- 本次采集统计（新增+重复）

## 📈 查看图表数据

### 单股票图表
- **≤5只股票**: 使用选项卡快速切换
- **>5只股票**: 使用下拉选择框

每个图表包含：
- 实时价格走势（颜色表示数据新鲜度）
- 成交量分布
- 详细统计信息
- 重复数据率分析

### 多股票对比图表
- **≤3只股票时可用**
- 标准化价格对比（以首个数据点为100%基准）
- 便于比较不同股票的相对走势

## ⚠️ 注意事项

1. **资源占用**: 多股票采集会占用更多CPU和内存
2. **采集间隔**: 建议不低于3秒，避免过于频繁请求
3. **并发限制**: 系统最多支持10个并发线程
4. **停止采集**: 一键停止会同时停止所有股票的采集
5. **状态切换**: 如需切换股票组合，请先停止当前采集

## 🔧 故障排除

### 常见问题
1. **启动失败**: 检查是否有股票已在运行，先停止再重新启动
2. **数据不更新**: 检查数据新鲜度指示器，可能需要手动刷新
3. **重复率过高**: 可能数据源更新缓慢，属正常现象

### 性能优化建议
1. **合理选择股票数量**: 建议同时采集3-5只股票
2. **适当调整间隔**: 根据系统性能调整采集间隔
3. **定期清理数据**: 使用数据质量检查功能

## 📞 技术支持

如遇问题，可查看：
1. 系统日志输出
2. 错误次数统计
3. 数据质量检查结果

多股票数据采集功能让您能够同时监控多个ETF的价格变化，大大提升数据采集效率！