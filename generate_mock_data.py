#!/usr/bin/env python3
"""
生成模拟的实时tick数据用于测试
"""

import sqlite3
import random
import time
from datetime import datetime, timedelta
import numpy as np

def generate_realistic_price_data(base_price=1.234, num_points=100, volatility=0.002):
    """生成真实的价格数据"""
    prices = [base_price]
    
    for i in range(num_points - 1):
        # 使用随机游走模型生成价格
        change = np.random.normal(0, volatility)
        new_price = prices[-1] * (1 + change)
        
        # 确保价格在合理范围内
        new_price = max(0.1, min(10.0, new_price))
        prices.append(new_price)
    
    return prices

def insert_mock_data(symbol='159740', num_records=50):
    """插入模拟数据到数据库"""
    try:
        conn = sqlite3.connect('ticks.db')
        cursor = conn.cursor()
        
        # 清除旧的测试数据
        cursor.execute("DELETE FROM ticks WHERE symbol = ?", (symbol,))
        
        # 生成价格数据
        base_price = 1.234
        prices = generate_realistic_price_data(base_price, num_records, 0.003)
        
        # 生成时间序列（从当前时间往前推）
        current_time = datetime.now()
        
        records = []
        for i, price in enumerate(prices):
            tick_time = current_time - timedelta(minutes=num_records-i-1)
            volume = random.randint(100, 10000)
            side = random.choice(['BUY', 'SELL'])
            
            records.append((
                symbol,
                tick_time.strftime('%Y-%m-%d %H:%M:%S'),
                round(price, 4),
                volume,
                side,
                None,  # raw
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')  # created_at
            ))
        
        # 批量插入
        cursor.executemany("""
            INSERT INTO ticks (symbol, tick_time, price, volume, side, raw, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, records)
        
        conn.commit()
        conn.close()
        
        print(f"成功插入 {len(records)} 条模拟数据")
        print(f"价格范围: {min(prices):.4f} - {max(prices):.4f}")
        print(f"时间范围: {records[0][1]} 到 {records[-1][1]}")
        
    except Exception as e:
        print(f"插入模拟数据失败: {e}")

def start_realtime_data_generator(symbol='159740', interval=2):
    """启动实时数据生成器"""
    try:
        conn = sqlite3.connect('ticks.db')
        cursor = conn.cursor()
        
        # 获取最后一个价格作为基准
        cursor.execute("SELECT price FROM ticks WHERE symbol = ? ORDER BY tick_time DESC LIMIT 1", (symbol,))
        result = cursor.fetchone()
        last_price = result[0] if result else 1.234
        
        print(f"开始生成实时数据，基准价格: {last_price:.4f}")
        print("按 Ctrl+C 停止...")
        
        while True:
            # 生成新的价格（小幅波动）
            change = np.random.normal(0, 0.001)  # 0.1%的标准差
            new_price = last_price * (1 + change)
            new_price = max(0.1, min(10.0, new_price))
            
            volume = random.randint(100, 5000)
            side = random.choice(['BUY', 'SELL'])
            current_time = datetime.now()
            
            # 插入新数据
            cursor.execute("""
                INSERT INTO ticks (symbol, tick_time, price, volume, side, raw, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                symbol,
                current_time.strftime('%Y-%m-%d %H:%M:%S'),
                round(new_price, 4),
                volume,
                side,
                None,
                current_time.strftime('%Y-%m-%d %H:%M:%S')
            ))
            
            conn.commit()
            last_price = new_price
            
            print(f"[{current_time.strftime('%H:%M:%S')}] 新价格: {new_price:.4f}, 成交量: {volume}")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n数据生成器已停止")
    except Exception as e:
        print(f"实时数据生成失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "realtime":
        # 实时模式
        start_realtime_data_generator()
    else:
        # 批量插入模式
        insert_mock_data(num_records=100)
        print("\n模拟数据已生成，现在可以测试实时交易面板了！")
        print("如需持续生成实时数据，请运行: python generate_mock_data.py realtime")
