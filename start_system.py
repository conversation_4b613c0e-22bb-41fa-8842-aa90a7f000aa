#!/usr/bin/env python3
"""
ETF套利交易系统启动脚本
一键启动统一控制面板
"""

import subprocess
import sys
import os

def main():
    """启动系统"""
    print("🎛️ ETF套利交易系统 v2.0")
    print("=" * 50)
    print("正在启动统一控制面板...")
    
    try:
        # 启动Streamlit应用
        subprocess.run([
            "streamlit", "run", "main_control_panel.py",
            "--server.port", "8501",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n系统已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请确保已安装streamlit: pip install streamlit")

if __name__ == "__main__":
    main()