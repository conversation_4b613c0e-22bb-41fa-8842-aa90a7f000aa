import sqlite3
import time
import logging
from typing import List, Dict, Optional, Tuple, Union, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum

import numpy as np
import pandas as pd
import argparse


# 导入统一配置
from strategy_config import (
    StrategyConfig,
    BUY_TRIGGER_DROP, PROFIT_TARGET, STOP_LOSS, MAX_HOLD_TIME,
    MAX_POSITION, DAILY_LOSS_LIMIT, MAX_DRAWDOWN_LIMIT,
    LAYERS, PARTIAL_SELL_LEVELS, PARTIAL_SELL_RATIOS, MIN_HOLD_TIME,_DEFAULT_CONFIG
)
DB_PATH: str = "ticks.db"

# 设置日志记录
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MarketRegime(Enum):
    TRENDING = "trending"
    RANGING = "ranging"
    VOLATILE = "volatile"


@dataclass
class PositionBatch:
    """持仓批次信息"""
    quantity: int
    cost: float  # 该批次的总成本
    buy_time: datetime
    partial_sold: List[bool] = None
    
    def __post_init__(self):
        if self.partial_sold is None:
            self.partial_sold = [False] * len(PARTIAL_SELL_LEVELS)
    
    @property
    def avg_cost(self) -> float:
        """计算该批次的平均成本"""
        return self.cost / self.quantity if self.quantity > 0 else 0.0
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算该批次的收益率"""
        if self.quantity <= 0 or self.avg_cost <= 0:
            return 0.0
        return (current_price - self.avg_cost) / self.avg_cost


@dataclass
class Position:
    """持仓信息"""
    batches: List[PositionBatch] = None
    total_quantity: int = 0
    total_cost: float = 0.0
    first_buy_time: Optional[datetime] = None
    # 简化的止盈状态跟踪
    profit_level_reached: int = 0  # 0=未止盈, 1=第一级, 2=第二级, 3=第三级

    def __post_init__(self):
        if self.batches is None:
            self.batches = []
    
    def add_position(self, qty: int, price: float, timestamp: datetime):
        """增加持仓"""
        batch = PositionBatch(
            quantity=qty,
            cost=qty * price,
            buy_time=timestamp
        )
        self.batches.append(batch)

        self.total_quantity += qty
        self.total_cost += qty * price
        if self.first_buy_time is None:
            self.first_buy_time = timestamp

        # 智能重置止盈级别：如果是重新建仓或大幅加仓，重置止盈级别
        if self._should_reset_profit_level(qty):
            self.profit_level_reached = 0
            logger.debug(f"增加持仓后重置止盈级别 - 数量: {qty}, 价格: {price:.4f}")
        
        logger.debug(f"增加持仓 - 批次数量: {qty}, 价格: {price:.4f}, 总数量: {self.total_quantity}, 总成本: {self.total_cost:.2f}")
    
    def reduce_position(self, qty: int) -> float:
        """减少持仓，返回减少的成本"""
        logger.debug(f"减少持仓 - 请求减少数量: {qty}, 当前总数量: {self.total_quantity}")
        
        if qty >= self.total_quantity:
            # 全部卖出
            cost_reduced = self.total_cost
            self.batches.clear()
            self.total_quantity = 0
            self.total_cost = 0.0
            self.first_buy_time = None
            logger.info(f"全部卖出 - 减少成本: {cost_reduced:.2f}")
            return cost_reduced
        else:
            # 部分卖出 - 按照先进先出原则
            cost_reduced = 0.0
            qty_to_sell = qty
            
            while qty_to_sell > 0 and self.batches:
                batch = self.batches[0]
                if batch.quantity <= qty_to_sell:
                    # 卖出整个批次
                    cost_reduced += batch.cost
                    qty_to_sell -= batch.quantity
                    self.batches.pop(0)
                    logger.debug(f"卖出整个批次 - 数量: {batch.quantity}, 成本: {batch.cost:.2f}")
                else:
                    # 部分卖出批次
                    ratio = qty_to_sell / batch.quantity
                    cost_reduced += batch.cost * ratio
                    batch.quantity -= qty_to_sell
                    batch.cost -= batch.cost * ratio
                    qty_to_sell = 0
                    logger.debug(f"部分卖出批次 - 卖出数量: {qty_to_sell}, 剩余数量: {batch.quantity}")
            
            self.total_quantity -= qty
            self.total_cost -= cost_reduced

            # 优化：清理空批次，减少内存占用
            self._cleanup_empty_batches()

            logger.debug(f"减少持仓完成 - 实际减少数量: {qty}, 减少成本: {cost_reduced:.2f}, 剩余数量: {self.total_quantity}")
            return cost_reduced
    
    def get_profit_rate(self, current_price: float) -> float:
        """计算整体收益率"""
        if self.total_quantity <= 0 or self.total_cost <= 0:
            return 0.0
        avg_cost = self.total_cost / self.total_quantity
        return (current_price - avg_cost) / avg_cost
    
    def get_hold_time(self, current_time: datetime = None) -> int:
        """获取持仓时间（秒）"""
        if self.first_buy_time is None:
            return 0
        # 如果提供了当前时间（回测模式），使用提供的时间；否则使用系统时间（实时模式）
        reference_time = current_time if current_time is not None else datetime.now()
        return int((reference_time - self.first_buy_time).total_seconds())
    
    def reset_partial_sold_flags(self):
        """重置止盈状态"""
        self.profit_level_reached = 0

    def _should_reset_profit_level(self, new_qty: int) -> bool:
        """判断是否应该重置止盈级别"""
        # 如果是首次买入，不需要重置
        if len(self.batches) <= 1:
            return False

        # 如果新买入数量占总仓位的比例超过30%，重置止盈级别
        old_quantity = self.total_quantity - new_qty
        if old_quantity == 0:
            return True

        new_ratio = new_qty / self.total_quantity
        return new_ratio >= 0.3  # 新买入占30%以上时重置

    def _cleanup_empty_batches(self):
        """清理空批次，优化内存使用"""
        self.batches = [batch for batch in self.batches if batch.quantity > 0]

    def check_partial_sell(self, current_price: float, config=None) -> Tuple[bool, int, float, str]:
        """
        简化的分批止盈检查
        返回 (是否卖出, 级别, 卖出比例, 原因)

        Args:
            current_price: 当前价格
            config: 可选的配置对象，如果提供则使用其参数，否则使用默认配置
        """
        if self.total_quantity <= 0:
            return False, -1, 0.0, ""

        # 计算整体收益率
        profit_rate = self.get_profit_rate(current_price)

        # 根据是否提供config来选择参数来源
        if config is not None:
            # 使用传入的配置参数
            profit_target = config.profit_target
            multiplier1 = config.partial_profit_multiplier1
            multiplier2 = config.partial_profit_multiplier2
            multiplier3 = config.partial_profit_multiplier3
            ratio1 = config.partial_sell_ratio1
            ratio2 = config.partial_sell_ratio2
            ratio3 = config.partial_sell_ratio3
        else:
            # 使用默认配置
            
            profit_target = _DEFAULT_CONFIG['profit_target']
            multiplier1 = _DEFAULT_CONFIG['partial_profit_multiplier1']
            multiplier2 = _DEFAULT_CONFIG['partial_profit_multiplier2']
            multiplier3 = _DEFAULT_CONFIG['partial_profit_multiplier3']
            ratio1 = _DEFAULT_CONFIG['partial_sell_ratio1']
            ratio2 = _DEFAULT_CONFIG['partial_sell_ratio2']
            ratio3 = _DEFAULT_CONFIG['partial_sell_ratio3']

        # 定义止盈级别和对应的卖出比例
        profit_levels = [
            (profit_target * multiplier1, ratio1, "止盈(第一级)"),
            (profit_target * multiplier2, ratio2, "止盈(第二级)"),
            (profit_target * multiplier3, ratio3, "止盈(第三级)")
        ]

        # 检查是否达到各个止盈级别
        for level_idx, (target_profit, sell_ratio, reason) in enumerate(profit_levels, 1):
            # 检查是否达到目标收益率且尚未触发该级别的止盈
            if profit_rate >= target_profit and self.profit_level_reached < level_idx:
                self.profit_level_reached = level_idx
                logger.info(f"触发分批止盈 - 级别: {level_idx}, 收益率: {profit_rate:.2%}, 目标收益率: {target_profit:.2%}, 卖出比例: {sell_ratio:.2%}")
                return True, level_idx, sell_ratio, reason

        return False, -1, 0.0, ""


class RiskManager:
    """风险管理器"""
    
    def __init__(self, initial_capital: float):
        self.initial_capital = initial_capital
        self.current_equity = initial_capital
        self.daily_pnl = 0.0
        self.max_equity = initial_capital
        self.current_date = None
        self.daily_loss_limit = DAILY_LOSS_LIMIT
        self.max_drawdown_limit = MAX_DRAWDOWN_LIMIT
        logger.debug(f"初始化风险管理器 - 初始资金: {initial_capital:.2f}")
        
    def update(self, timestamp: datetime, equity: float):
        """更新风险状态"""
        # 检查是否跨日
        if self.current_date is None or self.current_date != timestamp.date():
            # 跨日重置
            self.current_date = timestamp.date()
            self.daily_pnl = 0.0
            logger.debug(f"风险管理器跨日重置 - 当前日期: {self.current_date}")

        # 更新每日盈亏
        old_equity = self.current_equity
        self.daily_pnl += equity - self.current_equity
        
        # 更新当前权益和最高权益
        self.current_equity = equity
        self.max_equity = max(self.max_equity, equity)
        
        logger.debug(f"风险管理器更新 - 时间: {timestamp}, 权益: {equity:.2f}, 日盈亏: {self.daily_pnl:.2f}")

    def check_risk_limits(self) -> bool:
        """检查风险限制"""
        # 检查日损失限制
        daily_loss_rate = self.daily_pnl / self.initial_capital
        if daily_loss_rate <= self.daily_loss_limit:
            logger.warning(f"触发日损失限制: {daily_loss_rate:.2%} <= {self.daily_loss_limit:.2%}")
            return False
        
        # 检查最大回撤限制
        if self.max_equity > 0:
            drawdown = (self.current_equity - self.max_equity) / self.max_equity
            if drawdown <= self.max_drawdown_limit:
                logger.warning(f"触发最大回撤限制: {drawdown:.2%} <= {self.max_drawdown_limit:.2%}")
                return False
        
        return True
    
    def update_equity(self, equity: float):
        """更新权益（兼容原项目接口）"""
        old_equity = self.current_equity
        self.current_equity = equity
        self.max_equity = max(self.max_equity, equity)
        
        # 更新峰值权益（用于回撤计算）
        if not hasattr(self, 'peak_equity'):
            self.peak_equity = self.initial_capital
        self.peak_equity = max(self.peak_equity, equity)
        
        logger.debug(f"权益更新: {old_equity:.2f} -> {equity:.2f}")
    
    def reset(self):
        """重置风险管理系统"""
        self.current_equity = self.initial_capital
        self.daily_pnl = 0.0
        self.max_equity = self.initial_capital
        self.peak_equity = self.initial_capital
        self.current_date = None
        logger.info("风险管理器已重置")


def detect_market_regime(prices: List[float], window: int = 20) -> MarketRegime:
    """
    检测市场状态
    """
    if len(prices) < window:
        return MarketRegime.RANGING
    
    # 计算价格变化率
    returns = np.diff(prices[-window:]) / prices[-window:-1]
    
    # 计算波动率
    volatility = np.std(returns)
    
    # 计算趋势强度（简单移动平均斜率）
    ma = np.convolve(prices[-window:], np.ones(window)/window, mode='valid')
    trend_strength = (ma[-1] - ma[0]) / ma[0] if len(ma) > 0 else 0
    
    # 简单的市场状态判断逻辑
    if abs(trend_strength) > 0.02 and volatility < 0.02:
        return MarketRegime.TRENDING
    elif volatility > 0.03:
        return MarketRegime.VOLATILE
    else:
        return MarketRegime.RANGING


def calculate_dynamic_parameters(regime: MarketRegime, base_config: Dict) -> Dict:
    """
    根据市场状态计算动态参数
    """
    dynamic_params = base_config.copy()
    
    if regime == MarketRegime.TRENDING:
        # 趋势市：增加止盈目标，放宽止损
        dynamic_params['profit_target'] *= 1.2
        dynamic_params['stop_loss'] *= 0.8
    elif regime == MarketRegime.VOLATILE:
        # 震荡市：降低止盈目标，收紧止损
        dynamic_params['profit_target'] *= 0.8
        dynamic_params['stop_loss'] *= 1.2
    # RANGING状态下使用基础参数
    
    return dynamic_params