#!/usr/bin/env python3
"""
异步数据处理模块
实现高性能异步数据采集、处理和分发
"""

import asyncio
import aiohttp
import aiofiles
import logging
from typing import Dict, List, Optional, Any, Callable, Union, Tuple, AsyncGenerator
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque
import json
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import numpy as np
from pathlib import Path
import weakref
import queue

logger = logging.getLogger(__name__)

@dataclass
class DataPoint:
    """数据点"""
    symbol: str
    timestamp: datetime
    price: float
    volume: int
    source: str
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ProcessingTask:
    """处理任务"""
    task_id: str
    data: Any
    processor: str
    priority: int = 0
    created_at: datetime = field(default_factory=datetime.now)
    timeout: float = 30.0

@dataclass
class StreamMetrics:
    """流处理指标"""
    total_processed: int = 0
    processing_rate: float = 0.0
    error_count: int = 0
    last_update: datetime = field(default_factory=datetime.now)
    queue_size: int = 0
    active_tasks: int = 0

class AsyncDataStream:
    """异步数据流"""
    
    def __init__(self, name: str, buffer_size: int = 10000):
        self.name = name
        self.buffer_size = buffer_size
        self._buffer = deque(maxlen=buffer_size)
        self._subscribers = weakref.WeakSet()
        self._lock = asyncio.Lock()
        self.metrics = StreamMetrics()
        
        # 流控制
        self._is_active = False
        self._backpressure_threshold = buffer_size * 0.8
        
    async def publish(self, data: DataPoint):
        """发布数据到流"""
        async with self._lock:
            # 检查背压
            if len(self._buffer) >= self._backpressure_threshold:
                logger.warning(f"流 {self.name} 接近容量限制，可能存在背压")
            
            self._buffer.append(data)
            self.metrics.total_processed += 1
            self.metrics.last_update = datetime.now()
            self.metrics.queue_size = len(self._buffer)
            
            # 通知订阅者
            await self._notify_subscribers(data)
    
    async def subscribe(self, callback: Callable[[DataPoint], None]):
        """订阅数据流"""
        self._subscribers.add(callback)
    
    async def _notify_subscribers(self, data: DataPoint):
        """通知订阅者"""
        for subscriber in list(self._subscribers):
            try:
                if asyncio.iscoroutinefunction(subscriber):
                    await subscriber(data)
                else:
                    subscriber(data)
            except Exception as e:
                logger.error(f"通知订阅者失败: {e}")
    
    async def get_recent_data(self, count: int = 100) -> List[DataPoint]:
        """获取最近的数据"""
        async with self._lock:
            return list(self._buffer)[-count:]
    
    def get_metrics(self) -> StreamMetrics:
        """获取流指标"""
        return self.metrics

class AsyncTaskQueue:
    """异步任务队列"""
    
    def __init__(self, max_workers: int = 10, max_queue_size: int = 1000):
        self.max_workers = max_workers
        self.max_queue_size = max_queue_size
        
        self._queue = asyncio.PriorityQueue(maxsize=max_queue_size)
        self._workers = []
        self._is_running = False
        self._task_counter = 0
        
        # 统计信息
        self.stats = {
            'tasks_submitted': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'average_processing_time': 0.0
        }
        
        # 处理器注册
        self._processors = {}
    
    def register_processor(self, name: str, processor: Callable):
        """注册处理器"""
        self._processors[name] = processor
        logger.info(f"处理器 {name} 已注册")
    
    async def start(self):
        """启动任务队列"""
        if self._is_running:
            return
        
        self._is_running = True
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = asyncio.create_task(self._worker(f"worker-{i}"))
            self._workers.append(worker)
        
        logger.info(f"异步任务队列已启动，工作线程数: {self.max_workers}")
    
    async def stop(self):
        """停止任务队列"""
        if not self._is_running:
            return
        
        self._is_running = False
        
        # 等待所有任务完成
        await self._queue.join()
        
        # 取消工作线程
        for worker in self._workers:
            worker.cancel()
        
        await asyncio.gather(*self._workers, return_exceptions=True)
        self._workers.clear()
        
        logger.info("异步任务队列已停止")
    
    async def submit_task(self, task: ProcessingTask) -> bool:
        """提交任务"""
        try:
            # 优先级队列：负数表示高优先级
            priority_item = (-task.priority, self._task_counter, task)
            await self._queue.put(priority_item)
            
            self._task_counter += 1
            self.stats['tasks_submitted'] += 1
            
            return True
            
        except asyncio.QueueFull:
            logger.warning("任务队列已满，任务被拒绝")
            return False
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        logger.info(f"工作线程 {worker_name} 已启动")
        
        while self._is_running:
            try:
                # 获取任务（带超时）
                try:
                    priority_item = await asyncio.wait_for(
                        self._queue.get(), 
                        timeout=1.0
                    )
                    _, _, task = priority_item
                except asyncio.TimeoutError:
                    continue
                
                # 处理任务
                start_time = time.time()
                
                try:
                    await self._process_task(task)
                    self.stats['tasks_completed'] += 1
                    
                except Exception as e:
                    logger.error(f"任务处理失败 {task.task_id}: {e}")
                    self.stats['tasks_failed'] += 1
                
                finally:
                    # 更新处理时间统计
                    processing_time = time.time() - start_time
                    self._update_processing_time(processing_time)
                    
                    # 标记任务完成
                    self._queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"工作线程 {worker_name} 异常: {e}")
        
        logger.info(f"工作线程 {worker_name} 已停止")
    
    async def _process_task(self, task: ProcessingTask):
        """处理单个任务"""
        if task.processor not in self._processors:
            raise ValueError(f"未知处理器: {task.processor}")
        
        processor = self._processors[task.processor]
        
        # 检查超时
        if (datetime.now() - task.created_at).total_seconds() > task.timeout:
            raise TimeoutError(f"任务 {task.task_id} 超时")
        
        # 执行处理器
        if asyncio.iscoroutinefunction(processor):
            await processor(task.data)
        else:
            # 在线程池中执行同步处理器
            loop = asyncio.get_event_loop()
            with ThreadPoolExecutor() as executor:
                await loop.run_in_executor(executor, processor, task.data)
    
    def _update_processing_time(self, processing_time: float):
        """更新处理时间统计"""
        current_avg = self.stats['average_processing_time']
        completed = self.stats['tasks_completed']
        
        if completed == 1:
            self.stats['average_processing_time'] = processing_time
        else:
            # 移动平均
            self.stats['average_processing_time'] = (
                current_avg * 0.9 + processing_time * 0.1
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'queue_size': self._queue.qsize(),
            'active_workers': len([w for w in self._workers if not w.done()]),
            'registered_processors': list(self._processors.keys())
        }

class AsyncDataCollector:
    """异步数据采集器"""
    
    def __init__(self, session_timeout: int = 30):
        self.session_timeout = session_timeout
        self._session = None
        self._is_collecting = False
        
        # 数据流
        self.streams = {}
        
        # 采集统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'average_response_time': 0.0
        }
    
    async def start(self):
        """启动采集器"""
        if self._session:
            return
        
        timeout = aiohttp.ClientTimeout(total=self.session_timeout)
        self._session = aiohttp.ClientSession(timeout=timeout)
        self._is_collecting = True
        
        logger.info("异步数据采集器已启动")
    
    async def stop(self):
        """停止采集器"""
        self._is_collecting = False
        
        if self._session:
            await self._session.close()
            self._session = None
        
        logger.info("异步数据采集器已停止")
    
    def create_stream(self, name: str, buffer_size: int = 10000) -> AsyncDataStream:
        """创建数据流"""
        stream = AsyncDataStream(name, buffer_size)
        self.streams[name] = stream
        return stream
    
    async def collect_data(self, url: str, symbol: str, 
                          stream_name: str = None) -> Optional[DataPoint]:
        """采集单个数据点"""
        if not self._session:
            raise RuntimeError("采集器未启动")
        
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        try:
            async with self._session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # 解析数据（这里需要根据实际API格式调整）
                    data_point = DataPoint(
                        symbol=symbol,
                        timestamp=datetime.now(),
                        price=float(data.get('price', 0)),
                        volume=int(data.get('volume', 0)),
                        source='api',
                        metadata={'url': url, 'raw_data': data}
                    )
                    
                    # 发布到流
                    if stream_name and stream_name in self.streams:
                        await self.streams[stream_name].publish(data_point)
                    
                    self.stats['successful_requests'] += 1
                    
                    # 更新响应时间统计
                    response_time = time.time() - start_time
                    self._update_response_time(response_time)
                    
                    return data_point
                
                else:
                    logger.warning(f"HTTP错误 {response.status}: {url}")
                    self.stats['failed_requests'] += 1
                    return None
        
        except Exception as e:
            logger.error(f"数据采集失败 {url}: {e}")
            self.stats['failed_requests'] += 1
            return None
    
    async def collect_multiple(self, urls_symbols: List[Tuple[str, str]], 
                             stream_name: str = None) -> List[Optional[DataPoint]]:
        """并发采集多个数据点"""
        tasks = [
            self.collect_data(url, symbol, stream_name)
            for url, symbol in urls_symbols
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for result in results:
            if isinstance(result, Exception):
                logger.error(f"并发采集异常: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        return processed_results
    
    def _update_response_time(self, response_time: float):
        """更新响应时间统计"""
        current_avg = self.stats['average_response_time']
        successful = self.stats['successful_requests']
        
        if successful == 1:
            self.stats['average_response_time'] = response_time
        else:
            # 移动平均
            self.stats['average_response_time'] = (
                current_avg * 0.9 + response_time * 0.1
            )
    
    def get_stats(self) -> Dict[str, Any]:
        """获取采集统计"""
        return {
            **self.stats,
            'streams': {name: stream.get_metrics().__dict__ 
                       for name, stream in self.streams.items()},
            'is_collecting': self._is_collecting
        }

class AsyncDataProcessor:
    """异步数据处理器主类"""
    
    def __init__(self, max_workers: int = 10):
        self.collector = AsyncDataCollector()
        self.task_queue = AsyncTaskQueue(max_workers=max_workers)
        
        # 注册默认处理器
        self._register_default_processors()
        
        logger.info("异步数据处理器初始化完成")
    
    def _register_default_processors(self):
        """注册默认处理器"""
        
        async def price_analyzer(data: DataPoint):
            """价格分析处理器"""
            # 简单的价格分析逻辑
            if data.price > 0:
                logger.debug(f"分析价格数据: {data.symbol} @ {data.price}")
        
        def volume_analyzer(data: DataPoint):
            """成交量分析处理器（同步）"""
            if data.volume > 0:
                logger.debug(f"分析成交量数据: {data.symbol} volume={data.volume}")
        
        async def data_validator(data: DataPoint):
            """数据验证处理器"""
            if data.price <= 0 or data.volume < 0:
                raise ValueError(f"无效数据: {data}")
        
        self.task_queue.register_processor('price_analyzer', price_analyzer)
        self.task_queue.register_processor('volume_analyzer', volume_analyzer)
        self.task_queue.register_processor('data_validator', data_validator)
    
    async def start(self):
        """启动处理器"""
        await self.collector.start()
        await self.task_queue.start()
        logger.info("异步数据处理器已启动")
    
    async def stop(self):
        """停止处理器"""
        await self.task_queue.stop()
        await self.collector.stop()
        logger.info("异步数据处理器已停止")
    
    def create_data_stream(self, name: str, buffer_size: int = 10000) -> AsyncDataStream:
        """创建数据流"""
        return self.collector.create_stream(name, buffer_size)
    
    async def process_data_async(self, data: DataPoint, processor_name: str, 
                               priority: int = 0) -> bool:
        """异步处理数据"""
        task = ProcessingTask(
            task_id=f"{processor_name}_{int(time.time() * 1000)}",
            data=data,
            processor=processor_name,
            priority=priority
        )
        
        return await self.task_queue.submit_task(task)
    
    async def collect_and_process(self, url: str, symbol: str, 
                                processor_names: List[str],
                                stream_name: str = None) -> bool:
        """采集并处理数据"""
        # 采集数据
        data_point = await self.collector.collect_data(url, symbol, stream_name)
        
        if not data_point:
            return False
        
        # 提交处理任务
        success_count = 0
        for processor_name in processor_names:
            if await self.process_data_async(data_point, processor_name):
                success_count += 1
        
        return success_count == len(processor_names)
    
    def register_processor(self, name: str, processor: Callable):
        """注册自定义处理器"""
        self.task_queue.register_processor(name, processor)
    
    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """获取综合统计信息"""
        return {
            'collector_stats': self.collector.get_stats(),
            'task_queue_stats': self.task_queue.get_stats(),
            'timestamp': datetime.now().isoformat()
        }

# 全局异步数据处理器
_async_processor = None
_processor_lock = asyncio.Lock()

async def get_async_processor() -> AsyncDataProcessor:
    """获取异步数据处理器单例"""
    global _async_processor
    
    if _async_processor is None:
        async with _processor_lock:
            if _async_processor is None:
                _async_processor = AsyncDataProcessor()
    
    return _async_processor
