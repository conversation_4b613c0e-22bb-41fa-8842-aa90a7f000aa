#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Dash + Feffery 回测监测面板
- 参数面板：标的、买入跌幅、卖出涨幅、分层、数量、滑点、手续费、初始资金、风险利率、重采样周期
- 一键运行回测：调用 backtest.run_backtest
- 可视化：KPIs、净值曲线 + 回撤、成交列表
- 报表：优先读取 report_dir 下 equity_resampled.csv / trades.csv；若无则用 stats['trades'] 回退渲染表格

启动:
python app_backtest_dashboard.py --symbol 159740 --host 127.0.0.1 --port 8051
"""
from __future__ import annotations

import os
import sys
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

import dash
import dash
from dash import dcc, Input, Output, State
import plotly.graph_objs as go
from plotly.subplots import make_subplots
import feffery_antd_components as fac
import sqlite3
from datetime import date

# 日志
logger = logging.getLogger("bt_dashboard")
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s"))
    logger.addHandler(handler)
logger.setLevel(logging.INFO)

# 回测模块与默认参数
try:
    from backtest import (  # type: ignore
        load_ticks,
        backtest as run_backtest,
        BUY_DROP,
        PROFIT_TARGET,  # 更新参数名
        LAYER_QTY,
        BASE_QTY,
        DEFAULT_SLIPPAGE,
        DEFAULT_FEE,
    )
except Exception:
    # 兜底默认值（若 backtest 常量不可用）
    def load_ticks(symbol: str) -> pd.DataFrame:
        return pd.DataFrame(columns=["time", "price", "volume"])

    def run_backtest(
        df: pd.DataFrame,
        buy_drop: float = -0.006,
        sell_rise: float = 0.006,  # 保持参数名一致，但含义已改为收益目标
        base_qty: int = 1000,
        layer_qty: List[float] | Tuple[float, ...] = (0.4, 0.35, 0.25),
        base_slippage: float = 0.0008,
        fee: float = 0.00005,
        slippage_coef: float = 0.0,
        plot_file: Optional[str] = None,
        initial_capital: float = 1_000_000.0,
        risk_free_rate: float = 0.0,
        sample_freq: str = "1T",
        report_dir: Optional[str] = None,
    ) -> Dict[str, Any]:
        return {
            "final_equity": 1_000_000.0,
            "pnl": 0.0,
            "cum_return": 0.0,
            "annual_return": 0.0,
            "annual_vol": 0.0,
            "sharpe_annual": 0.0,
            "sortino_annual": 0.0,
            "calmar": 0.0,
            "max_drawdown": 0.0,
            "num_trades": 0,
            "num_rounds": 0,
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "avg_hold_time_secs": 0.0,
            "median_hold_time_secs": 0.0,
            "exposure_ratio": 0.0,
            "trades": [],
        }

    BUY_DROP = -0.002
    PROFIT_TARGET = 0.0035
    LAYER_QTY = (0.4, 0.35, 0.25)
    BASE_QTY = 1000
    DEFAULT_SLIPPAGE = 0.0008
    DEFAULT_FEE = 0.00005


# 安全转换
def _safe_float(v: Any, default: float = 0.0) -> float:
    try:
        f = float(v)
        if np.isnan(f) or np.isinf(f):
            return default
        return float(f)
    except Exception:
        return default


def _safe_int(v: Any, default: int = 0) -> int:
    try:
        return int(v)
    except Exception:
        return default


def _ensure_dir(p: Path) -> None:
    try:
        p.mkdir(parents=True, exist_ok=True)
    except Exception as e:
        logger.error(f"创建目录失败: {e}")


def _load_ticks_for_backtest(symbol: str) -> pd.DataFrame:
    """加载回测期间的tick数据"""
    try:
        # 尝试从 ticks.db 加载数据
        db_path = Path("./ticks.db")
        if not db_path.exists():
            return pd.DataFrame(columns=["time", "price", "volume"])
        
        conn = sqlite3.connect(db_path)
        cur = conn.cursor()
        # 获取最近几天的数据用于K线图显示
        rows = cur.execute(
            "SELECT tick_time, price, volume FROM ticks "
            "WHERE symbol=? AND tick_time >= date('now', '-7 days') "
            "ORDER BY tick_time ASC",
            (symbol,)
        ).fetchall()
        conn.close()
        
        if not rows:
            return pd.DataFrame(columns=["time", "price", "volume"])
        
        df = pd.DataFrame(rows, columns=["tick_time", "price", "volume"])
        df["time"] = pd.to_datetime(df["tick_time"], errors="coerce")
        df = df.dropna(subset=["time"])
        return df[["time", "price", "volume"]]
    except Exception:
        return pd.DataFrame(columns=["time", "price", "volume"])


def _load_backtest_signals(symbol: str, report_dir: str) -> pd.DataFrame:
    """加载回测交易信号"""
    try:
        trades_file = Path(report_dir) / "trades.csv"
        if not trades_file.exists():
            return pd.DataFrame(columns=["time", "signal", "price"])
        
        df = pd.read_csv(trades_file)
        if df.empty:
            return pd.DataFrame(columns=["time", "signal", "price"])
        
        # 查找时间列
        time_col = None
        for col in ["time", "timestamp", "datetime", "ts"]:
            if col in df.columns:
                time_col = col
                break
        
        if time_col is None:
            return pd.DataFrame(columns=["time", "signal", "price"])
        
        # 查找信号列
        signal_col = None
        for col in ["side", "action", "signal", "direction", "type"]:
            if col in df.columns:
                signal_col = col
                break
        
        if signal_col is None:
            return pd.DataFrame(columns=["time", "signal", "price"])
        
        # 查找价格列
        price_col = None
        for col in ["price", "fill_price", "execution_price"]:
            if col in df.columns:
                price_col = col
                break
        
        if price_col is None:
            return pd.DataFrame(columns=["time", "signal", "price"])
        
        result = df[[time_col, signal_col, price_col]].copy()
        result.columns = ["time", "signal", "price"]
        result["time"] = pd.to_datetime(result["time"], errors="coerce")
        result = result.dropna(subset=["time"])
        
        # 标准化信号
        result["signal"] = result["signal"].astype(str).str.upper()
        result = result[result["signal"].isin(["B", "BUY", "S", "SELL", "LONG", "SHORT"])]
        result["signal"] = result["signal"].map({
            "B": "B", "BUY": "B", "LONG": "B",
            "S": "S", "SELL": "S", "SHORT": "S"
        })
        
        return result
    except Exception:
        return pd.DataFrame(columns=["time", "signal", "price"])


def _build_kline_fig(ticks_df: pd.DataFrame, signals_df: pd.DataFrame) -> go.Figure:
    """构建K线图 + 买卖信号"""
    fig = make_subplots(
        rows=2, cols=1, shared_xaxes=True,
        row_heights=[0.75, 0.25], vertical_spacing=0.08,
        subplot_titles=("价格走势与交易信号", "成交量")
    )
    
    if not ticks_df.empty:
        # 构建1分钟K线数据
        try:
            df_1min = ticks_df.set_index("time").resample("1min").agg({
                "price": ["first", "max", "min", "last"],
                "volume": "sum"
            }).ffill()
            
            df_1min.columns = ["open", "high", "low", "close", "volume"]
            df_1min = df_1min.dropna()
            
            if not df_1min.empty:
                # K线图
                fig.add_trace(
                    go.Candlestick(
                        x=df_1min.index,
                        open=df_1min["open"],
                        high=df_1min["high"],
                        low=df_1min["low"],
                        close=df_1min["close"],
                        name="K线",
                        increasing_line_color="#ef5350",
                        decreasing_line_color="#26a69a"
                    ),
                    row=1, col=1
                )
                
                # 成交量
                colors = ["#ef5350" if c >= o else "#26a69a" 
                         for c, o in zip(df_1min["close"], df_1min["open"])]
                fig.add_trace(
                    go.Bar(
                        x=df_1min.index,
                        y=df_1min["volume"],
                        name="成交量",
                        marker_color=colors,
                        showlegend=False
                    ),
                    row=2, col=1
                )
        except Exception:
            # 如果K线构建失败，使用分时线
            fig.add_trace(
                go.Scatter(
                    x=ticks_df["time"], y=ticks_df["price"],
                    mode="lines", name="价格",
                    line=dict(color="#1f77b4", width=1.5)
                ),
                row=1, col=1
            )
    
    # 添加买卖信号
    if not signals_df.empty:
        buy_signals = signals_df[signals_df["signal"] == "B"]
        sell_signals = signals_df[signals_df["signal"] == "S"]
        
        if not buy_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=buy_signals["time"],
                    y=buy_signals["price"],
                    mode="markers",
                    marker=dict(
                        symbol="triangle-up",
                        size=12,
                        color="#2ca02c",
                        line=dict(width=2, color="#ffffff")
                    ),
                    name="买入信号",
                    showlegend=True
                ),
                row=1, col=1
            )
        
        if not sell_signals.empty:
            fig.add_trace(
                go.Scatter(
                    x=sell_signals["time"],
                    y=sell_signals["price"],
                    mode="markers",
                    marker=dict(
                        symbol="triangle-down",
                        size=12,
                        color="#d62728",
                        line=dict(width=2, color="#ffffff")
                    ),
                    name="卖出信号",
                    showlegend=True
                ),
                row=1, col=1
            )
    
    fig.update_layout(
        margin=dict(l=40, r=20, t=50, b=40),
        height=600,
        template="plotly_white",
        showlegend=True,
        legend=dict(
            orientation="h",
            yanchor="bottom", y=1.02,
            xanchor="left", x=0,
            bgcolor="rgba(255,255,255,0.8)"
        ),
        xaxis=dict(
            title="时间",
            rangebreaks=[
                dict(bounds=["sat", "mon"]),  # 跳过周末
                dict(bounds=[15, 9.5], pattern="hour"),  # 跳过收盘到开盘时间
                dict(bounds=[11.5, 13], pattern="hour")  # 跳过午休时间
            ]
        ),
        yaxis=dict(title="价格", tickformat=".4f"),
        xaxis2=dict(
            title="时间",
            rangebreaks=[
                dict(bounds=["sat", "mon"]),  # 跳过周末
                dict(bounds=[15, 9.5], pattern="hour"),  # 跳过收盘到开盘时间
                dict(bounds=[11.5, 13], pattern="hour")  # 跳过午休时间
            ]
        ),
        yaxis2=dict(title="成交量")
    )
    
    return fig


def _build_equity_fig(eq: pd.DataFrame) -> go.Figure:
    """
    生成净值 + 回撤双图
    期望 eq 含: time, equity (必要), drawdown (可选，若无则据 equity 计算)
    """
    fig = make_subplots(
        rows=2, cols=1, shared_xaxes=True, 
        row_heights=[0.72, 0.28], vertical_spacing=0.08,
        subplot_titles=("净值曲线", "回撤")
    )
    if not eq.empty:
        # 净值
        fig.add_trace(
            go.Scatter(
                x=eq["time"], y=eq["equity"], mode="lines",
                line=dict(color="#1f77b4", width=2), name="净值",
                connectgaps=True,
                fill="tozeroy", fillcolor="rgba(31,119,180,0.1)"
            ),
            row=1, col=1
        )
        # 回撤
        dd = None
        if "drawdown" in eq.columns:
            dd = pd.to_numeric(eq["drawdown"], errors="coerce")
        if dd is None or dd.isna().all():
            # 根据净值计算回撤
            try:
                e = pd.to_numeric(eq["equity"], errors="coerce").fillna(method="ffill")
                peak = e.cummax()
                dd = (e - peak) / peak
            except Exception:
                dd = pd.Series([0.0] * len(eq), index=eq.index)
        fig.add_trace(
            go.Bar(
                x=eq["time"], y=dd.values, name="回撤",
                marker_color="rgba(214,39,40,0.6)", showlegend=False
            ),
            row=2, col=1
        )

    fig.update_layout(
        margin=dict(l=40, r=20, t=50, b=40),
        height=600,
        template="plotly_white",
        showlegend=True,
        legend=dict(
            orientation="h", yanchor="bottom", y=1.02, 
            xanchor="left", x=0, bgcolor="rgba(255,255,255,0.8)"
        ),
        xaxis=dict(
            title="时间",
            rangebreaks=[
                dict(bounds=["sat", "mon"]),  # 跳过周末
                dict(bounds=[15, 9.5], pattern="hour"),  # 跳过收盘到开盘时间
                dict(bounds=[11.5, 13], pattern="hour")  # 跳过午休时间
            ],
        ),
        yaxis=dict(title="净值", tickformat=".2f"),
        yaxis2=dict(title="回撤", tickformat=".1%")
    )
    return fig


def make_app(default_symbol: str = "159740") -> dash.Dash:
    app = dash.Dash(__name__)
    app.title = f"回测监测面板 - {default_symbol}"

    # 控件区
    controls = fac.AntdCard([
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("标的", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="单标的代码，如 159740"
                    )
                ], size="small"),
                fac.AntdInput(id="inp-symbol", defaultValue=default_symbol, placeholder="159740", style={"marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("买入触发跌幅", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="跌幅≤该阈值触发买入(负数)"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-buy-drop", defaultValue=float(BUY_DROP), step=0.0001, style={"width": "100%", "marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("持仓收益目标", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="持仓收益率达到该目标时一次性卖出"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-sell-rise", defaultValue=float(PROFIT_TARGET), step=0.0001, style={"width": "100%", "marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("基准下单极数", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "极pointer"}),
                        title="单次下单基础数量"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-base-qty", defaultValue=int(BASE_QTY), step=100, style={"width": "100%", "marginTop": 4})
            ], span=6),
        ], gutter=[8, 8]),
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("分层比例", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="逗号分隔，如 0.4,0.35,0.25"
                    )
                ], size="small"),
                fac.AntdInput(id="inp-layers", defaultValue=",".join(str(x) for x in LAYER_QTY), style={"marginTop": 4})
            ], span=10),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("基础滑点", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="成交价格的相对偏移"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-slippage", defaultValue=float(DEFAULT_SLIPPAGE), step=0.0001, style={"width": "100%", "marginTop": 4})
            ], span=4),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("手续费", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="交易费用比例"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-fee", defaultValue=float(DEFAULT_FEE), step=0.0001, style={"width": "100%", "marginTop": 4})
            ], span=4),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("滑点体量系数", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="随成交量增大滑点"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-slippage-coef", defaultValue=0.0, step=0.1, style={"width": "100%", "marginTop": 4})
            ], span=6),
        ], gutter=[8, 8], style={"marginTop": 8}),
        fac.AntdRow([
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("初始资金", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="回测起始资金"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-init-cap", defaultValue=1_000_000.0, step=10000, style={"width": "100%", "marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("风险利率(年化)", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="年化无风险利率"
                    )
                ], size="small"),
                fac.AntdInputNumber(id="inp-rf", defaultValue=0.0, step=0.001, style={"width": "100%", "marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("重采样周期", strong=True),
                    fac.AntdTooltip(
                        fac.AntdIcon(icon="antd-question-circle", style={"color": "#1890ff", "cursor": "pointer"}),
                        title="如 1T/5T"
                    )
                ], size="small"),
                fac.AntdInput(id="inp-sample-freq", defaultValue="1T", style={"marginTop": 4})
            ], span=6),
            fac.AntdCol([
                fac.AntdSpace([
                    fac.AntdText("", strong=True),  # 占位符，保持对齐
                    fac.AntdText("", style={"visibility": "hidden"})  # 隐藏的占位符
                ], size="small"),
                fac.AntdButton("运行回测", id="btn-run", type="primary", block=True, style={"marginTop": 4})
            ], span=6),
        ], gutter=[8, 8], style={"marginTop": 8})
    ], title="参数配置", size="small")

    # 展示区
    # 展示区 - 美化设计
    kpi_card = fac.AntdCard(
        id="kpi-cards", 
        title="📊 关键指标", 
        size="small", 
        styles={"body": {"padding": "16px"}},
        style={"borderRadius": "8px", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}
    )
    
    equity_card = fac.AntdCard(
        dcc.Graph(id="fig-backtest", style={"width": "100%", "height": "600px"}), 
        title="📈 净值与回撤", 
        size="small",
        style={"borderRadius": "8px", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}
    )
    
    kline_card = fac.AntdCard(
        dcc.Graph(id="fig-kline", style={"width": "100%", "height": "600px"}), 
        title="📊 K线图与交易信号", 
        size="small",
        style={"borderRadius": "8px", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}
    )
    
    table_card = fac.AntdCard(
        fac.AntdTable(id="tbl-trades", bordered=True, pagination={"pageSize": 10}), 
        title="📋 成交明细", 
        size="small",
        style={"borderRadius": "8px", "boxShadow": "0 2px 8px rgba(0,0,0,0.1)"}
    )
    
    msg_text = fac.AntdText(id="run-msg", children="", style={"fontSize": "14px"})

    app.layout = fac.AntdLayout([
        # 美化的头部
        fac.AntdHeader([
            fac.AntdRow([
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdTitle("🚀 ETF套利回测监测面板", level=2, style={"margin": 0, "color": "#1890ff"}),
                        fac.AntdTag(f"标的: {default_symbol}", color="blue")
                    ], size="middle")
                ], span=16),
                fac.AntdCol([
                    fac.AntdSpace([
                        fac.AntdIcon(icon="antd-dashboard", style={"fontSize": "24px", "color": "#1890ff"}),
                        msg_text
                    ], size="small", align="center")
                ], span=8, style={"textAlign": "right"})
            ], align="middle")
        ], style={
            "background": "linear-gradient(135deg, #667eea 0%, #764ba2 100%)", 
            "padding": "16px 24px",
            "boxShadow": "0 2px 8px rgba(0,0,0,0.15)"
        }),
        
        # 主内容区
        fac.AntdContent([
            # 参数配置区
            fac.AntdRow([
                fac.AntdCol(controls, span=24)
            ], style={"marginBottom": 16}),
            
            # KPI指标区
            fac.AntdRow([
                fac.AntdCol(kpi_card, span=24)
            ], style={"marginBottom": 16}),
            
            # 图表区 - 左右布局
            fac.AntdRow([
                fac.AntdCol(equity_card, span=12),
                fac.AntdCol(kline_card, span=12)
            ], gutter=16, style={"marginBottom": 16}),
            
            # 交易明细区
            fac.AntdRow([
                fac.AntdCol(table_card, span=24)
            ])
        ], style={
            "padding": "20px", 
            "background": "#f0f2f5",
            "minHeight": "calc(100vh - 80px)"
        })
    ], style={"minHeight": "100vh"})

    @app.callback(
        Output("kpi-cards", "children"),
        Output("fig-backtest", "figure"),
        Output("fig-kline", "figure"),
        Output("tbl-trades", "columns"),
        Output("tbl-trades", "data"),
        Output("run-msg", "children"),
        Input("btn-run", "nClicks"),
        State("inp-symbol", "value"),
        State("inp-buy-drop", "value"),
        State("inp-sell-rise", "value"),
        State("inp-base-qty", "value"),
        State("inp-layers", "value"),
        State("inp-slippage", "value"),
        State("inp-fee", "value"),
        State("inp-slippage-coef", "value"),
        State("inp-init-cap", "value"),
        State("inp-rf", "value"),
        State("inp-sample-freq", "value"),
        prevent_initial_call=True
    )
    def _run_bt(_n, symbol, buy_drop, sell_rise, base_qty, layers_str, slippage, fee, slp_coef, init_cap, rf, sample_freq):
        # 默认输出
        empty_fig = go.Figure()
        empty_kline_fig = go.Figure()
        empty_cols: List[Dict[str, Any]] = []
        empty_rows: List[Dict[str, Any]] = []
        msg = ""

        try:
            sym = str(symbol).strip() if symbol else ""
            if not sym:
                return dash.no_update, empty_fig, empty_kline_fig, empty_cols, empty_rows, "请填写标的代码"

            # 解析分层
            try:
                layers = [float(x.strip()) for x in str(layers_str).split(",") if str(x).strip() != ""]
                if not layers:
                    layers = list(LAYER_QTY)
            except Exception:
                layers = list(LAYER_QTY)

            # 加载数据
            # 加载数据
            df = load_ticks(sym)
            if df is None or df.empty:
                return dash.no_update, empty_fig, empty_kline_fig, empty_cols, empty_rows, f"[{sym}] 无可用 tick 数据"

            # 输出目录
            report_root = Path("./bt_reports_dashboard") / sym
            _ensure_dir(report_root)

            # 运行回测
            stats = run_backtest(
                df,
                buy_drop=_safe_float(buy_drop, BUY_DROP),
                sell_rise=_safe_float(sell_rise, PROFIT_TARGET),
                base_qty=_safe_int(base_qty, BASE_QTY),
                layer_qty=layers,
                base_slippage=_safe_float(slippage, DEFAULT_SLIPPAGE),
                fee=_safe_float(fee, DEFAULT_FEE),
                slippage_coef=_safe_float(slp_coef, 0.0),
                plot_file=None,
                initial_capital=_safe_float(init_cap, 1_000_000.0),
                risk_free_rate=_safe_float(rf, 0.0),
                sample_freq=str(sample_freq or "1T"),
                report_dir=str(report_root),
            )

            # KPI 卡片
            def card(label: str, value: Any, fmt: Optional[str] = None):
                try:
                    if fmt == "pct":
                        txt = f"{_safe_float(value)*100:.2f}%"
                    elif fmt == "int":
                        txt = f"{_safe_int(value)}"
                    else:
                        txt = f"{_safe_float(value):.4f}"
                except Exception:
                    txt = str(value)
                return fac.AntdCard(fac.AntdStatistic(title=label, value=txt), size="small")

            kpi_keys: List[Tuple[str, str, Optional[str]]] = [
                ("final_equity", "期末净值", None),
                ("pnl", "收益", None),
                ("cum_return", "累计收益率", "pct"),
                ("annual_return", "年化收益", "pct"),
                ("annual_vol", "年化波动", "pct"),
                ("sharpe_annual", "年化Sharpe", None),
                ("sortino_annual", "年化Sortino", None),
                ("calmar", "Calmar", None),
                ("max_drawdown", "最大回撤", "pct"),
                ("num_trades", "成交笔数", "int"),
                ("num_rounds", "回合数", "int"),
                ("win_rate", "胜率", "pct"),
                ("profit_factor", "ProfitFactor", None),
                ("exposure_ratio", "曝险比例", "pct"),
            ]
            # 改为紧凑自适应的描述列表，减少留白
            kpi_items_desc = []
            for k, label, fmt in kpi_keys:
                v = stats.get(k, None) if isinstance(stats, dict) else None
                # 文本化
                try:
                    if fmt == "pct":
                        txt = f"{_safe_float(v)*100:.2f}%"
                    elif fmt == "int":
                        txt = f"{_safe_int(v)}"
                    else:
                        txt = f"{_safe_float(v):.4f}"
                except Exception:
                    txt = str(v)
                kpi_items_desc.append({"label": label, "children": txt})
            kpi_row = fac.AntdDescriptions(
                items=kpi_items_desc,
                size="small",
                bordered=False,
                layout="horizontal",
                column={"xs": 1, "sm": 2, "md": 3, "lg": 4, "xl": 4}
            )

            # 读取净值/回撤
            eq_file = report_root / "equity_resampled.csv"
            fig = empty_fig
            if eq_file.exists():
                try:
                    eq = pd.read_csv(eq_file)
                    eq = pd.read_csv(eq_file)
                    if "time" in eq.columns:
                        eq["time"] = pd.to_datetime(eq["time"], errors="coerce")
                        eq = eq.dropna(subset=["time"])
                        # 确保时间列按时间顺序排序
                        eq = eq.sort_values("time").reset_index(drop=True)
                    else:
                        # 如果没有时间列，尝试从索引构造或使用行号
                        if eq.index.dtype == 'datetime64[ns]':
                            eq["time"] = eq.index
                        else:
                            # 使用更合理的时间构造：从今天开始往前推
                            eq["time"] = pd.date_range(
                                end=pd.Timestamp.now().normalize() + pd.Timedelta(hours=15), 
                                periods=len(eq), 
                                freq="1min"
                            )
                    # 列命名兼容
                    if "equity" not in eq.columns:
                        # 尝试从 'equity_value' 或 'equity' 兼容
                        for cand in ("equity_value", "equity_curve", "value"):
                            if cand in eq.columns:
                                eq = eq.rename(columns={cand: "equity"})
                                break
                    fig = _build_equity_fig(eq)
                    fig = _build_equity_fig(eq)
                except Exception as e:
                    logger.error(f"读取净值文件失败: {e}")
                    fig = empty_fig

            # 生成K线图
            kline_fig = empty_kline_fig
            try:
                # 加载tick数据用于K线图
                ticks_df = _load_ticks_for_backtest(sym)
                # 加载交易信号
                signals_df = _load_backtest_signals(sym, str(report_root))
                # 生成K线图
                kline_fig = _build_kline_fig(ticks_df, signals_df)
            except Exception as e:
                logger.error(f"生成K线图失败: {e}")
                kline_fig = empty_kline_fig

            # 读取成交
            cols, rows = empty_cols, empty_rows
            buy_count = 0
            sell_count = 0
            trades_file = report_root / "trades.csv"
            if trades_file.exists():
                try:
                    tdf = pd.read_csv(trades_file)
                    # 统计买卖方向
                    side_col = None
                    for cand in ("side", "action", "signal", "direction", "type"):
                        if cand in tdf.columns:
                            side_col = cand
                            break
                    if side_col:
                        s = tdf[side_col].astype(str).str.upper()
                        buy_count = int(s.isin(["B","BUY","LONG","OPEN_LONG"]).sum())
                        sell_count = int(s.isin(["S","SELL","SHORT","CLOSE_LONG","EXIT"]).sum())
                    cols = [{"title": c, "dataIndex": c} for c in tdf.columns]
                    rows = tdf.to_dict(orient="records")
                except Exception as e:
                    logger.error(f"读取交易文件失败: {e}")
                    cols, rows = empty_cols, empty_rows
            elif isinstance(stats, dict) and isinstance(stats.get("trades"), (list, tuple)):
                try:
                    tdf = pd.DataFrame(stats["trades"])
                    if not tdf.empty:
                        side_col = None
                        for cand in ("side", "action", "signal", "direction", "type"):
                            if cand in tdf.columns:
                                side_col = cand
                                break
                        if side_col:
                            s = tdf[side_col].astype(str).str.upper()
                            buy_count = int(s.isin(["B","BUY","LONG","OPEN_LONG"]).sum())
                            sell_count = int(s.isin(["S","SELL","SHORT","CLOSE_LONG","EXIT"]).sum())
                        cols = [{"title": c, "dataIndex": c} for c in tdf.columns]
                        rows = tdf.to_dict(orient="records")
                except Exception:
                    cols, rows = empty_cols, empty_rows

            msg = f"[{sym}] 回测完成。报表目录: {report_root}"
            msg = f"[{sym}] 回测完成。报表目录: {report_root}"
            if buy_count or sell_count:
                msg += f" | 成交统计：BUY={buy_count}, SELL={sell_count}"
            return kpi_row, fig, kline_fig, cols, rows, msg
            return kpi_row, fig, kline_fig, cols, rows, msg

        except Exception as e:
            logger.error(f"回测运行失败: {e}")
            return dash.no_update, empty_fig, empty_kline_fig, empty_cols, empty_rows, f"回测失败: {e}"

    return app


def run_backtest_dashboard(symbol: str, host: str = "127.0.0.1", port: int = 8051) -> None:
    app = make_app(symbol)
    # Dash 3+
    app.run(debug=False, host=host, port=port)


if __name__ == "__main__":
    import argparse
    p = argparse.ArgumentParser()
    p.add_argument("--symbol", default="159740")
    p.add_argument("--host", default="127.0.0.1")
    p.add_argument("--port", type=int, default=8051)
    args = p.parse_args()
    run_backtest_dashboard(args.symbol, args.host, args.port)