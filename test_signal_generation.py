#!/usr/bin/env python3
"""
测试信号生成功能
"""

import sqlite3
import pandas as pd
from datetime import datetime
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from strategy_engine_enhanced import EnhancedStrategy
from app_enhanced_realtime_dashboard import RealtimeSimulator

def test_data_retrieval():
    """测试数据获取"""
    print("=== 测试数据获取 ===")
    
    try:
        conn = sqlite3.connect('ticks.db')
        
        # 检查数据总数
        cursor = conn.execute('SELECT COUNT(*) FROM ticks WHERE symbol = ?', ('159740',))
        total_count = cursor.fetchone()[0]
        print(f"数据库中159740的记录数: {total_count}")
        
        # 获取最近20条记录
        query = """
        SELECT tick_time, price, volume
        FROM ticks
        WHERE symbol = ?
        ORDER BY tick_time DESC
        LIMIT 20
        """
        
        df = pd.read_sql_query(query, conn, params=['159740'])
        print(f"获取到的记录数: {len(df)}")
        
        if not df.empty:
            df = df.sort_values('tick_time').reset_index(drop=True)
            df['tick_time'] = pd.to_datetime(df['tick_time'])
            df['price'] = pd.to_numeric(df['price'], errors='coerce')
            
            print(f"价格范围: {df['price'].min():.4f} - {df['price'].max():.4f}")
            print(f"时间范围: {df['tick_time'].min()} 到 {df['tick_time'].max()}")
            print("最近5条记录:")
            print(df.tail().to_string())
        
        conn.close()
        return df
        
    except Exception as e:
        print(f"数据获取失败: {e}")
        return pd.DataFrame()

def test_signal_calculation(df):
    """测试信号计算"""
    print("\n=== 测试信号计算 ===")
    
    if df.empty:
        print("没有数据，无法测试信号计算")
        return
    
    try:
        # 创建策略引擎
        strategy = EnhancedStrategy(symbol='159740')
        
        # 重命名列以匹配策略引擎期望的格式
        test_df = df.rename(columns={'tick_time': 'time'}).copy()
        
        print(f"传入信号计算的数据:")
        print(f"  数据点数: {len(test_df)}")
        print(f"  列名: {list(test_df.columns)}")
        print(f"  价格范围: {test_df['price'].min():.4f} - {test_df['price'].max():.4f}")
        
        # 计算信号
        signal = strategy._safe_return_20ticks(test_df)
        print(f"计算得到的信号: {signal:.6f} ({signal*100:.4f}%)")
        
        # 测试买卖判断
        buy_threshold = strategy.current_params.get('buy_drop', -0.006)
        profit_threshold = strategy.current_params.get('profit_target', 0.0025)
        
        print(f"买入阈值: {buy_threshold:.6f} ({buy_threshold*100:.4f}%)")
        print(f"止盈阈值: {profit_threshold:.6f} ({profit_threshold*100:.4f}%)")
        
        if signal <= buy_threshold:
            print("🟢 信号判断: 买入信号")
        elif signal >= profit_threshold:
            print("🔴 信号判断: 卖出信号")
        else:
            print("🟡 信号判断: 持有")
            
        return signal
        
    except Exception as e:
        print(f"信号计算失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_realtime_simulator():
    """测试实时模拟器"""
    print("\n=== 测试实时模拟器 ===")
    
    try:
        simulator = RealtimeSimulator()
        
        # 获取最新的tick数据
        conn = sqlite3.connect('ticks.db')
        cursor = conn.execute("""
            SELECT tick_time, price, volume
            FROM ticks
            WHERE symbol = '159740'
            ORDER BY tick_time DESC
            LIMIT 1
        """)
        
        latest_tick = cursor.fetchone()
        conn.close()
        
        if latest_tick:
            tick_data = {
                'time': latest_tick[0],
                'price': latest_tick[1],
                'volume': latest_tick[2]
            }
            
            print(f"最新tick数据: {tick_data}")
            
            # 处理tick数据
            result = simulator.process_tick(tick_data)
            print(f"处理结果: {result}")
            
            return result
        else:
            print("没有找到最新的tick数据")
            return None
            
    except Exception as e:
        print(f"实时模拟器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    print("开始测试信号生成功能...")
    
    # 1. 测试数据获取
    df = test_data_retrieval()
    
    # 2. 测试信号计算
    if not df.empty:
        signal = test_signal_calculation(df)
    
    # 3. 测试实时模拟器
    result = test_realtime_simulator()
    
    print("\n=== 测试完成 ===")
    if not df.empty and signal is not None:
        print("✅ 信号生成功能正常")
    else:
        print("❌ 信号生成功能存在问题")
