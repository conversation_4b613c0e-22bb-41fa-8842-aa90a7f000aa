# 增强版ETF套利策略

## 概述

基于原有策略的增强版本，添加了止损机制、动态参数调整、市场状态识别等功能。

## 主要改进

### 1. 风险控制增强
- **止损机制**: 设置-2%止损和最大持仓时间限制
- **风险管理**: 日内损失限制和最大回撤控制
- **资金管理**: 动态仓位管理和风险预算

### 2. 智能参数调整
- **动态阈值**: 根据市场波动率自动调整买卖阈值
- **市场识别**: 自动识别趋势市、震荡市和异常市场
- **参数优化**: 基于历史表现自动优化参数

### 3. 分批交易策略
- **分批买入**: 保持原有分层买入逻辑
- **分批卖出**: 在0.3%、0.6%、1.2%分批止盈
- **仓位管理**: 更灵活的仓位控制

## 文件结构

```
strategy_engine_enhanced.py  # 增强版策略引擎
strategy_config.py          # 策略配置管理
strategy_monitor.py         # 性能监控工具
run_enhanced_strategy.py    # 启动脚本
```

## 使用方法

### 1. 运行增强版策略
```bash
python run_enhanced_strategy.py --symbol 159740 --mode enhanced
```

### 2. 性能监控
```bash
python strategy_monitor.py --symbol 159740 --days 7
```

### 3. 配置调整
编辑 `strategy_config.py` 中的参数设置。

## 核心参数

### 基础参数
- `BUY_TRIGGER_DROP`: 买入触发跌幅 (-0.6%)
- `PROFIT_TARGET`: 持仓收益目标 (0.6%)
- `STOP_LOSS`: 止损阈值 (-2.0%)
- `MAX_HOLD_TIME`: 最大持仓时间 (3600秒)

### 风险控制
- `DAILY_LOSS_LIMIT`: 日损失限制 (-5%)
- `MAX_DRAWDOWN_LIMIT`: 最大回撤限制 (-10%)
- `RISK_FREE_RATE`: 无风险利率 (3%)

### 分批交易
- `PARTIAL_SELL_LEVELS`: [0.3%, 0.6%, 1.2%]
- `PARTIAL_SELL_RATIOS`: [30%, 40%, 30%]

## 策略逻辑

### 买入条件
1. 20秒回报率 ≤ 买入阈值
2. 当前仓位 < 最大仓位
3. 风险检查通过
4. 资金充足

### 卖出条件
1. **止盈**: 持仓收益率达到分批卖出点位
2. **止损**: 持仓亏损达到止损线
3. **时间止损**: 持仓时间超过最大限制
4. **风险止损**: 触发风险管理限制

### 动态调整
- 根据市场波动率调整买卖阈值
- 根据市场状态调整策略参数
- 根据历史表现优化参数设置

## 监控指标

### 性能指标
- 总收益率
- 最大回撤
- 夏普比率
- 胜率
- 平均持仓时间

### 风险指标
- VaR (风险价值)
- 最大连续亏损
- 波动率
- Beta系数

## 注意事项

1. **参数调整**: 建议先在模拟环境测试参数调整效果
2. **风险控制**: 严格遵守风险管理规则，不要随意放宽限制
3. **市场适应**: 定期检查策略在不同市场环境下的表现
4. **监控报警**: 设置关键指标的报警阈值

## 后续优化方向

1. **机器学习**: 使用ML模型预测最优参数
2. **多因子模型**: 结合更多市场因子
3. **组合优化**: 多标的组合策略
4. **高频优化**: 更精细的时间窗口和信号