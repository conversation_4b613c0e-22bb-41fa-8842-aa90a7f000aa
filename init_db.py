# init_db.py
"""
初始化本地 SQLite 数据库与 ticks 表
用法: python init_db.py
"""
import sqlite3
from pathlib import Path

DB = Path(__file__).resolve().parent / "ticks.db"


def init_db(path=DB):
    conn = sqlite3.connect(path)
    c = conn.cursor()
    c.execute("""
    CREATE TABLE IF NOT EXISTS ticks (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        symbol TEXT NOT NULL,
        tick_time TEXT NOT NULL,
        price REAL NOT NULL,
        volume INTEGER NOT NULL,
        side TEXT,
        raw TEXT
    );
    """)
    # 唯一索引，避免重复写入（同一标的、时间、价格、成交量视为重复）
    c.execute("CREATE UNIQUE INDEX IF NOT EXISTS ux_ticks_symbol_time_price_vol ON ticks(symbol, tick_time, price, volume);")
    # 记录每个标的上次写入的 tick 时间，方便增量拉取
    c.execute("""
    CREATE TABLE IF NOT EXISTS last_ticks (
        symbol TEXT PRIMARY KEY,
        last_time TEXT
    );
    """)
    c.execute("CREATE INDEX IF NOT EXISTS idx_last_ticks_symbol ON last_ticks(symbol);")
    conn.commit()
    conn.close()


if __name__ == "__main__":
    init_db()
    print(f"initialized {DB}")
