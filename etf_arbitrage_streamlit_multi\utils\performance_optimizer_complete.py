#!/usr/bin/env python3
"""
性能优化模块 - 数据采样和图表优化 (完整版)
解决大数据量导致的前端卡顿问题
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from typing import Dict, List, Optional, Tuple, Union
import warnings
warnings.filterwarnings('ignore')

# ==================== 主要接口函数 ====================

def optimize_chart_data(df: pd.DataFrame, config: dict) -> pd.DataFrame:
    """优化图表数据 - 主要接口函数"""
    return smart_sample_data(
        df, 
        max_points=config.get('max_points', 2000),
        preserve_extremes=config.get('preserve_extremes', True)
    )

def optimize_trade_data(trades: pd.DataFrame, config: dict) -> pd.DataFrame:
    """优化交易数据"""
    if len(trades) <= config.get('max_points', 2000):
        return trades
    return smart_sample_data(trades, max_points=config.get('max_points', 2000))

def smart_sample_data(df: pd.DataFrame, max_points: int = 2000, preserve_extremes: bool = True) -> pd.DataFrame:
    """智能数据采样 - 保留关键特征"""
    try:
        if df is None or df.empty:
            return df
        
        # 如果数据量不大，直接返回
        if len(df) <= max_points:
            return df
        
        print(f"📊 数据采样: {len(df)} -> {max_points} 点")
        
        # 重置索引确保连续
        df_work = df.reset_index(drop=True).copy()
        
        # 计算采样间隔
        step = len(df_work) / max_points
        
        # 基础均匀采样
        indices = np.linspace(0, len(df_work) - 1, max_points, dtype=int)
        sampled_df = df_work.iloc[indices].copy()
        
        # 如果需要保留极值点
        if preserve_extremes and len(df_work.columns) > 0:
            # 找到数值列
            numeric_cols = df_work.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                # 对每个数值列找极值
                extreme_indices = set()
                
                for col in numeric_cols:
                    if col in df_work.columns:
                        # 最大值和最小值的索引
                        max_idx = df_work[col].idxmax()
                        min_idx = df_work[col].idxmin()
                        
                        if pd.notna(max_idx):
                            extreme_indices.add(max_idx)
                        if pd.notna(min_idx):
                            extreme_indices.add(min_idx)
                
                # 添加极值点
                if extreme_indices:
                    extreme_df = df_work.loc[list(extreme_indices)]
                    sampled_df = pd.concat([sampled_df, extreme_df]).drop_duplicates()
                    
                    # 按原始顺序排序
                    if 'timestamp' in sampled_df.columns:
                        sampled_df = sampled_df.sort_values('timestamp')
                    elif sampled_df.index.name:
                        sampled_df = sampled_df.sort_index()
        
        # 限制最终大小
        if len(sampled_df) > max_points:
            sampled_df = sampled_df.iloc[::len(sampled_df)//max_points][:max_points]
        
        return sampled_df.reset_index(drop=True)
        
    except Exception as e:
        print(f"⚠️ 采样失败，返回原数据: {e}")
        return df

# ==================== 优化图表生成函数 ====================

def create_optimized_price_chart(df: pd.DataFrame, config: dict, signals: pd.DataFrame = None) -> go.Figure:
    """创建性能优化的价格走势图"""
    try:
        # 优化数据
        optimized_df = optimize_chart_data(df, config)
        
        fig = go.Figure()
        
        # 添加价格线
        if 'close' in optimized_df.columns:
            fig.add_trace(go.Scatter(
                x=optimized_df.index if 'timestamp' not in optimized_df.columns else optimized_df['timestamp'],
                y=optimized_df['close'],
                mode='lines',
                name='价格',
                line=dict(color='blue', width=1)
            ))
        
        # 添加交易信号
        if signals is not None and not signals.empty:
            optimized_signals = optimize_trade_data(signals, config)
            
            # 买入信号
            buy_signals = optimized_signals[optimized_signals.get('signal', 0) > 0]
            if not buy_signals.empty:
                fig.add_trace(go.Scatter(
                    x=buy_signals.index,
                    y=buy_signals.get('price', buy_signals.get('close', 0)),
                    mode='markers',
                    name='买入',
                    marker=dict(color='red', size=8, symbol='triangle-up')
                ))
            
            # 卖出信号
            sell_signals = optimized_signals[optimized_signals.get('signal', 0) < 0]
            if not sell_signals.empty:
                fig.add_trace(go.Scatter(
                    x=sell_signals.index,
                    y=sell_signals.get('price', sell_signals.get('close', 0)),
                    mode='markers',
                    name='卖出',
                    marker=dict(color='green', size=8, symbol='triangle-down')
                ))
        
        # 优化布局
        fig.update_layout(
            title="价格走势与交易信号",
            xaxis_title="时间",
            yaxis_title="价格",
            height=config.get('chart_height', 600),
            showlegend=True,
            hovermode='x unified'
        )
        
        # 启用WebGL渲染
        if config.get('use_webgl', True):
            fig.update_traces(line=dict(simplify=True))
        
        return fig
        
    except Exception as e:
        print(f"⚠️ 图表生成失败: {e}")
        # 返回简单图表
        return go.Figure().add_annotation(text=f"图表生成失败: {e}", x=0.5, y=0.5)

def create_optimized_equity_curve(equity_data: pd.DataFrame, config: dict) -> go.Figure:
    """创建性能优化的净值曲线"""
    try:
        optimized_data = optimize_chart_data(equity_data, config)
        
        fig = go.Figure()
        
        if 'equity' in optimized_data.columns:
            fig.add_trace(go.Scatter(
                x=optimized_data.index,
                y=optimized_data['equity'],
                mode='lines',
                name='净值曲线',
                line=dict(color='green', width=2)
            ))
        
        # 添加回撤
        if 'drawdown' in optimized_data.columns:
            fig.add_trace(go.Scatter(
                x=optimized_data.index,
                y=optimized_data['drawdown'],
                mode='lines',
                name='回撤',
                line=dict(color='red', width=1),
                yaxis='y2'
            ))
        
        fig.update_layout(
            title="净值曲线与回撤",
            xaxis_title="时间",
            yaxis_title="净值",
            yaxis2=dict(title="回撤", overlaying='y', side='right'),
            height=config.get('chart_height', 600),
            showlegend=True
        )
        
        return fig
        
    except Exception as e:
        print(f"⚠️ 净值图表生成失败: {e}")
        return go.Figure().add_annotation(text=f"净值图表生成失败: {e}", x=0.5, y=0.5)

def create_optimized_trade_chart(trades: pd.DataFrame, config: dict) -> go.Figure:
    """创建性能优化的交易分析图"""
    try:
        optimized_trades = optimize_trade_data(trades, config)
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('交易盈亏', '持仓时间'),
            vertical_spacing=0.1
        )
        
        if 'pnl' in optimized_trades.columns:
            # 盈亏分布
            colors = ['green' if x > 0 else 'red' for x in optimized_trades['pnl']]
            fig.add_trace(
                go.Bar(x=optimized_trades.index, y=optimized_trades['pnl'], 
                      marker_color=colors, name='盈亏'),
                row=1, col=1
            )
        
        if 'duration' in optimized_trades.columns:
            # 持仓时间
            fig.add_trace(
                go.Scatter(x=optimized_trades.index, y=optimized_trades['duration'],
                          mode='markers', name='持仓时间'),
                row=2, col=1
            )
        
        fig.update_layout(
            height=config.get('chart_height', 600),
            showlegend=True,
            title_text="交易分析"
        )
        
        return fig
        
    except Exception as e:
        print(f"⚠️ 交易图表生成失败: {e}")
        return go.Figure().add_annotation(text=f"交易图表生成失败: {e}", x=0.5, y=0.5)

# ==================== 性能监控函数 ====================

def display_performance_info(original_size: int, optimized_size: int):
    """显示性能优化信息"""
    if original_size > optimized_size:
        compression_ratio = (1 - optimized_size / original_size) * 100
        st.info(f"📊 数据优化: {original_size:,} → {optimized_size:,} 点 (压缩 {compression_ratio:.1f}%)")

def get_performance_stats(df: pd.DataFrame, config: dict) -> dict:
    """获取性能统计信息"""
    return {
        'original_size': len(df),
        'max_points': config.get('max_points', 2000),
        'will_optimize': len(df) > config.get('max_points', 2000),
        'compression_ratio': max(0, (1 - min(len(df), config.get('max_points', 2000)) / len(df)) * 100)
    }

# ==================== 兼容性函数 ====================

class DataSampler:
    """智能数据采样器 - 兼容性类"""
    
    def __init__(self, max_points: int = 2000):
        self.max_points = max_points
    
    def smart_sample(self, df: pd.DataFrame, time_col: str = 'time', 
                    value_cols: List[str] = None) -> pd.DataFrame:
        """智能采样 - 保留关键数据点"""
        return smart_sample_data(df, self.max_points, preserve_extremes=True)

# ==================== 导出函数列表 ====================

__all__ = [
    'optimize_chart_data',
    'optimize_trade_data', 
    'smart_sample_data',
    'create_optimized_price_chart',
    'create_optimized_equity_curve',
    'create_optimized_trade_chart',
    'display_performance_info',
    'get_performance_stats',
    'DataSampler'
]